






#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
@protocol XXGPlayDelegate;

NS_ASSUME_NONNULL_BEGIN

@interface XXGPlayCN : NSObject

+ (void)xxpk_setPlayDelegate:(id<XXGPlayDelegate>)delegate;


+ (void)xxpk_comein;


+ (void)xxpk_logout;


+ (void)xxpk_createOrder:(NSString *)xxpk_cpOrderId
        xxpk_productCode:(NSString *)xxpk_productCode
             xxpk_amount:(NSString *)xxpk_amount
        xxpk_productName:(NSString *)xxpk_productName
           xxpk_serverId:(NSString *)xxpk_serverId
          xxpk_extraInfo:(NSString *)xxpk_extraInfo
             xxpk_roleId:(NSString *)xxpk_roleId
           xxpk_roleName:(NSString *)xxpk_roleName
          xxpk_roleLevel:(NSString *)xxpk_roleLevel;


+ (void)xxpk_uploadRoleInfo:(NSString * _Nonnull)xxpk_serverId
            xxpk_serverName:(NSString * _Nonnull)xxpk_serverName
                xxpk_roleId:(NSString * _Nonnull)xxpk_roleId
              xxpk_roleName:(NSString * _Nonnull)xxpk_roleName
             xxpk_roleLevel:(NSString * _Nonnull)xxpk_roleLevel
                xxpk_extend:(NSDictionary * _Nullable)xxpk_extend;


+ (void)xxpk_didFinishLaunchingWithOptions:(NSDictionary *_Nullable)launchOptions xconnectOptions:(UISceneConnectionOptions *_Nullable)connectionOptions;


+ (BOOL)xxpk_applicationOpenURL:(NSURL *_Nullable)url xoptions:(NSDictionary<UIApplicationOpenURLOptionsKey,id> *_Nullable)options xURLContexts:(NSSet<UIOpenURLContext *> *_Nullable)URLContexts;


+ (void)xxpk_openUserCenterSidebar:(NSString *)type;


+ (void)xxpk_iapRepair;
@end

NS_ASSUME_NONNULL_END
