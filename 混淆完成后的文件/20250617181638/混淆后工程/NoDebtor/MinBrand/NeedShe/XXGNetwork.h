






#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface XXGNetwork : NSObject

@property (nonatomic, copy) NSString *xxpk_url;

+ (instancetype)xxpk_defaultNetwork;

- (void)xxpk_sendRequest:(NSString *)url
                  params:(NSDictionary * _Nullable)params
                 success:(void(^_Nullable)(NSDictionary *responseObject))success
                 failure:(void(^_Nullable)(NSError *error))failure;

@end

NS_ASSUME_NONNULL_END
