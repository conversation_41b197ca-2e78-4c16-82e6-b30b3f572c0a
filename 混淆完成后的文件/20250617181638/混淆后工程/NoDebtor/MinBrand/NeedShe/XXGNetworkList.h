






#import "XXGNetwork.h"
#import "XXGBoxContent.h"

NS_ASSUME_NONNULL_BEGIN

@interface XXGNetworkList : XXGNetwork

- (void)xxpk_networkStart:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure;

- (void)xxpk_networkRegisterWithBoxName:(NSString *)boxName boxKey:(NSString *)boxKey success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure;

- (void)xxpk_networkAccountWithBoxName:(NSString *)boxName boxKey:(NSString *)boxKey success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure;

- (void)xxpk_networkGuest:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure;

- (void)xxpk_networkFacebookWithUid:(NSString *)uid uToken:(NSString *)uToken authToken:(NSString *)authToken nonce:(NSString *)nonce success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure;

- (void)xxpk_networkBindFacebookWithUid:(NSString *)uid uToken:(NSString *)uToken authToken:(NSString *)authToken nonce:(NSString *)nonce success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure;

- (void)xxpk_networkVKWithUid:(NSString *)uid uToken:(NSString *)uToken success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure;

- (void)xxpk_networkBindVKWithUid:(NSString *)uid uToken:(NSString *)uToken success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure;

- (void)xxpk_networkPoopoWithUid:(NSString *)uid uToken:(NSString *)uToken success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure;

- (void)xxpk_networkReportAdjustId:(NSString *)arg;


- (void)xxpk_networkReportlogWithType:(NSString *)xxpk_type xxpk_content:(NSString *)xxpk_content;

- (void)xxpk_networkToken:(nullable void(^)(NSDictionary *responseObject))success failure:(nullable void(^)(NSError *error))failure;


- (void)xxpk_networkVerifyCodeType:(NSString *)type mobile:(NSString *)xxpk_mobile dialCode:(NSString *)dialCode success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure;

- (void)xxpk_networkMobileWithMobile:(NSString *)xxpk_mobile code:(NSString *)code dialCode:(NSString *)dialCode success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure;

- (void)xxpk_networkForgetKeyWithMobile:(NSString *)xxpk_mobile code:(NSString *)code dialCode:(NSString *)dialCode newKey:(NSString *)newKey success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure;

- (void)xxpk_networkChangeBoxKeyWithOldKBoxKey:(NSString *)oldBoxKey newBoxKey:(NSString *)newBoxKey success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure;

- (void)xxpk_networkBindMobileWithMobile:(NSString *)xxpk_mobile code:(NSString *)code dialCode:(NSString *)dialCode success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure;

- (void)xxpk_networkCreateOrder:(BOOL)isCoin params:(NSDictionary *)params success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure;

- (void)xxpk_networkValidateReceipt:(NSDictionary *)params success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure;

- (void)xxpk_networkOrderExtra:(NSString *)xxpk_orderId pmethod:(NSString *)pmethod success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure;

- (void)xxpk_networkCheckOrderWithIsCoin:(BOOL)isCoin
                            xxpk_orderId:(NSString *)xxpk_orderId
                                 success:(void(^)(NSDictionary *responseObject))success
                                 failure:(void(^)(NSError *error))failure
                              retryCount:(NSInteger)retryCount
                          currentAttempt:(NSInteger)currentAttempt;

- (void)xxpk_networkUploadRoleInfo:(NSDictionary *)params success:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure;

- (void)xxpk_networkMqtt:(void(^)(NSDictionary *responseObject))success;

- (void)xxpk_networkRemoveAccount:(void(^)(NSDictionary *responseObject))success failure:(void(^)(NSError *error))failure;
@end

NS_ASSUME_NONNULL_END
