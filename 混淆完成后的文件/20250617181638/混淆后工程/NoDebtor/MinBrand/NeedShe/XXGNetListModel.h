






#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface XXGNetListModel : NSObject

@property (nonatomic, copy) NSString *xxpk_list_account_remove;
@property (nonatomic, copy) NSString *xxpk_list_adjustid_report;
@property (nonatomic, copy) NSString *xxpk_list_adview;
@property (nonatomic, copy) NSString *xxpk_list_asa_report;
@property (nonatomic, copy) NSString *xxpk_list_facebook_auth;
@property (nonatomic, copy) NSString *xxpk_list_id_report;
@property (nonatomic, copy) NSString *xxpk_list_comein;
@property (nonatomic, copy) NSString *xxpk_list_comein_guest;
@property (nonatomic, copy) NSString *xxpk_list_comein_mobile;
@property (nonatomic, copy) NSString *xxpk_list_comein_token;
@property (nonatomic, copy) NSString *xxpk_list_comein_v;
@property (nonatomic, copy) NSString *xxpk_list_comin_one_click;
@property (nonatomic, copy) NSString *xxpk_list_bind_mobile;
@property (nonatomic, copy) NSString *xxpk_list_booking;
@property (nonatomic, copy) NSString *xxpk_list_coin_booking;
@property (nonatomic, copy) NSString *xxpk_list_booking_extra;
@property (nonatomic, copy) NSString *xxpk_list_booking_check;
@property (nonatomic, copy) NSString *xxpk_list_coin_booking_check;
@property (nonatomic, copy) NSString *xxpk_list_booking_receipt;
@property (nonatomic, copy) NSString *xxpk_list_password_change;
@property (nonatomic, copy) NSString *xxpk_list_password_reset;
@property (nonatomic, copy) NSString *xxpk_list_real_name;
@property (nonatomic, copy) NSString *xxpk_list_register;
@property (nonatomic, copy) NSString *xxpk_list_role;
@property (nonatomic, copy) NSString *xxpk_list_sms_code;
@property (nonatomic, copy) NSString *xxpk_list_subscribe;
@property (nonatomic, copy) NSString *xxpk_list_vk_auth;
@property (nonatomic, copy) NSString *xxpk_list_v_auth;
@property (nonatomic, copy) NSString *xxpk_list_test_report;

@end

NS_ASSUME_NONNULL_END
