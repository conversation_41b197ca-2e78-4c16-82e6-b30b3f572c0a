






#import "XXGBoxManager.h"
#import "NSObject+XXGModel.h"
#import "XXGPlayKitConfig.h"

@interface XXGBoxManager()
@property(nonatomic, strong) XXGBoxContent *xxpk_box;
@end

@implementation XXGBoxManager

+ (instancetype)shared {
    static id shared = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        shared = [[super allocWithZone:NULL] init];
    });
    return shared;
}


+ (NSDictionary *)xxpk_comeinedBoxJson {
    NSMutableDictionary *comeinedDic = [[[NSUserDefaults standardUserDefaults] objectForKey:__data_core.xxpk_boxm_comeinedbox_key] mutableCopy];
    NSMutableDictionary *returnDic = nil;
    if (comeinedDic) {
        returnDic = [NSMutableDictionary new];
        returnDic[__data_core.xxpk_id] = comeinedDic[__data_core.xxpk_id];
        returnDic[__data_core.xxpk_name] = comeinedDic[__data_core.xxpk_name];
        returnDic[__data_core.xxpk_token] = comeinedDic[__data_core.xxpk_token];
    }
    return returnDic;
}

+ (XXGBoxContent * _Nullable)xxpk_comeinedBox {
    if (!XXGBoxManager.shared.xxpk_box) {
        NSDictionary *dic = [[NSUserDefaults standardUserDefaults] objectForKey:__data_core.xxpk_boxm_comeinedbox_key];
        if (!dic) {
            XXGBoxManager.shared.xxpk_box = nil;
        }else {
            XXGBoxManager.shared.xxpk_box = [XXGBoxContent xxpk_modelWithDict:dic];
        }
    }
    return XXGBoxManager.shared.xxpk_box;
}

+ (void)xxpk_setComeinedBox:(XXGBoxContent *)xxpk_box {
    if (xxpk_box) {
        XXGBoxManager.shared.xxpk_box = xxpk_box;
        
        NSMutableDictionary *tempJson = [xxpk_box xxpk_modelToDict];
        [tempJson removeObjectForKey:__data_core.xxpk_created];
        
        [[NSUserDefaults standardUserDefaults] setObject:tempJson forKey:__data_core.xxpk_boxm_comeinedbox_key];
        [[NSUserDefaults standardUserDefaults] synchronize];
    }
}

+ (void)xxpk_removeComeinedBox {
    XXGBoxManager.shared.xxpk_box = nil;
    [[NSUserDefaults standardUserDefaults] removeObjectForKey:__data_core.xxpk_boxm_comeinedbox_key];
    [[NSUserDefaults standardUserDefaults] synchronize];
}



+ (NSMutableArray *)xxpk_currentBoxs {
    NSArray *array = [[NSUserDefaults standardUserDefaults] objectForKey:__data_core.xxpk_boxm_boxs_key];
    if (array) {
        return [array mutableCopy];
    }
    return [NSMutableArray array];
}


+ (void)xxpk_saveBoxs:(NSArray *)boxs {
    [[NSUserDefaults standardUserDefaults] setObject:boxs forKey:__data_core.xxpk_boxm_boxs_key];
    [[NSUserDefaults standardUserDefaults] synchronize];
}



+ (BOOL)xxpk_saveBoxContentToLocal:(XXGBoxContent *)xxpk_box {
    if (!xxpk_box || xxpk_box.xxpk_boxId.length == 0) return NO;
    
    NSMutableArray *mutableArray = [self xxpk_currentBoxs];
    
    
    NSInteger index = [mutableArray indexOfObjectPassingTest:^BOOL(NSDictionary *dic, NSUInteger idx, BOOL *stop) {
        return [[XXGBoxContent xxpk_modelWithDict:dic].xxpk_boxId isEqualToString:xxpk_box.xxpk_boxId];
    }];
    
    if (index != NSNotFound) {
        
        NSMutableDictionary *tempJson = [xxpk_box xxpk_modelToDict];
        [tempJson removeObjectForKey:__data_core.xxpk_created];
        
        
        mutableArray[index] = tempJson;
    } else {
        NSMutableDictionary *tempJson = [xxpk_box xxpk_modelToDict];
        [tempJson removeObjectForKey:__data_core.xxpk_created];
        
        
        [mutableArray addObject:tempJson];
    }
    
    [self xxpk_saveBoxs:mutableArray];
    return YES;
}


+ (BOOL)xxpk_deleteBoxToLocal:(XXGBoxContent *)xxpk_box {
    if (!xxpk_box || xxpk_box.xxpk_boxId.length == 0) return NO;
    
    NSMutableArray *mutableArray = [self xxpk_currentBoxs];
    NSInteger index = [mutableArray indexOfObjectPassingTest:^BOOL(NSDictionary *dic, NSUInteger idx, BOOL *stop) {
        return [[XXGBoxContent xxpk_modelWithDict:dic].xxpk_boxId isEqualToString:xxpk_box.xxpk_boxId];
    }];
    
    if (index != NSNotFound) {
        [mutableArray removeObjectAtIndex:index];
        [self xxpk_saveBoxs:mutableArray];
        return YES;
    }
    return NO;
}

+ (BOOL)xxpk_deleteBoxToLocalWithName:(NSString *)name {
    XXGBoxContent *xxpk_box = [self xxpk_localBoxContentWithBoxName:name];
    if (!xxpk_box || xxpk_box.xxpk_boxId.length == 0) return NO;
    
    NSMutableArray *mutableArray = [self xxpk_currentBoxs];
    NSInteger index = [mutableArray indexOfObjectPassingTest:^BOOL(NSDictionary *dic, NSUInteger idx, BOOL *stop) {
        return [[XXGBoxContent xxpk_modelWithDict:dic].xxpk_boxId isEqualToString:xxpk_box.xxpk_boxId];
    }];
    
    if (index != NSNotFound) {
        [mutableArray removeObjectAtIndex:index];
        [self xxpk_saveBoxs:mutableArray];
        return YES;
    }
    return NO;
}


+ (NSArray<XXGBoxContent *> *)xxpk_getBoxsContentFromLocal {
    NSArray *array = [[NSUserDefaults standardUserDefaults] objectForKey:__data_core.xxpk_boxm_boxs_key];
    if (!array) return @[];
    
    NSMutableArray *resultArray = [NSMutableArray array];
    for (NSDictionary *json in array) {
        XXGBoxContent *xxpk_box = [XXGBoxContent xxpk_modelWithDict:json];
        if (xxpk_box) {
            [resultArray addObject:xxpk_box];
        }
    }
    return resultArray;
}


+ (XXGBoxContent *)xxpk_localBoxContentWithBoxName:(NSString *)boxName {
    if (!boxName || boxName.length == 0) return nil;
    
    NSArray *array = [[NSUserDefaults standardUserDefaults] objectForKey:__data_core.xxpk_boxm_boxs_key];
    NSInteger index = [array indexOfObjectPassingTest:^BOOL(NSDictionary *json, NSUInteger idx, BOOL *stop) {
        return [[XXGBoxContent xxpk_modelWithDict:json].xxpk_boxName isEqualToString:boxName];
    }];
    
    if (index != NSNotFound) {
        NSDictionary *json = array[index];
        return [XXGBoxContent xxpk_modelWithDict:json];
    }
    return nil;
}


+ (XXGBoxContent *)xxpk_localBoxContentWithBoxType:(XXGComeinType)boxType {
    NSArray *array = [[NSUserDefaults standardUserDefaults] objectForKey:__data_core.xxpk_boxm_boxs_key];
    NSInteger index = [array indexOfObjectPassingTest:^BOOL(NSDictionary *json, NSUInteger idx, BOOL *stop) {
        return ([XXGBoxContent xxpk_modelWithDict:json].xxpk_boxType == boxType);
    }];
    
    if (index != NSNotFound) {
        NSDictionary *json = array[index];
        return [XXGBoxContent xxpk_modelWithDict:json];
    }
    return nil;
}

@end
