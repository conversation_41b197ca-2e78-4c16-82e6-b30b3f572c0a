






#import "XXGThirdManager.h"
#import "XXGBoxManager.h"
#import "XXGPlayKitConfig.h"
#import "NSString+XXGString.h"
#import "XXGNetworkList.h"
#import "XXGPlayKitCore.h"
#import "NSObject+XXGModel.h"

#import "XXGFacebookManager.h"
    #import "XXGAppsFlyerManager.h"
    #import "XXGFirebaseManager.h"
    #import "XXGVKManager.h"
    #import "XXGAdjustManager.h"
    #import "XXGAppLovinManager.h"
    #import "XXGPoopoManager.h"

@implementation XXGThirdManager

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

+ (void)load {
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(xxpk_startStatusChange:) name:XXGSetting.XXGNotificationNameKeyStartStatusChange object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(xxpk_comeinStatusChange:) name:XXGSetting.XXGNotificationNameKeyComeinStatusChange object:nil];
}

+ (void)xxpk_startStatusChange:(NSNotification *)notification {
    
    
    if ([XXGPlayKitConfig shared].xxpk_startStatus == XXGPlayKitStartStatusFinish) {
        
        

        if (XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_adaption_report_appsFlyer &&
            XXGPlayKitConfig.shared.xxpk_extraParams.xxpk_afDevKey.xxpk_isNotEmpty &&
            XXGPlayKitConfig.shared.xxpk_extraParams.xxpk_afAppid.xxpk_isNotEmpty) {
            [XXGAppsFlyerManager xxpk_appsFlyerLibConfigureAtSDKInitStatusFinishWithKey:XXGPlayKitConfig.shared.xxpk_extraParams.xxpk_afDevKey
                                                                           xxpk_appleid:XXGPlayKitConfig.shared.xxpk_extraParams.xxpk_afAppid
                                                                      withActivateEvent:XXGPlayKitConfig.shared.xxpk_extraParams.xxpk_afActivate];
        }
        
        
        if (XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_adaption_report_firebase &&
            XXGPlayKitConfig.shared.xxpk_extraParams.xxpk_fireActivate.xxpk_isNotEmpty) {
            [XXGFirebaseManager xxpk_logActivateWithEvent:XXGPlayKitConfig.shared.xxpk_extraParams.xxpk_fireActivate];
        }
        
        
        if (XXGPlayKitConfig.shared.xxpk_extraParams.xxpk_vk_clientid.xxpk_isNotEmpty) {
            [XXGVKManager xxpk_startVKWithClientID:XXGPlayKitConfig.shared.xxpk_extraParams.xxpk_vk_clientid clientSecret:XXGPlayKitConfig.shared.xxpk_extraParams.xxpk_vk_client_secret];
        }
        
        
        if (XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_adaption_report_adjust &&
            XXGPlayKitConfig.shared.xxpk_extraParams.xxpk_adjustAppToken.xxpk_isNotEmpty) {
            [XXGAdjustManager xxpk_adjustConfigWithAppToken:XXGPlayKitConfig.shared.xxpk_extraParams.xxpk_adjustAppToken activateEvent:XXGPlayKitConfig.shared.xxpk_extraParams.xxpk_adjustActivate adchangeBlock:^(NSString * adid) {
                [[XXGNetworkList xxpk_defaultNetwork] xxpk_networkReportAdjustId:adid];
            }];
        }
        
        
        if (XXGPlayKitConfig.shared.xxpk_extraParams.xxpk_max_key && XXGPlayKitConfig.shared.xxpk_extraParams.xxpk_max_key.xxpk_isNotEmpty &&
            XXGPlayKitConfig.shared.xxpk_extraParams.xxpk_max_reward_id && XXGPlayKitConfig.shared.xxpk_extraParams.xxpk_max_reward_id.xxpk_isNotEmpty) {
            [XXGAppLovinManager xxpk_initApplovinSDKWithKey:XXGPlayKitConfig.shared.xxpk_extraParams.xxpk_max_key xxpk_rewardedadid:XXGPlayKitConfig.shared.xxpk_extraParams.xxpk_max_reward_id xxpk_testDevices:@[]];
        }
        
        
        if (XXGPlayKitConfig.shared.xxpk_isPoopo) {
            [XXGPoopoManager xxpk_userLogout:^{
                [XXGPlayKitCore.shared xxpk_logout];
            }];
            
            [XXGPoopoManager xxpk_initSDKWithProductCode:XXGPlayKitConfig.shared.xxpk_extraParams.xxpk_poopo_code];
        }
        
        

    }
}

+ (void)xxpk_comeinStatusChange:(NSNotification *)notification {
    if ([XXGPlayKitConfig shared].xxpk_comeinStatus == XXGPlayKitComeInStatusFinish) {
        [self xxpk_logViewedContentEvent];
if (XXGBoxManager.xxpk_comeinedBox.xxpk_boxType == XXGComeinTypeMobile &&
            XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_adaption_report_firebase) {
            [XXGFirebaseManager xxpk_initiateOnDeviceConversionMeasurementWithPhoneNumber:XXGBoxManager.xxpk_comeinedBox.xxpk_boxMobile];
        }
    }
}

+ (void)xxpk_didFinishLaunchingWithOptions:(NSDictionary *)launchOptions xconnectOptions:(UISceneConnectionOptions *)connetOptions {
NSMutableDictionary *_launchOptions = [launchOptions mutableCopy];
    if (!_launchOptions && connetOptions) {
        _launchOptions = [NSMutableDictionary new];
        _launchOptions[UIApplicationOpenURLOptionsSourceApplicationKey] = connetOptions.sourceApplication;
    }
    [XXGFacebookManager xxpk_application:[UIApplication sharedApplication] xdidFinishLaunchingWithOptions:_launchOptions];
    [XXGFirebaseManager xxpk_application:[UIApplication sharedApplication] xdidFinishLaunchingWithOptions:_launchOptions];

}

+ (BOOL)xxpk_applicationOpenURL:(NSURL *)url xoptions:(NSDictionary<UIApplicationOpenURLOptionsKey,id> *)options xURLContexts:(NSSet<UIOpenURLContext *> *)URLContexts{
    
    NSMutableDictionary *_options = [options mutableCopy];
    if (!_options && URLContexts) {
        _options = [NSMutableDictionary new];
        _options[UIApplicationOpenURLOptionsSourceApplicationKey] = URLContexts.allObjects.firstObject.options.sourceApplication;
    }
    NSURL *_url = url;
    if (!url && URLContexts) {
        _url = URLContexts.allObjects.firstObject.URL;
    }
    
[XXGFacebookManager xxpk_application:[UIApplication sharedApplication] xopenURL:_url xoptions:_options];
    [XXGVKManager xxpk_application:[UIApplication sharedApplication] xopenURL:_url xoptions:_options];

    return YES;
}


+ (void)xxpk_logViewedContentEvent {
    if (![XXGBoxManager xxpk_comeinedBox]) {
        return;
    }
if ([XXGBoxManager xxpk_comeinedBox].xxpk_created) {
        if (XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_adaption_report_facebook) {
            [XXGFacebookManager xxpk_logCompletedRegistrationEvent];
        }
        if (XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_adaption_report_appsFlyer) {
            [XXGAppsFlyerManager xxpk_logCompletedRegistrationEvent:XXGBoxManager.xxpk_comeinedBox.xxpk_boxId];
        }
        if (XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_adaption_report_firebase) {
            [XXGFirebaseManager xxpk_logCompletedRegistrationEvent:XXGBoxManager.xxpk_comeinedBox.xxpk_boxId];
        }
        if (XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_adaption_report_adjust &&
            XXGPlayKitConfig.shared.xxpk_extraParams.xxpk_adjustRegister.xxpk_isNotEmpty) {
            [XXGAdjustManager xxpk_logCompletedRegistrationEvent:XXGPlayKitConfig.shared.xxpk_extraParams.xxpk_adjustRegister withUid:XXGBoxManager.xxpk_comeinedBox.xxpk_boxId];
        }
    }else {
        if (XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_adaption_report_facebook) {
            [XXGFacebookManager xxpk_logViewedContentEvent];
        }
        if (XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_adaption_report_appsFlyer) {
            [XXGAppsFlyerManager xxpk_logViewedContentEvent:XXGBoxManager.xxpk_comeinedBox.xxpk_boxId];
        }
        if (XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_adaption_report_firebase) {
            [XXGFirebaseManager xxpk_logViewedContentEvent:XXGBoxManager.xxpk_comeinedBox.xxpk_boxId];
        }
        if (XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_adaption_report_adjust &&
            XXGPlayKitConfig.shared.xxpk_extraParams.xxpk_adjustLogin.xxpk_isNotEmpty) {
            [XXGAdjustManager xxpk_logViewedContentEvent:XXGPlayKitConfig.shared.xxpk_extraParams.xxpk_adjustLogin withUid:XXGBoxManager.xxpk_comeinedBox.xxpk_boxId];
        }
    }
}


+ (void)xxpk_logAddedToCartEvent {
    
    if (![XXGBoxManager xxpk_comeinedBox]) {
        return;
    }
if (XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_adaption_report_facebook
        && XXGPlayKitConfig.shared.xxpk_extraParams.xxpk_fbClickPay.xxpk_isNotEmpty) {
        [XXGFacebookManager xxpk_logAddedToCartEvent:XXGPlayKitConfig.shared.xxpk_extraParams.xxpk_fbClickPay withUid:[XXGBoxManager xxpk_comeinedBox].xxpk_boxId];
    }
    if (XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_adaption_report_appsFlyer
        && XXGPlayKitConfig.shared.xxpk_extraParams.xxpk_afClickPay.xxpk_isNotEmpty) {
        [XXGAppsFlyerManager xxpk_logAddedToCartEvent:XXGPlayKitConfig.shared.xxpk_extraParams.xxpk_afClickPay withUid:[XXGBoxManager xxpk_comeinedBox].xxpk_boxId];
    }
    if (XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_adaption_report_firebase
        && XXGPlayKitConfig.shared.xxpk_extraParams.xxpk_fireClickPay.xxpk_isNotEmpty) {
        [XXGFirebaseManager xxpk_logAddedToCartEvent:XXGPlayKitConfig.shared.xxpk_extraParams.xxpk_fireClickPay withUid:[XXGBoxManager xxpk_comeinedBox].xxpk_boxId];
    }
    if (XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_adaption_report_adjust
        && XXGPlayKitConfig.shared.xxpk_extraParams.xxpk_adjustClickPay.xxpk_isNotEmpty) {
        [XXGAdjustManager xxpk_logAddedToCartEvent:XXGPlayKitConfig.shared.xxpk_extraParams.xxpk_adjustClickPay withUid:XXGBoxManager.xxpk_comeinedBox.xxpk_boxId];
    }

}


+ (void)xxpk_logPurchasedEventOrderId:(NSString*)xxpk_orderId
                        currency:(NSString*)currency
                                price:(double)price{
    if (![XXGBoxManager xxpk_comeinedBox]) {
        return;
    }
if (XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_adaption_report_facebook
        && [XXGPlayKitConfig shared].xxpk_extraParams.xxpk_fbPay
        && [XXGPlayKitConfig shared].xxpk_extraParams.xxpk_fbPay.xxpk_isNotEmpty) {
        [XXGFacebookManager xxpk_logPurchasedEventOrderId:xxpk_orderId currency:currency price:price];
    }
    if (XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_adaption_report_appsFlyer
        && [XXGPlayKitConfig shared].xxpk_extraParams.xxpk_afPay
        && [XXGPlayKitConfig shared].xxpk_extraParams.xxpk_afPay.xxpk_isNotEmpty) {
        [XXGAppsFlyerManager xxpk_logPurchasedEvent:XXGPlayKitConfig.shared.xxpk_extraParams.xxpk_afPay xxpk_orderId:xxpk_orderId currency:currency price:price];
    }
    if (XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_adaption_report_firebase
        && [XXGPlayKitConfig shared].xxpk_extraParams.xxpk_firePay
        && [XXGPlayKitConfig shared].xxpk_extraParams.xxpk_firePay.xxpk_isNotEmpty) {
        [XXGFirebaseManager xxpk_logPurchasedEvent:XXGPlayKitConfig.shared.xxpk_extraParams.xxpk_firePay xxpk_orderId:xxpk_orderId currency:currency price:price];
    }
    if (XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_adaption_report_adjust
        && XXGPlayKitConfig.shared.xxpk_extraParams.xxpk_adjustPay
        && XXGPlayKitConfig.shared.xxpk_extraParams.xxpk_adjustPay.xxpk_isNotEmpty) {
        [XXGAdjustManager xxpk_logPurchasedEvent:XXGPlayKitConfig.shared.xxpk_extraParams.xxpk_adjustPay xxpk_orderId:xxpk_orderId currency:currency price:price withUid:XXGBoxManager.xxpk_comeinedBox.xxpk_boxId];
    }
}

+ (void)xxpk_logFacebookEvent:(NSString *)event params:(NSDictionary *_Nullable)params {
    if (![XXGBoxManager xxpk_comeinedBox]) {
        return;
    }
    if (XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_adaption_report_facebook && event.xxpk_isNotEmpty) {
        [XXGFacebookManager xxpk_fbUniversalLogEvent:event withUid:[XXGBoxManager xxpk_comeinedBox].xxpk_boxId params:params];
    }
}
+ (void)xxpk_logAppFlyerEvent:(NSString *)event params:(NSDictionary *_Nullable)params {
    if (![XXGBoxManager xxpk_comeinedBox]) {
        return;
    }
    if (XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_adaption_report_appsFlyer && event.xxpk_isNotEmpty) {
        [XXGAppsFlyerManager xxpk_appsFlyerUniversalLogEvent:event params:params withUid:XXGBoxManager.xxpk_comeinedBox.xxpk_boxId];
    }
}
+ (void)xxpk_logFirebaseEvent:(NSString *)event params:(NSDictionary *_Nullable)params {
    if (![XXGBoxManager xxpk_comeinedBox]) {
        return;
    }
    if (XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_adaption_report_firebase && event.xxpk_isNotEmpty) {
        [XXGFirebaseManager xxpk_firebaseUniversalLogEvent:event params:params withUid:XXGBoxManager.xxpk_comeinedBox.xxpk_boxId];
    }
}
+ (void)xxpk_logAdjustEvent:(NSString *)event params:(NSDictionary *_Nullable)params {
    if (![XXGBoxManager xxpk_comeinedBox]) {
        return;
    }
    if (XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_adaption_report_adjust && event.xxpk_isNotEmpty) {
        [XXGAdjustManager xxpk_adjustUniversalLogEvent:event params:params withUid:XXGBoxManager.xxpk_comeinedBox.xxpk_boxId];
    }
}

@end
