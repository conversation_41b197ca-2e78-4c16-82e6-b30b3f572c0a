






#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface XXGShanYanManager : NSObject

+ (void)xxpk_preGetPhonenumberWithAppId:(NSString *)appId complete:(void (^_Nullable)(BOOL isCanLogin))complete;

+ (void)xxpk_getLoginTokenController:(UIViewController *_Nonnull)controller array:(NSArray *)array success:(void (^_Nullable)(NSDictionary * _Nonnull resultDic))success xxpk_error:(void (^_Nullable)(NSString * _Nonnull error))error shanyanAction:(void(^)(NSInteger))action;

@end

NS_ASSUME_NONNULL_END
