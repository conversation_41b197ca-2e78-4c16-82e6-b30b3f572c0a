






#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface XXGBDASignalManager : NSObject

+ (void)xxpk_didFinishLaunchingWithOptions:(NSDictionary *)launchOptions xconnectOptions:(UISceneConnectionOptions *)connetOptions;

+ (BOOL)xxpk_application:(UIApplication *)application
                xopenURL:(NSURL *)url
                xoptions:(nullable NSDictionary<UIApplicationOpenURLOptionsKey, id> *)options;
@end

NS_ASSUME_NONNULL_END
