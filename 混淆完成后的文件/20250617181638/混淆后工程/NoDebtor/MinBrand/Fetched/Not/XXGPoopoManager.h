






#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface XXGPoopoManager : NSObject

+ (BOOL)xxpk_application:(UIApplication *)application
                xopenURL:(NSURL *)url
                xoptions:(nullable NSDictionary<UIApplicationOpenURLOptionsKey, id> *)options;

+ (void)xxpk_initSDKWithProductCode:(NSString *)xxpk_productCode;

+ (void)xxpk_login:(void(^)(NSString *uid, NSString*token))callback;

+ (void)xxpk_creatOrder:(NSString *)xxpk_productCode
                orderNo:(NSString *)orderNo
                subject:(NSString *)subject
                  total:(NSString *)totalPrice
              currentcy:(NSString *)currentcy
          extras_params:(NSString *)extras_params;

+ (void)xxpk_uploadRoleInfo:(NSString * _Nonnull)xxpk_serverId
            xxpk_serverName:(NSString * _Nonnull)xxpk_serverName
                xxpk_roleId:(NSString * _Nonnull)xxpk_roleId
              xxpk_roleName:(NSString * _Nonnull)xxpk_roleName
             xxpk_roleLevel:(NSString * _Nonnull)xxpk_roleLevel;

+ (void)xxpk_logout;

+ (void)xxpk_userLogout:(void(^)(void))xxpk_userLogout;
@end

NS_ASSUME_NONNULL_END
