



#import "XXGAppsFlyerManager.h"
#import "XXGPlayKitConfig.h"
#import "NSObject+XXGPerformSelector.h"
#import "ZBObjectiveCBeaver.h"

@implementation XXGAppsFlyerManager

+ (Class)xxpk_middlewareClass {
    Class class = NSClassFromString(__data_core.xxpk_middleware_appflyer);
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        ZBLogInfo(__data_core.xxpk_log_manager_appsflyer,class?[NSString stringWithFormat:__data_core.xxpk_manager_status_exist_version,[class xxpk_performSelector:@selector(xxpk_version)]]:__data_core.xxpk_manager_status_not_exist);
    });
    return class;
}

+ (NSString *)xxpk_appsFlyerUID {
    if ([self xxpk_middlewareClass]) {
        return [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_appsFlyerUID)];
    }
    return @"";
}

+ (void)xxpk_appsFlyerLibConfigureAtSDKInitStatusFinishWithKey:(NSString *)key xxpk_appleid:(NSString *)aid withActivateEvent:(NSString *)event{
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_appsFlyerLibConfigureAtSDKInitStatusFinishWithKey:xxpk_appleid:withActivateEvent:) withObject:key withObject:aid withObject:event];
    }
}


+ (void)xxpk_logViewedContentEvent:(NSString *)uid {
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_logViewedContentEvent:) withObject:uid];
    }
}


+ (void)xxpk_logCompletedRegistrationEvent:(NSString *)uid  {
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_logCompletedRegistrationEvent:) withObject:uid];
    }
}


+ (void)xxpk_logAddedToCartEvent:(NSString *)event withUid:(NSString *)uid  {
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_logAddedToCartEvent:withUid:) withObject:event withObject:uid];
    }
}


+ (void)xxpk_logPurchasedEvent:(NSString *)event
                  xxpk_orderId:(NSString*)xxpk_orderId
                 currency:(NSString*)currency
                    price:(double)price {
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_logPurchasedEvent:xxpk_orderId:currency:price:) withObject:event withObject:xxpk_orderId withObject:currency withObject:@(price)];
    }
}


+ (void)xxpk_appsFlyerUniversalLogEvent:(NSString *)event params:(NSDictionary *)params withUid:(NSString *)uid{
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_appsFlyerUniversalLogEvent:params:withUid:) withObject:event withObject:params withObject:uid];
    }
}

@end

