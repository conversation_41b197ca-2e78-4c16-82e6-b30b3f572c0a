






#import "XXGVKManager.h"
#import "XXGPlayKitConfig.h"
#import "NSObject+XXGPerformSelector.h"
#import "ZBObjectiveCBeaver.h"

@implementation XXGVKManager

+ (id)xxpk_middlewareClass {
    Class class = NSClassFromString(__data_core.xxpk_middleware_vk);
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        ZBLogInfo(__data_core.xxpk_log_manager_vk,class?__data_core.xxpk_manager_status_exist:__data_core.xxpk_manager_status_not_exist);
    });
    return class;
}

+ (void)xxpk_oauthOnViewController:(UIViewController *)vc handler:(void(^)(BOOL isCancell,NSString *userID, NSString*token, NSString*error))handler {
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_oauthOnViewController:handler:) withObject:vc withObject:handler];
    }else {
        handler(NO,@"", @"", __string_core.xxpk_comeinError);
    }
}

+ (BOOL)xxpk_application:(UIApplication *)application
                xopenURL:(NSURL *)url
                xoptions:(nullable NSDictionary<UIApplicationOpenURLOptionsKey, id> *)options {
    if ([self xxpk_middlewareClass]) {
        return [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_application:xopenURL:xoptions:) withObject:application withObject:url withObject:options];
    }
    return NO;
}

+ (void)xxpk_startVKWithClientID:(NSString *)clientId clientSecret:(NSString *)clientSecret{
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_startVKWithClientID:clientSecret:) withObject:clientId withObject:clientSecret];
    }
}
@end
