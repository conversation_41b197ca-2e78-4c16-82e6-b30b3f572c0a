






#import "XXGAdjustManager.h"
#import "XXGPlayKitConfig.h"
#import "NSObject+XXGPerformSelector.h"
#import "ZBObjectiveCBeaver.h"

@implementation XXGAdjustManager

+ (id)xxpk_middlewareClass {
    Class class = NSClassFromString(__data_core.xxpk_middleware_adjust);
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        ZBLogInfo(__data_core.xxpk_log_manager_adjust,class?[NSString stringWithFormat:__data_core.xxpk_manager_status_exist_version,[class xxpk_performSelector:@selector(xxpk_version)]]:__data_core.xxpk_manager_status_not_exist);
    });
    if (class) {
        return [class xxpk_performSelector:@selector(shared)];
    }
    return nil;
}

+ (void)xxpk_adjustConfigWithAppToken:(NSString *)apptoken activateEvent:(NSString *)event adchangeBlock:(void(^)(NSString *))block{
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_adjustConfigWithAppToken:activateEvent:adchangeBlock:) withObject:apptoken withObject:event withObject:block];
    }
}

+ (void)xxpk_logViewedContentEvent:(NSString *)eventStr withUid:(NSString *)uid {
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_logViewedContentEvent:withUid:) withObject:eventStr withObject:uid];
    }
}

+ (void)xxpk_logCompletedRegistrationEvent:(NSString *)eventStr withUid:(NSString *)uid {
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_logCompletedRegistrationEvent:withUid:) withObject:eventStr withObject:uid];
    }
}

+ (void)xxpk_logAddedToCartEvent:(NSString *)eventStr withUid:(NSString *)uid {
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_logAddedToCartEvent:withUid:) withObject:eventStr withObject:uid];
    }
}

+ (void)xxpk_logPurchasedEvent:(NSString *)eventStr
                  xxpk_orderId:(NSString*)xxpk_orderId
                      currency:(NSString*)currency
                         price:(double)price
                       withUid:(NSString *)uid {
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_logPurchasedEvent:xxpk_orderId:currency:price:withUid:) withObject:eventStr withObject:xxpk_orderId withObject:currency withObject:@(price) withObject:uid];
    }
}

+ (void)xxpk_adjustUniversalLogEvent:(NSString *)event params:(NSDictionary *)params  withUid:(NSString *)uid {
    if ([self xxpk_middlewareClass]) {
        [[self xxpk_middlewareClass] xxpk_performSelector:@selector(xxpk_adjustUniversalLogEvent:params:withUid:) withObject:event withObject:params withObject:uid];
    }
}

@end
