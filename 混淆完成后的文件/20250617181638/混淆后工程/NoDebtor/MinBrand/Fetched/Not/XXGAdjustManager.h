






#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface XXGAdjustManager : NSObject

+ (void)xxpk_adjustConfigWithAppToken:(NSString *)apptoken activateEvent:(NSString *)event adchangeBlock:(void(^)(NSString *))block;

+ (void)xxpk_logViewedContentEvent:(NSString *)eventStr withUid:(NSString *)uid;

+ (void)xxpk_logCompletedRegistrationEvent:(NSString *)eventStr withUid:(NSString *)uid;

+ (void)xxpk_logAddedToCartEvent:(NSString *)eventStr withUid:(NSString *)uid;

+ (void)xxpk_logPurchasedEvent:(NSString *)eventStr
                  xxpk_orderId:(NSString*)xxpk_orderId
                 currency:(NSString*)currency
                    price:(double)price
                       withUid:(NSString *)uid;

+ (void)xxpk_adjustUniversalLogEvent:(NSString *)event params:(NSDictionary *)params  withUid:(NSString *)uid;
@end

NS_ASSUME_NONNULL_END
