






#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface XXGVKManager : NSObject

+ (void)xxpk_oauthOnViewController:(UIViewController *)vc handler:(void(^)(BOOL isCancell,NSString *userID, NSString*token, NSString*error))handler;

+ (BOOL)xxpk_application:(UIApplication *)application
                xopenURL:(NSURL *)url
                xoptions:(nullable NSDictionary<UIApplicationOpenURLOptionsKey, id> *)options;

+ (void)xxpk_startVKWithClientID:(NSString *)clientId clientSecret:(NSString *)clientSecret;

@end

NS_ASSUME_NONNULL_END
