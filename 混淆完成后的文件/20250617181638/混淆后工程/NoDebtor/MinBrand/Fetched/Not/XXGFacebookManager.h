






#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface XXGFacebookManager : NSObject

+ (void)xxpk_application:(UIApplication * _Nonnull)application xdidFinishLaunchingWithOptions:(NSDictionary<UIApplicationLaunchOptionsKey, id> * _Nullable)launchOptions;

+ (BOOL)xxpk_application:(UIApplication *)application
                xopenURL:(NSURL *)url
                xoptions:(nullable NSDictionary<UIApplicationOpenURLOptionsKey, id> *)options;

+ (void)xxpk_oauth:(UIViewController *)vc handler:(void(^)(NSString *userID, NSString*name, NSString*token,NSString *auth_token,NSString *nonce, NSError*error, BOOL isCancelled))handler;

+ (void)xxpk_jumpToFacebookAndFollw:(NSString *)fbhome;


+ (void)xxpk_launchFriendFinderDialogWithCompletionHandler:(void(^)(BOOL success, NSError * _Nullable error))completionHandler;

+ (void)xxpk_logViewedContentEvent;

+ (void)xxpk_logCompletedRegistrationEvent;

+ (void)xxpk_logAddedToCartEvent:(NSString *)event withUid:(NSString *)uid;

+ (void)xxpk_logPurchasedEventOrderId:(NSString*)xxpk_orderId
                             currency:(NSString*)currency
                                price:(double)price;

+ (void)xxpk_fbUniversalLogEvent:(NSString *)eventName withUid:(NSString *)uid params:(NSDictionary *)params;

+ (void)xxpk_sharedLinkToFacebookWithUrl:(NSString *)linkUrl withvc:(UIViewController *)vc;

+ (void)xxpk_sharedImageToFacebookWithImage:(UIImage *)image  withvc:(UIViewController *)vc;

+ (void)xxpk_sharedImageToFacebookWithImageUrl:(NSString *)imageUrl  withvc:(UIViewController *)vc;

@end

NS_ASSUME_NONNULL_END
