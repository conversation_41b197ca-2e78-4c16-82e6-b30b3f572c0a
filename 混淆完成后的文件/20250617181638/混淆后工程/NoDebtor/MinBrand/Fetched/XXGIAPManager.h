






#import <Foundation/Foundation.h>
@class XXGProductBody,XXGSelectProductItem,XXGIAPManager;

NS_ASSUME_NONNULL_BEGIN

@protocol XXGIAPManagerDelegate <NSObject>

@optional

- (void)xxpk_IAPManagerOpenOfOrderUrl:(NSString *)url;

- (void)xxpk_iapManager:(XXGIAPManager *)manager paySuccessWithItem:(XXGProductBody *)xxpk_item;

- (void)xxpk_iapManager:(XXGIAPManager *)manager payFialedWithMessage:(NSString *)message;

- (void)xxpk_iapManagerCancel:(XXGIAPManager *)manager;

@end

@interface XXGIAPManager : NSObject

+ (instancetype)shared;

@property (nonatomic, assign) BOOL xxpk_isCoinOrder;

@property (nonatomic, strong) XXGProductBody *xxpk_item;

@property (nonatomic, weak) id<XXGIAPManagerDelegate>xxpk_delegate;

- (void)xxpk_registerP;

- (void)xxpk_createOrder:(XXGProductBody *)item xxpk_isCoinOrder:(BOOL)isCoin;

+ (void)xxpk_iapRepair;

@end

NS_ASSUME_NONNULL_END
