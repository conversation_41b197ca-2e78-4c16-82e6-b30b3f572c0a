






#import <Foundation/Foundation.h>
#import "XXGBoxContent.h"

NS_ASSUME_NONNULL_BEGIN

@interface XXGBoxManager : NSObject

+ (NSDictionary *)xxpk_comeinedBoxJson;

+ (XXGBoxContent * _Nullable)xxpk_comeinedBox;

+ (void)xxpk_setComeinedBox:(XXGBoxContent *)xxpk_box;

+ (void)xxpk_removeComeinedBox;

+ (BOOL)xxpk_saveBoxContentToLocal:(XXGBoxContent *)xxpk_box;

+ (BOOL)xxpk_deleteBoxToLocal:(XXGBoxContent *)xxpk_box;

+ (BOOL)xxpk_deleteBoxToLocalWithName:(NSString *)name;

+ (NSArray<XXGBoxContent *> *)xxpk_getBoxsContentFromLocal;

+ (XXGBoxContent *)xxpk_localBoxContentWithBoxName:(NSString *)boxName;

+ (XXGBoxContent *)xxpk_localBoxContentWithBoxType:(XXGComeinType)boxType;

@end

NS_ASSUME_NONNULL_END
