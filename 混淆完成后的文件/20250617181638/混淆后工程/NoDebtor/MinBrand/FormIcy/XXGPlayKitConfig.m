






#import "XXGPlayKitConfig.h"
#import "NSData+SunHope.h"
#import "XXGLocalizedModel.h"
#import "XXGDatasModel.h"
#import "XXGLocaleString.h"
#import "NSString+XXGString.h"
#import "ZBLogMacros.h"

@implementation XXGPlayKitConfig

- (instancetype)init
{
    self = [super init];
    if (self) {
        _xxpk_startStatus = XXGPlayKitStartStatusNot;
        _xxpk_comeinStatus = XXGPlayKitComeInStatusNot;
        _xxpk_closeButtonHidden = YES;
        self.xxpk_adaptionCof.xxpk_logStatus = YES;
    }
    return self;
}

+ (instancetype)shared {
    static id shared = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        shared = [[super allocWithZone:NULL] init];
    });
    return shared;
}

- (NSString *)xxpk_startid {
    if (self.xxpk_testAppId && self.xxpk_testAppId.xxpk_isNotEmpty) {
        _xxpk_startid = self.xxpk_testAppId;
    }
    if (!_xxpk_startid) {
        _xxpk_startid = self.xxpk_data_core.xxpk_startid;
    }
    return _xxpk_startid;
}

- (NSString *)xxpk_security {
    if (!_xxpk_security) {
        _xxpk_security = self.xxpk_startid.md5;
    }
    return _xxpk_security;
}

- (XXGLocalizedCore *)xxpk_string_core {
    if (!_xxpk_string_core) {
        _xxpk_string_core = [XXGLocaleString xxpk_loadLanguagesWithClass:[XXGLocalizedCore class]];
    }
    return _xxpk_string_core;
}

- (XXGDatasCore *)xxpk_data_core {
    if (!_xxpk_data_core) {
        _xxpk_data_core = [XXGLocaleString xxpk_loadDatasWithClass:[XXGDatasCore class]];
    }
    return _xxpk_data_core;
}

- (XXGStartBody *)xxpk_body {
    if (!_xxpk_body) {
        _xxpk_body = [XXGStartBody new];
    }
    return _xxpk_body;
}

- (XXGDeviceInfo *)xxpk_deviceInfo {
    if (!_xxpk_deviceInfo) {
        _xxpk_deviceInfo = [XXGDeviceInfo new];
    }
    return _xxpk_deviceInfo;
}

- (XXGAdaptionCof *)xxpk_adaptionCof {
    if (!_xxpk_adaptionCof) {
        _xxpk_adaptionCof = [XXGAdaptionCof new];
    }
    return _xxpk_adaptionCof;
}

- (XXGExtraParams *)xxpk_extraParams{
    if (!_xxpk_extraParams) {
        _xxpk_extraParams = [XXGExtraParams new];
    }
    return _xxpk_extraParams;
}

- (void)setXxpk_startStatus:(XXGPlayKitStartStatus)xxpk_startStatus {
    _xxpk_startStatus = xxpk_startStatus;
    [[NSNotificationCenter defaultCenter] postNotificationName:XXGSetting.XXGNotificationNameKeyStartStatusChange object:@(xxpk_startStatus)];
}

- (void)setXxpk_comeinStatus:(XXGPlayKitComeInStatus)xxpk_comeinStatus {
    _xxpk_comeinStatus = xxpk_comeinStatus;
    [[NSNotificationCenter defaultCenter] postNotificationName:XXGSetting.XXGNotificationNameKeyComeinStatusChange object:@(xxpk_comeinStatus)];
}

- (BOOL)xxpk_isCanal {
if (self.xxpk_isPoopo) {
        return YES;
    }
    return NO;
}

- (BOOL)xxpk_isPoopo {
    return self.xxpk_extraParams.xxpk_poopo_code && self.xxpk_extraParams.xxpk_poopo_code.xxpk_isNotEmpty;
}
@end
