






#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import <XXGPlayProtocol.h>
#import "XXGProductBody.h"
#import "XXGRoleBody.h"

@class WKWebView;
@protocol XXGUIkitDelegate;
static BOOL isUcenterBind = NO;

NS_ASSUME_NONNULL_BEGIN

@interface XXGPlayKitCore : NSObject


- (void)__xxpk_comeined:(NSDictionary *)responseObject;

@property (nonatomic, weak) id<XXGPlayDelegate> xxpk_delegate;

+ (instancetype)shared;

- (void)xxpk_coreComein;

- (void)xxpk_logout;

- (void)xxpk_creatOrder:(XXGProductBody *)body xxpk_isCoinOrder:(BOOL)isCoin;

- (void)xxpk_uploadRoleInfo:(XXGRoleBody *)roleInfo;

- (void)xxpk_didFinishLaunchingWithOptions:(NSDictionary *)launchOptions xconnectOptions:(UISceneConnectionOptions *)connetOptions;

- (BOOL)xxpk_applicationOpenURL:(NSURL *)url xoptions:(NSDictionary<UIApplicationOpenURLOptionsKey,id> *)options xURLContexts:(NSSet<UIOpenURLContext *> *)URLContexts;

- (void)xxpk_coreHandleOpenUrl:(NSString *)url;

- (void)xxpk_openUserCenterSidebar:(NSString *)type;

- (void)xxpk_iapRepair;

- (void)xxpk_dissmissCurrentUI;
- (void)xxpk_showUIofChangeBoxKey:(id)object;
- (void)xxpk_showUIofbindMobile:(id)object hasWkView:(nullable WKWebView *)hasWkView;
- (void)xxpk_showUIofSelectPayMethod:(id)objcet xxpk_delegate:(id<XXGUIkitDelegate>)xxpk_delegate;
- (void)xxpk_showUIofUCenter:(id)object;
- (void)xxpk_showUIofPopup:(id _Nullable)objcet;
- (void)xxpk_showUIofTrampoline:(id)objcet;
- (void)xxpk_showUIofSavePS:(id)object;

@end

NS_ASSUME_NONNULL_END
