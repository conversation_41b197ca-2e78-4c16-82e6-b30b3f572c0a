






#import "XXGWKMethodAction.h"
#import "XXGPlayKitConfig.h"
#import "NSString+XXGString.h"
#import "XXGUIKit.h"
#import "XXGPlayKitCore.h"
#import "XXGAlertView.h"
#import "XXGBoxManager.h"
#import "NSString+URLEncoding.h"
#import "XXGIAPManager.h"
#import "NSObject+XXGModel.h"
#import "XXGNetworkList.h"
#import "XXGToast.h"
#import "NSURL+XXGAnalyse.h"
#import "XXGPlayKitCore+Others.h"
#import "XXGProductBody.h"
#import "ZBObjectiveCBeaver.h"
#import "XXGWindowManager.h"
@implementation XXGWKMethodAction

+ (void)xxpk_wkView:(WKWebView *)wkView makeMethodAction:(NSString *)method arg:(id)arg {
    ZBLogInfo(@"WebView事件-%@",method);
    if (method.xxpk_isEmpty) {
        return;
    }
    if ([method isEqualToString:__data_core.xxpk_core_mt_changep]) { 
        [XXGPlayKitCore.shared xxpk_showUIofChangeBoxKey:wkView];
    }else if ([method isEqualToString:__data_core.xxpk_core_mt_bindm]) {
        [XXGPlayKitCore.shared xxpk_showUIofbindMobile:@(NO) hasWkView:wkView];
    }else if ([method isEqualToString:__data_core.xxpk_core_mt_switcha]) {
        [self __xxpk_wk_switchAccount];
    }else if ([method isEqualToString:__data_core.xxpk_recomein]) {
        [XXGPlayKitCore.shared xxpk_logout];
    }else if ([method isEqualToString:__data_core.xxpk_core_mt_openURL]) {
        [self __xxpk_openUrl:arg];
    }else if ([method isEqualToString:__data_core.xxpk_core_mt_ucenter]) {
        [self __xxpk_wk_ucenter:arg];
    }else if ([method isEqualToString:__data_core.xxpk_core_mt_iapRepair]) {
        [self __xxpk_wk_iapRepair:arg];
    }else if ([method isEqualToString:__data_core.xxpk_core_mt_getInfomation]) {
        [self __xxpk_wk_getInfomation:wkView];
    }else if([method isEqualToString:__data_core.xxpk_core_mt_accountRemove]) {
        [self __xxpk_removeAccount];
    }else if([method isEqualToString:__data_core.xxpk_core_mt_getApiUrl]) {
        [self __xxpk_getApiUrl:wkView];
    }else if([method isEqualToString:__data_core.xxpk_core_mt_getToken]) {
        [self __xxpk_getToken:wkView];
    }else if([method isEqualToString:__data_core.xxpk_core_mt_popup]) {
        [self __xxpk_popup:arg];
    }
    
    
    else if ([method isEqualToString:__data_core.xxpk_core_mt_userInfoSub]||
              [method isEqualToString:__data_core.xxpk_core_mt_closeSplash]) { //userInfoSub & closeSplash
        [XXGUIKit xxpk_dissmissCurrentWinow];
    }
    
    
    else if([method isEqualToString:__data_core.xxpk_core_mt_openUserCenterSidebar]) {//openUserCenterSidebar
        [XXGPlayKitCore.shared xxpk_openUserCenterSidebar:arg];
    }else if([method isEqualToString:__data_core.xxpk_core_mt_coin_p]) {//coinP
        [self __xxpk_coinP:arg];
    }

else if([method isEqualToString:__data_core.xxpk_core_mt_facebookShare]) {
        [self __xxpk_facebookShare:arg];
    }else if([method isEqualToString:__data_core.xxpk_core_mt_facebookSub]) {
        [self __xxpk_facebookSub];
    }else if([method isEqualToString:__data_core.xxpk_core_mt_facebookBind]) {
        [self __xxpk_facebookBind];
    }else if([method isEqualToString:__data_core.xxpk_core_mt_facebookInvite]) {
        [self __xxpk_facebookInvite];
    }
}


+ (void)__xxpk_coinP:(NSString *)json {
    NSData *jsonData = [json dataUsingEncoding:NSUTF8StringEncoding];
    NSDictionary *dic = [NSJSONSerialization JSONObjectWithData:jsonData options:NSJSONReadingMutableContainers error:nil];
    if (!dic) {
        return;
    }
    XXGProductBody *body = [XXGProductBody xxpk_modelWithDict:dic];
    [XXGPlayKitCore.shared xxpk_creatOrder:body xxpk_isCoinOrder:YES];
}

+ (void)__xxpk_popup:(NSURL *)url {
    NSDictionary *ext = [url xxpk_analyse];
    if (ext.allKeys.count == 0) {
        return;
    }
    if ([ext[__data_core.xxpk_action] isEqualToString:__data_core.xxpk_open]) {
        [[XXGPlayKitCore shared] xxpk_showUIofPopup:ext];
    }else {
        [XXGUIKit xxpk_dissmissCurrentWinow];
    }
}

+ (void)__xxpk_getToken:(WKWebView *)vkview {
    NSString * jsFunc = [NSString stringWithFormat:__data_core.xxpk_core_mt_func_getToken,[XXGBoxManager xxpk_comeinedBox].xxpk_boxToken].mutableCopy;
    [vkview evaluateJavaScript:jsFunc completionHandler:^(id _Nullable resultValue, NSError * _Nullable error) {
        
    }];
}

+ (void)__xxpk_getApiUrl:(WKWebView *)vkview {
    NSString * jsFunc = [NSString stringWithFormat:__data_core.xxpk_core_mt_func_getApiUrl,XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_box_center.xxpk_url].mutableCopy;
    [vkview evaluateJavaScript:jsFunc completionHandler:^(id _Nullable resultValue, NSError * _Nullable error) {
        
    }];
}

+ (void)__xxpk_removeAccount {
    [[XXGNetworkList xxpk_defaultNetwork] xxpk_networkRemoveAccount:^(NSDictionary * _Nonnull responseObject) {
        [XXGPlayKitCore.shared xxpk_logout];
        [XXGToast showBottom:__string_core.xxpk_account_removed];
    } failure:^(NSError * _Nonnull error) {
        NSString *errorDescrip = [NSString stringWithFormat:__data_core.xxpk_error_code, error.localizedDescription, error.code];
        [XXGAlertView xxpk_showAlertWithTitle:__string_core.xxpk_tips message:errorDescrip completion:nil];
    }];
}

+ (void)__xxpk_openUrl:(NSURL *)url {
    NSDictionary * ext = [url xxpk_analyse];
    if (ext.allKeys.count == 0) {
        return;
    }
    [XXGPlayKitCore.shared xxpk_coreHandleOpenUrl:ext[__data_core.xxpk_url]];
}


+ (void)__xxpk_wk_ucenter:(NSURL *)url {
    
    NSString *query = url.query;
    
    if (query.xxpk_isNotEmpty && query.length > 4) {
        query = [query substringFromIndex:4]; 
        [XXGUIKit xxpk_dissmissAllWindows];
        [XXGPlayKitCore.shared xxpk_showUIofUCenter:query.xxpk_urlDecodedString];
    }else {
        [XXGUIKit xxpk_dissmissAllWindows];
        [XXGPlayKitCore.shared xxpk_showUIofUCenter:XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_box_center.xxpk_url];
    }
}


+ (void)__xxpk_wk_iapRepair:(NSURL *)url {
    [XXGPlayKitCore.shared xxpk_iapRepair];
}

+ (void)__xxpk_wk_getInfomation:(WKWebView *)vkview {
    NSMutableDictionary *dic = [XXGPlayKitConfig.shared.xxpk_body xxpk_modelToDict];
    XXGBoxContent *box = [XXGBoxManager xxpk_comeinedBox];
    NSMutableDictionary *udic = [NSMutableDictionary new];
    udic[__data_core.xxpk_core_mt_getInfomation_uid] = box.xxpk_boxId;
    udic[__data_core.xxpk_core_mt_getInfomation_name] = box.xxpk_boxName;
    udic[__data_core.xxpk_core_mt_getInfomation_token] = box.xxpk_boxToken;
udic[__data_core.xxpk_core_mt_getInfomation_fbuid] = box.xxpk_facebookUid;
    udic[__data_core.xxpk_core_mt_getInfomation_fbtoken] = box.xxpk_facebookToken;
    udic[__data_core.xxpk_core_mt_getInfomation_fbauthtoken] = box.xxpk_facebookAuthToken;
    udic[__data_core.xxpk_core_mt_getInfomation_fbnonce] = box.xxpk_facebookNonce;
    dic[__data_core.xxpk_core_mt_getInfomation_user] = udic;
    NSData *data = [NSJSONSerialization dataWithJSONObject:dic options:kNilOptions error:nil];
    NSString *string = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
    NSString * jsFunc = [NSString stringWithFormat:__data_core.xxpk_core_mt_func_getInfomation,string].mutableCopy;
    [vkview evaluateJavaScript:jsFunc completionHandler:^(id _Nullable result, NSError * _Nullable error) {
        
    }];
}

+ (void)__xxpk_wk_switchAccount {
    [XXGAlertView xxpk_showAlertWithTitle:__string_core.xxpk_tips message:__string_core.xxpk_switchBox buttonTitles:@[__string_core.xxpk_ok,__string_core.xxpk_cancel] completion:^(NSInteger buttonIndex) {
        if (buttonIndex == 0) {
            [XXGPlayKitCore.shared xxpk_logout];
        }
    }];
}

+ (void)__xxpk_facebookShare:(NSURL *)url {
    NSDictionary * ext = [url xxpk_analyse];
    if (ext.allKeys.count == 0) {
        return;
    }
    NSString *linkUrl = ext[__data_core.xxpk_url];
    NSString *imgUrl = ext[__data_core.xxpk_imgUrl];
    if (linkUrl.xxpk_isNotEmpty) {
        [XXGPlayKitCore xxpk_facebookShareWithUrl:linkUrl];
        return;
    }
    if (imgUrl.xxpk_isNotEmpty) {
        [XXGPlayKitCore xxpk_facebookShareWithImgUrl:imgUrl];
        return;
    }
}

+ (void)__xxpk_facebookSub {
    [XXGPlayKitCore xxpk_facebookSub];
}

+ (void)__xxpk_facebookBind {
    [XXGPlayKitCore xxpk_facebookBind:^(NSDictionary * _Nullable userInfo, NSString * _Nonnull errorMsg) {
        if (errorMsg) {
            [XXGAlertView xxpk_showAlertWithTitle:__string_core.xxpk_tips message:errorMsg completion:nil];
        }else {
            [XXGToast showBottom:__string_core.xxpk_bind_sus];
        }
    }];
}

+ (void)__xxpk_facebookInvite {
    [XXGPlayKitCore xxpk_facebookInvite];
}

@end
