






#import "XXGExecuteActions.h"
#import "XXGAlertView.h"
#import "XXGPlayKitConfig.h"
#import "UIColor+XXGColor.h"
#import "XXGPlayKitCore.h"
#import "XXGMQTTManager.h"
#import "ZBObjectiveCBeaver.h"

@implementation XXGExecuteActions

+ (void)xxpk_executeActions:(NSArray<XXGActionItem *> *)actions index:(NSUInteger)index completion:(void(^)(void))completion {
    if (index >= actions.count){
        if (completion) {
            completion();
        }
        return;
    }

    XXGActionItem *item = actions[index];

    switch (item.xxpk_action) {
        case XXGActionTypeOpenClose:
            [self xxpk_handleOpenCloseAction:item]; break;
        case XXGActionTypeClose:
           [self xxpk_handleCloseAction:item]; break;
        case XXGActionTypeUpdate:
           [self xxpk_handleUpdateAction:item]; break;
        case XXGActionTypeTrampoline:
           [self xxpk_handleTrampolineAction:item]; break;
        case XXGActionTypeSplash:
           [self xxpk_handleSplashAction:item]; break;
        case XXGActionTypeMobile:
           [self xxpk_handleMobileAction:item]; break;
        case XXGActionTypeSubscribe:
           [self xxpk_handleSubscribeAction:item]; break;

        default:
        case XXGActionTypeUnknown:break;
    }

    
    [self xxpk_executeActions:actions index:index + 1 completion:completion];
}

+ (void)xxpk_handleOpenCloseAction:(XXGActionItem *)item {
    
    ZBLogInfo(__data_core.xxpk_log_action_open_close);
}


+ (void)xxpk_handleCloseAction:(XXGActionItem *)item {
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [XXGAlertView xxpk_showAlertWithTitle:__string_core.xxpk_tips message:item.xxpk_message completion:^(NSInteger buttonIndex) {
            exit(0);
        }];
    });
}

+ (void)xxpk_handleUpdateAction:(XXGActionItem *)item {
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        NSArray *buttons = item.xxpk_force? @[__string_core.xxpk_update] : @[__string_core.xxpk_update, __string_core.xxpk_updateLater];
        [XXGAlertView xxpk_showAlertWithTitle:__string_core.xxpk_newVersion message:item.xxpk_message buttonTitles:buttons completion:^(NSInteger buttonIndex) {
            if (buttonIndex == 0) {
                [self xxpk_handleUpdateAction:item];
                [XXGPlayKitCore.shared xxpk_coreHandleOpenUrl:item.xxpk_target];
            }
        }];
    });
}

+ (void)xxpk_handleTrampolineAction:(XXGActionItem *)item {
    ZBLogInfo(__data_core.xxpk_log_action_trampoline,item.xxpk_target);
    XXGPlayKitConfig.shared.xxpk_trampoline = YES;
    XXGPlayKitCore.shared.xxpk_delegate = nil;
    [[XXGPlayKitCore shared] xxpk_showUIofTrampoline:item.xxpk_target];
}

+ (void)xxpk_handleSplashAction:(XXGActionItem *)item {
    ZBLogInfo(__data_core.xxpk_log_action_splash,item.xxpk_target);
    [[XXGPlayKitCore shared] xxpk_showUIofPopup:item.xxpk_target];
}

+ (void)xxpk_handleMobileAction:(XXGActionItem *)item {
    ZBLogInfo(__data_core.xxpk_log_action_mobile,item.xxpk_force);
    [[XXGPlayKitCore shared] xxpk_showUIofbindMobile:@(item.xxpk_force) hasWkView:nil];
}

+ (void)xxpk_handleSubscribeAction:(XXGActionItem *)item {
    ZBLogInfo(__data_core.xxpk_log_action_mqtt);
    [[XXGMQTTManager shared] xxpk_connect];
}

@end
