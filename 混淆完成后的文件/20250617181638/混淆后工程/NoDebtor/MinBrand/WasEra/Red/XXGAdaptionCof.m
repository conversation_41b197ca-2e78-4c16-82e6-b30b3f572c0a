






#import "XXGAdaptionCof.h"
#import "NSObject+XXGModel.h"
#import "XXGPlayKitConfig.h"

@implementation XXGAdaptionCof

+ (NSDictionary *)xxpk_replacedKeyFromPropertyName {
    return __data_core.xxpk_adaption_cof;
}

- (NSArray<XXGSkinModel *> *)xxpk_adaption_skin_btns {
    
    @synchronized (self) {
        if (!_xxpk_adaption_skin_btns) {
            
            NSMutableArray<NSDictionary *> *filteredDictionaries = [NSMutableArray array];
            
            
            [self.xxpk_adaption_skin_comein enumerateKeysAndObjectsUsingBlock:^(NSString *key, id _Nonnull obj, BOOL * _Nonnull stop) {
                
                if (![obj isKindOfClass:[NSDictionary class]]) {
                    
                    return;
                }
                NSDictionary *originalDict = (NSDictionary *)obj;
                
                
                NSMutableDictionary *combinedDict = [NSMutableDictionary dictionaryWithDictionary:originalDict];
                combinedDict[__data_core.xxpk_name] = key;
                
                
                BOOL status = NO;
                if (originalDict[__data_core.xxpk_status] && [originalDict[__data_core.xxpk_status] respondsToSelector:@selector(boolValue)]) {
                    status = [originalDict[__data_core.xxpk_status] boolValue];
                }
                
                
                if (status) {
                    [filteredDictionaries addObject:[combinedDict copy]]; 
                }
            }];
            
            
            _xxpk_adaption_skin_btns = [XXGSkinModel xxpk_modelArrayWithDictArray:filteredDictionaries];
        }
    }
    return _xxpk_adaption_skin_btns;
}

@end
