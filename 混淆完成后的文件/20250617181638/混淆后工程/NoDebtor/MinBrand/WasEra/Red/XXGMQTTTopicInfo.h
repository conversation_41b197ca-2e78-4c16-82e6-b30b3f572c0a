






#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface XXGMQTTTopicInfo : NSObject


@property (nonatomic, copy) NSString *xxpk_type;
@property (nonatomic, copy) NSString *xxpk_message;


@property (nonatomic, assign) NSInteger xxpk_count;
@property (nonatomic, assign) CGFloat xxpk_position;
@property (nonatomic, assign) CGFloat xxpk_speed;
@property (nonatomic, assign) CGFloat xxpk_style_background_alpha;
@property (nonatomic, copy) NSString *xxpk_style_background_color;
@property (nonatomic, copy) NSString *xxpk_style_text_color;
@property (nonatomic, assign) CGFloat xxpk_style_text_font_size;
@property (nonatomic, copy) NSString *xxpk_click_action;
@property (nonatomic, copy) NSString *xxpk_click_url;


@property (nonatomic, strong) NSArray *xxpk_alert_buttons;
@property (nonatomic, copy) NSString *xxpk_title;


@property (nonatomic, copy) NSString *xxpk_action;
@property (nonatomic, copy) NSString *xxpk_url;


@property (nonatomic, assign) NSInteger xxpk_retry;

@end

NS_ASSUME_NONNULL_END
