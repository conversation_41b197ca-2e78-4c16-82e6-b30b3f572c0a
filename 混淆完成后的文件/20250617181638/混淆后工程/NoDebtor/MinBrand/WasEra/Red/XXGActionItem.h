






#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, XXGActionType) {
    XXGActionTypeOpenClose      = 0,
    XXGActionTypeClose          = 1,
    XXGActionTypeUpdate         = 2,
    XXGActionTypeTrampoline     = 3,
    XXGActionTypeSplash         = 4,
    
    XXGActionTypeMobile         = 5,
    XXGActionTypeRealName       = 6,
    XXGActionTypeSubscribe      = 7,
    XXGActionTypeUnknown        = 999
};

@interface XXGActionItem : NSObject

@property (nonatomic, assign) XXGActionType xxpk_action;

@property (nonatomic, copy) NSString *xxpk_target;

@property (nonatomic, copy) NSString *xxpk_message;

@property (nonatomic, assign) BOOL xxpk_force;

@property (nonatomic, copy) NSString *xxpk_bangs;

@property (nonatomic, copy) NSString *xxpk_orientation;

@end

NS_ASSUME_NONNULL_END
