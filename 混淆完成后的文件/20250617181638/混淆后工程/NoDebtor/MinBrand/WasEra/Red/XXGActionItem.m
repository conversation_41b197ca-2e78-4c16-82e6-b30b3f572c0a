






#import "XXGActionItem.h"
#import "XXGPlayKitConfig.h"

@interface XXGActionItem()

@property (nonatomic, copy) NSString *xxpk_type;

@end

@implementation XXGActionItem

+ (NSDictionary *)xxpk_replacedKeyFromPropertyName {
    return __data_core.xxpk_action_item;
}

- (XXGActionType)xxpk_action {
    
    static NSDictionary<NSString *, NSNumber *> *actionTypeMap;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        actionTypeMap = @{
            
            __data_core.xxpk_open_close  : @(XXGActionTypeOpenClose),
            __data_core.xxpk_close       : @(XXGActionTypeClose),
            __data_core.xxpk_update      : @(XXGActionTypeUpdate),
            
            
            __data_core.xxpk_trampoline  : @(XXGActionTypeTrampoline),
            __data_core.xxpk_splash      : @(XXGActionTypeSplash),
            
            
            __data_core.xxpk_mobile      : @(XXGActionTypeMobile),
            __data_core.xxpk_real_name   : @(XXGActionTypeRealName),
            __data_core.xxpk_subscribe   : @(XXGActionTypeSubscribe)
        };
    });
    
    
    NSNumber *actionNumber = actionTypeMap[self.xxpk_type];
    return actionNumber ? actionNumber.unsignedIntegerValue : XXGActionTypeUnknown;
}

@end
