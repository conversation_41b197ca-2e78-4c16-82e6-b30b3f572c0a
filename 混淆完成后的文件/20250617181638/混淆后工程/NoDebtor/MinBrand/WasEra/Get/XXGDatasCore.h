






#import "XXGDatasModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface XXGDatasCore : XXGDatasModel

@property(nonatomic, copy) NSString *xxpk_startid;
@property(nonatomic, copy) NSString *xxpk_error_code;

@property(nonatomic, copy) NSString *xxpk_base_url_k;
@property(nonatomic, copy) NSString *xxpk_base_os_url;
@property(nonatomic, copy) NSString *xxpk_base_cn_url;
@property(nonatomic, copy) NSString *xxpk_base_fix;

@property(nonatomic, copy) NSString *xxpk_action;
@property(nonatomic, copy) NSString *xxpk_actions;
@property(nonatomic, copy) NSString *xxpk_device;
@property(nonatomic, copy) NSString *xxpk_extend;
@property(nonatomic, copy) NSString *xxpk_api_list;
@property(nonatomic, copy) NSString *xxpk_secret;
@property(nonatomic, copy) NSString *xxpk_id;
@property(nonatomic, copy) NSString *xxpk_config;
@property(nonatomic, copy) NSString *xxpk_adaption;
@property(nonatomic, copy) NSString *xxpk_skin;
@property(nonatomic, copy) NSString *xxpk_login;
@property(nonatomic, copy) NSString *xxpk_extra_params_str;
@property(nonatomic, copy) NSString *xxpk_server;
@property(nonatomic, copy) NSString *xxpk_timestamp;
@property(nonatomic, copy) NSString *xxpk_box;
@property(nonatomic, copy) NSString *xxpk_token;
@property(nonatomic, copy) NSString *xxpk_sign;
@property(nonatomic, copy) NSString *xxpk_gzip;
@property(nonatomic, copy) NSString *xxpk_content_encoding;
@property(nonatomic, copy) NSString *xxpk_application_json;
@property(nonatomic, copy) NSString *xxpk_content_type;
@property(nonatomic, copy) NSString *xxpk_http_method;
@property(nonatomic, copy) NSString *xxpk_status;
@property(nonatomic, copy) NSString *xxpk_redirect;
@property(nonatomic, copy) NSString *xxpk_error;
@property(nonatomic, copy) NSString *xxpk_network;
@property(nonatomic, copy) NSString *xxpk_errmsg;
@property(nonatomic, copy) NSString *xxpk_ok;
@property(nonatomic, copy) NSString *xxpk_tip;
@property(nonatomic, copy) NSString *xxpk_recomein;
@property(nonatomic, copy) NSString *xxpk_url;
@property(nonatomic, copy) NSString *xxpk_imgUrl;
@property(nonatomic, copy) NSString *xxpk_boxName;
@property(nonatomic, copy) NSString *xxpk_name;
@property(nonatomic, copy) NSString *xxpk_boxKey;
@property(nonatomic, copy) NSString *xxpk_purpose;
@property(nonatomic, copy) NSString *xxpk_dial_code;
@property(nonatomic, copy) NSString *xxpk_version;
@property(nonatomic, copy) NSString *xxpk_platform;
@property(nonatomic, copy) NSString *xxpk_campaign;
@property(nonatomic, copy) NSString *xxpk_ios;
@property(nonatomic, copy) NSString *xxpk_lang;
@property(nonatomic, copy) NSString *xxpk_uniqueId;
@property(nonatomic, copy) NSString *xxpk_open_close;
@property(nonatomic, copy) NSString *xxpk_close;
@property(nonatomic, copy) NSString *xxpk_update;
@property(nonatomic, copy) NSString *xxpk_trampoline;
@property(nonatomic, copy) NSString *xxpk_splash;
@property(nonatomic, copy) NSString *xxpk_real_name;
@property(nonatomic, copy) NSString *xxpk_subscribe;
@property(nonatomic, copy) NSString *xxpk_sms_code;
@property(nonatomic, copy) NSString *xxpk_new_key;
@property(nonatomic, copy) NSString *xxpk_old_key;
@property(nonatomic, copy) NSString *xxpk_order;
@property(nonatomic, copy) NSString *xxpk_currency;
@property(nonatomic, copy) NSString *xxpk_pm;
@property(nonatomic, copy) NSString *xxpk_iap;
@property(nonatomic, copy) NSString *xxpk_h5;
@property(nonatomic, copy) NSString *xxpk_poopo_p;
@property(nonatomic, copy) NSString *xxpk_p_status;
@property(nonatomic, copy) NSString *xxpk_p_coupon_id;
@property(nonatomic, copy) NSString *xxpk_payload;
@property(nonatomic, copy) NSString *xxpk_state;
@property(nonatomic, copy) NSString *xxpk_user_info_url;
@property(nonatomic, copy) NSString *xxpk_landscape1;
@property(nonatomic, copy) NSString *xxpk_landscape2;
@property(nonatomic, copy) NSString *xxpk_open;
@property(nonatomic, copy) NSString *xxpk_uid;
@property(nonatomic, copy) NSString *xxpk_boxm_boxs_key;
@property(nonatomic, copy) NSString *xxpk_boxm_comeinedbox_key;
@property(nonatomic, copy) NSString *xxpk_core_open_page;
@property(nonatomic, copy) NSString *xxpk_type;
@property(nonatomic, copy) NSString *xxpk_type_shanyan;
@property(nonatomic, copy) NSString *xxpk_created;
@property(nonatomic, copy) NSString *xxpk_cp_extra;
@property(nonatomic, copy) NSString *xxpk_data;
@property(nonatomic, copy) NSString *xxpk_content;
@property(nonatomic, copy) NSString *xxpk_scheme_wx;
@property(nonatomic, copy) NSString *xxpk_wx_oauth;
@property(nonatomic, copy) NSString *xxpk_weixin;
@property(nonatomic, copy) NSString *xxpk_open_weixin_auth;

@property(nonatomic, copy) NSString *xxpk_log_config_success;
@property(nonatomic, copy) NSString *xxpk_log_resource_load_begin;
@property(nonatomic, copy) NSString *xxpk_log_resource_load_success;
@property(nonatomic, copy) NSString *xxpk_log_init_start;
@property(nonatomic, copy) NSString *xxpk_log_init_already;
@property(nonatomic, copy) NSString *xxpk_log_init_success;
@property(nonatomic, copy) NSString *xxpk_log_init_failed;
@property(nonatomic, copy) NSString *xxpk_log_init_login;
@property(nonatomic, copy) NSString *xxpk_log_login_ing;
@property(nonatomic, copy) NSString *xxpk_log_login_logined;
@property(nonatomic, copy) NSString *xxpk_log_login_prepare;
@property(nonatomic, copy) NSString *xxpk_log_login_not_init;
@property(nonatomic, copy) NSString *xxpk_log_login_start;
@property(nonatomic, copy) NSString *xxpk_log_login_success;
@property(nonatomic, copy) NSString *xxpk_log_logout;
@property(nonatomic, copy) NSString *xxpk_log_report_role;
@property(nonatomic, copy) NSString *xxpk_log_report_role_success;
@property(nonatomic, copy) NSString *xxpk_log_report_role_failed;
@property(nonatomic, copy) NSString *xxpk_log_pay_start;
@property(nonatomic, copy) NSString *xxpk_log_pay_success;
@property(nonatomic, copy) NSString *xxpk_log_pay_failed;
@property(nonatomic, copy) NSString *xxpk_log_pay_cancel;
@property(nonatomic, copy) NSString *xxpk_log_cold_start_url;
@property(nonatomic, copy) NSString *xxpk_log_hot_start_url;
@property(nonatomic, copy) NSString *xxpk_log_net_status;
@property(nonatomic, copy) NSString *xxpk_log_action_open_close;
@property(nonatomic, copy) NSString *xxpk_log_action_trampoline;
@property(nonatomic, copy) NSString *xxpk_log_action_splash;
@property(nonatomic, copy) NSString *xxpk_log_action_mobile;
@property(nonatomic, copy) NSString *xxpk_log_action_real_name;
@property(nonatomic, copy) NSString *xxpk_log_action_mqtt;
@property(nonatomic, copy) NSString *xxpk_log_att_duplicate_request;
@property(nonatomic, copy) NSString *xxpk_log_att_start_check;
@property(nonatomic, copy) NSString *xxpk_log_att_current_status;
@property(nonatomic, copy) NSString *xxpk_log_att_authorized_direct;
@property(nonatomic, copy) NSString *xxpk_log_att_denied;
@property(nonatomic, copy) NSString *xxpk_log_att_restricted;
@property(nonatomic, copy) NSString *xxpk_log_att_not_determined;
@property(nonatomic, copy) NSString *xxpk_log_att_ios_below_14;
@property(nonatomic, copy) NSString *xxpk_log_att_wait_app_active;
@property(nonatomic, copy) NSString *xxpk_log_att_app_active_delay;
@property(nonatomic, copy) NSString *xxpk_log_att_delay_app_state;
@property(nonatomic, copy) NSString *xxpk_log_att_app_active_request;
@property(nonatomic, copy) NSString *xxpk_log_att_app_inactive;
@property(nonatomic, copy) NSString *xxpk_log_att_add_observer2;
@property(nonatomic, copy) NSString *xxpk_log_att_remove_observer2;
@property(nonatomic, copy) NSString *xxpk_log_att_app_active_direct;
@property(nonatomic, copy) NSString *xxpk_log_att_remove_observer;
@property(nonatomic, copy) NSString *xxpk_log_att_showing_dialog;
@property(nonatomic, copy) NSString *xxpk_log_att_request_complete;
@property(nonatomic, copy) NSString *xxpk_log_att_callback_status;
@property(nonatomic, copy) NSString *xxpk_log_att_current_actual_status;
@property(nonatomic, copy) NSString *xxpk_log_att_authorized_success;
@property(nonatomic, copy) NSString *xxpk_log_att_still_not_determined;
@property(nonatomic, copy) NSString *xxpk_log_att_denied_restricted;
@property(nonatomic, copy) NSString *xxpk_log_att_waiting_user;
@property(nonatomic, copy) NSString *xxpk_log_att_still_waiting;
@property(nonatomic, copy) NSString *xxpk_log_att_timeout;
@property(nonatomic, copy) NSString *xxpk_log_att_timeout_final;
@property(nonatomic, copy) NSString *xxpk_log_att_user_choice;
@property(nonatomic, copy) NSString *xxpk_log_att_final_authorized;
@property(nonatomic, copy) NSString *xxpk_log_att_final_denied;
@property(nonatomic, copy) NSString *xxpk_log_att_final_restricted;
@property(nonatomic, copy) NSString *xxpk_log_att_wait_end;
@property(nonatomic, copy) NSString *xxpk_log_att_ios_below_14_wait;
@property(nonatomic, copy) NSString *xxpk_att_status_not_determined;
@property(nonatomic, copy) NSString *xxpk_att_status_restricted;
@property(nonatomic, copy) NSString *xxpk_att_status_denied;
@property(nonatomic, copy) NSString *xxpk_att_status_authorized;
@property(nonatomic, copy) NSString *xxpk_att_status_unknown;
@property(nonatomic, copy) NSString *xxpk_att_status_ios_not_support;
@property(nonatomic, copy) NSString *xxpk_app_state_active;
@property(nonatomic, copy) NSString *xxpk_app_state_inactive;
@property(nonatomic, copy) NSString *xxpk_app_state_background;
@property(nonatomic, copy) NSString *xxpk_app_state_unknown;
@property(nonatomic, copy) NSString *xxpk_log_manager_vk;
@property(nonatomic, copy) NSString *xxpk_log_manager_applovin;
@property(nonatomic, copy) NSString *xxpk_log_manager_poopo;
@property(nonatomic, copy) NSString *xxpk_log_manager_appsflyer;
@property(nonatomic, copy) NSString *xxpk_log_manager_facebook;
@property(nonatomic, copy) NSString *xxpk_log_manager_firebase;
@property(nonatomic, copy) NSString *xxpk_log_manager_adjust;
@property(nonatomic, copy) NSString *xxpk_manager_status_exist;
@property(nonatomic, copy) NSString *xxpk_manager_status_not_exist;
@property(nonatomic, copy) NSString *xxpk_manager_status_exist_version;
@property(nonatomic, copy) NSString *xxpk_log_manager_bdasignal;
@property(nonatomic, copy) NSString *xxpk_log_manager_shanyan;
@property(nonatomic, copy) NSString *xxpk_log_perform_selector;
@property(nonatomic, copy) NSString *xxpk_log_perform_instance_not_found;
@property(nonatomic, copy) NSString *xxpk_log_perform_class_not_found;
@property(nonatomic, copy) NSString *xxpk_log_perform_param_mismatch;
@property(nonatomic, copy) NSString *xxpk_log_iap_restore;
@property(nonatomic, copy) NSString *xxpk_log_iap_product_feedback;
@property(nonatomic, copy) NSString *xxpk_log_iap_product_count;
@property(nonatomic, copy) NSString *xxpk_log_iap_product_title;
@property(nonatomic, copy) NSString *xxpk_log_iap_product_desc;
@property(nonatomic, copy) NSString *xxpk_log_iap_product_price;
@property(nonatomic, copy) NSString *xxpk_log_iap_product_id;
@property(nonatomic, copy) NSString *xxpk_log_iap_currency_info;
@property(nonatomic, copy) NSString *xxpk_log_iap_start_purchase;
@property(nonatomic, copy) NSString *xxpk_log_iap_transaction_deferred;
@property(nonatomic, copy) NSString *xxpk_log_iap_lost_transaction_id;
@property(nonatomic, copy) NSString *xxpk_log_iap_purchase_complete;
@property(nonatomic, copy) NSString *xxpk_log_iap_add_products;
@property(nonatomic, copy) NSString *xxpk_log_iap_order_missing;
@property(nonatomic, copy) NSString *xxpk_log_iap_transaction_failed;
@property(nonatomic, copy) NSString *xxpk_log_iap_restore_received;
@property(nonatomic, copy) NSString *xxpk_log_iap_restore_product_id;
@property(nonatomic, copy) NSString *xxpk_log_iap_restore_error;
@property(nonatomic, copy) NSString *xxpk_log_iap_order_verify_success;
@property(nonatomic, copy) NSString *xxpk_log_iap_prepare_delete_order;
@property(nonatomic, copy) NSString *xxpk_log_iap_verify_callback;
@property(nonatomic, copy) NSString *xxpk_log_iap_receipt_refresh_success;
@property(nonatomic, copy) NSString *xxpk_log_iap_receipt_refresh_error;
@property(nonatomic, copy) NSString *xxpk_log_iap_verifying;
@property(nonatomic, copy) NSString *xxpk_log_iap_start_verify;
@property(nonatomic, copy) NSString *xxpk_log_iap_error;
@property(nonatomic, copy) NSString *xxpk_log_iap_delete_order_success;
@property(nonatomic, copy) NSString *xxpk_log_iap_delete_order_failed;
@property(nonatomic, copy) NSString *xxpk_log_mqtt_received;


@property(nonatomic, copy) NSArray *xxpk_security_jb_paths;
@property(nonatomic, copy) NSArray *xxpk_security_dylib_set;
@property(nonatomic, copy) NSArray *xxpk_security_check_paths;
@property(nonatomic, copy) NSArray *xxpk_security_check_classes;
@property(nonatomic, copy) NSString *xxpk_security_cydia_url_1;
@property(nonatomic, copy) NSString *xxpk_security_cydia_url_2;
@property(nonatomic, copy) NSString *xxpk_security_test_path;
@property(nonatomic, copy) NSString *xxpk_security_test_content;
@property(nonatomic, copy) NSString *xxpk_security_system_lib_path;
@property(nonatomic, copy) NSString *xxpk_security_dyld_env_var;
@property(nonatomic, copy) NSArray *xxpk_security_symlink_paths;

@property(nonatomic, copy) NSString *xxpk_net_oauth;
@property(nonatomic, copy) NSString *xxpk_net_src;
@property(nonatomic, copy) NSString *xxpk_net_auth_token;
@property(nonatomic, copy) NSString *xxpk_net_nonce;
@property(nonatomic, copy) NSString *xxpk_net_src_facebook;
@property(nonatomic, copy) NSString *xxpk_net_src_vk;
@property(nonatomic, copy) NSString *xxpk_net_src_poopo;
@property(nonatomic, assign) NSInteger xxpk_net_code_error;
@property(nonatomic, copy) NSString *xxpk_net_real_id;
@property(nonatomic, copy) NSString *xxpk_net_real_name;
@property(nonatomic, copy) NSString *xxpk_net_real_adjid;
@property(nonatomic, copy) NSString *xxpk_net_code;
@property(nonatomic, copy) NSString *xxpk_net_appid;


@property(nonatomic, copy) NSString *xxpk_middleware_facebook;
@property(nonatomic, copy) NSString *xxpk_middleware_appflyer;
@property(nonatomic, copy) NSString *xxpk_middleware_firebase;
@property(nonatomic, copy) NSString *xxpk_middleware_vk;
@property(nonatomic, copy) NSString *xxpk_middleware_adjust;
@property(nonatomic, copy) NSString *xxpk_middleware_poopo;
@property(nonatomic, copy) NSString *xxpk_middleware_applovin;


@property(nonatomic, copy) NSString *xxpk_middleware_shanyan;
@property(nonatomic, copy) NSString *xxpk_middleware_bdasignal;

@property(nonatomic, copy) NSString *xxpk_mqtt_exit;
@property(nonatomic, copy) NSString *xxpk_mqtt_unsubscribe;
@property(nonatomic, copy) NSString *xxpk_mqtt_topic;
@property(nonatomic, copy) NSString *xxpk_mqtt_qos;
@property(nonatomic, copy) NSString *xxpk_mqtt_type;
@property(nonatomic, copy) NSString *xxpk_mqtt_type_redot;
@property(nonatomic, copy) NSString *xxpk_mqtt_type_marquee;
@property(nonatomic, copy) NSString *xxpk_mqtt_type_alert;
@property(nonatomic, copy) NSString *xxpk_mqtt_type_popup;
@property(nonatomic, copy) NSString *xxpk_mqtt_type_ucenter;
@property(nonatomic, copy) NSString *xxpk_mqtt_type_offline;
@property(nonatomic, copy) NSString *xxpk_mqtt_type_apple_review;
@property(nonatomic, copy) NSString *xxpk_mqtt_label;
@property(nonatomic, copy) NSString *xxpk_mqtt_click;
@property(nonatomic, copy) NSString *xxpk_mqtt_jump;
@property(nonatomic, copy) NSString *xxpk_mqtt_url;
@property(nonatomic, copy) NSString *xxpk_mqtt_action;
@property(nonatomic, copy) NSString *xxpk_mqtt_open;

@property(nonatomic, copy) NSString *xxpk_core_mt_openURL;
@property(nonatomic, copy) NSString *xxpk_core_mt_changep;
@property(nonatomic, copy) NSString *xxpk_core_mt_bindm;
@property(nonatomic, copy) NSString *xxpk_core_mt_switcha;
@property(nonatomic, copy) NSString *xxpk_core_mt_ucenter;
@property(nonatomic, copy) NSString *xxpk_core_mt_iapRepair;
@property(nonatomic, copy) NSString *xxpk_core_mt_getInfomation;
@property(nonatomic, copy) NSString *xxpk_core_mt_getInfomation_uid;
@property(nonatomic, copy) NSString *xxpk_core_mt_getInfomation_name;
@property(nonatomic, copy) NSString *xxpk_core_mt_getInfomation_token;
@property(nonatomic, copy) NSString *xxpk_core_mt_getInfomation_fbuid;
@property(nonatomic, copy) NSString *xxpk_core_mt_getInfomation_fbtoken;
@property(nonatomic, copy) NSString *xxpk_core_mt_getInfomation_fbauthtoken;
@property(nonatomic, copy) NSString *xxpk_core_mt_getInfomation_fbnonce;
@property(nonatomic, copy) NSString *xxpk_core_mt_getInfomation_user;
@property(nonatomic, copy) NSString *xxpk_core_mt_userInfoSub;
@property(nonatomic, copy) NSString *xxpk_core_mt_closeSplash;
@property(nonatomic, copy) NSString *xxpk_core_mt_openUserCenterSidebar;
@property(nonatomic, copy) NSString *xxpk_core_mt_accountRemove;
@property(nonatomic, copy) NSString *xxpk_core_mt_getApiUrl;
@property(nonatomic, copy) NSString *xxpk_core_mt_getToken;
@property(nonatomic, copy) NSString *xxpk_core_mt_facebookShare;
@property(nonatomic, copy) NSString *xxpk_core_mt_facebookSub;
@property(nonatomic, copy) NSString *xxpk_core_mt_facebookBind;
@property(nonatomic, copy) NSString *xxpk_core_mt_facebookInvite;
@property(nonatomic, copy) NSString *xxpk_core_mt_popup;
@property(nonatomic, copy) NSString *xxpk_core_mt_coin_p;
@property(nonatomic, copy) NSString *xxpk_core_mt_wxbind;

@property(nonatomic, copy) NSString *xxpk_core_mt_func_getInfomation;
@property(nonatomic, copy) NSString *xxpk_core_mt_func_getToken;
@property(nonatomic, copy) NSString *xxpk_core_mt_func_getApiUrl;

@property(nonatomic, copy) NSString *xxpk_vk_roter_os;
@property(nonatomic, copy) NSString *xxpk_vk_roter_cn;
@property(nonatomic, copy) NSString *xxpk_vk_add_token;
@property(nonatomic, copy) NSString *xxpk_vk_and;
@property(nonatomic, copy) NSString *xxpk_vk_wenhao;

@property(nonatomic, strong) NSDictionary *xxpk_start_body;
@property(nonatomic, strong) NSDictionary *xxpk_device_info;
@property(nonatomic, strong) NSDictionary *xxpk_product_body;
@property(nonatomic, strong) NSDictionary *xxpk_receipt_body;
@property(nonatomic, strong) NSDictionary *xxpk_role_body;
@property(nonatomic, strong) NSDictionary *xxpk_action_item;
@property(nonatomic, strong) NSDictionary *xxpk_box_content;
@property(nonatomic, strong) NSDictionary *xxpk_skin_model;
@property(nonatomic, strong) NSDictionary *xxpk_theme_color;
@property(nonatomic, strong) NSDictionary *xxpk_docker_cof;
@property(nonatomic, strong) NSDictionary *xxpk_service_info;
@property(nonatomic, strong) NSDictionary *xxpk_box_center_cof;
@property(nonatomic, strong) NSDictionary *xxpk_adaption_cof;
@property(nonatomic, strong) NSDictionary *xxpk_extra_params;
@property(nonatomic, strong) NSDictionary *xxpk_extends;
@property(nonatomic, strong) NSDictionary *xxpk_select_product;
@property(nonatomic, strong) NSDictionary *xxpk_product;
@property(nonatomic, strong) NSDictionary *xxpk_mqtt_info;
@property(nonatomic, strong) NSDictionary *xxpk_mqtt_topic_info;
@property(nonatomic, strong) NSDictionary *xxpk_server_info;
@property(nonatomic, strong) NSDictionary *xxpk_net_list;

@end

NS_ASSUME_NONNULL_END
