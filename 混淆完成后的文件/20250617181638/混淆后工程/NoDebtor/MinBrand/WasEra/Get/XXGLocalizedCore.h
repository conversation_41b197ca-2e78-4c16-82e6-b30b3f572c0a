






#import "XXGLocalizedModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface XXGLocalizedCore : XXGLocalizedModel

@property(nonatomic, copy) NSString *xxpk_startError;
@property(nonatomic, copy) NSString *xxpk_comeinError;
@property(nonatomic, copy) NSString *xxpk_bindError;
@property(nonatomic, copy) NSString *xxpk_weixinBindSucces;
@property(nonatomic, copy) NSString *xxpk_registerError;
@property(nonatomic, copy) NSString *xxpk_newVersion;
@property(nonatomic, copy) NSString *xxpk_update;
@property(nonatomic, copy) NSString *xxpk_updateLater;
@property(nonatomic, copy) NSString *xxpk_uploadrole_error;
@property(nonatomic, copy) NSString *xxpk_p_params_error;
@property(nonatomic, copy) NSString *xxpk_p_not_config;
@property(nonatomic, copy) NSString *xxpk_p_notype;
@property(nonatomic, copy) NSString *xxpk_p_error;
@property(nonatomic, copy) NSString *xxpk_p_pornop;
@property(nonatomic, copy) NSString *xxpk_p_cancel;
@property(nonatomic, copy) NSString *xxpk_p_sustip;
@property(nonatomic, copy) NSString *xxpk_comein_sus;
@property(nonatomic, copy) NSString *xxpk_comein_cancel;
@property(nonatomic, copy) NSString *xxpk_switchBox;
@property(nonatomic, copy) NSString *xxpk_account_removed;
@property(nonatomic, copy) NSString *xxpk_isbinded;
@property(nonatomic, copy) NSString *xxpk_bind_sus;
@property(nonatomic, copy) NSString *xxpk_one_click;
@property(nonatomic, copy) NSString *xxpk_click_agreem;
@property(nonatomic, copy) NSString *xxpk_protoclon_core;
@property(nonatomic, copy) NSString *xxpk_and;


@property(nonatomic, copy) NSString *xxpk_tool_iap_repair_start;
@property(nonatomic, copy) NSString *xxpk_tool_iap_repair_complete;

@end

NS_ASSUME_NONNULL_END
