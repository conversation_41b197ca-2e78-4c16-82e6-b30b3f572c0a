






#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface XXGDeviceInfo : NSObject

@property (nonatomic, copy) NSString *xxpk_name;
@property (nonatomic, copy) NSString *xxpk_idfa;
@property (nonatomic, copy) NSString *xxpk_idfv;
@property (nonatomic, copy) NSString *xxpk_model;
@property (nonatomic, copy) NSString *xxpk_os;
@property (nonatomic, copy) NSString *xxpk_osVersion;
@property (nonatomic, copy) NSString *xxpk_vindatool;
@property (nonatomic, copy) NSString *xxpk_docPath;
@property (nonatomic, copy) NSString *xxpk_network;
@property (nonatomic, copy) NSString *xxpk_lang;
@property (nonatomic, copy) NSString *xxpk_scale;
@property (nonatomic, copy) NSString *xxpk_landscape;
@property (nonatomic, copy) NSString *xxpk_deeplink;
@property (nonatomic, copy) NSString *xxpk_afid;
@property (nonatomic, copy) NSString *xxpk_firebaseId;

@end

NS_ASSUME_NONNULL_END
