






#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface XXGRoleBody : NSObject


@property (nonatomic, copy) NSString * xxpk_serverName;


@property (nonatomic, copy) NSString * xxpk_serverId;


@property (nonatomic, copy) NSString * xxpk_roleId;


@property (nonatomic, copy) NSString * xxpk_roleName;


@property (nonatomic, copy) NSString * xxpk_roleLevel;

@property (nonatomic, strong) NSDictionary * xxpk_extend;
@end

NS_ASSUME_NONNULL_END
