






#import <Foundation/Foundation.h>
#import "XXGUIkitProtocol.h"
@class UIWindow;

typedef NS_ENUM(NSUInteger, XXGShowViewControllerType) {
    XXGShowViewControllerTypeComein,
    XXGShowViewControllerTypeSelectAccount,
    XXGShowViewControllerTypeBindMobile,
    XXGShowViewControllerTypeUserCenter,
    XXGShowViewControllerTypeSelectPP,
    XXGShowViewControllerTypeChangePassword,
    XXGShowViewControllerTypePopup,
    XXGShowViewControllerTypeSavePS,

};

NS_ASSUME_NONNULL_BEGIN

@interface XXGUIKit : NSObject


+ (void)__xxpk_showNoStackViewControllerWithType:(XXGShowViewControllerType)type xxpk_object:(id)xxpk_delegate xxpk_delegate:(id<XXGUIkitDelegate>)xxpk_delegate;

+ (void)xxpk_showViewControllerWithType:(XXGShowViewControllerType)type xxpk_delegate:(id<XXGUIkitDelegate> _Nullable)xxpk_delegate;
+ (void)xxpk_showViewControllerWithType:(XXGShowViewControllerType)type xxpk_object:(id _Nullable)xxpk_delegate xxpk_delegate:(id<XXGUIkitDelegate> _Nullable)xxpk_delegate;

+ (UIWindow *)xxpk_currentWindow;
+ (void)xxpk_dissmissCurrentWinow;
+ (void)xxpk_dissmissAllWindows;
@end

NS_ASSUME_NONNULL_END
