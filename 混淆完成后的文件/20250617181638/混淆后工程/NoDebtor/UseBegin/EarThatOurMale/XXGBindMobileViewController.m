






#import "XXGBindMobileViewController.h"
#import "XXGSendCodeButton.h"
#import "XXGToast.h"
#import "XXGMobileTextField.h"
#import "NSString+XXGString.h"
@import WebKit;

@interface XXGBindMobileViewController ()

@property (nonatomic, strong) XXGMobileTextField *xxpk_mobileTextField;
@property (nonatomic, strong) UITextField *xxpk_codeTextField;
@property (nonatomic, strong) XXGSendCodeButton *xxpk_codeButton;

@end

@implementation XXGBindMobileViewController


- (XXGSendCodeButton *)xxpk_codeButton {
    if (!_xxpk_codeButton) {
        _xxpk_codeButton = [[XXGSendCodeButton alloc] init];
    }
    return _xxpk_codeButton;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.xxpk_closeButton.hidden = [self.xxpk_object[0] boolValue];
    
    UILabel *tipLabel = [XXGUIDriver xxpk_labelNormal:XXGUIDriver.xxpk_string_ui.xxpk_bind_mobile_tips];
    tipLabel.numberOfLines = 0;
    [self.view addSubview:tipLabel];
    [tipLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float30);
        make.left.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float30);
        make.right.mas_equalTo(-XXGUIDriver.xxpk_data_ui.xxpk_float30);
    }];
    
    
    self.xxpk_mobileTextField = [[XXGMobileTextField alloc] initWithController:self];
    [self.view addSubview:self.xxpk_mobileTextField];
    [self.xxpk_mobileTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(tipLabel.mas_bottom).offset(XXGUIDriver.xxpk_data_ui.xxpk_float12);
        make.left.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float25);
        make.right.mas_equalTo(-XXGUIDriver.xxpk_data_ui.xxpk_float25);
        make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float38);
    }];
    
    
    self.xxpk_codeTextField = [XXGUIDriver xxpk_textFieldOfVerificationCode];
    [self.view addSubview:self.xxpk_codeTextField];
    [self.xxpk_codeTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.xxpk_mobileTextField.mas_bottom).offset(XXGUIDriver.xxpk_data_ui.xxpk_float17);
        make.left.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float25);
        make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float38);
    }];
    
    weakify(self);
    self.xxpk_codeButton.xxpk_sendCodeAction = ^{
        strongify(self);
        NSString *xxpk_dialCode = self.xxpk_mobileTextField.xxpk_dial_code;
        NSString *xxpk_mobile = self.xxpk_mobileTextField.xxpk_mobile_num;
        if (self.xxpk_mobileTextField.xxpk_mobileTextField.text.xxpk_isEmpty) {
            [self.xxpk_codeButton xxpk_stopCountdown];
            [XXGAlertView xxpk_showAlertWithTitle:XXGUIDriver.xxpk_string_ui.xxpk_tips message:XXGUIDriver.xxpk_string_ui.xxpk_moblie_verified completion:nil];
            return;
        }
        if ([self.xxpk_delegate respondsToSelector:@selector(xxpk_sendCodeButtonDidClickWithType:xxpk_moblil:dialCode:completion:)]) {
            [XXGLoadingView showLoadingOnWindow];
            [self.xxpk_delegate xxpk_sendCodeButtonDidClickWithType:XXGUIDriver.xxpk_data_ui.xxpk_code_bind xxpk_moblil:xxpk_mobile dialCode:xxpk_dialCode completion:^(id object) {
                [XXGLoadingView hideLoadingFromWindow];
                if ([object boolValue]) {
                    [XXGToast showBottom:XXGUIDriver.xxpk_string_ui.xxpk_sendedVerificationCode];
                }else {
                    [self.xxpk_codeButton xxpk_stopCountdown];
                }
            }];
        }
    };
    [self.view addSubview:self.xxpk_codeButton];
    [self.xxpk_codeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.xxpk_codeTextField);
        make.height.equalTo(self.xxpk_codeTextField);
        make.left.equalTo(self.xxpk_codeTextField.mas_right).offset(XXGUIDriver.xxpk_data_ui.xxpk_float10);
        make.right.equalTo(self.xxpk_mobileTextField);
    }];
    
    
    [self.xxpk_codeButton setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    
    
    UIButton *xxpk_bindButton = [XXGUIDriver xxpk_buttonMainColor:XXGUIDriver.xxpk_string_ui.xxpk_bind_mobile];
    [xxpk_bindButton addTarget:self action:@selector(xxpk_bindButtonAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:xxpk_bindButton];
    [xxpk_bindButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.xxpk_codeTextField.mas_bottom).offset(XXGUIDriver.xxpk_data_ui.xxpk_float17);
        make.left.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float25);
        make.right.mas_equalTo(-XXGUIDriver.xxpk_data_ui.xxpk_float25);
        make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float40);
    }];
}

- (void)xxpk_bindButtonAction:(id)sender {
    if (self.xxpk_mobileTextField.xxpk_mobileTextField.text.xxpk_isEmpty) {
        [XXGAlertView xxpk_showAlertWithTitle:XXGUIDriver.xxpk_string_ui.xxpk_tips message:XXGUIDriver.xxpk_string_ui.xxpk_moblie_verified completion:nil];
        return;
    }
    if (self.xxpk_codeTextField.text.xxpk_isEmpty) {
        [XXGAlertView xxpk_showAlertWithTitle:XXGUIDriver.xxpk_string_ui.xxpk_tips message:XXGUIDriver.xxpk_string_ui.xxpk_code_verified completion:nil];
        return;
    }
    NSString *xxpk_dialCode = self.xxpk_mobileTextField.xxpk_dial_code;
    NSString *xxpk_mobile = self.xxpk_mobileTextField.xxpk_mobile_num;
    if ([self.xxpk_delegate respondsToSelector:@selector(xxpk_bindMobileButtonDidClickWithMobile:code:dialCode:completion:)]) {
        [XXGLoadingView showLoadingOnWindow];
        [self.xxpk_delegate xxpk_bindMobileButtonDidClickWithMobile:xxpk_mobile code:self.xxpk_codeTextField.text dialCode:xxpk_dialCode completion:^(id object) {
            [XXGLoadingView hideLoadingFromWindow];
            if ([object boolValue]) {
                [[XXGWindowManager shared] xxpk_dismissWindowWithRootViewController:self.navigationController];
                [XXGToast showBottom:XXGUIDriver.xxpk_string_ui.xxpk_bind_mobile_success];
                if ([self.xxpk_object[1] isKindOfClass:[WKWebView class]]) {
                    WKWebView *xxpk_vkview = (WKWebView *)self.xxpk_object[1];
                    [xxpk_vkview reload];
                }
            }
        }];
    }
}

- (void)dealloc {
    [self.xxpk_codeButton xxpk_stopCountdown];
}

@end
