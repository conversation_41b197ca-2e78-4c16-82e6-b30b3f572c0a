






#import "XXGContentTextViewController.h"
#import <WebKit/WebKit.h>
#import <WebKit/WKFoundation.h>
#import "NSString+XXGString.h"
#import "NSString+URLEncoding.h"

@interface XXGContentTextViewController ()<UIScrollViewDelegate,WKNavigationDelegate>

@property (nonatomic, strong) UISegmentedControl *xxpk_segmentedControl;
@property (nonatomic, strong) UIView * xxpk_contentView1;
@property (nonatomic, strong) UIView * xxpk_contentView2;

@property (nonatomic, strong) UIScrollView * xxpk_scrollView;

@end

@implementation XXGContentTextViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.xxpk_closeButton.hidden = YES;
    self.xxpk_backButton.hidden = YES;
    
    UISegmentedControl *segmentView = [[UISegmentedControl alloc] initWithItems:@[XXGUIDriver.xxpk_string_ui.xxpk_content_userprotocol,XXGUIDriver.xxpk_string_ui.xxpk_content_privacypolicy]];
    segmentView.layer.masksToBounds = YES; 
    segmentView.layer.cornerRadius = 2;    
    [segmentView setTitleTextAttributes:@{NSForegroundColorAttributeName:[XXGUIDriver xxpk_mainColor]} forState:UIControlStateSelected];
    [segmentView setTitleTextAttributes:@{NSForegroundColorAttributeName:[XXGUIDriver xxpk_mainColor]} forState:UIControlStateNormal];
    [self.view addSubview:segmentView];
    [segmentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.view);
        make.top.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float7);
    }];
    [segmentView addTarget:self action:@selector(segmentChange:) forControlEvents:UIControlEventValueChanged];
    self.xxpk_segmentedControl = segmentView;
    
    _xxpk_scrollView = [[UIScrollView alloc]init];
    _xxpk_scrollView.pagingEnabled = YES;
    _xxpk_scrollView.delegate = self;
    [self.view addSubview:_xxpk_scrollView];
    [_xxpk_scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view).offset(XXGUIDriver.xxpk_data_ui.xxpk_float15);
        make.right.equalTo(self.view).offset(-XXGUIDriver.xxpk_data_ui.xxpk_float15);
        make.top.equalTo(self.view).offset(XXGUIDriver.xxpk_data_ui.xxpk_float45);
        make.bottom.equalTo(self.view).offset(-XXGUIDriver.xxpk_data_ui.xxpk_float43);
    }];
    
    UIView *containerView = [UIView new];
    containerView.backgroundColor = UIColor.whiteColor;
    [self.xxpk_scrollView addSubview:containerView];
    [containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.xxpk_scrollView);
        make.height.equalTo(_xxpk_scrollView);
    }];
    
    UIView * contentView1 = [self getContentView:[XXGUIDriver xxpk_contentText1]];
    [containerView addSubview:contentView1];
    [contentView1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(0);
        make.top.bottom.equalTo(containerView);
        make.width.mas_equalTo(self.xxpk_scrollView);
    }];
    self.xxpk_contentView1 = contentView1;
    
    UIView * contentView2 = [self getContentView:[XXGUIDriver xxpk_contentText2]];
    [containerView addSubview:contentView2];
    [contentView2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(contentView1.mas_right);
        make.bottom.top.equalTo(containerView);
        make.width.mas_equalTo(self.xxpk_scrollView);
    }];
    self.xxpk_contentView2 = contentView2;
    
    [containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(contentView2.mas_right);
    }];
    
    if (![self.xxpk_object boolValue]) {
        UIButton *xxpk_noButton = [XXGUIDriver xxpk_buttonMainColor:XXGUIDriver.xxpk_string_ui.xxpk_noagree];
        [xxpk_noButton setBackgroundImage:[UIImage xxpk_imageWithColor:[[UIColor lightGrayColor] colorWithAlphaComponent:0.5f]] forState:UIControlStateNormal];
        [xxpk_noButton addTarget:self action:@selector(xxpk_noButtonDidClick:) forControlEvents:(UIControlEventTouchUpInside)];
        [self.view addSubview:xxpk_noButton];
        [xxpk_noButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.view).offset(-XXGUIDriver.xxpk_data_ui.xxpk_float4);
            make.centerX.equalTo(self.view).multipliedBy(.65);
            make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float35);
        }];
    }
    
    UIButton *xxpk_okButton =  [XXGUIDriver xxpk_buttonMainColor:XXGUIDriver.xxpk_string_ui.xxpk_agree];
    [xxpk_okButton addTarget:self action:@selector(xxpk_okButtonDidClick:) forControlEvents:(UIControlEventTouchUpInside)];
    [self.view addSubview:xxpk_okButton];
    [xxpk_okButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.view).offset(-XXGUIDriver.xxpk_data_ui.xxpk_float4);
        make.centerX.equalTo(self.view).multipliedBy(![self.xxpk_object boolValue]?1.35:1);
        make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float35);
    }];
    
    segmentView.selectedSegmentIndex = 0;
    [self segmentChange:segmentView];
}

-(void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView{
    [self.xxpk_segmentedControl setSelectedSegmentIndex:scrollView.contentOffset.x/self.view.frame.size.width ==0?0:1];
    [self xxpk_setContentText:scrollView.contentOffset.x/self.view.frame.size.width ==0?0:1];
}

- (void)segmentChange:(UISegmentedControl *)sender {
    [self xxpk_setContentText:sender.selectedSegmentIndex == 0?0:1];
    [self.xxpk_scrollView setContentOffset:CGPointMake(sender.selectedSegmentIndex == 0?0:self.xxpk_scrollView.frame.size.width, 0) animated:YES];
}

- (void)xxpk_setContentText:(NSInteger)type {
    NSString *contentUrl = nil;
    UIView *contentView = nil;
    contentUrl = type == 0 ? [XXGUIDriver xxpk_contentText1]:[XXGUIDriver xxpk_contentText2];
    contentView = type == 0 ? self.xxpk_contentView1:self.xxpk_contentView2;
    
    if (contentUrl.xxpk_isEmpty) {
        return;
    }
    
    if ([[contentUrl pathExtension] containsString:XXGUIDriver.xxpk_data_ui.xxpk_content_txt]) {
        UITextView *ctView = (UITextView *)contentView;
        if (ctView.text.length > 0) {
            return;
        }

        
        [XXGLoadingView showLoadingOnView:contentView];

        
        NSURL *url = [NSURL URLWithString:contentUrl];
        NSURLSessionDataTask *task = [[NSURLSession sharedSession] dataTaskWithURL:url
                                                                 completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {
            
            dispatch_async(dispatch_get_main_queue(), ^{
                
                [XXGLoadingView hideLoadingFromView:contentView];
                
                if (error || data.length == 0) {
                    
                    ctView.text = XXGUIDriver.xxpk_string_ui.xxpk_contenttext_loaderror;
                    return;
                }
                
                
                NSString *contentString = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
                ctView.text = contentString ?: XXGUIDriver.xxpk_string_ui.xxpk_contenttext_analysiserror;
            });
        }];
        
        [task resume];

    }else {
        WKWebView *wkview = (WKWebView *)contentView;
        if (!wkview.isLoading && wkview.estimatedProgress == 1) {
            [XXGLoadingView hideLoadingFromView:contentView];
            return;
        }
        [XXGLoadingView showLoadingOnView:contentView];
        NSString *urlstring =  [contentUrl.xxpk_urlDecodedString stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
        NSURL *url = [NSURL URLWithString:urlstring];
        NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:url cachePolicy:NSURLRequestReloadIgnoringCacheData timeoutInterval:15.0];
        [wkview loadRequest:request];
    }
}

- (void)xxpk_noButtonDidClick:(id)sender {
    [self xxpk_backButtonAction:nil];
    if (self.xxpk_completion) {
        self.xxpk_completion(NO);
    }
}

- (void)xxpk_okButtonDidClick:(id)sender {
    [self xxpk_backButtonAction:nil];
    if (self.xxpk_completion) {
        self.xxpk_completion(YES);
    }
}

- (UIView *)getContentView:(NSString *)string {
    UIView *contentview = nil;
    if ([[string pathExtension] containsString:XXGUIDriver.xxpk_data_ui.xxpk_content_txt]) {
        UITextView * textview = [UITextView new];
        textview.editable = NO;
        textview.backgroundColor = UIColor.whiteColor;
        textview.textColor = UIColor.grayColor;
        contentview = textview;
    }else {
        WKWebView *xxpk_wkview = [[WKWebView alloc] initWithFrame:CGRectZero];
        xxpk_wkview.backgroundColor = UIColor.clearColor;
        xxpk_wkview.scrollView.backgroundColor = UIColor.lightGrayColor;
        xxpk_wkview.opaque = YES;
        xxpk_wkview.scrollView.bounces =NO;
        xxpk_wkview.scrollView.showsVerticalScrollIndicator = NO;
        xxpk_wkview.scrollView.showsHorizontalScrollIndicator = NO;
        xxpk_wkview.navigationDelegate = self;
        contentview = xxpk_wkview;
    }
    return contentview;
}

- (void)webView:(WKWebView *)webView didFinishNavigation:(WKNavigation *)navigation {
    [XXGLoadingView hideLoadingFromView:webView];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    UIEdgeInsets safeInsets = [[XXGWindowManager shared] xxpk_currentWindow].safeAreaInsets;
    safeInsets.top    += 10;
    safeInsets.left   += 10;
    safeInsets.bottom += 10;
    safeInsets.right  += 10;

    [self.view mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(safeInsets);
    }];
}

@end
