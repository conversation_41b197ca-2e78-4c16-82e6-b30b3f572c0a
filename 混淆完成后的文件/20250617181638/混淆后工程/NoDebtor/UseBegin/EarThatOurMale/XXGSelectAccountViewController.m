






#import "XXGSelectAccountViewController.h"
#import "XXGComeinViewController.h"
#import "XXGSelectAccountCell.h"

@interface XXGSelectAccountViewController ()<UITableViewDelegate,UITableViewDataSource>

@property (nonatomic, strong) UIView *xxpk_backgroundView;

@property (nonatomic, strong) UIView *xxpk_logoView;

@property (nonatomic, strong) UITableView *xxpk_tableView;


@property (nonatomic, assign) BOOL xxpk_showSelect;

@property (nonatomic, weak) id xxpk_selectBox;

@property (nonatomic, strong) NSMutableArray *xxpk_LocalBoxArray;

@property (nonatomic, strong) NSMutableArray *xxpk_boxArray;

@property (nonatomic, strong) UIButton *xxpk_otherButton;
@property (nonatomic, strong) UIButton *xxpk_comeinButton;

@end

@implementation XXGSelectAccountViewController

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    if (_xxpk_boxArray.count > 0 && self.xxpk_showSelect) {
        self.xxpk_showSelect = NO;
    }
}

- (void)viewWillAppear:(BOOL)animated {
    
    [self.view mas_makeConstraints:^(MASConstraintMaker *make) {
        CGFloat bottom = XXGUIDriver.xxpk_data_ui.xxpk_float30;
        make.centerX.equalTo(self.view.superview);
        make.centerY.equalTo(self.view.superview).offset(+bottom/2);
        make.height.mas_equalTo([XXGUIDriver xxpk_mainContentViewSize].height+bottom);
        make.width.mas_equalTo([XXGUIDriver xxpk_mainContentViewSize].width);
    }];
}

- (void)setXxpk_showSelect:(BOOL)xxpk_showSelect {
    
    _xxpk_showSelect = xxpk_showSelect;
    
    _xxpk_boxArray = xxpk_showSelect ? _xxpk_LocalBoxArray : [NSMutableArray arrayWithObject:_xxpk_selectBox];
    
    [self.xxpk_tableView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(!xxpk_showSelect ? XXGUIDriver.xxpk_data_ui.xxpk_float55 : self.xxpk_boxArray.count > 3 ? 3 * XXGUIDriver.xxpk_data_ui.xxpk_float55  : self.xxpk_boxArray.count * XXGUIDriver.xxpk_data_ui.xxpk_float55);
    }];
    
    self.xxpk_tableView.scrollEnabled = xxpk_showSelect;
    
    [self.xxpk_tableView reloadData];
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.002 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self.xxpk_tableView setContentOffset:CGPointMake(0, 0) animated:NO];
    });
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.view.backgroundColor = UIColor.clearColor;
    
    _xxpk_LocalBoxArray = [[XXGUIDriver xxpk_boxsFromLocal] mutableCopy];
    
    _xxpk_selectBox = _xxpk_LocalBoxArray.firstObject;
    
    [self xxpk_addSubViews];
    
    self.xxpk_showSelect = NO;
}

- (void)xxpk_addSubViews {
    
    _xxpk_backgroundView = [[UIView alloc] init];
    _xxpk_backgroundView.backgroundColor = UIColor.whiteColor;
    _xxpk_backgroundView.layer.cornerRadius = 2;
    [self.view addSubview:_xxpk_backgroundView];
    [self.view sendSubviewToBack:_xxpk_backgroundView];
    [_xxpk_backgroundView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view);
        make.centerX.equalTo(self.view);
        make.size.mas_equalTo([XXGUIDriver xxpk_mainContentViewSize]);
    }];
    
    
    UIView *xxpk_logoView = [XXGUIDriver xxpk_logoView];
    [self.view addSubview:xxpk_logoView];
    self.xxpk_logoView = xxpk_logoView;
    [xxpk_logoView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(XXGUIDriver.xxpk_data_ui.xxpk_float10);
        make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float60);
        make.left.equalTo(self.xxpk_backButton.mas_right);
        make.right.equalTo(self.xxpk_closeButton.mas_left);
    }];
    
    
    _xxpk_tableView = [[UITableView alloc] initWithFrame:CGRectZero style:(UITableViewStylePlain)];
    _xxpk_tableView.backgroundColor = [UIColor whiteColor];
    _xxpk_tableView.layer.masksToBounds = YES;
    _xxpk_tableView.separatorInset = UIEdgeInsetsMake(0, 0, 0, 0);
    _xxpk_tableView.separatorColor = [UIColor systemGroupedBackgroundColor];
    _xxpk_tableView.layer.borderColor = [XXGUIDriver xxpk_mainColor].CGColor;
    _xxpk_tableView.layer.borderWidth = 0.6;
    _xxpk_tableView.layer.cornerRadius = 2;
    _xxpk_tableView.rowHeight = XXGUIDriver.xxpk_data_ui.xxpk_float55;
    _xxpk_tableView.delegate = self;
    _xxpk_tableView.dataSource = self;
    [_xxpk_tableView registerClass:[XXGSelectAccountCell class] forCellReuseIdentifier:NSStringFromClass(XXGSelectAccountCell.class)];
    [self.view addSubview:_xxpk_tableView];
    [self.xxpk_tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.xxpk_logoView.mas_bottom).offset(XXGUIDriver.xxpk_data_ui.xxpk_float10);
        make.left.equalTo(self.xxpk_backgroundView).offset(XXGUIDriver.xxpk_data_ui.xxpk_float25);
        make.right.equalTo(self.xxpk_backgroundView).offset(-XXGUIDriver.xxpk_data_ui.xxpk_float25);
        make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float55);
    }];
    
    
    self.xxpk_otherButton = [XXGUIDriver xxpk_buttonNormal:XXGUIDriver.xxpk_string_ui.xxpk_otherComein];
    [self.xxpk_otherButton addTarget:self action:@selector(xxpk_ohterComeinButtonAction:) forControlEvents:(UIControlEventTouchUpInside)];
    [self.xxpk_backgroundView addSubview:self.xxpk_otherButton];
    [self.xxpk_otherButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.xxpk_backgroundView).offset(-XXGUIDriver.xxpk_data_ui.xxpk_float10);
        make.centerX.equalTo(self.view);
    }];
    
    
    self.xxpk_comeinButton = [XXGUIDriver xxpk_buttonMainColor:XXGUIDriver.xxpk_string_ui.xxpk_comein];
    [self.xxpk_comeinButton addTarget:self action:@selector(xxpk_comeinButtonDidClick:) forControlEvents:UIControlEventTouchUpInside];
    [self.xxpk_backgroundView addSubview:self.xxpk_comeinButton];
    [self.xxpk_comeinButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.xxpk_otherButton.mas_top).offset(-XXGUIDriver.xxpk_data_ui.xxpk_float17);
        make.left.right.equalTo(self.xxpk_tableView);
        make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float40);
    }];
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return _xxpk_boxArray.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    XXGSelectAccountCell *cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass(XXGSelectAccountCell.class) forIndexPath:indexPath];
    NSArray *xxpk_box = _xxpk_boxArray[indexPath.row];
    
    cell.xxpk_boxName.text = xxpk_box[0];
    
    cell.xxpk_imageView.image = [[UIImage xxpk_imageBundleOfName:xxpk_box[1]] imageWithRenderingMode:(UIImageRenderingModeAlwaysTemplate)];
    
    cell.xxpk_comeinTime.text = [NSString stringWithFormat:@"%@ %@",XXGUIDriver.xxpk_string_ui.xxpk_lastComeinTime,[self xxpk_distanceTimeWithBeforeTime:[xxpk_box[2] doubleValue]]];
    
    cell.accessoryType = self.xxpk_showSelect ? UITableViewCellAccessoryNone :  UITableViewCellAccessoryDisclosureIndicator;
    
    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    _xxpk_selectBox = _xxpk_boxArray[indexPath.row];
    self.xxpk_showSelect = !self.xxpk_showSelect;
}


- (BOOL)tableView:(UITableView *)tableView canEditRowAtIndexPath:(NSIndexPath *)indexPath {
    return self.xxpk_showSelect;
}

- (UITableViewCellEditingStyle)tableView:(UITableView *)tableView editingStyleForRowAtIndexPath:(NSIndexPath *)indexPath {
    return UITableViewCellEditingStyleDelete;
}

- (void)tableView:(UITableView *)tableView commitEditingStyle:(UITableViewCellEditingStyle)editingStyle forRowAtIndexPath:(NSIndexPath *)indexPath {
    
    if (editingStyle == UITableViewCellEditingStyleDelete) {
        
        id xxpk_box = _xxpk_boxArray[indexPath.row];
        
        [_xxpk_boxArray removeObject:xxpk_box];
        
        [_xxpk_LocalBoxArray removeObject:xxpk_box];
        
        if ([self.xxpk_delegate respondsToSelector:@selector(xxpk_selectDeleteBoxWithBoxName:completion:)]) {
            [self.xxpk_delegate xxpk_selectDeleteBoxWithBoxName:xxpk_box[0] completion:^(id object) {
                
            }];
        }
        
        if(_xxpk_LocalBoxArray.count > 0){
            
            _xxpk_boxArray = _xxpk_LocalBoxArray;
            _xxpk_selectBox = _xxpk_boxArray.firstObject;
            self.xxpk_showSelect = YES;
            
        }
    }
}


- (NSString *)tableView:(UITableView *)tableView titleForDeleteConfirmationButtonForRowAtIndexPath:(NSIndexPath *)indexPath {
    return @"Delete";
}

- (void)xxpk_touchesBlank:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    [super xxpk_touchesBlank:touches withEvent:event];
    self.xxpk_showSelect = NO;
}

- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    [super touchesBegan:touches withEvent:event];
    self.xxpk_showSelect = NO;
}


- (void)xxpk_ohterComeinButtonAction:(UIButton *)sender {
    XXGComeinViewController *xxpk_comeinVC = [XXGComeinViewController new];
    xxpk_comeinVC.xxpk_delegate = self.xxpk_delegate;
    [self.navigationController pushViewController:xxpk_comeinVC animated:NO];
}

- (void)xxpk_comeinButtonDidClick:(UIButton *)sender {
    if ([self.xxpk_delegate respondsToSelector:@selector(xxpk_selectComeinButtonDidClickWithBoxName:completion:)]) {
        [XXGLoadingView showLoadingOnWindow];
        [self.xxpk_delegate xxpk_selectComeinButtonDidClickWithBoxName:self.xxpk_selectBox[0] completion:^(id object) {
            [XXGLoadingView hideLoadingFromWindow];
        }];
    }
}


- (NSString *)xxpk_distanceTimeWithBeforeTime:(double)beTime {
    
    NSTimeInterval now = [[NSDate date] timeIntervalSince1970];
    double distanceTime = now - beTime;
    NSString * distanceStr;
    
    NSDate * beDate = [NSDate dateWithTimeIntervalSince1970:beTime];
    NSDateFormatter * df = [[NSDateFormatter alloc] init];
    [df setDateFormat:@"HH:mm"];
    NSString * timeStr = [df stringFromDate:beDate];
    
    [df setDateFormat:@"dd"];
    NSString * nowDay = [df stringFromDate:[NSDate date]];
    NSString * lastDay = [df stringFromDate:beDate];
    
    if (distanceTime < 60) {
        distanceStr = XXGUIDriver.xxpk_string_ui.xxpk_justNow;
    }else if (distanceTime < 60 * 60) {
        distanceStr = [NSString stringWithFormat:@"%ld%@",(long)distanceTime / 60, XXGUIDriver.xxpk_string_ui.xxpk_minutesAgo];
    }else if(distanceTime < 24 * 60 * 60 && [nowDay integerValue] == [lastDay integerValue]){
        distanceStr = [NSString stringWithFormat:@"%@ %@",XXGUIDriver.xxpk_string_ui.xxpk_today,timeStr];
    }else if(distanceTime < 24 * 60 * 60 * 2 && [nowDay integerValue] != [lastDay integerValue]){
        if ([nowDay integerValue] - [lastDay integerValue] == 1 || ([lastDay integerValue] - [nowDay integerValue] > 10 && [nowDay integerValue] == 1)) {
            distanceStr = [NSString stringWithFormat:@"%@ %@",XXGUIDriver.xxpk_string_ui.xxpk_yesterday,timeStr];
        }else{
            [df setDateFormat:@"MM-dd HH:mm"];
            distanceStr = [df stringFromDate:beDate];
        }
    }else if(distanceTime < 24 * 60 * 60 * 365){
        [df setDateFormat:@"MM-dd HH:mm"];
        distanceStr = [df stringFromDate:beDate];
    }else{
        [df setDateFormat:@"yyyy-MM-dd HH:mm"];
        distanceStr = [df stringFromDate:beDate];
    }
    return distanceStr;
}

@end
