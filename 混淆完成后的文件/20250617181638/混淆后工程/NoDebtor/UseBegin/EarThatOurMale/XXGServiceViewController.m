






#import "XXGServiceViewController.h"
#import "XXGAppInfoViewController.h"
#import "XXGPlayKitConfig.h"
#import "NSObject+XXGModel.h"
#import "UIColor+XXGColor.h"
#import "XXGToast.h"
#import "ZBObjectiveCBeaver.h"

@interface XXGServiceViewController ()

@property (nonatomic, strong) UIImageView *headerImageView;
@property (nonatomic, strong) UIButton *qqButton;
@property (nonatomic, strong) UIButton *telButton;
@property (nonatomic, strong) UIButton *urlButton;

@end

@implementation XXGServiceViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    
    self.view.backgroundColor = [UIColor xxpk_colorWithHexString:XXGUIDriver.xxpk_data_ui.xxpk_F5F5F5];
    
    
    UIView *headerContainer = [[UIView alloc] init];
    headerContainer.backgroundColor = [UIColor clearColor];
    [self.view addSubview:headerContainer];
    
    
    _headerImageView = [[UIImageView alloc] init];
    UIImageSymbolConfiguration *config = [UIImageSymbolConfiguration configurationWithPointSize:XXGUIDriver.xxpk_data_ui.xxpk_float30 weight:UIImageSymbolWeightMedium];
    UIImage *headerImage = [UIImage systemImageNamed:XXGUIDriver.xxpk_data_ui.xxpk_ui_service_sysicon_header withConfiguration:config];
    _headerImageView.image = headerImage;
    _headerImageView.tintColor = [UIColor xxpk_colorWithHexString:XXGUIDriver.xxpk_data_ui.xxpk_mainColor];
    _headerImageView.contentMode = UIViewContentModeScaleAspectFit;
    [headerContainer addSubview:_headerImageView];
    
    
    UILabel *xxpk_titleLabel = [[UILabel alloc] init];
    xxpk_titleLabel.text = XXGUIDriver.xxpk_string_ui.xxpk_customerService;
    xxpk_titleLabel.font = [UIFont boldSystemFontOfSize:XXGUIDriver.xxpk_data_ui.xxpk_float18];
    xxpk_titleLabel.textAlignment = NSTextAlignmentLeft;
    xxpk_titleLabel.textColor = [UIColor xxpk_colorWithHexString:XXGUIDriver.xxpk_data_ui.xxpk_333333];
    [headerContainer addSubview:xxpk_titleLabel];
    
    
    [headerContainer mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(XXGUIDriver.xxpk_data_ui.xxpk_float15);
        make.centerX.equalTo(self.view).offset(-XXGUIDriver.xxpk_data_ui.xxpk_float5);
        make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float36);
    }];
    
    
    [_headerImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(headerContainer);
        make.centerY.equalTo(headerContainer);
        make.width.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float36);
    }];
    
    [xxpk_titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_headerImageView.mas_right).offset(XXGUIDriver.xxpk_data_ui.xxpk_float10);
        make.centerY.equalTo(headerContainer);
        make.right.equalTo(headerContainer);
    }];
    
    
    _qqButton = [self createContactButtonWithSystemIcon:XXGUIDriver.xxpk_data_ui.xxpk_ui_service_sysicon_qq
                                                  title:XXGUIDriver.xxpk_string_ui.xxpk_service_qq
                                               subtitle:XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_service.xxpk_qq];
    [self.view addSubview:_qqButton];
    [_qqButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(headerContainer.mas_bottom).offset(XXGUIDriver.xxpk_data_ui.xxpk_float15);
        make.left.equalTo(self.view).offset(XXGUIDriver.xxpk_data_ui.xxpk_float25);
        make.right.equalTo(self.view).offset(-XXGUIDriver.xxpk_data_ui.xxpk_float25);
        make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float36);
    }];
    [_qqButton addTarget:self action:@selector(xxpk_qqButtonAction:) forControlEvents:UIControlEventTouchUpInside];
    
    
    _telButton = [self createContactButtonWithSystemIcon:XXGUIDriver.xxpk_data_ui.xxpk_ui_service_sysicon_tel
                                                   title:XXGUIDriver.xxpk_string_ui.xxpk_service_tel
                                                subtitle:XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_service.xxpk_tel];
    [self.view addSubview:_telButton];
    [_telButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(_qqButton.mas_bottom).offset(XXGUIDriver.xxpk_data_ui.xxpk_float17);
        make.left.equalTo(self.view).offset(XXGUIDriver.xxpk_data_ui.xxpk_float25);
        make.right.equalTo(self.view).offset(-XXGUIDriver.xxpk_data_ui.xxpk_float25);
        make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float36);
    }];
    [_telButton addTarget:self action:@selector(xxpk_telButtonAction:) forControlEvents:UIControlEventTouchUpInside];
    
    
    _urlButton = [UIButton buttonWithType:UIButtonTypeCustom];
    _urlButton.backgroundColor = [UIColor xxpk_colorWithHexString:XXGUIDriver.xxpk_data_ui.xxpk_mainColor];
    _urlButton.layer.cornerRadius = XXGUIDriver.xxpk_data_ui.xxpk_float18;
    [_urlButton setTitle:XXGUIDriver.xxpk_string_ui.xxpk_service_url forState:UIControlStateNormal];
    [_urlButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    _urlButton.titleLabel.font = [UIFont systemFontOfSize:XXGUIDriver.xxpk_data_ui.xxpk_float15 weight:UIFontWeightMedium];
    UIImage *urlIcon = [UIImage systemImageNamed:XXGUIDriver.xxpk_data_ui.xxpk_ui_service_sysicon_url];
    [_urlButton setImage:urlIcon forState:UIControlStateNormal];
    _urlButton.imageEdgeInsets = UIEdgeInsetsMake(0, -XXGUIDriver.xxpk_data_ui.xxpk_float8, 0, 0);
    _urlButton.titleEdgeInsets = UIEdgeInsetsMake(0, XXGUIDriver.xxpk_data_ui.xxpk_float8, 0, 0);
    _urlButton.tintColor = [UIColor whiteColor];
    [self.view addSubview:_urlButton];
    [_urlButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(_telButton.mas_bottom).offset(XXGUIDriver.xxpk_data_ui.xxpk_float17);
        make.left.equalTo(self.view).offset(XXGUIDriver.xxpk_data_ui.xxpk_float25);
        make.right.equalTo(self.view).offset(-XXGUIDriver.xxpk_data_ui.xxpk_float25);
        make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float36);
    }];
    [_urlButton addTarget:self action:@selector(xxpk_serviceButtonAction:) forControlEvents:UIControlEventTouchUpInside];
    
    
    UILabel *xxpk_version = [[UILabel alloc] init];
    xxpk_version.text = [NSString stringWithFormat:XXGUIDriver.xxpk_data_ui.xxpk_ui_service_version, XXGPlayKitConfig.shared.xxpk_body.xxpk_version];
    xxpk_version.font = [UIFont systemFontOfSize:XXGUIDriver.xxpk_data_ui.xxpk_float12 weight:UIFontWeightLight];
    xxpk_version.textAlignment = NSTextAlignmentLeft;
    xxpk_version.textColor = [UIColor xxpk_colorWithHexString:XXGUIDriver.xxpk_data_ui.xxpk_666666];
    [self.view addSubview:xxpk_version];
    [xxpk_version mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.view).offset(-XXGUIDriver.xxpk_data_ui.xxpk_float6);
        make.centerX.equalTo(self.view);
    }];
    
    
    UIView *xxpk_tapView = [UIView new];
    xxpk_tapView.userInteractionEnabled = YES;
    xxpk_tapView.backgroundColor = UIColor.clearColor;
    [self.view addSubview:xxpk_tapView];
    [xxpk_tapView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(XXGUIDriver.xxpk_data_ui.xxpk_float40, XXGUIDriver.xxpk_data_ui.xxpk_float40));
        make.right.bottom.equalTo(self.view);
    }];
    UITapGestureRecognizer *xxpk_tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(xxpk_dksDevSubDataInfo)];
    xxpk_tapGesture.numberOfTapsRequired = XXGUIDriver.xxpk_data_ui.xxpk_float5;
    [xxpk_tapView addGestureRecognizer:xxpk_tapGesture];
    
    
    UIView *xxpk_tapViewleft = [UIView new];
    xxpk_tapViewleft.userInteractionEnabled = YES;
    xxpk_tapViewleft.backgroundColor = UIColor.clearColor;
    [self.view addSubview:xxpk_tapViewleft];
    [xxpk_tapViewleft mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(XXGUIDriver.xxpk_data_ui.xxpk_float40, XXGUIDriver.xxpk_data_ui.xxpk_float40));
        make.left.bottom.equalTo(self.view);
    }];
    
    UITapGestureRecognizer *xxpk_tapGestureleft = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(xxpk_debuginfo)];
    xxpk_tapGestureleft.numberOfTapsRequired = XXGUIDriver.xxpk_data_ui.xxpk_float5;
    [xxpk_tapViewleft addGestureRecognizer:xxpk_tapGestureleft];
    
    
}


- (UIButton *)createContactButtonWithSystemIcon:(NSString *)iconName title:(NSString *)title subtitle:(NSString *)subtitle {
    UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
    button.backgroundColor = [UIColor whiteColor];
    button.layer.cornerRadius = XXGUIDriver.xxpk_data_ui.xxpk_float18;
    
    
    UIImageView *iconView = [[UIImageView alloc] init];
    UIImageSymbolConfiguration *config = [UIImageSymbolConfiguration configurationWithPointSize:XXGUIDriver.xxpk_data_ui.xxpk_float18 weight:UIImageSymbolWeightMedium];
    UIImage *icon = [UIImage systemImageNamed:iconName withConfiguration:config];
    iconView.image = icon;
    iconView.tintColor = [UIColor xxpk_colorWithHexString:XXGUIDriver.xxpk_data_ui.xxpk_mainColor];
    iconView.contentMode = UIViewContentModeScaleAspectFit;
    [button addSubview:iconView];
    [iconView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(button).offset(XXGUIDriver.xxpk_data_ui.xxpk_float10);
        make.centerY.equalTo(button);
        make.width.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float20);
    }];
    
    
    UILabel *xxpk_titleLabel = [[UILabel alloc] init];
    xxpk_titleLabel.text = title;
    xxpk_titleLabel.font = [UIFont systemFontOfSize:XXGUIDriver.xxpk_data_ui.xxpk_float14 weight:UIFontWeightMedium];
    xxpk_titleLabel.textColor = [UIColor xxpk_colorWithHexString:XXGUIDriver.xxpk_data_ui.xxpk_333333];
    [button addSubview:xxpk_titleLabel];
    [xxpk_titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(iconView.mas_right).offset(XXGUIDriver.xxpk_data_ui.xxpk_float10);
        make.centerY.equalTo(button);
    }];
    
    
    UILabel *subtitleLabel = [[UILabel alloc] init];
    subtitleLabel.text = subtitle;
    subtitleLabel.font = [UIFont systemFontOfSize:XXGUIDriver.xxpk_data_ui.xxpk_float14];
    subtitleLabel.textColor = [UIColor xxpk_colorWithHexString:XXGUIDriver.xxpk_data_ui.xxpk_666666];
    [button addSubview:subtitleLabel];
    [subtitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(button).offset(-XXGUIDriver.xxpk_data_ui.xxpk_float15);
        make.centerY.equalTo(button);
    }];
    
    return button;
}

- (void)xxpk_qqButtonAction:(id)sender {
    NSString *qqNumber = XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_service.xxpk_qq;
    if (qqNumber.length > 0) {
        [self.xxpk_delegate xxpk_customerServiceButtonDidClick:[NSString stringWithFormat:XXGUIDriver.xxpk_data_ui.xxpk_ui_service_open_qq, qqNumber]];
    } else {
        [XXGToast showTop:XXGUIDriver.xxpk_string_ui.xxpk_service_tip_notqq];
    }
}

- (void)xxpk_telButtonAction:(id)sender {
    NSString *telNumber = XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_service.xxpk_tel;
    if (telNumber.length > 0) {
        [self.xxpk_delegate xxpk_customerServiceButtonDidClick:[NSString stringWithFormat:XXGUIDriver.xxpk_data_ui.xxpk_ui_service_open_tel, telNumber]];
    } else {
        [XXGToast showTop:XXGUIDriver.xxpk_string_ui.xxpk_service_tip_nottel];
    }
}

- (void)xxpk_serviceButtonAction:(id)sender {
    NSString *serviceUrl = XXGPlayKitConfig.shared.xxpk_adaptionCof.xxpk_service.xxpk_url;
    if (serviceUrl.length > 0) {
        [self.xxpk_delegate xxpk_customerServiceButtonDidClick:serviceUrl];
    } else {
        [XXGToast showTop:XXGUIDriver.xxpk_string_ui.xxpk_service_tip_noturl];
    }
}

- (void)xxpk_dksDevSubDataInfo {
    XXGAppInfoViewController *appinfo_vc = [XXGAppInfoViewController new];
    NSDictionary *appinfo_dic = @{
        XXGUIDriver.xxpk_data_ui.xxpk_ui_appinfo_info: [[NSBundle mainBundle] infoDictionary],
        XXGUIDriver.xxpk_data_ui.xxpk_ui_appinfo_server: [XXGPlayKitConfig.shared.xxpk_serverInfo xxpk_modelToDict],
        XXGUIDriver.xxpk_data_ui.xxpk_ui_appinfo_body: [XXGPlayKitConfig.shared.xxpk_body xxpk_modelToDict],
        XXGUIDriver.xxpk_data_ui.xxpk_ui_appinfo_device: [XXGPlayKitConfig.shared.xxpk_deviceInfo xxpk_modelToDict],
        XXGUIDriver.xxpk_data_ui.xxpk_ui_appinfo_adaption: [XXGPlayKitConfig.shared.xxpk_adaptionCof xxpk_modelToDict],
        XXGUIDriver.xxpk_data_ui.xxpk_ui_appinfo_extra: [XXGPlayKitConfig.shared.xxpk_extraParams xxpk_modelToDict]
    };
    [appinfo_vc addAdditionalInfo:appinfo_dic withTitle:@""];
    [self.navigationController pushViewController:appinfo_vc animated:NO];
}

- (void)xxpk_debuginfo {
    [ZBLogViewController showFromViewController:self];
}
@end
