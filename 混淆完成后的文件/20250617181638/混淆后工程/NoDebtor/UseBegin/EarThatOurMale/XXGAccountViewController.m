






#import "XXGAccountViewController.h"
#import "XXGRegistViewController.h"
#import "XXGForgetViewController.h"

@interface XXGAccountViewController ()<XXGForgetDelegate>

@property (nonatomic, strong) UITextField *xxpk_accountTextField;
@property (nonatomic, strong) UITextField *xxpk_passwordTextField;
@end

@implementation XXGAccountViewController


- (void)viewDidLoad {
    [super viewDidLoad];
    
    
    self.xxpk_accountTextField = [XXGUIDriver xxpk_textFieldOfAccount];
    [self.view addSubview:self.xxpk_accountTextField];
    [self.xxpk_accountTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float45);
        make.left.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float25);
        make.right.mas_equalTo(-XXGUIDriver.xxpk_data_ui.xxpk_float25);
        make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float38);
    }];
    
    
    self.xxpk_passwordTextField = [XXGUIDriver xxpk_textFieldOfPassword:NO];
    [self.view addSubview:self.xxpk_passwordTextField];
    [self.xxpk_passwordTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.xxpk_accountTextField.mas_bottom).offset(XXGUIDriver.xxpk_data_ui.xxpk_float12);
        make.left.right.equalTo(self.xxpk_accountTextField);
        make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float38);
    }];
    UIButton *button0 = self.xxpk_passwordTextField.rightView.subviews.firstObject;
    [button0 addTarget:self action:@selector(__xxpk_textFieldButtonClickHandler:) forControlEvents:(UIControlEventTouchUpInside)];
    
    
    UIButton *button = [XXGUIDriver xxpk_buttonMainColor:XXGUIDriver.xxpk_string_ui.xxpk_comein];
    [button addTarget:self action:@selector(xxpk_accountComeinRequestButtonAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:button];
    [button mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.xxpk_passwordTextField.mas_bottom).offset(XXGUIDriver.xxpk_data_ui.xxpk_float12);
        make.left.right.equalTo(self.xxpk_passwordTextField);
        make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float40);
    }];
    
    
    if (![XXGUIDriver xxpk_isComeinOnly]) {
        UIButton *button1 = [XXGUIDriver xxpk_buttonMainColor:[NSString stringWithFormat:@" %@ ",XXGUIDriver.xxpk_string_ui.xxpk_register]];
        [button1 addTarget:self action:@selector(xxpk_registerButtonAction:) forControlEvents:UIControlEventTouchUpInside];
        [self.view addSubview:button1];
        [button1 mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(button);
            make.top.equalTo(button.mas_bottom).offset(XXGUIDriver.xxpk_data_ui.xxpk_float17);
            make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float22);
        }];
    }
    
    
    UIButton *button2 = [XXGUIDriver xxpk_buttonNormal:XXGUIDriver.xxpk_string_ui.xxpk_forgetKey];
    [button2 addTarget:self action:@selector(xxpk_forgetButtonAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:button2];
    [button2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(button);
        make.top.equalTo(button.mas_bottom).offset(XXGUIDriver.xxpk_data_ui.xxpk_float17);
        make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float22);
    }];
}

- (void)__xxpk_textFieldButtonClickHandler:(UIButton *)sender {
    sender.selected = !sender.isSelected;
    self.xxpk_passwordTextField.secureTextEntry = !self.xxpk_passwordTextField.isSecureTextEntry;
}

- (void)xxpk_accountComeinRequestButtonAction:(UIButton *)sender {
    if (self.xxpk_accountTextField.text.length < XXGUIDriver.xxpk_data_ui.xxpk_float6) {
        [XXGAlertView xxpk_showAlertWithTitle:XXGUIDriver.xxpk_string_ui.xxpk_tips message:XXGUIDriver.xxpk_string_ui.xxpk_account_verified completion:nil];
        return;
    }
    if (self.xxpk_passwordTextField.text.length < XXGUIDriver.xxpk_data_ui.xxpk_float6) {
        [XXGAlertView xxpk_showAlertWithTitle:XXGUIDriver.xxpk_string_ui.xxpk_tips message:XXGUIDriver.xxpk_string_ui.xxpk_boxkey_verified completion:nil];
        return;
    }
    if ([self.xxpk_delegate respondsToSelector:@selector(xxpk_accountComeinButtonDidClickWithBoxName:boxKey:completion:)]) {
        [XXGLoadingView showLoadingOnWindow];
        [self.xxpk_delegate xxpk_accountComeinButtonDidClickWithBoxName:self.xxpk_accountTextField.text boxKey:self.xxpk_passwordTextField.text completion:^(id object) {
            [XXGLoadingView hideLoadingFromWindow];
        }];
    }
}

- (void)xxpk_registerButtonAction:(UIButton *)sender {
    XXGRegistViewController *xxpk_vc = [XXGRegistViewController new];
    xxpk_vc.xxpk_delegate = self.xxpk_delegate;
    [self.navigationController pushViewController:xxpk_vc animated:NO];
}

- (void)xxpk_forgetButtonAction:(UIButton *)sender {
    XXGForgetViewController *xxpk_vc = [XXGForgetViewController new];
    xxpk_vc.xxpk_delegate = self.xxpk_delegate;
    xxpk_vc.xxpk_forgetDelegate = self;
    [self.navigationController pushViewController:xxpk_vc animated:NO];
    
}

- (void)xxpk_foregetFinishWithName:(NSString *)xxpk_forgetName xxpk_forgetPassword:(NSString *)xxpk_forgetPassword {
    self.xxpk_accountTextField.text = xxpk_forgetName;
    self.xxpk_passwordTextField.text = xxpk_forgetPassword;
    UIButton *button0 = self.xxpk_passwordTextField.rightView.subviews.firstObject;
    button0.selected = YES;
    self.xxpk_passwordTextField.secureTextEntry = NO;
}

@end
