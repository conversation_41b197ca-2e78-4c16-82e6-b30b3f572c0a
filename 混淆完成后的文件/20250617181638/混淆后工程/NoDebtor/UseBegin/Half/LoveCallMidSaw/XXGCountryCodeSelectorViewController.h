






#import "XXGBaseViewController.h"
#import "XXGCountry.h"
#import "XXGLocaleString.h"

NS_ASSUME_NONNULL_BEGIN

@protocol XXGCountryCodeSelectorDelegate <NSObject>
- (void)xxpk_countryCodeSelectorDidSelectCountry:(XXGCountry *)country;
@end

@interface XXGCountryCodeSelectorViewController : XXGBaseViewController

@property (nonatomic, weak) id<XXGCountryCodeSelectorDelegate> xxpk_codeDelegate;

@end

NS_ASSUME_NONNULL_END
