






#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface XXGLoadingView : UIView


- (void)startAnimating;

- (void)stopAnimating;

- (void)setLoadingText:(NSString *)text;



+ (void)showLoadingOnWindow;
+ (void)showLoadingOnWindowWithText:(NSString *)text;

+ (void)hideLoadingFromWindow;

+ (XXGLoadingView *)showLoadingOnView:(UIView *)view;

+ (XXGLoadingView *)showLoadingOnView:(UIView *)view withText:(NSString *_Nullable)text;

+ (void)hideLoadingFromView:(UIView *)view;

@end

NS_ASSUME_NONNULL_END
