






#import "XXGSendCodeButton.h"
#import "XXGUIDriver.h"
#import "UIImage+XXGImage.h"

@interface XXGSendCodeButton ()


@property (nonatomic, strong) NSTimer *xxpk_countdownTimer;

@property (nonatomic, assign) NSInteger xxpk_remainingSeconds;

@property (nonatomic, copy) NSString *xxpk_originalTitle;

@end

@implementation XXGSendCodeButton

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self xxpk_commonInit];
    }
    return self;
}

- (instancetype)initWithCoder:(NSCoder *)coder {
    self = [super initWithCoder:coder];
    if (self) {
        [self xxpk_commonInit];
    }
    return self;
}


- (void)xxpk_commonInit {
    
    self.xxpk_countdownDuration = 60;
    self.xxpk_originalTitle = XXGUIDriver.xxpk_string_ui.xxpk_sendVerificationCode;
    [self setTitle:self.xxpk_originalTitle forState:UIControlStateNormal];
    [self setBackgroundImage:[UIImage xxpk_imageWithColor:XXGUIDriver.xxpk_mainColor] forState:UIControlStateNormal];
    [self setBackgroundImage:[UIImage xxpk_imageWithColor:[[UIColor lightGrayColor] colorWithAlphaComponent:0.5f]] forState:UIControlStateHighlighted];
    self.titleLabel.font = [UIFont systemFontOfSize:16];
    self.layer.cornerRadius = 2.f;
    self.layer.masksToBounds = YES;
    
    self.contentEdgeInsets = UIEdgeInsetsMake(0, 5, 0, 5);
    
    [self sizeToFit];
    
    
    [self addTarget:self action:@selector(xxpk_buttonClicked) forControlEvents:UIControlEventTouchUpInside];
}


- (void)xxpk_buttonClicked {
    [self xxpk_startCountdown];
    if (self.xxpk_sendCodeAction) {
        self.xxpk_sendCodeAction();
    }
}


- (void)xxpk_startCountdown {
    self.enabled = NO;
    self.xxpk_remainingSeconds = self.xxpk_countdownDuration;
    [self xxpk_updateButtonTitle];
    
    
    self.xxpk_countdownTimer = [NSTimer scheduledTimerWithTimeInterval:1.0
                                                                 target:self
                                                               selector:@selector(xxpk_timerFired:)
                                                               userInfo:nil
                                                                repeats:YES];
}


- (void)xxpk_timerFired:(NSTimer *)timer {
    self.xxpk_remainingSeconds--;
    if (self.xxpk_remainingSeconds <= 0) {
        [self xxpk_stopCountdown];
    } else {
        [self xxpk_updateButtonTitle];
    }
}


- (void)xxpk_updateButtonTitle {
    NSString *title = [NSString stringWithFormat:@"%@(%ld)",XXGUIDriver.xxpk_string_ui.xxpk_reSendVerificationCode, (long)self.xxpk_remainingSeconds];
    [self setTitle:title forState:UIControlStateDisabled];
}


- (void)xxpk_stopCountdown {
    [self.xxpk_countdownTimer invalidate];
    self.xxpk_countdownTimer = nil;
    self.enabled = YES;
    [self setTitle:self.xxpk_originalTitle forState:UIControlStateNormal];
}

- (void)dealloc {
    
    [self.xxpk_countdownTimer invalidate];
    self.xxpk_countdownTimer = nil;
}

@end
