






#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, XXGToastPosition) {
    XXGToastPositionTop,
    XXGToastPositionCenter,
    XXGToastPositionBottom
};

@interface XXGToast : UIView


+ (void)show:(NSString *)message
    duration:(NSTimeInterval)duration
    position:(XXGToastPosition)position;


+ (void)showTop:(NSString *)message;
+ (void)showCenter:(NSString *)message;
+ (void)showBottom:(NSString *)message;


+ (void)setDefaultBackgroundColor:(UIColor *)color;
+ (void)setDefaultTextColor:(UIColor *)color;
+ (void)setDefaultFont:(UIFont *)font;
+ (void)setDefaultCornerRadius:(CGFloat)radius;

@end
NS_ASSUME_NONNULL_END
