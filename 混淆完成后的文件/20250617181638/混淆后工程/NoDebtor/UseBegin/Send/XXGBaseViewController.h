






#import "XXGOrientationViewController.h"
#import <UIKit/UIKit.h>
#import "XXGUIkitProtocol.h"
#import "XXGUIDriver.h"
#import "XXGWindowManager.h"
#import "XXGAlertView.h"
#import "XXGLoadingView.h"

#import "UIImage+XXGImage.h"
#import "Masonry.h"

#define weakify(obj) __weak typeof(obj) weak##obj = obj;
#define strongify(obj) __strong typeof(obj) obj = weak##obj;

NS_ASSUME_NONNULL_BEGIN

@interface XXGBaseViewController : XXGOrientationViewController

@property (nonatomic, weak) id <XXGUIkitDelegate>xxpk_delegate;
@property (nonatomic, strong) id xxpk_object;
@property (nonatomic, copy) void(^xxpk_complate)(id xxpk_object);
@property (nonatomic, strong) UIButton *xxpk_backButton;
@property (nonatomic, strong) UIButton *xxpk_closeButton;

- (void)xxpk_touchesBlank:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event;

- (void)xxpk_backButtonAction:(UIButton *_Nullable)sender;

- (void)xxpk_closeButtonAction:(UIButton *_Nullable)sender;
@end

NS_ASSUME_NONNULL_END
