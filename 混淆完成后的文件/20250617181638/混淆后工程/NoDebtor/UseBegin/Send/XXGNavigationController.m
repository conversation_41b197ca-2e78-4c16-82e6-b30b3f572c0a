






#import "XXGNavigationController.h"
#import "XXGBaseViewController.h"

@interface XXGNavigationController ()

@end

@implementation XXGNavigationController


- (BOOL)shouldAutorotate {
    return self.topViewController.shouldAutorotate;
}

- (UIInterfaceOrientationMask)supportedInterfaceOrientations {
    return self.topViewController.supportedInterfaceOrientations;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.interactivePopGestureRecognizer.enabled = NO;
    [self setNavigationBarHidden:YES];
    self.view.backgroundColor = [UIColor.blackColor colorWithAlphaComponent:.3];
}

- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    
    CGPoint point = [[touches anyObject] locationInView:self.view];
    
    UIView *topView = self.topViewController.view;
    
    point = [topView.layer convertPoint:point fromLayer:self.view.layer];
    
    XXGBaseViewController *vc = (XXGBaseViewController *)self.topViewController;
    if (![topView.layer containsPoint:point]) {
        [vc xxpk_touchesBlank:touches withEvent:event];
    }else{  
        [super touchesBegan:touches withEvent:event];
    }
}

- (void)pushViewController:(XXGBaseViewController *)viewController animated:(BOOL)animated
{
    if (self.childViewControllers.count > 0) {
        viewController.xxpk_backButton.hidden = NO;
    }else {
        viewController.xxpk_backButton.hidden = YES;
    }
    [super pushViewController:viewController animated:animated];
    
}
- (void)dealloc {
    
}
@end
