






#import <Foundation/Foundation.h>

#import "ZBLog.h"


NS_ASSUME_NONNULL_BEGIN

@interface ZBLevelString : NSObject
@property (nonatomic, copy) NSString *zb_verbose;
@property (nonatomic, copy) NSString *zb_debug;
@property (nonatomic, copy) NSString *zb_info;
@property (nonatomic, copy) NSString *zb_warning;
@property (nonatomic, copy) NSString *zb_error;
@property (nonatomic, copy) NSString *zb_all;
@end

@interface ZBLevelColor : NSObject
@property (nonatomic, copy) NSString *zb_verbose;
@property (nonatomic, copy) NSString *zb_debug;
@property (nonatomic, copy) NSString *zb_info;
@property (nonatomic, copy) NSString *zb_warning;
@property (nonatomic, copy) NSString *zb_error;
@property (nonatomic, copy) NSString *zb_all;
@end

@interface ZBBaseDestination : NSObject



@property (nonatomic, strong, readonly) dispatch_queue_t zb_queue;


@property (nonatomic, assign) ZBLogLevel zb_minLevel;


@property (nonatomic, assign) BOOL zb_asynchronously;


@property (nonatomic, strong) ZBLevelString *zb_levelString;



@property (nonatomic, strong) ZBLevelColor *zb_levelColor;



- (NSString *)formatDate:(NSString *)dateFormat timeZone:(nullable NSString *)timeZone;


- (NSString *)colorForLevel:(ZBLogLevel)level;




- (NSString *)zb_send:(ZBLogLevel)zb_level
               zb_msg:(NSString *)zb_msg
            zb_thread:(NSString *)zb_thread
              zb_file:(NSString *)zb_file
          zb_function:(NSString *)zb_function
              zb_line:(NSUInteger)zb_line
           zb_context:(id)zb_context;



- (BOOL)zb_shouldLevelBeLogged:(ZBLogLevel)zb_level
                       zb_path:(NSString *)zb_path
                   zb_function:(NSString *)zb_function
                    zb_message:(NSString *)zb_message;
@end

NS_ASSUME_NONNULL_END
