






#import "ZBFileDestination.h"
#import "XXGPlayKitConfig.h"
#import "NSData+SunHope.h"

@interface ZBFileDestination() {
    NSURL *_zb_logFileURL;
    BOOL _zb_syncAfterEachWrite;
    NSInteger _maxDays;
    NSDateFormatter *_dateFormatter;
    BOOL _encryptionEnabled;
}

@end

@implementation ZBFileDestination

- (instancetype)init
{
    self = [super init];
    if (self) {
        _zb_syncAfterEachWrite = NO;
        _maxDays = 7;
        _encryptionEnabled = NO;

        _dateFormatter = [[NSDateFormatter alloc] init];
        _dateFormatter.dateFormat = __data_core.xxpk_tools_logger_file_date_format;

        if (!_zb_logFileURL) {
            NSURL *baseURL = [[NSFileManager defaultManager] URLsForDirectory:NSCachesDirectory inDomains:NSUserDomainMask].firstObject;
            _zb_logFileURL = [baseURL URLByAppendingPathComponent:NSStringFromClass(self.class) isDirectory:YES];
        }
    }
    return self;
}


- (instancetype)initWithLogFileURL:(NSURL *)zb_url
{
    self = [super init];
    if (self) {
        self.zb_logFileURL = zb_url;
    }
    return self;
}


- (NSString *)zb_send:(ZBLogLevel)zb_level zb_msg:(NSString *)zb_msg zb_thread:(NSString *)zb_thread zb_file:(NSString *)zb_file zb_function:(NSString *)zb_function zb_line:(NSUInteger)zb_line zb_context:(id)zb_context {

    NSString *time = [self formatDate:__data_core.xxpk_tools_logger_formatter timeZone:nil];

    NSString *color = [self colorForLevel:zb_level];

    NSString *line = [NSString stringWithFormat:@"%lu", (unsigned long)zb_line];

    NSString *formattedString = [NSString stringWithFormat:__data_core.xxpk_tools_logger_format_file,color,time,zb_function,line,zb_msg];

    if (![formattedString isEqualToString:@""]) {
        NSURL *currentFile = [self currentLogFileURL];
        [self zb_saveToFile:formattedString fileURL:currentFile];
    }

    return formattedString;
}



- (BOOL)zb_saveToFile:(NSString *)zb_str {
    return [self zb_saveToFile:zb_str fileURL:_zb_logFileURL];
}

- (BOOL)zb_saveToFile:(NSString *)zb_str fileURL:(NSURL *)fileURL {
    if (!fileURL) {
        return NO;
    }

    NSString *line = zb_str;

    
    if (_encryptionEnabled) {
        NSData *originalData = [line dataUsingEncoding:NSUTF8StringEncoding];
        if (!originalData) {
            return NO;
        }

        NSData *encryptedData = [originalData ___xxpk_encryptWithRandomIV];
        if (!encryptedData) {
            
            return NO;
        }

        
        line = [encryptedData base64EncodedStringWithOptions:0];
    }

    
    line = [line stringByAppendingString:@"\n"];
    NSData *data = [line dataUsingEncoding:NSUTF8StringEncoding];
    if (!data) {
        return NO;
    }

    return [self zb_write:data zb_to:fileURL];
}

- (BOOL)zb_write:(NSData *)zb_data zb_to:(NSURL *)zb_url {
    __block BOOL zb_success = NO;
    NSFileCoordinator *zb_coordinator = [[NSFileCoordinator alloc] initWithFilePresenter:nil];
    NSError *zb_error = nil;
    [zb_coordinator coordinateWritingItemAtURL:zb_url options:0 error:&zb_error byAccessor:^(NSURL * _Nonnull zb_newURL) {

        NSError *zb_error = nil;

        if (![[NSFileManager defaultManager] fileExistsAtPath:zb_url.path]) {

            NSURL *zb_directoryURL = zb_url.URLByDeletingLastPathComponent;
            if (![[NSFileManager defaultManager] fileExistsAtPath:zb_directoryURL.path]) {
                [[NSFileManager defaultManager] createDirectoryAtURL:zb_directoryURL withIntermediateDirectories:YES attributes:nil error:&zb_error];
            }

            [[NSFileManager defaultManager] createFileAtPath:zb_url.path contents:nil attributes:nil];
        }

        NSFileHandle *zb_fileHandle = [NSFileHandle fileHandleForWritingToURL:zb_url error:&zb_error];
        [zb_fileHandle seekToEndOfFile];
        [zb_fileHandle writeData:zb_data];
        if (_zb_syncAfterEachWrite) {
            [zb_fileHandle synchronizeFile];
        }
        [zb_fileHandle closeFile];

        if (zb_error) {
            
        }else {
            zb_success = YES;
        }

    }];

    if (zb_error) {
        
    }

    return zb_success;
}

- (NSURL *)zb_logFileURL {
    return _zb_logFileURL;
}

- (void)setZb_logFileURL:(NSURL *)zb_logFileURL {
    _zb_logFileURL = zb_logFileURL;
}

- (BOOL)zb_syncAfterEachWrite {
    return _zb_syncAfterEachWrite;
}

- (void)setZb_syncAfterEachWrite:(BOOL)zb_syncAfterEachWrite {
    _zb_syncAfterEachWrite = zb_syncAfterEachWrite;
}




- (NSInteger)maxDays {
    return _maxDays;
}

- (void)setMaxDays:(NSInteger)maxDays {
    _maxDays = maxDays;
}

- (BOOL)encryptionEnabled {
    return _encryptionEnabled;
}

- (void)setEncryptionEnabled:(BOOL)encryptionEnabled {
    _encryptionEnabled = encryptionEnabled;
}



- (NSURL *)currentLogFileURL {
    NSString *dateString = [_dateFormatter stringFromDate:[NSDate date]];
    return [_zb_logFileURL URLByAppendingPathComponent:dateString];
}

- (NSArray<NSURL *> *)allLogFiles {
    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSError *error;

    if (![fileManager fileExistsAtPath:_zb_logFileURL.path]) {
        return @[];
    }

    NSArray *logFiles = [fileManager contentsOfDirectoryAtURL:_zb_logFileURL
                                includingPropertiesForKeys:@[NSURLCreationDateKey]
                                                   options:NSDirectoryEnumerationSkipsHiddenFiles
                                                     error:&error];
    if (error) {
        
        return @[];
    }

    return [logFiles sortedArrayUsingComparator:^NSComparisonResult(NSURL *url1, NSURL *url2) {
        NSDate *date1, *date2;
        [url1 getResourceValue:&date1 forKey:NSURLCreationDateKey error:nil];
        [url2 getResourceValue:&date2 forKey:NSURLCreationDateKey error:nil];
        return [date2 compare:date1]; 
    }];
}

- (NSString *)readLogFile:(NSURL *)fileURL {
    NSError *error;

    
    NSString *fileContent = [NSString stringWithContentsOfURL:fileURL encoding:NSUTF8StringEncoding error:&error];
    if (error || !fileContent) {
        
        return @"";
    }

    
    if (_encryptionEnabled) {
        NSMutableString *allContent = [NSMutableString string];

        
        NSArray *lines = [fileContent componentsSeparatedByString:@"\n"];

        for (NSString *line in lines) {
            
            if (line.length == 0) {
                continue;
            }

            
            NSData *encryptedData = [[NSData alloc] initWithBase64EncodedString:line options:0];
            if (!encryptedData) {
                
                continue;
            }

            
            NSData *decryptedData = [encryptedData __xxpk_decryptDataWithRandomIV];
            if (!decryptedData) {
                
                continue;
            }

            
            NSString *decryptedLine = [[NSString alloc] initWithData:decryptedData encoding:NSUTF8StringEncoding];
            if (decryptedLine) {
                [allContent appendString:decryptedLine];
                [allContent appendString:@"\n"];
            } else {
                
            }
        }

        return allContent;
    } else {
        
        return fileContent;
    }
}

- (NSString *)readAllLogs {
    NSArray *logFiles = [self allLogFiles];
    NSMutableString *allContent = [NSMutableString string];

    for (NSURL *fileURL in logFiles) {
        NSString *content = [self readLogFile:fileURL];
        if (content.length > 0) {
            [allContent appendFormat:__data_core.xxpk_tools_logger_file_separator, fileURL.lastPathComponent];
            [allContent appendString:content];
            [allContent appendString:@"\n"];
        }
    }

    return allContent;
}

- (NSString *)readAllLogsRaw {
    NSArray *logFiles = [self allLogFiles];
    NSMutableString *allContent = [NSMutableString string];

    for (NSURL *fileURL in logFiles) {
        
        NSError *error;
        NSString *fileContent = [NSString stringWithContentsOfURL:fileURL encoding:NSUTF8StringEncoding error:&error];
        if (error || !fileContent) {
            
            continue;
        }

        if (fileContent.length > 0) {
            [allContent appendFormat:__data_core.xxpk_tools_logger_file_separator, fileURL.lastPathComponent];
            [allContent appendString:fileContent];
            [allContent appendString:@"\n"];
        }
    }

    return allContent;
}

- (NSString *)readLogsForDate:(NSDate *)date {
    if (!date) {
        return @"";
    }

    NSString *dateString = [_dateFormatter stringFromDate:date];
    NSURL *fileURL = [_zb_logFileURL URLByAppendingPathComponent:dateString];

    return [self readLogFile:fileURL];
}

- (NSArray<NSDate *> *)allLogDates {
    NSMutableArray *dates = [NSMutableArray array];
    NSArray *logFiles = [self allLogFiles];

    for (NSURL *fileURL in logFiles) {
        NSString *fileName = fileURL.lastPathComponent;
        
        NSDate *date = [_dateFormatter dateFromString:fileName];
        if (date) {
            [dates addObject:date];
        }
    }

    
    [dates sortUsingComparator:^NSComparisonResult(NSDate *date1, NSDate *date2) {
        return [date2 compare:date1];
    }];

    return dates;
}

- (void)cleanupOldLogs {
    if (_maxDays <= 0) return;

    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSArray *logFiles = [self allLogFiles];
    NSDate *cutoffDate = [NSDate dateWithTimeIntervalSinceNow:-_maxDays * 24 * 60 * 60];

    for (NSURL *fileURL in logFiles) {
        NSDate *creationDate;
        [fileURL getResourceValue:&creationDate forKey:NSURLCreationDateKey error:nil];

        if (creationDate && [creationDate compare:cutoffDate] == NSOrderedAscending) {
            NSError *error;
            [fileManager removeItemAtURL:fileURL error:&error];
            if (error) {
                
            }
        }
    }
}

@end
