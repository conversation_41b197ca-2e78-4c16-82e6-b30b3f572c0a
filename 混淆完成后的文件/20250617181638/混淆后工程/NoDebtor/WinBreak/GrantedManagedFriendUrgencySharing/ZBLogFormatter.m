






#import "ZBLogFormatter.h"
#import "XXGPlayKitConfig.h"

@implementation ZBLogFormatter

+ (NSString *)formatObject:(id)obj {
    if (!obj) {
        return __data_core.xxpk_tools_logger_null;
    }

    if ([obj isKindOfClass:[NSDictionary class]]) {
        return [self formatDictionary:obj];
    } else if ([obj isKindOfClass:[NSArray class]]) {
        return [self formatArray:obj];
    } else if ([obj isKindOfClass:[NSError class]]) {
        return [self formatError:obj];
    } else if ([obj isKindOfClass:[NSString class]]) {
        return obj;
    } else {
        return [obj description];
    }
}

+ (NSString *)formatDictionary:(NSDictionary *)dict {
    return [self formatDictionary:dict withIndent:0 maxDepth:7];
}

+ (NSString *)formatDictionary:(NSDictionary *)dict withIndent:(NSInteger)indent maxDepth:(NSInteger)maxDepth {
    if (!dict || dict.count == 0) {
        return @"{}";
    }

    if (maxDepth <= 0) {
        return [NSString stringWithFormat:@"{%@}", [NSString stringWithFormat:__data_core.xxpk_tools_logger_items, (long)dict.count]];
    }

    NSString *indentStr = [self indentStringForLevel:indent];
    NSString *nextIndentStr = [self indentStringForLevel:indent + 1];

    NSMutableString *result = [NSMutableString stringWithString:@"{\n"];

    NSArray *sortedKeys = [dict.allKeys sortedArrayUsingComparator:^NSComparisonResult(id obj1, id obj2) {
        return [[obj1 description] compare:[obj2 description]];
    }];

    for (NSString *key in sortedKeys) {
        id value = dict[key];
        NSString *formattedValue = [self formatValue:value withIndent:indent + 1 maxDepth:maxDepth - 1];
        [result appendFormat:@"%@%@: %@\n", nextIndentStr, key, formattedValue];
    }

    [result appendFormat:@"%@}", indentStr];
    return result;
}

+ (NSString *)formatArray:(NSArray *)array {
    return [self formatArray:array withIndent:0 maxDepth:5];
}

+ (NSString *)formatArray:(NSArray *)array withIndent:(NSInteger)indent maxDepth:(NSInteger)maxDepth {
    if (!array || array.count == 0) {
        return @"[]";
    }

    if (maxDepth <= 0) {
        return [NSString stringWithFormat:@"[%@]", [NSString stringWithFormat:__data_core.xxpk_tools_logger_items, (long)array.count]];
    }

    
    if (array.count <= 3 && [self isSimpleArray:array]) {
        NSMutableArray *items = [NSMutableArray array];
        for (id item in array) {
            [items addObject:[self formatSimpleValue:item]];
        }
        return [NSString stringWithFormat:@"[%@]", [items componentsJoinedByString:@", "]];
    }

    NSString *indentStr = [self indentStringForLevel:indent];
    NSString *nextIndentStr = [self indentStringForLevel:indent + 1];

    NSMutableString *result = [NSMutableString stringWithString:@"[\n"];

    for (NSInteger i = 0; i < array.count; i++) {
        id item = array[i];
        NSString *formattedItem = [self formatValue:item withIndent:indent + 1 maxDepth:maxDepth - 1];
        [result appendFormat:@"%@[%ld]: %@\n", nextIndentStr, (long)i, formattedItem];
    }

    [result appendFormat:@"%@]", indentStr];
    return result;
}

+ (NSString *)formatValue:(id)value withIndent:(NSInteger)indent maxDepth:(NSInteger)maxDepth {
    if (!value) {
        return __data_core.xxpk_tools_logger_null;
    }

    if ([value isKindOfClass:[NSDictionary class]]) {
        return [self formatDictionary:value withIndent:indent maxDepth:maxDepth];
    } else if ([value isKindOfClass:[NSArray class]]) {
        return [self formatArray:value withIndent:indent maxDepth:maxDepth];
    } else {
        return [self formatSimpleValue:value];
    }
}

+ (NSString *)indentStringForLevel:(NSInteger)level {
    return [@"" stringByPaddingToLength:level * 2 withString:@" " startingAtIndex:0];
}

+ (BOOL)isSimpleArray:(NSArray *)array {
    for (id item in array) {
        if ([item isKindOfClass:[NSDictionary class]] || [item isKindOfClass:[NSArray class]]) {
            return NO;
        }
    }
    return YES;
}

+ (NSString *)formatSimpleValue:(id)value {
    if (!value) {
        return __data_core.xxpk_tools_logger_null;
    }

    if ([value isKindOfClass:[NSString class]]) {
        NSString *str = (NSString *)value;
            return [NSString stringWithFormat:@"\"%@\"", str];
    } else if ([value isKindOfClass:[NSNumber class]]) {
        return [value description];
    } else if ([value isKindOfClass:[NSDate class]]) {
        NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
        formatter.dateFormat = __data_core.xxpk_tools_logger_date_format;
        return [NSString stringWithFormat:@"\"%@\"", [formatter stringFromDate:value]];
    } else if ([value isKindOfClass:[NSURL class]]) {
        return [NSString stringWithFormat:@"\"%@\"", [(NSURL *)value absoluteString]];
    } else if ([value isKindOfClass:[NSData class]]) {
        NSData *data = (NSData *)value;
        return [NSString stringWithFormat:__data_core.xxpk_tools_logger_data_bytes, (unsigned long)data.length];
    } else {
        NSString *desc = [value description];
        
        if (desc.length > 200) {
            return [NSString stringWithFormat:@"%@%@", [desc substringToIndex:200], __data_core.xxpk_tools_logger_ellipsis];
        }
        return desc;
    }
}

+ (NSString *)formatRequestParams:(NSDictionary *)params {
    if (!params || params.count == 0) {
        return __data_core.xxpk_tools_logger_empty;
    }

    return [self formatDictionary:params];
}

+ (NSString *)formatResponse:(id)response {
    if (!response) {
        return __data_core.xxpk_tools_logger_null;
    }

    if ([response isKindOfClass:[NSData class]]) {
        NSData *data = (NSData *)response;

        NSError *error;
        id jsonObj = [NSJSONSerialization JSONObjectWithData:data options:0 error:&error];
        if (jsonObj) {
            return [self formatObject:jsonObj];
        }

        NSString *stringContent = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
        if (stringContent) {
            if (stringContent.length > 500) {
                return [NSString stringWithFormat:@"%@\n%@%@",
                       [NSString stringWithFormat:__data_core.xxpk_tools_logger_text_chars, (unsigned long)stringContent.length],
                       [stringContent substringToIndex:500], __data_core.xxpk_tools_logger_ellipsis];
            } else {
                return [NSString stringWithFormat:@"%@\n%@", __data_core.xxpk_tools_logger_text, stringContent];
            }
        }

        return [NSString stringWithFormat:__data_core.xxpk_tools_logger_data, (unsigned long)data.length];
    }

    return [self formatObject:response];
}

+ (NSString *)formatError:(NSError *)error {
    if (!error) {
        return __data_core.xxpk_tools_logger_no_error;
    }

    NSMutableString *result = [NSMutableString string];
    [result appendFormat:@"%@ %ld\n", __data_core.xxpk_tools_logger_code, (long)error.code];
    [result appendFormat:@"%@ %@\n", __data_core.xxpk_tools_logger_desc, error.localizedDescription];

    if (error.userInfo.count > 0) {
        [result appendFormat:@"%@\n", __data_core.xxpk_tools_logger_info];
        [result appendString:[self formatDictionary:error.userInfo]];
    }

    return result;
}

@end



NSString* ZBFormatDict(id obj) {
    return [ZBLogFormatter formatObject:obj];
}
