






#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN



@interface ZBLogFormatter : NSObject



+ (NSString *)formatObject:(nullable id)obj;



+ (NSString *)formatDictionary:(nullable NSDictionary *)dict;



+ (NSString *)formatDictionary:(nullable NSDictionary *)dict withIndent:(NSInteger)indent maxDepth:(NSInteger)maxDepth;



+ (NSString *)formatArray:(nullable NSArray *)array;



+ (NSString *)formatArray:(nullable NSArray *)array withIndent:(NSInteger)indent maxDepth:(NSInteger)maxDepth;



+ (NSString *)formatRequestParams:(nullable NSDictionary *)params;



+ (NSString *)formatResponse:(nullable id)response;



+ (NSString *)formatError:(nullable NSError *)error;

@end



NSString* ZBFormatDict(id _Nullable obj);

NS_ASSUME_NONNULL_END
