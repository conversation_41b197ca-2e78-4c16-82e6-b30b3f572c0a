






#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface XXGNetworkCore : NSObject
<
NSURLSessionDelegate,
NSURLSessionTaskDelegate
>

+ (instancetype)shared;

- (void)xxpk_sendBaseRequest:(NSMutableURLRequest *)request
                     process:(NSData * _Nullable (^_Nullable)(NSData * _Nullable rawData))processBlock 
                     success:(void(^_Nullable)(NSDictionary * responseObject))success
                     failure:(void(^_Nullable)(NSError *error))failure
                  retryCount:(NSInteger)retryCount;

@end

NS_ASSUME_NONNULL_END
