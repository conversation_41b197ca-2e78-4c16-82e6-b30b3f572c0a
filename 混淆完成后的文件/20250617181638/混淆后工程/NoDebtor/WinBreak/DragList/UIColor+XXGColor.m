






#import "UIColor+XXGColor.h"

@implementation UIColor (XXGColor)

+ (UIColor *)xxpk_colorWithHexString:(NSString *)hexString {
    if (hexString.length <= 0) return nil;
    
    NSString *colorString = [[hexString stringByReplacingOccurrencesOfString: @"#" withString: @""] uppercaseString];
    CGFloat alpha, red, blue, green;
    switch ([colorString length]) {
        case 3: 
            alpha = 1.0f;
            red   = [self __xxpk_colorComponentFrom: colorString start: 0 length: 1];
            green = [self __xxpk_colorComponentFrom: colorString start: 1 length: 1];
            blue  = [self __xxpk_colorComponentFrom: colorString start: 2 length: 1];
            break;
        case 4: 
            alpha = [self __xxpk_colorComponentFrom: colorString start: 0 length: 1];
            red   = [self __xxpk_colorComponentFrom: colorString start: 1 length: 1];
            green = [self __xxpk_colorComponentFrom: colorString start: 2 length: 1];
            blue  = [self __xxpk_colorComponentFrom: colorString start: 3 length: 1];
            break;
        case 6: 
            alpha = 1.0f;
            red   = [self __xxpk_colorComponentFrom: colorString start: 0 length: 2];
            green = [self __xxpk_colorComponentFrom: colorString start: 2 length: 2];
            blue  = [self __xxpk_colorComponentFrom: colorString start: 4 length: 2];
            break;
        case 8: 
            alpha = [self __xxpk_colorComponentFrom: colorString start: 0 length: 2];
            red   = [self __xxpk_colorComponentFrom: colorString start: 2 length: 2];
            green = [self __xxpk_colorComponentFrom: colorString start: 4 length: 2];
            blue  = [self __xxpk_colorComponentFrom: colorString start: 6 length: 2];
            break;
        default: {
            NSAssert(NO, @"Color value %@ is invalid. It should be a hex value of the form #RBG, #ARGB, #RRGGBB, or #AARRGGBB", hexString);
            return nil;
        }
            break;
    }
    return [UIColor colorWithRed: red green: green blue: blue alpha: alpha];
}

+ (CGFloat)__xxpk_colorComponentFrom:(NSString *)string start:(NSUInteger)start length:(NSUInteger)length {
    NSString *substring = [string substringWithRange: NSMakeRange(start, length)];
    NSString *fullHex = length == 2 ? substring : [NSString stringWithFormat: @"%@%@", substring, substring];
    unsigned hexComponent;
    [[NSScanner scannerWithString: fullHex] scanHexInt: &hexComponent];
    return hexComponent / 255.0;
}

- (UIColor *)xxpk_lighterByPercentage:(CGFloat)percentage {
    return [self __xxpk_adjustByPercentage:percentage];
}

- (UIColor *)xxpk_darkerByPercentage:(CGFloat)percentage {
    return [self __xxpk_adjustByPercentage:-1*fabs(percentage)];
}

- (UIColor *)__xxpk_adjustByPercentage:(CGFloat)percentage {
    CGFloat red,green,blue,alpha;
    if ([self getRed:&red green:&green blue:&blue alpha:&alpha]) {
        return [UIColor colorWithRed:MIN(red+percentage/100, 1.0) green:MIN(green+percentage/100, 1.0) blue:MIN(blue+percentage/100, 1.0) alpha:alpha];
    }else {
        return nil;
    }
}

@end
