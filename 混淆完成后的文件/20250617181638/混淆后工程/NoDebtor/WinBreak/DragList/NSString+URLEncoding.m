






#import "NSString+URLEncoding.h"

@implementation NSString (URLEncoding)

- (NSString *)xxpk_urlEncodedString {
    NSCharacterSet *allowedCharacters = [[NSCharacterSet characterSetWithCharactersInString:@"!*'();:@&=+$,/?%#[]"] invertedSet];
    return [self stringByAddingPercentEncodingWithAllowedCharacters:allowedCharacters];
}

- (NSString *)xxpk_urlDecodedString {
    NSString *strWithSpace = [self stringByReplacingOccurrencesOfString:@"+" withString:@" "];
    return [strWithSpace stringByRemovingPercentEncoding];
}

@end
