






#import "NSString+XXGString.h"

@implementation NSObject (XXGString)

- (BOOL)xxpk_isEmpty {
    
    
    if (self == nil || (id)self == [NSNull null]) {
        return YES;
    }
    
    
    if ([self isKindOfClass:[NSString class]]) {
        NSString *str = (NSString *)self;
        if (str.length == 0) {
            return YES;
        }
        
        NSString *trimmed = [str stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
        return (trimmed.length == 0);
    }
    
    







    
    
    
    return YES;
}

- (BOOL)xxpk_isNotEmpty {
    return ![self xxpk_isEmpty];
}

//- (BOOL)xxpk_isNotEmpty {

//}

//- (BOOL)xxpk_isEmpty {




//}

@end
