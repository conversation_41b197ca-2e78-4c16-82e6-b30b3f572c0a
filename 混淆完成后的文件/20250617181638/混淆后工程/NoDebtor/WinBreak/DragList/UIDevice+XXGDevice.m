






#import "UIDevice+XXGDevice.h"
#import "XXGWindowManager.h"
@import UIKit;

@implementation UIDevice (XXGDevice)

static NSInteger isIPad = -1;
+ (BOOL)isIPad {
    if (isIPad < 0) {
        isIPad = [UIDevice currentDevice].userInterfaceIdiom == UIUserInterfaceIdiomPad ? 1 : 0;
    }
    return isIPad > 0;
}

+ (BOOL)hasNotch {
    if (@available(iOS 11.0, *)) {
        
        UIWindow *window = XXGWindowManager.shared.xxpk_currentWindow;
        
        UIEdgeInsets safeArea = window.safeAreaInsets;
        
        
        BOOL isiPhone = ([UIDevice currentDevice].userInterfaceIdiom == UIUserInterfaceIdiomPhone);
        
        
        return isiPhone && (
            safeArea.top > 20.0 ||          
            safeArea.left > 0 ||            
            safeArea.right > 0              
        );
    }
    return NO; 
}

@end
