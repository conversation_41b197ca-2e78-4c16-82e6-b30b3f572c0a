






#import "NSURL+XXGAnalyse.h"
#import "NSString+URLEncoding.h"

@implementation NSURL (XXGAnalyse)

- (NSDictionary *)xxpk_analyse {
    
    NSArray * array = [[self query] componentsSeparatedByString:@"&"];

    NSMutableDictionary * ParaDict = [NSMutableDictionary new];

    for(int i = 0 ; i < [array count]; i++){

        NSArray * keyAndValue = [array[i] componentsSeparatedByString:@"="];

        if([keyAndValue count] == 2 && keyAndValue[0] && keyAndValue[1]){

            [ParaDict setObject:[keyAndValue[1] xxpk_urlDecodedString] forKey:keyAndValue[0]];

        }
    }
    return ParaDict;
}

@end
