






#import "UIViewController+XXGViewController.h"

@implementation UIViewController (XXGViewController)

- (void)xxpk_addToParentViewController:(UIViewController *)vc {
    if (vc) {
        [vc willMoveToParentViewController:self];
        [self addChildViewController:vc];
        [self.view addSubview:vc.view];
        
        
        vc.view.translatesAutoresizingMaskIntoConstraints = NO;

        UIView *superview = vc.view.superview;
        [NSLayoutConstraint activateConstraints:@[
            [vc.view.topAnchor constraintEqualToAnchor:superview.topAnchor],
            [vc.view.leadingAnchor constraintEqualToAnchor:superview.leadingAnchor],
            [vc.view.bottomAnchor constraintEqualToAnchor:superview.bottomAnchor],
            [vc.view.trailingAnchor constraintEqualToAnchor:superview.trailingAnchor]
        ]];
        
        [vc didMoveToParentViewController:self];
    }
}

- (void)xxpk_removeFromParentViewController {
    if (self && self.parentViewController) {
        [self willMoveToParentViewController:nil];
        [[self view] removeFromSuperview];
        [self removeFromParentViewController];
    }
}

- (void)xxpk_removeChildViewControllers {
    for (UIViewController *vc in self.childViewControllers) {
        [vc xxpk_removeFromParentViewController];
    }
}

@end
