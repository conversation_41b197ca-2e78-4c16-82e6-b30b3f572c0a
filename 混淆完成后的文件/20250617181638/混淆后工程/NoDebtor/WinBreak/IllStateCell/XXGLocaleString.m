






#import "XXGLocaleString.h"
#import "NSData+SunHope.h"
#import "NSString+XXGString.h"
#import "NSObject+XXGModel.h"
#import "XXGPlayKitConfig.h"

@implementation XXGLocaleString

+ (NSString *)getBundle {
    return [[NSBundle mainBundle] pathForResource:XXGPlayKitConfig.shared.xxpk_current_sdkname ofType:@"bundle"];
}

+ (id)xxpk_loadLanguagesWithClass:(Class)class {
    
    static NSMutableDictionary *resultDic;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        
        NSString *jsonPath = [[self getBundle] stringByAppendingPathComponent:__data_core.xxpk_tools_languages];
#ifdef XXGPLAYKIT_DEBUG
        jsonPath = [[[self getBundle] stringByAppendingPathComponent:__data_core.xxpk_tools_languages] stringByAppendingPathExtension:@"json"];
#endif
        NSDictionary *decryptedJSONObject = [self __xxpk_loadFileAtPath:jsonPath];
        resultDic = [NSMutableDictionary dictionary];
        for (NSString *key in decryptedJSONObject.allKeys) {
           NSDictionary *langDict = decryptedJSONObject[key];
           if ([langDict isKindOfClass:[NSDictionary class]]) {
               NSString *translation = langDict[[self xxpk_preferredLanguage]];
               if (translation) {
                   resultDic[key] = translation;
               }
           }
        }
    });
    
    return [class xxpk_modelWithDict:resultDic];
}

+ (id)xxpk_loadDatasWithClass:(Class)class {
    
    static id decryptedJSONObject;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        
        NSString *jsonPath = [[self getBundle] stringByAppendingPathComponent:@"finishingLineCardioidMostGeorgianHas"];
#ifdef XXGPLAYKIT_DEBUG
        jsonPath = [[[self getBundle] stringByAppendingPathComponent:@"finishingLineCardioidMostGeorgianHas"] stringByAppendingPathExtension:@"json"];
#endif
        decryptedJSONObject = [self __xxpk_loadFileAtPath:jsonPath];
    });
    
    return [class xxpk_modelWithDict:decryptedJSONObject];
}

+ (NSArray *)xxpk_loadCountries:(Class)class {
    
    static id decryptedJSONObject;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
    
        NSString *jsonPath = [[self getBundle] stringByAppendingPathComponent:__data_core.xxpk_tools_countries];
#ifdef XXGPLAYKIT_DEBUG
        jsonPath = [[[self getBundle] stringByAppendingPathComponent:__data_core.xxpk_tools_countries] stringByAppendingPathExtension:@"json"];
#endif
        decryptedJSONObject = [self __xxpk_loadFileAtPath:jsonPath];
    });
    
    return [class xxpk_modelArrayWithDictArray:decryptedJSONObject];
}


+ (id)__xxpk_loadFileAtPath:(NSString *)filePath {
    NSData *fileData = [NSData dataWithContentsOfFile:filePath];
    if (!fileData) {
        
        return nil;
    }
    
    id jsonObject = nil;
    NSError *error = nil;
    jsonObject = [NSJSONSerialization JSONObjectWithData:fileData options:0 error:&error];
    if (error) {
        
        jsonObject = nil;
    }
    
    if (!jsonObject) {
        jsonObject = [fileData xxpk_decryptJsonWithRandomFactor];
    }
    
    if (!jsonObject) {
        
    }
    return jsonObject;
}


+ (NSString *)xxpk_preferredLanguage {
    NSString *xxpk_preferredLanguage = [NSLocale preferredLanguages].firstObject;
    NSArray *xxpk_suportLanguages = [__data_core.xxpk_tools_support_anguage componentsSeparatedByString:@","];
    NSString *xxpk_suportLanguage = [xxpk_suportLanguages filteredArrayUsingPredicate:[NSPredicate predicateWithBlock:^BOOL(NSString *value, NSDictionary<NSString *,id> * _Nullable bindings) {
        return [xxpk_preferredLanguage hasPrefix:value];
    }]].firstObject;
return xxpk_suportLanguage?:xxpk_suportLanguages[0];

}

@end
