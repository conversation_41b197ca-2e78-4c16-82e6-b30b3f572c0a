







#import <Foundation/Foundation.h>


typedef NS_ENUM(NSUInteger, TransactionStatus) {
    TransactionStatusWaitApple,
    TransactionStatusAppleCancel,
    TransactionStatusAppleFailed,
    TransactionStatusAppleSucc,
    TransactionStatusSeriverError,
    TransactionStatusSeriverFailed,
    TransactionStatusSeriverSucc,
};


@interface XXGIAPTransactionModel : NSObject





@property(nonatomic, copy) NSString *xxpk_transactionIdentifier;



@property(nonatomic, strong, readonly) NSDate *xxpk_transactionDate;



@property(nonatomic, copy, readonly) NSString *xxpk_productIdentifier;


@property (nonatomic, copy) NSString *xxpk_applicationUsername;




@property(nonatomic, assign) TransactionStatus xxpk_transactionStatus;





@property (nonatomic,copy)NSString * xxpk_appStoreReceipt;




@property (nonatomic, strong) NSError *xxpk_error;



@property (nonatomic, assign) NSInteger xxpk_cancelStatusCheckCount;






@property(nonatomic, copy, readonly) NSString *xxpk_userId;



@property(nonatomic, copy,readonly) NSString *xxpk_seriverOrder;



@property(nonatomic, copy) NSString *xxpk_priceString;



@property(nonatomic, copy) NSString *xxpk_codeString;






+ (instancetype)xxpk_modelWithProductIdentifier:(NSString *)productIdentifier
                       applicationUsername:(NSString *)applicationUsername;

+ (XXGIAPTransactionModel *)xxpk_modelWithDic:(NSDictionary *)dic;
- (NSMutableDictionary *)xxpk_toDic;


@end
