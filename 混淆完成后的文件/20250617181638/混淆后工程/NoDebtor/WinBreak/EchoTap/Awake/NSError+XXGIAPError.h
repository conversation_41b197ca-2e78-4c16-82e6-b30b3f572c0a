








#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, XXGIAPErrorCode) {
    XXGIAPErrorCodePaying = 40001,
    XXGIAPErrorCodeParameter,
    XXGIAPErrorCodePermission,
    XXGIAPErrorCodeProductId,
    XXGIAPErrorCodeReceipt,
    XXGIAPErrorCodeVerifyInvalid,
    XXGIAPErrorCodeNet,
    XXGIAPErrorCodeNotRegistered,
    XXGIAPErrorCodeHasUnfinishedTransaction
};

@interface NSError (XXGIAPError)

+ (instancetype)errorWithXXGIAPCode:(XXGIAPErrorCode)code;

@end

NS_ASSUME_NONNULL_END
