








#import <Foundation/Foundation.h>

@class XXGIAPTransactionModel;
@class SKProduct;

typedef enum : NSUInteger {
    XXGIAPVerifyFailed,
    XXGIAPVerifyValid,
    XXGIAPVerifyInvalid,
    XXGIAPVerifyNeedRefreshReceipt
} XXGIAPVerifyResult;

typedef enum : NSUInteger {
    XXGIAPLoadingStatus_None,
    XXGIAPLoadingStatus_CheckingProduct,
    XXGIAPLoadingStatus_Paying,
    XXGIAPLoadingStatus_Restoring,
    XXGIAPLoadingStatus_Verifying,
} XXGIAPLoadingStatus;


typedef void(^VerifyRsultBlock)(XXGIAPVerifyResult result);

@protocol XXGIAPPayDelegate <NSObject>



- (void)verifyWithModel:(XXGIAPTransactionModel *)model resultAction:(VerifyRsultBlock)resultAction;

@optional



- (void)currentStatus:(XXGIAPLoadingStatus)status;



-(void)onLaunProductListFinish:(SKProduct *)products withError:(NSError*)error;




-(void)onIAPPaymentSucess:(XXGIAPTransactionModel*)model;




-(void)onIAPPayFailue:(XXGIAPTransactionModel*)model  withError:(NSError*)error;




-(void)onIAPRestoreResult:(NSArray*)productIdentifiers  withError:(NSError*)error;



-(void)onDistributeGoodsFinish:(XXGIAPTransactionModel*)model;


-(void)onDistributeGoodsFailue:(XXGIAPTransactionModel*)model withError:(NSError *)error;






-(void)onRedistributeGoodsFinish:(XXGIAPTransactionModel*)model;


-(void)onRedistributeGoodsFailue:(XXGIAPTransactionModel*)model withError:(NSError *)error;








- (void)XXGIAPLog:(NSString *)log;
@end

