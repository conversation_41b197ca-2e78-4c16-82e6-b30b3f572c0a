







#import "XXGIAPConfig.h"
#import "XXGIAPHelpManager.h"


static BOOL _sendLog = YES;
static BOOL _enableLoading = YES;

@implementation XXGIAPConfig

- (void)verifyWithModel:(XXGIAPTransactionModel *)model resultAction:(VerifyRsultBlock)resultAction{}
- (void)currentStatus:(XXGIAPLoadingStatus)status{};
-(void)onLaunProductListFinish:(SKProduct *)products withError:(NSError*)error{}
-(void)onIAPPaymentSucess:(XXGIAPTransactionModel*)model{};
-(void)onIAPPayFailue:(XXGIAPTransactionModel*)model  withError:(NSError*)error{};
-(void)onIAPRestoreResult:(NSArray*)productIdentifiers  withError:(NSError*)error{};
-(void)onDistributeGoodsFinish:(XXGIAPTransactionModel*)model{};
-(void)onDistributeGoodsFailue:(XXGIAPTransactionModel*)model withError:(NSError *)error{};
-(void)onRedistributeGoodsFinish:(XXGIAPTransactionModel*)model{};
-(void)onRedistributeGoodsFailue:(XXGIAPTransactionModel*)model withError:(NSError *)error{};
- (void)XXGIAPLog:(NSString *)log{};

+(void)sendLog:(NSString *)format, ... {

    if (_sendLog) {
        va_list paramList;
        va_start(paramList,format);
        NSString* log = [[NSString alloc]initWithFormat:format arguments:paramList];
        va_end(paramList);
        NSString *result = [@"[IAP]:" stringByAppendingString:log];
        if ([XXGIAPHelpManager sharedManager].delegate && [[XXGIAPHelpManager sharedManager].delegate respondsToSelector:@selector(XXGIAPLog:)]) {
            [[XXGIAPHelpManager sharedManager].delegate XXGIAPLog:result];
        }
    }
    
}

+ (BOOL)enableLog{
    return _sendLog;
}
+ (void)setEnableLog:(BOOL)enableLog{
    _sendLog = enableLog;
}

+ (BOOL)enableLoading{
    return _enableLoading;
}

+ (void)setEnableLoading:(BOOL)enableLoading{
    _enableLoading = enableLoading;
}

@end
