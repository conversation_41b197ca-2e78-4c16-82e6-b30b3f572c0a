


#import "SDWebImageCompat.h"


typedef NS_ENUM(NSUInteger, SDImageScaleMode) {
    
    SDImageScaleModeFill = 0,
    
    SDImageScaleModeAspectFit = 1,
    
    SDImageScaleModeAspectFill = 2
};

#if SD_UIKIT || SD_WATCH
typedef UIRectCorner SDRectCorner;
#else
typedef NS_OPTIONS(NSUInteger, SDRectCorner) {
    SDRectCornerTopLeft     = 1 << 0,
    SDRectCornerTopRight    = 1 << 1,
    SDRectCornerBottomLeft  = 1 << 2,
    SDRectCornerBottomRight = 1 << 3,
    SDRectCornerAllCorners  = ~0UL
};
#endif



@interface UIImage (Transform)





- (nullable UIImage *)sd_resizedImageWithSize:(CGSize)size scaleMode:(SDImageScaleMode)scaleMode;



- (nullable UIImage *)sd_croppedImageWithRect:(CGRect)rect;



- (nullable UIImage *)sd_roundedCornerImageWithRadius:(CGFloat)cornerRadius
                                              corners:(SDRectCorner)corners
                                          borderWidth:(CGFloat)borderWidth
                                          borderColor:(nullable UIColor *)borderColor;



- (nullable UIImage *)sd_rotatedImageWithAngle:(CGFloat)angle fitSize:(BOOL)fitSize;



- (nullable UIImage *)sd_flippedImageWithHorizontal:(BOOL)horizontal vertical:(BOOL)vertical;





- (nullable UIImage *)sd_tintedImageWithColor:(nonnull UIColor *)tintColor;



- (nullable UIImage *)sd_tintedImageWithColor:(nonnull UIColor *)tintColor blendMode:(CGBlendMode)blendMode;



- (nullable UIColor *)sd_colorAtPoint:(CGPoint)point;



- (nullable NSArray<UIColor *> *)sd_colorsWithRect:(CGRect)rect;





- (nullable UIImage *)sd_blurredImageWithRadius:(CGFloat)blurRadius;

#if SD_UIKIT || SD_MAC


- (nullable UIImage *)sd_filteredImageWithFilter:(nonnull CIFilter *)filter;
#endif

@end
