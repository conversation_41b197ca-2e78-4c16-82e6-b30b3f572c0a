






#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN


typedef void(^XXGAlertViewCompletion)(NSInteger buttonIndex);

@interface XXGAlertView : UIView



+ (void)xxpk_showAlertWithTitle:(NSString *_Nullable)title
                        message:(NSString *)message
                   buttonTitles:(NSArray<NSString *> *)buttonTitles
                     completion:(XXGAlertViewCompletion _Nullable)completion;

+ (void)xxpk_showAlertWithTitle:(NSString *_Nullable)title message:(NSString *)message completion:(XXGAlertViewCompletion _Nullable)completion;


@end

NS_ASSUME_NONNULL_END
