






#import "XXGLoadingView.h"
#import "XXGWindowManager.h"
#import "XXGUIDriver.h"
#import "Masonry.h"

@interface XXGLoadingView ()

@property (nonatomic, strong) UIView *xxpk_loadingBackgroundView;
@property (nonatomic, strong) UIActivityIndicatorView *activityIndicator;
@property (nonatomic, strong) UILabel *loadingLabel;
@end

@implementation XXGLoadingView


static XXGLoadingView *sharedLoadingView = nil;



- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupView];
    }
    return self;
}
- (instancetype)initWithCoder:(NSCoder *)coder {
    self = [super initWithCoder:coder];
    if (self) {
        [self setupView];
    }
    return self;
}
- (void)setupView {
    
    
    self.xxpk_loadingBackgroundView = [UIView new];
    self.xxpk_loadingBackgroundView.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.5];
    self.xxpk_loadingBackgroundView.layer.cornerRadius = 2.0;
    self.xxpk_loadingBackgroundView.clipsToBounds = YES;
    [self addSubview:self.xxpk_loadingBackgroundView];
    
    
    self.activityIndicator = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleLarge];
    self.activityIndicator.color = XXGUIDriver.xxpk_mainColor;
    [self.xxpk_loadingBackgroundView addSubview:self.activityIndicator];
    
    
    self.loadingLabel = [[UILabel alloc] init];
    self.loadingLabel.text = XXGUIDriver.xxpk_string_ui.xxpk_loading;
    self.loadingLabel.textColor = [UIColor whiteColor];
    self.loadingLabel.font = [UIFont systemFontOfSize:14];
    self.loadingLabel.numberOfLines = 0;
    self.loadingLabel.textAlignment = NSTextAlignmentCenter;
    [self.xxpk_loadingBackgroundView addSubview:self.loadingLabel];
    
    
    [self.xxpk_loadingBackgroundView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(XXGUIDriver.xxpk_data_ui.xxpk_float120, XXGUIDriver.xxpk_data_ui.xxpk_float120));
        make.center.equalTo(self);
    }];
    
    [self.activityIndicator mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float20);
        make.centerX.equalTo(self.xxpk_loadingBackgroundView.mas_centerX);
    }];
    
    [self.loadingLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.activityIndicator.mas_bottom).offset(XXGUIDriver.xxpk_data_ui.xxpk_float20);
        make.centerX.equalTo(self.xxpk_loadingBackgroundView.mas_centerX);
        make.left.equalTo(self.xxpk_loadingBackgroundView.mas_left).offset(XXGUIDriver.xxpk_data_ui.xxpk_float8);
        make.right.equalTo(self.xxpk_loadingBackgroundView.mas_right).offset(-XXGUIDriver.xxpk_data_ui.xxpk_float8);
    }];
    
    
    self.hidden = YES;
}



- (void)startAnimating {
    self.hidden = NO;
    [self.activityIndicator startAnimating];
}

- (void)stopAnimating {
    [self.activityIndicator stopAnimating];
    self.hidden = YES;
}

- (void)setLoadingText:(NSString *)text {
    self.loadingLabel.text = text;
    
    
    CGFloat textWidth = [text boundingRectWithSize:CGSizeMake(CGFLOAT_MAX, CGFLOAT_MAX)
                                          options:NSStringDrawingUsesLineFragmentOrigin
                                       attributes:@{NSFontAttributeName: self.loadingLabel.font}
                                          context:nil].size.width;
    UIWindow *window = [[XXGWindowManager shared] xxpk_currentWindow];
    CGFloat desiredWidth = MIN(MAX(120, textWidth + 2 * 8), window.bounds.size.width);
    [self.xxpk_loadingBackgroundView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(desiredWidth);
    }];
    
    [self layoutIfNeeded];
}


+ (void)showLoadingOnWindow {
    [self showLoadingOnWindowWithText:XXGUIDriver.xxpk_string_ui.xxpk_loading];
}

+ (void)showLoadingOnWindowWithText:(NSString *)text {
    dispatch_async(dispatch_get_main_queue(), ^{
        UIWindow *window = [[XXGWindowManager shared] xxpk_currentWindow];
        
        if (!sharedLoadingView) {
            CGSize size = UIScreen.mainScreen.bounds.size;
            sharedLoadingView = [[XXGLoadingView alloc] initWithFrame:CGRectMake(0, 0, size.width, size.height)];
            sharedLoadingView.center = window.center;
        }
        if (!sharedLoadingView.superview) {
            [window addSubview:sharedLoadingView];
        }
        [sharedLoadingView setLoadingText:text];
        [sharedLoadingView startAnimating];
    });
}

+ (void)hideLoadingFromWindow {
    dispatch_async(dispatch_get_main_queue(), ^{
        [sharedLoadingView stopAnimating];
        [sharedLoadingView removeFromSuperview];
        sharedLoadingView = nil;
    });
}


+ (XXGLoadingView *)showLoadingOnView:(UIView *)view {
    return [self showLoadingOnView:view withText:XXGUIDriver.xxpk_string_ui.xxpk_loading];
}

+ (XXGLoadingView *)showLoadingOnView:(UIView *)view withText:(NSString *)text {
    __block XXGLoadingView *loadingView = nil;
    dispatch_async(dispatch_get_main_queue(), ^{
        
        loadingView = [[XXGLoadingView alloc] initWithFrame:CGRectMake(0, 0, view.frame.size.width, view.frame.size.height)];
        loadingView.center = CGPointMake(CGRectGetMidX(view.bounds), CGRectGetMidY(view.bounds));
        [loadingView setLoadingText:text];
        [loadingView startAnimating];
        [view addSubview:loadingView];
    });
    return loadingView;
}

+ (void)hideLoadingFromView:(UIView *)view {
    dispatch_async(dispatch_get_main_queue(), ^{
        
        for (UIView *subview in view.subviews) {
            if ([subview isKindOfClass:[XXGLoadingView class]]) {
                [(XXGLoadingView *)subview stopAnimating];
                [subview removeFromSuperview];
            }
        }
    });
}

@end
