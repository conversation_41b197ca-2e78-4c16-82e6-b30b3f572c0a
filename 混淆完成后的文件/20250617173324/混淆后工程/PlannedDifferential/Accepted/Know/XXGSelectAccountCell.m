






#import "XXGSelectAccountCell.h"
#import "XXGUIDriver.h"
#import "Masonry.h"

@implementation XXGSelectAccountCell

-(id)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if(self){
        
        self.backgroundColor = UIColor.whiteColor;
        self.contentView.backgroundColor = UIColor.whiteColor;
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        
        
        self.xxpk_imageView = [UIImageView new];
        self.xxpk_imageView.tintColor = [XXGUIDriver xxpk_mainColor];
        self.xxpk_imageView.layer.cornerRadius = 15;
        [self.contentView addSubview:self.xxpk_imageView];
        [self.xxpk_imageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float10);
            make.centerY.mas_equalTo(self);
            make.width.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float30);
        }];
        
        
        self.xxpk_boxName = [UILabel new];
        self.xxpk_boxName.font = [UIFont boldSystemFontOfSize:16];
        self.xxpk_boxName.textColor = UIColor.darkGrayColor;
        [self.contentView addSubview:self.xxpk_boxName];
        [self.xxpk_boxName mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.xxpk_imageView.mas_right).offset(XXGUIDriver.xxpk_data_ui.xxpk_float10);
            make.bottom.equalTo(self.contentView.mas_centerY);
        }];
        
        
        self.xxpk_comeinTime = [UILabel new];
        self.xxpk_comeinTime.font =  [UIFont systemFontOfSize:11];
        self.xxpk_comeinTime.textColor = UIColor.grayColor;
        [self.contentView addSubview:self.xxpk_comeinTime];
        [self.xxpk_comeinTime mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.contentView.mas_centerY).offset(XXGUIDriver.xxpk_data_ui.xxpk_float5);
            make.left.equalTo(self.xxpk_boxName);
        }];
    }
    return self;
}

@end
