







#import "XXGLiveBarrageCell.h"
#define weakify(obj) __weak typeof(obj) weak##obj = obj;
#define strongify(obj) __strong typeof(obj) obj = weak##obj;

@interface XXGLiveBarrageCell()



@property (nonatomic, strong) NSTimer *timer;

@property (nonatomic, assign) BOOL afterDelayAble;

@end

@implementation XXGLiveBarrageCell

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:CGRectZero];
    if (self) {
        
        _barrageSize = CGSizeMake(200, 40);
        _barrageSpeed = 4;
        _model = nil;
        _channelCount = 3;
        _margin = 0;
        _afterDelayExit = 0;
        _status = EStatusOfBarrage_Stop;
        _afterDelayAble = NO;
        
    }
    return self;
}

- (void)receiveFrameNewValue
{
    CGFloat barrageX = [[self.layer presentationLayer] frame].origin.x;
    CGFloat barrageWidth = self.frame.size.width;
    
    
    CGFloat speed = (self.superview.frame.size.width + barrageWidth) / self.barrageSpeed;
    
    
    CGFloat beginExitTime = barrageWidth / speed;
    
    if (_afterDelayExit > 0) {
        self.status = EStatusOfBarrage_AfterDelay;
        if (-1< barrageX < 1) {
            
            if (_afterDelayAble) { return;}
            _afterDelayAble = YES;
            [self pause];
            [self performSelector:@selector(resume) withObject:nil afterDelay:_afterDelayExit];
            [self performSelector:@selector(changeStatus) withObject:nil afterDelay:_afterDelayExit - beginExitTime];
        }
    }
}
- (void)changeStatus
{
    self.status = EStatusOfBarrage_Start;
}

- (void)startAnimations:(void(^)(void))animations completion:(void(^)(BOOL))completion
{
    self.status = EStatusOfBarrage_Start;
    
    _timer = [NSTimer timerWithTimeInterval:0.01 target:self selector:@selector(receiveFrameNewValue) userInfo:nil repeats:YES];
    [[NSRunLoop currentRunLoop] addTimer:_timer forMode:NSRunLoopCommonModes];
    
    
    weakify(self);
    [UIView animateWithDuration:self.barrageSpeed delay:0 options:(UIViewAnimationOptionCurveLinear | UIViewAnimationOptionAllowUserInteraction) animations:^{
        
        if (animations) {
            animations();
        }
        
    } completion:^(BOOL finished) {
        strongify(self);
        self->_status = EStatusOfBarrage_Start;
        
        if (completion) {
            completion(finished);
        }
        
        if(self->_timer) {
            [self->_timer invalidate];
            self->_timer = nil;
        }
        
    }];
}

- (void)pause
{
    
    CFTimeInterval pauseTime = [self.layer convertTime:CACurrentMediaTime() fromLayer:nil];
    
    
    self.layer.timeOffset = pauseTime;
    
    
    self.layer.speed = 0;
}

- (void)resume
{
    
    CFTimeInterval pauseTime = self.layer.timeOffset;
    
    CFTimeInterval timeSincePause = CACurrentMediaTime() - pauseTime;
    
    self.layer.timeOffset = 0;
    
    self.layer.beginTime = timeSincePause;
    
    self.layer.speed = 1;
}


@end
