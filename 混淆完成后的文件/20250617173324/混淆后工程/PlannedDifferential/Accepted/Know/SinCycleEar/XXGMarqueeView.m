

#import "XXGMarqueeView.h"
#import "XXGMarqueeViewCell.h"

@implementation XXGMarqueeView

- (void)insertMarqueeViewWithModel:(XXGMQTTTopicInfo *)model {
    
    NSMutableArray *cells = [NSMutableArray new];
    for (int i = 0; i<model.xxpk_count; i++) {
        XXGMarqueeViewCell *cell = [[XXGMarqueeViewCell alloc]init];
        cell.barrageSpeed = model.xxpk_speed;
        cell.channelCount = 1;
        cell.margin = 6;
        cell.afterDelayExit = CGFLOAT_MIN;
        CGRect fontRect = [model.xxpk_message boundingRectWithSize:CGSizeMake(CGFLOAT_MAX, CGFLOAT_MAX) options:NSStringDrawingUsesLineFragmentOrigin attributes:[NSDictionary dictionaryWithObject:[UIFont systemFontOfSize:model.xxpk_style_text_font_size] forKey:NSFontAttributeName] context:nil];
        cell.barrageSize = CGSizeMake(fontRect.size.width+8, fontRect.size.height+4);
        cell.model = model;
        [cells addObject:cell];
    }

    [self insertBarrages:cells];
    
}

@end
