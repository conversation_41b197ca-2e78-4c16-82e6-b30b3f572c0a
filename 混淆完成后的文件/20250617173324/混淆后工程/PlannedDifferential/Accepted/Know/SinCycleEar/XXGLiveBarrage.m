








#import "XXGLiveBarrage.h"

#define BARRAGE_WIDTH self.frame.size.width

@interface XXGLiveBarrage()



@property (nonatomic, strong) NSMutableArray                            *channelArray;



@property (nonatomic, strong) NSMutableArray <XXGLiveBarrageCell *>      *dataArray;



@property (strong, nonatomic) NSMutableArray <XXGLiveBarrageCell *>      *showBarrages;



@property (assign, nonatomic) NSInteger                                 count;



@property (nonatomic, assign) EStatusOfBarrage                          status;



@property (nonatomic, assign) NSInteger                                 channelCount;



@property (nonatomic, assign) CGFloat                                   margin;

@end

@implementation XXGLiveBarrage

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.status = EStatusOfBarrage_Stop;
    }
    return self;
}

- (void)creatBarrage
{
    
    if (self.dataArray.firstObject) {
        
        
        XXGLiveBarrageCell *barrageView = self.dataArray.firstObject;
        
        barrageView.frame = CGRectMake(BARRAGE_WIDTH, 0, barrageView.barrageSize.width, barrageView.barrageSize.height);
        
        self.margin = barrageView.margin;
        
        self.channelCount = barrageView.channelCount;
        
        
        NSInteger row = [self xxpk_canBarrageSendInTheChannel:barrageView];
        
        
        if (row >= 0) {
            
            
            [self.dataArray removeObjectAtIndex:0];
            
            
            if (![self.subviews containsObject:barrageView]) {
                [self addSubview:barrageView];
            }
            barrageView.frame = CGRectMake(BARRAGE_WIDTH,  row * (barrageView.barrageSize.height + _margin), barrageView.barrageSize.width, barrageView.barrageSize.height);
            
            
            [_channelArray setObject:barrageView atIndexedSubscript:row];
            
            
            if ([self.delegate respondsToSelector:@selector(xxpk_barrageView:willDisplayCell:)]) {
                [self.delegate xxpk_barrageView:self willDisplayCell:barrageView];
            }
            
            
            [self.showBarrages addObject:barrageView];
            
            [barrageView startAnimations:^{
                
                
                [barrageView setTransform:CGAffineTransformMakeTranslation(- barrageView.frame.size.width-BARRAGE_WIDTH, 0)];
                
            } completion:^(BOOL finished) {
                
                [barrageView removeFromSuperview];
                
                
                [self.showBarrages removeObject:barrageView];
                
                
                if ([self.delegate respondsToSelector:@selector(xxpk_barrageView:didEndDisplayingCell:)]) {
                    [self.delegate xxpk_barrageView:self didEndDisplayingCell:barrageView];
                }
                
                
                if (--self.count <= 0) {
                    if ([self.delegate respondsToSelector:@selector(xxpk_barrageViewCompletedCurrentAnimations:)]) {
                        [self.delegate xxpk_barrageViewCompletedCurrentAnimations:self];
                    }
                    self.count = 0;
                }
                
                

            }];
        }
    }
    
    [self performSelector:@selector(creatBarrage) withObject:nil afterDelay:0.45f];
}


- (void)insertBarrages:(NSArray <XXGLiveBarrageCell *> *)barrages
{
    self.count += barrages.count;
    [self.dataArray addObjectsFromArray:barrages];
}

- (void)start
{
    if (self.status == EStatusOfBarrage_Start) {
        return;
    }
    self.status = EStatusOfBarrage_Start;
    
    [self creatBarrage];
}

- (void)stop
{
    if (self.status == EStatusOfBarrage_Stop) {
        return;
    }
    self.status = EStatusOfBarrage_Stop;
    
    if (self.showBarrages.count) {
        [self.showBarrages makeObjectsPerformSelector:@selector(pause)];
    }
    
    if (self.dataArray.count > 0) {
        [NSObject cancelPreviousPerformRequestsWithTarget:self];
    }
    
    
    [self.showBarrages  makeObjectsPerformSelector:@selector(removeFromSuperview)];
    self.channelCount       = 0;
    self.count              = 0;
    [self.showBarrages  removeAllObjects];
    [self.dataArray     removeAllObjects];
    [self.channelArray  removeAllObjects];
    
    self.showBarrages       = nil;
    self.dataArray          = nil;
    self.channelArray       = nil;
}


- (NSInteger)xxpk_canBarrageSendInTheChannel:(XXGLiveBarrageCell *)newBarrage
{
    for (int row = 0; row<_channelArray.count; row++) {
        NSObject *object = _channelArray[row];
        if ([object isKindOfClass:[NSNumber class]]) { 
            
            return row;
            
        }else if ([object isKindOfClass:[XXGLiveBarrageCell class]]) { 
            
            XXGLiveBarrageCell *oldBarrage = (XXGLiveBarrageCell*)object;
            
            if ([self xxpk_canBarrageSendInTheChannel:oldBarrage newBullet:newBarrage]) {
                
                return row;
            }
        }
    }
    
    return -1;
}


- (BOOL)xxpk_canBarrageSendInTheChannel:(XXGLiveBarrageCell *)oldBarrage newBullet:(XXGLiveBarrageCell *)newBarrage
{
    
    if (oldBarrage.status == EStatusOfBarrage_AfterDelay) {
        return NO;
    }
    
    
    CGRect rect = [oldBarrage.layer.presentationLayer frame];
    if (rect.origin.x>BARRAGE_WIDTH - oldBarrage.frame.size.width) {
        
        return NO;
    }else if (rect.size.width == 0)
    {
        
        return NO;
    }
    else if (oldBarrage.frame.size.width > newBarrage.frame.size.width) {
        
        return YES;
    }else
    {
        
        CGFloat time = BARRAGE_WIDTH/(BARRAGE_WIDTH+newBarrage.frame.size.width)*newBarrage.barrageSpeed;
        
        CGFloat endX = rect.origin.x - time/(oldBarrage.barrageSpeed)*(BARRAGE_WIDTH + oldBarrage.frame.size.width);
        if (endX < -oldBarrage.frame.size.width) {
            
            return YES;
        }
    }
    return NO;
}


- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event
{
    UITouch *touch = [touches anyObject];
    CGPoint clickPoint  = [touch locationInView:self];
    for (XXGLiveBarrageCell *barrageView in [self subviews])
    {
        if ([barrageView.layer.presentationLayer hitTest:clickPoint])
        {
            
            if ([self.delegate respondsToSelector:@selector(xxpk_barrageView:didSelectedCell:)]) {
                [self.delegate xxpk_barrageView:self didSelectedCell:barrageView];
            }
            break;
        }
    }
}




- (NSMutableArray<XXGLiveBarrageCell *> *)dataArray {
    if (!_dataArray) {
        _dataArray = [[NSMutableArray alloc] init];
    }
    return _dataArray;
}


- (NSMutableArray<XXGLiveBarrageCell *> *)showBarrages {
    if (!_showBarrages) {
        _showBarrages = [[NSMutableArray alloc] init];
    }
    return _showBarrages;
}


- (void)setChannelCount:(NSInteger)channelCount
{
    
    if (self.channelArray.count < channelCount) { 
        
        for (NSInteger row = self.channelArray.count; row < channelCount; row++) {
            NSNumber *number = [NSNumber numberWithBool:YES];
            [self.channelArray setObject:number atIndexedSubscript:row];
        }
        
    }else {
        
        for (NSInteger row = channelCount; row < self.channelArray.count; row++) {
            [self.channelArray removeObjectAtIndex:row];
        }
    }
    
    _channelCount = channelCount;
    
}


- (NSMutableArray *)channelArray {
    if (!_channelArray) {
        _channelArray = [[NSMutableArray alloc] init];
    }
    return _channelArray;
}

@end
