






#import "XXGCountryCodeButton.h"
#import "XXGUIDriver.h"
#import "UIImage+XXGImage.h"
#import "XXGNavigationController.h"
#import "UIImage+XXGImage.h"
#import "UIColor+XXGColor.h"

@implementation XXGCountryCodeButton

- (instancetype)initWithCurrentViewController:(UIViewController *)viewController {
    self = [super init];
    if (self) {
        self.xxpk_currentViewController = viewController;
        [self xxpk_commonInit];
    }
    return self;
}


- (void)xxpk_commonInit {
    
    NSArray *xxpk_allCountries = [XXGLocaleString xxpk_loadCountries:[XXGCountry class]];
    
    
    NSString *currentCountryCode = [[NSLocale currentLocale] objectForKey:NSLocaleCountryCode];
    
    __block XXGCountry *matchedCountry = nil;
    [xxpk_allCountries enumerateObjectsUsingBlock:^(XXGCountry *country, NSUInteger idx, BOOL *stop) {
        if ([country.xxpk_countryCode caseInsensitiveCompare:currentCountryCode] == NSOrderedSame) {
            matchedCountry = country;
            *stop = YES; 
        }
    }];
    self.xxpk_currentCountry = matchedCountry;
    
    
    NSString *title = [NSString stringWithFormat:@"%@%@",XXGUIDriver.xxpk_data_ui.xxpk_code_jia, matchedCountry.xxpk_dialCode];
    [self setTitle:title forState:UIControlStateNormal];
    
    
    UIImage *originalImage = [UIImage xxpk_imageBundleOfName:XXGUIDriver.xxpk_data_ui.xxpk_img_code_pulldown];
    
    
    CGSize targetImageSize = CGSizeMake(13, 13); 
    
    
    UIImage *scaledImage = [self xxpk_imageWithImage:originalImage scaledToSize:targetImageSize];
    
    
    [self setImage:scaledImage forState:UIControlStateNormal];
    [self setImage:[scaledImage imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate] forState:UIControlStateHighlighted]; 
    
    
    self.imageView.contentMode = UIViewContentModeScaleAspectFit;
    
    
    self.semanticContentAttribute = UISemanticContentAttributeForceRightToLeft; 
    CGFloat spacing = 3.0; 
    self.imageEdgeInsets = UIEdgeInsetsMake(0, spacing, 0, -spacing);  
    self.titleEdgeInsets = UIEdgeInsetsMake(0, -spacing, 0, spacing);   
    
    
    [self setBackgroundImage:[UIImage xxpk_imageWithColor:[XXGUIDriver.xxpk_mainColor xxpk_lighterByPercentage:8]] forState:UIControlStateNormal];
    [self setBackgroundImage:[UIImage xxpk_imageWithColor:[[UIColor lightGrayColor] colorWithAlphaComponent:0.5f]]
                   forState:UIControlStateHighlighted];
    self.titleLabel.font = [UIFont systemFontOfSize:16];
    self.layer.cornerRadius = 2.f;
    self.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMinXMaxYCorner;
    self.layer.masksToBounds = YES;
    
    
    self.contentEdgeInsets = UIEdgeInsetsMake(8, 12, 8, 12); 
    
    
    [self sizeToFit];
    
    
    [self addTarget:self action:@selector(xxpk_buttonClicked) forControlEvents:UIControlEventTouchUpInside];
}


- (UIImage *)xxpk_imageWithImage:(UIImage *)image scaledToSize:(CGSize)targetSize {
    
    UIGraphicsBeginImageContextWithOptions(targetSize, NO, 0.0);
    
    
    CGFloat widthRatio = targetSize.width / image.size.width;
    CGFloat heightRatio = targetSize.height / image.size.height;
    CGFloat scaleFactor = MIN(widthRatio, heightRatio);
    
    
    CGRect scaledRect = CGRectMake(0, 0,
                                  image.size.width * scaleFactor,
                                  image.size.height * scaleFactor);
    
    
    CGPoint origin = CGPointMake((targetSize.width - scaledRect.size.width) / 2.0,
                               (targetSize.height - scaledRect.size.height) / 2.0);
    [image drawInRect:CGRectMake(origin.x, origin.y,
                                scaledRect.size.width,
                                scaledRect.size.height)];
    
    UIImage *newImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    
    return newImage;
}


- (void)xxpk_buttonClicked {
    XXGCountryCodeSelectorViewController *vc = [XXGCountryCodeSelectorViewController new];
    vc.xxpk_codeDelegate = self;
    [self.xxpk_currentViewController.navigationController pushViewController:vc animated:NO];
}

- (void)xxpk_countryCodeSelectorDidSelectCountry:(XXGCountry *)country {
    NSString *title = [NSString stringWithFormat:@"%@%@",XXGUIDriver.xxpk_data_ui.xxpk_code_jia, country.xxpk_dialCode];
    [self setTitle:title forState:UIControlStateNormal];
    self.xxpk_currentCountry = country;
}

- (void)dealloc {
    
}
@end
