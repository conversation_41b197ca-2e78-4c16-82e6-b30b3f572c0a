






#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, XXGFloatViewEdge) {
    XXGFloatViewEdgeNone,
    XXGFloatViewEdgeTop,
    XXGFloatViewEdgeLeft,
    XXGFloatViewEdgeRight,
    XXGFloatViewEdgeBottom
};

@interface XXGFloatView : UIControl


+ (instancetype)shared;

+ (void)xxpk_show;


+ (void)xxpk_showWithImage:(UIImage *)image;


+ (void)xxpk_updateImage:(UIImage *)image edgeImage:(nullable UIImage *)edgeImage;


+ (void)xxpk_hide;


+ (BOOL)xxpk_isShowing;


@property (nonatomic, copy) void(^xxpk_tapHandler)( NSString * _Nullable url);

@property (nonatomic, strong) NSDictionary *xxpk_redotJson;

@end

NS_ASSUME_NONNULL_END
