






#import "XXGLocalizedModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface XXGLocalizedUI : XXGLocalizedModel

@property(nonatomic, copy) NSString *xxpk_enterVerificationCode;
@property(nonatomic, copy) NSString *xxpk_sendVerificationCode;
@property(nonatomic, copy) NSString *xxpk_reSendVerificationCode;
@property(nonatomic, copy) NSString *xxpk_sendedVerificationCode;
@property(nonatomic, copy) NSString *xxpk_enterMobile;
@property(nonatomic, copy) NSString *xxpk_enterBox;
@property(nonatomic, copy) NSString *xxpk_enterBoxKey;
@property(nonatomic, copy) NSString *xxpk_enterBoxKeyNew;
@property(nonatomic, copy) NSString *xxpk_readProtocl;
@property(nonatomic, copy) NSString *xxpk_protocl;
@property(nonatomic, copy) NSString *xxpk_protoclon;
@property(nonatomic, copy) NSString *xxpk_loading;
@property(nonatomic, copy) NSString *xxpk_customerService;
@property(nonatomic, copy) NSString *xxpk_agree;
@property(nonatomic, copy) NSString *xxpk_noagree;
@property(nonatomic, copy) NSString *xxpk_comein;
@property(nonatomic, copy) NSString *xxpk_register;
@property(nonatomic, copy) NSString *xxpk_forgetKey;
@property(nonatomic, copy) NSString *xxpk_forgetKey_sus;
@property(nonatomic, copy) NSString *xxpk_boxkey_verified;
@property(nonatomic, copy) NSString *xxpk_account_verified;
@property(nonatomic, copy) NSString *xxpk_moblie_verified;
@property(nonatomic, copy) NSString *xxpk_code_verified;
@property(nonatomic, copy) NSString *xxpk_realname_verified;
@property(nonatomic, copy) NSString *xxpk_otherComein;
@property(nonatomic, copy) NSString *xxpk_lastComeinTime;
@property(nonatomic, copy) NSString *xxpk_justNow;
@property(nonatomic, copy) NSString *xxpk_minutesAgo;
@property(nonatomic, copy) NSString *xxpk_today;
@property(nonatomic, copy) NSString *xxpk_yesterday;
@property(nonatomic, copy) NSString *xxpk_comein_mobileAndReg;
@property(nonatomic, copy) NSString *xxpk_change_boxkey;
@property(nonatomic, copy) NSString *xxpk_change_boxkey_success;
@property(nonatomic, copy) NSString *xxpk_bind_mobile;
@property(nonatomic, copy) NSString *xxpk_bind_mobile_success;
@property(nonatomic, copy) NSString *xxpk_bind_mobile_tips;
@property(nonatomic, copy) NSString *xxpk_p_pm;
@property(nonatomic, copy) NSString *xxpk_p_sup;
@property(nonatomic, copy) NSString *xxpk_p_amount_text;
@property(nonatomic, copy) NSString *xxpk_p_discount_text;
@property(nonatomic, copy) NSString *xxpk_content_userprotocol;
@property(nonatomic, copy) NSString *xxpk_content_privacypolicy;
@property(nonatomic, copy) NSString *xxpk_realname_tips;
@property(nonatomic, copy) NSString *xxpk_realname_entername;
@property(nonatomic, copy) NSString *xxpk_realname_enternumber;
@property(nonatomic, copy) NSString *xxpk_realname_btn;
@property(nonatomic, copy) NSString *xxpk_realname_sustips;
@property(nonatomic, copy) NSString *xxpk_saveps_tips;
@property(nonatomic, copy) NSString *xxpk_saveps_btn;
@property(nonatomic, copy) NSString *xxpk_saveps_btn_cancel;
@property(nonatomic, copy) NSString *xxpk_saveps_box;
@property(nonatomic, copy) NSString *xxpk_saveps_key;
@property(nonatomic, copy) NSString *xxpk_saveps_toast;
@property(nonatomic, copy) NSString *xxpk_saveps_error;
@property(nonatomic, copy) NSString *xxpk_floatview_hintlabel;
@property(nonatomic, copy) NSString *xxpk_floatview_hidetips;
@property(nonatomic, copy) NSString *xxpk_country_searchBar_placeholder;
@property(nonatomic, copy) NSString *xxpk_service_qq;
@property(nonatomic, copy) NSString *xxpk_service_tel;
@property(nonatomic, copy) NSString *xxpk_service_url;
@property(nonatomic, copy) NSString *xxpk_service_tip_notqq;
@property(nonatomic, copy) NSString *xxpk_service_tip_nottel;
@property(nonatomic, copy) NSString *xxpk_service_tip_noturl;
@property(nonatomic, copy) NSString *xxpk_contenttext_loaderror;
@property(nonatomic, copy) NSString *xxpk_contenttext_analysiserror;


@end

NS_ASSUME_NONNULL_END
