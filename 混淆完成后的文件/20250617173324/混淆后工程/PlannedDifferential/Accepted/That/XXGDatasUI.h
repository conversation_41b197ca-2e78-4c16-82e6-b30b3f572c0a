






#import "XXGDatasModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface XXGDatasUI : XXGDatasModel

@property(nonatomic, copy) NSString *xxpk_ui_base_btn_back;
@property(nonatomic, copy) NSString *xxpk_ui_base_btn_close;
@property(nonatomic, copy) NSString *xxpk_ui_base_keyboardShimWin;
@property(nonatomic, copy) NSString *xxpk_ui_FLOAT_CENTER;
@property(nonatomic, copy) NSString *xxpk_ui_action;
@property(nonatomic, copy) NSString *xxpk_ui_show;
@property(nonatomic, copy) NSString *xxpk_ui_hide;
@property(nonatomic, copy) NSString *xxpk_ui_show_once;
@property(nonatomic, copy) NSString *xxpk_ui_url;
@property(nonatomic, copy) NSString *xxpk_ui_boxName;
@property(nonatomic, copy) NSString *xxpk_ui_boxKey;
@property(nonatomic, copy) NSString *xxpk_ui_appinfo_copytip;
@property(nonatomic, copy) NSString *xxpk_ui_appinfo_info;
@property(nonatomic, copy) NSString *xxpk_ui_appinfo_server;
@property(nonatomic, copy) NSString *xxpk_ui_appinfo_body;
@property(nonatomic, copy) NSString *xxpk_ui_appinfo_device;
@property(nonatomic, copy) NSString *xxpk_ui_appinfo_adaption;
@property(nonatomic, copy) NSString *xxpk_ui_appinfo_extra;
@property(nonatomic, copy) NSString *xxpk_ui_service_sysicon_header;
@property(nonatomic, copy) NSString *xxpk_ui_service_sysicon_qq;
@property(nonatomic, copy) NSString *xxpk_ui_service_sysicon_tel;
@property(nonatomic, copy) NSString *xxpk_ui_service_sysicon_url;
@property(nonatomic, copy) NSString *xxpk_ui_service_open_qq;
@property(nonatomic, copy) NSString *xxpk_ui_service_open_tel;
@property(nonatomic, copy) NSString *xxpk_ui_service_version;

@property(nonatomic, copy) NSString *xxpk_code_comein;
@property(nonatomic, copy) NSString *xxpk_code_bind;
@property(nonatomic, copy) NSString *xxpk_code_forget;
@property(nonatomic, copy) NSString *xxpk_code_jia;
@property(nonatomic, copy) NSString *xxpk_wk_abonementjs;
@property(nonatomic, copy) NSString *xxpk_wk_injectionJSString;
@property(nonatomic, copy) NSString *xxpk_wk_touchCallout;
@property(nonatomic, copy) NSString *xxpk_wk_UserSelect;
@property(nonatomic, copy) NSString *xxpk_wk_http;
@property(nonatomic, copy) NSString *xxpk_wk_kds_token;

@property(nonatomic, copy) NSString *xxpk_core_mt_continueOrder;
@property(nonatomic, copy) NSString *xxpk_core_mt_openUserCenterSidebar;
@property(nonatomic, copy) NSString *xxpk_core_mt_coin_p;

@property(nonatomic, copy) NSString *xxpk_p_selcitem_note_b;
@property(nonatomic, copy) NSString *xxpk_p_selcitem_note_bb;

@property(nonatomic, copy) NSString *xxpk_content_txt;
@property(nonatomic, copy) NSString *xxpk_popup_style;
@property(nonatomic, copy) NSString *xxpk_popup_width;
@property(nonatomic, copy) NSString *xxpk_popup_height;
@property(nonatomic, copy) NSString *xxpk_popup_url;
@property(nonatomic, copy) NSString *xxpk_popup_close_button;
@property(nonatomic, copy) NSString *xxpk_popup_shade_close;
@property(nonatomic, copy) NSString *xxpk_popup_is_alpha;

@property(nonatomic, copy) NSString *xxpk_func_guestBtnDidClick;
@property(nonatomic, copy) NSString *xxpk_func_mobileBtnDidClick;
@property(nonatomic, copy) NSString *xxpk_func_registerBtnDidClick;
@property(nonatomic, copy) NSString *xxpk_func_vxBtnDidClick;
@property(nonatomic, copy) NSString *xxpk_func_oneClickBtnDidClick;
@property(nonatomic, copy) NSString *xxpk_func_vkBtnDidClick;
@property(nonatomic, copy) NSString *xxpk_func_facebookBtnDidClick;
@property(nonatomic, copy) NSString *xxpk_func_poopoBtnDidClick;


@property(nonatomic, copy) NSString *xxpk_textColor;
@property(nonatomic, copy) NSString *xxpk_mainColor;
@property(nonatomic, copy) NSString *xxpk_backgroundColor;

@property(nonatomic, copy) NSString *xxpk_F5F5F5;

@property(nonatomic, copy) NSString *xxpk_E1F5FE;
@property(nonatomic, copy) NSString *xxpk_333333;
@property(nonatomic, copy) NSString *xxpk_666666;

@property(nonatomic, assign) CGFloat xxpk_ucenterW;
@property(nonatomic, assign) CGFloat xxpk_ppselecteWidth;
@property(nonatomic, assign) CGFloat xxpk_float09;
@property(nonatomic, assign) CGFloat xxpk_float045;
@property(nonatomic, assign) CGFloat xxpk_float1;
@property(nonatomic, assign) CGFloat xxpk_float2;
@property(nonatomic, assign) CGFloat xxpk_float3;
@property(nonatomic, assign) CGFloat xxpk_float4;
@property(nonatomic, assign) CGFloat xxpk_float5;
@property(nonatomic, assign) CGFloat xxpk_float6;
@property(nonatomic, assign) CGFloat xxpk_float7;
@property(nonatomic, assign) CGFloat xxpk_float8;
@property(nonatomic, assign) CGFloat xxpk_float9;
@property(nonatomic, assign) CGFloat xxpk_float10;
@property(nonatomic, assign) CGFloat xxpk_float12;
@property(nonatomic, assign) CGFloat xxpk_float13;
@property(nonatomic, assign) CGFloat xxpk_float14;
@property(nonatomic, assign) CGFloat xxpk_float15;
@property(nonatomic, assign) CGFloat xxpk_float16;
@property(nonatomic, assign) CGFloat xxpk_float17;
@property(nonatomic, assign) CGFloat xxpk_float18;
@property(nonatomic, assign) CGFloat xxpk_float20;
@property(nonatomic, assign) CGFloat xxpk_float22;
@property(nonatomic, assign) CGFloat xxpk_float24;

@property(nonatomic, assign) CGFloat xxpk_float25;
@property(nonatomic, assign) CGFloat xxpk_float26;
@property(nonatomic, assign) CGFloat xxpk_float28;
@property(nonatomic, assign) CGFloat xxpk_float30;
@property(nonatomic, assign) CGFloat xxpk_float35;
@property(nonatomic, assign) CGFloat xxpk_float36;

@property(nonatomic, assign) CGFloat xxpk_float38;
@property(nonatomic, assign) CGFloat xxpk_float40;
@property(nonatomic, assign) CGFloat xxpk_float43;
@property(nonatomic, assign) CGFloat xxpk_float45;
@property(nonatomic, assign) CGFloat xxpk_float48;
@property(nonatomic, assign) CGFloat xxpk_float52;
@property(nonatomic, assign) CGFloat xxpk_float55;
@property(nonatomic, assign) CGFloat xxpk_float57;
@property(nonatomic, assign) CGFloat xxpk_float60;
@property(nonatomic, assign) CGFloat xxpk_float70;
@property(nonatomic, assign) CGFloat xxpk_float75;
@property(nonatomic, assign) CGFloat xxpk_float120;
@property(nonatomic, assign) CGFloat xxpk_float180;

@end

NS_ASSUME_NONNULL_END
