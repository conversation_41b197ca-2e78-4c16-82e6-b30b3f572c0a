






#import <Foundation/Foundation.h>
#import "XXGLocalizedUI.h"
#import "XXGDatasUI.h"
@import UIKit;

NS_ASSUME_NONNULL_BEGIN

@interface XXGUIDriver : NSObject <UITextFieldDelegate>

@property (nonatomic, strong, class, readonly) XXGLocalizedUI *xxpk_string_ui;
@property (nonatomic, strong, class, readonly) XXGDatasUI *xxpk_data_ui;

+ (NSString *)xxpk_comeinedBoxName;
+ (NSString *)xxpk_comeinedBoxToken;

+ (NSString *)xxpk_docker_image;
+ (CGFloat)xxpk_ucenter_size;
+ (NSString *)xxpk_ucenter_bangs_color;

+ (NSString *)xxpk_contentText1;
+ (NSString *)xxpk_contentText2;

+ (BOOL)xxpk_isComeinOnly;

+ (BOOL)xxpk_closeButtonHidden;

+ (NSArray *)xxpk_boxsFromLocal;

+ (CGSize)xxpk_mainContentViewSize;

+ (UIColor *)xxpk_textColor;

+ (UIColor *)xxpk_mainColor;

+ (UIColor *)xxpk_backgroundColor;

+ (UIView *)xxpk_logoView;

+ (void)xxpk_closeButtonAction;

+ (UILabel *)xxpk_labelNormal:(NSString *)title;

+ (UIButton *)xxpk_buttonNormal:(NSString *)title;

+ (UIButton *)xxpk_buttonMainColor:(NSString *)title;

+ (NSArray *)xxpk_comeinBtnsWithTarget:(id)target action:(SEL)action;

+ (UITextField *)xxpk_textFieldOfVerificationCode;

+ (UITextField *)xxpk_textFieldOfMobile;

+ (UITextField *)xxpk_textFieldOfAccount;

+ (UITextField *)xxpk_textFieldOfPassword:(BOOL)isNew;

+ (UITextField *)__xxpk_textField:(NSString *)placeholder isSecure:(BOOL)isSecure;
@end

NS_ASSUME_NONNULL_END
