






#import "XXGUIKit.h"
#import "XXGWindowManager.h"
#import "XXGComeinViewController.h"
#import "XXGNavigationController.h"
#import "XXGBaseViewController.h"
#import "XXGSelectAccountViewController.h"
#import "XXGPopupViewController.h"
#import "XXGChangeViewController.h"
#import "XXGBindMobileViewController.h"
#import "XXGUCenterViewController.h"
#import "XXGSelectPPViewController.h"
#import "XXGSaveNamePSViewController.h"

@implementation XXGUIKit
+ (void)__xxpk_showNoStackViewControllerWithType:(XXGShowViewControllerType)type xxpk_object:(id)object xxpk_delegate:(id<XXGUIkitDelegate>)xxpk_delegate {
    XXGNavigationController *nav = [self __xxpk_getShowVCWithType:type xxpk_object:object xxpk_delegate:xxpk_delegate];
    [[XXGWindowManager shared] __xxpk_showNoStackWindowWithRootViewController:nav];
}

+ (void)xxpk_showViewControllerWithType:(XXGShowViewControllerType)type xxpk_delegate:(id<XXGUIkitDelegate>)xxpk_delegate {
    [self xxpk_showViewControllerWithType:type xxpk_object:nil xxpk_delegate:xxpk_delegate];
}
+ (void)xxpk_showViewControllerWithType:(XXGShowViewControllerType)type xxpk_object:(id)xxpk_object xxpk_delegate:(id<XXGUIkitDelegate> _Nullable)xxpk_delegate {
    XXGNavigationController *nav = [self __xxpk_getShowVCWithType:type xxpk_object:xxpk_object xxpk_delegate:xxpk_delegate];
    [[XXGWindowManager shared] xxpk_showWindowWithRootViewController:nav];
}

+ (XXGNavigationController *)__xxpk_getShowVCWithType:(XXGShowViewControllerType)type xxpk_object:(id)xxpk_object xxpk_delegate:(id<XXGUIkitDelegate> _Nullable)xxpk_delegate {
    XXGBaseViewController *vc = nil;
    switch (type) {
        case XXGShowViewControllerTypeComein:
            vc = [[XXGComeinViewController alloc] init];
            break;
        case XXGShowViewControllerTypeSelectAccount:
            vc = [XXGSelectAccountViewController new];
            break;
        case XXGShowViewControllerTypeBindMobile:
            vc = [XXGBindMobileViewController new];
            break;
        case XXGShowViewControllerTypeUserCenter:
            vc = [XXGUCenterViewController new];
            break;
        case XXGShowViewControllerTypeSelectPP:
            vc = [XXGSelectPPViewController new];
            break;
        case XXGShowViewControllerTypeChangePassword:
            vc = [XXGChangeViewController new];
            break;
        case XXGShowViewControllerTypePopup:
            vc = [XXGPopupViewController new];
            break;
        case XXGShowViewControllerTypeSavePS:
            vc = [XXGSaveNamePSViewController new];
            break;

    }
    vc.xxpk_delegate = xxpk_delegate;
    vc.xxpk_object = xxpk_object;
    XXGNavigationController *nav = [[XXGNavigationController alloc] initWithRootViewController:vc];
    return nav;
}

+ (UIWindow *)xxpk_currentWindow {
    return XXGWindowManager.shared.xxpk_currentWindow;
}

+ (void)xxpk_dissmissCurrentWinow {
    [[XXGWindowManager shared] xxpk_dismissWindow];
}

+ (void)xxpk_dissmissAllWindows {
    [[XXGWindowManager shared] xxpk_dismissAllWindows];
}
@end
