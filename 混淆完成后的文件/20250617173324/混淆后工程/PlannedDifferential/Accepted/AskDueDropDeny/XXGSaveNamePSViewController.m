






#import "XXGSaveNamePSViewController.h"
#import "XXGToast.h"
#import "XXGAppInfo.h"

@interface XXGSaveNamePSViewController ()

@property (nonatomic, strong) UIImageView *xxpk_iconView;
@property (nonatomic, strong) UIButton *xxpk_cancelButton;
@property (nonatomic, strong) UIView *xxpk_logoView;
@property (nonatomic, strong) UILabel *xxpk_tipLabel;
@property (nonatomic, strong) UITextField *xxpk_accountTextField;
@property (nonatomic, strong) UITextField *xxpk_passwordTextField;
@end

@implementation XXGSaveNamePSViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.xxpk_closeButton.hidden = YES;
    
    if ([XXGAppInfo xxpk_getAppIconImage]) {
        self.xxpk_iconView = [[UIImageView alloc] initWithImage:[XXGAppInfo xxpk_getAppIconImage]];
        [self.view addSubview:self.xxpk_iconView];
        self.xxpk_iconView.hidden = YES;
        [self.xxpk_iconView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float40);
            make.left.equalTo(self.xxpk_backButton.mas_right);
            make.top.equalTo(self.view).offset(XXGUIDriver.xxpk_data_ui.xxpk_float10);
        }];
    }
    
    self.xxpk_logoView = [XXGUIDriver xxpk_logoView];
    self.xxpk_logoView.hidden = YES;
    [self.view addSubview:self.xxpk_logoView];
    [self.xxpk_logoView mas_makeConstraints:^(MASConstraintMaker *make) {
        if ([XXGAppInfo xxpk_getAppIconImage]) {
            make.centerY.equalTo(self.xxpk_iconView);
            make.left.equalTo(self.xxpk_iconView.mas_right).offset(XXGUIDriver.xxpk_data_ui.xxpk_float8);
        }else {
            make.top.equalTo(self.view).offset(XXGUIDriver.xxpk_data_ui.xxpk_float10);
            make.left.equalTo(self.xxpk_backButton.mas_right).offset(XXGUIDriver.xxpk_data_ui.xxpk_float8);
        }
        make.right.equalTo(self.xxpk_closeButton.mas_left);
        make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float40);
    }];
    
    self.xxpk_tipLabel = [XXGUIDriver xxpk_labelNormal:XXGUIDriver.xxpk_string_ui.xxpk_saveps_tips];
    self.xxpk_tipLabel.numberOfLines = 0;
    [self.view addSubview:self.xxpk_tipLabel];
    [self.xxpk_tipLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float26);
        make.left.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float30);
        make.right.mas_equalTo(-XXGUIDriver.xxpk_data_ui.xxpk_float30);
    }];
    
    
    self.xxpk_accountTextField = [XXGUIDriver xxpk_textFieldOfAccount];
    self.xxpk_accountTextField.enabled = NO;
    self.xxpk_accountTextField.text = self.xxpk_object[XXGUIDriver.xxpk_data_ui.xxpk_ui_boxName];
    [self __xxpk_setTextFieldLeftView:self.xxpk_accountTextField text:XXGUIDriver.xxpk_string_ui.xxpk_saveps_box];
    [self.view addSubview:self.xxpk_accountTextField];
    [self.xxpk_accountTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.xxpk_tipLabel.mas_bottom).offset(XXGUIDriver.xxpk_data_ui.xxpk_float12);
        make.left.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float25);
        make.right.mas_equalTo(-XXGUIDriver.xxpk_data_ui.xxpk_float25);
        make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float38);
    }];
    
    
    self.xxpk_passwordTextField = [XXGUIDriver xxpk_textFieldOfAccount];
    self.xxpk_passwordTextField.enabled = NO;
    self.xxpk_passwordTextField.text = self.xxpk_object[XXGUIDriver.xxpk_data_ui.xxpk_ui_boxKey];
    [self __xxpk_setTextFieldLeftView:self.xxpk_passwordTextField text:XXGUIDriver.xxpk_string_ui.xxpk_saveps_key];
    [self.view addSubview:self.xxpk_passwordTextField];
    [self.xxpk_passwordTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.xxpk_accountTextField.mas_bottom).offset(XXGUIDriver.xxpk_data_ui.xxpk_float12);
        make.left.right.equalTo(self.xxpk_accountTextField);
        make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float38);
    }];
    
    
    UIButton *xxpk_saveButton = [XXGUIDriver xxpk_buttonMainColor:XXGUIDriver.xxpk_string_ui.xxpk_saveps_btn];
    [xxpk_saveButton addTarget:self action:@selector(xxpk_saveButtonAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:xxpk_saveButton];
    [xxpk_saveButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.xxpk_passwordTextField.mas_bottom).offset(XXGUIDriver.xxpk_data_ui.xxpk_float12);
        make.left.right.equalTo(self.xxpk_passwordTextField);
        make.height.mas_equalTo(XXGUIDriver.xxpk_data_ui.xxpk_float40);
    }];
    
    
    self.xxpk_cancelButton = [XXGUIDriver xxpk_buttonNormal:XXGUIDriver.xxpk_string_ui.xxpk_saveps_btn_cancel];
    [self.xxpk_cancelButton setContentEdgeInsets:UIEdgeInsetsMake(0, 0, 0, 0)];
    [self.xxpk_cancelButton addTarget:self action:@selector(xxpk_cancelButtonAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.xxpk_cancelButton];
    [self.xxpk_cancelButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(xxpk_saveButton.mas_bottom).offset(XXGUIDriver.xxpk_data_ui.xxpk_float3);
        make.centerX.equalTo(self.view);
    }];
}

- (void)__xxpk_setTextFieldLeftView:(UITextField *)textField text:(NSString *)text
{
    CGRect frame = {{0,0},CGSizeMake(XXGUIDriver.xxpk_data_ui.xxpk_float40, XXGUIDriver.xxpk_data_ui.xxpk_float38)};
    UILabel *leftview = [[UILabel alloc] initWithFrame:frame];
    leftview.text = text;
    leftview.textColor = UIColor.redColor;
    textField.leftViewMode = UITextFieldViewModeAlways;
    textField.leftView = leftview;
}

- (void)xxpk_cancelButtonAction:(UIButton *)sender {
    [[XXGWindowManager shared] xxpk_dismissWindowWithRootViewController:self.navigationController];
}

- (void)xxpk_saveButtonAction:(UIButton *)sender {
    sender.hidden = YES;
    self.xxpk_tipLabel.hidden = YES;
    self.xxpk_logoView.hidden = NO;
    self.xxpk_iconView.hidden = NO;
    self.xxpk_cancelButton.hidden = YES;
    
    [self.view mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.center.mas_equalTo(self.view.superview);
        make.size.mas_equalTo(CGSizeMake(XXGUIDriver.xxpk_data_ui.xxpk_contentSizeWidth, XXGUIDriver.xxpk_data_ui.xxpk_contentSizeHeight-XXGUIDriver.xxpk_data_ui.xxpk_float20));
    }];
    [self.xxpk_accountTextField mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.xxpk_logoView.mas_bottom).offset(XXGUIDriver.xxpk_data_ui.xxpk_float12);
    }];
    [self.view layoutIfNeeded];
    
    BOOL isCan = [[[[NSBundle mainBundle] infoDictionary] allKeys] containsObject:XXGUIDriver.xxpk_data_ui.xxpk_tools_photolibaddusgaedes];
    if (!isCan) {
        self.xxpk_closeButton.hidden = NO;
        [XXGAlertView xxpk_showAlertWithTitle:nil message:XXGUIDriver.xxpk_string_ui.xxpk_saveps_error completion:nil];
        return;
    }
    CGSize size = self.view.frame.size;
    size.height -= XXGUIDriver.xxpk_data_ui.xxpk_float43;
    UIGraphicsBeginImageContextWithOptions(size, NO, 0.0);
    [self.view.layer renderInContext:UIGraphicsGetCurrentContext()];
    UIImage *image = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    
    UIImageWriteToSavedPhotosAlbum(image, self, @selector(image:didFinishSavingWithError:contextInfo:), (__bridge void *)self);
}

- (void)image:(UIImage *)image didFinishSavingWithError:(NSError *)error contextInfo:(void *)contextInfo
{
    
    if(!error){
        [[XXGWindowManager shared] xxpk_dismissWindowWithRootViewController:self.navigationController];
        [XXGToast showTop:XXGUIDriver.xxpk_string_ui.xxpk_saveps_toast];
    }else {
        self.xxpk_closeButton.hidden = NO;
        [XXGAlertView xxpk_showAlertWithTitle:nil message:XXGUIDriver.xxpk_string_ui.xxpk_saveps_error completion:nil];
    }
}

@end
