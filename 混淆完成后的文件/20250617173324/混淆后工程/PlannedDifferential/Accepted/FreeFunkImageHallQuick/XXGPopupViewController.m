






#import "XXGPopupViewController.h"
#import "XXGWindowManager.h"

@interface XXGPopupViewController ()

@end

@implementation XXGPopupViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.xxpk_wkview.opaque = NO;
    if ([self __xxpk_isValidUrl:self.xxpk_object]) {
        NSMutableDictionary *dic = [NSMutableDictionary new];
        dic[XXGUIDriver.xxpk_data_ui.xxpk_popup_style] = @{
            XXGUIDriver.xxpk_data_ui.xxpk_popup_width:@(MAXFLOAT),
            XXGUIDriver.xxpk_data_ui.xxpk_popup_height:@(MAXFLOAT)
        };
        dic[XXGUIDriver.xxpk_data_ui.xxpk_popup_url] = self.xxpk_object;
        dic[XXGUIDriver.xxpk_data_ui.xxpk_popup_close_button] = @(NO);
        dic[XXGUIDriver.xxpk_data_ui.xxpk_popup_shade_close] = @(NO);
        self.xxpk_object = dic;
    }
    
    if (![self.xxpk_object[XXGUIDriver.xxpk_data_ui.xxpk_popup_is_alpha] boolValue]) {
        self.view.backgroundColor = UIColor.blackColor;
    }else {
        self.view.backgroundColor = [UIColor.blackColor colorWithAlphaComponent:0];
    }
    self.xxpk_router = self.xxpk_object[XXGUIDriver.xxpk_data_ui.xxpk_popup_url];
    
}

- (BOOL)__xxpk_isValidUrl:(NSString *)url
{
    if (![url isKindOfClass:[NSString class]]) {
        return NO;
    }
    NSString *regex =@"[a-zA-z]+://[^\\s]*";
    NSPredicate *urlTest = [NSPredicate predicateWithFormat:@"SELF MATCHES %@",regex];
    return [urlTest evaluateWithObject:url];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    if ([self.xxpk_object[XXGUIDriver.xxpk_data_ui.xxpk_popup_close_button] boolValue]) {
        self.xxpk_closeButton.hidden = NO;
        [self.view bringSubviewToFront:self.xxpk_closeButton];
    }
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    CGFloat width = [self.xxpk_object[XXGUIDriver.xxpk_data_ui.xxpk_popup_style][XXGUIDriver.xxpk_data_ui.xxpk_popup_width] floatValue];
    CGFloat height = [self.xxpk_object[XXGUIDriver.xxpk_data_ui.xxpk_popup_style][XXGUIDriver.xxpk_data_ui.xxpk_popup_height] floatValue];
    CGFloat ScreenW = [UIScreen mainScreen].bounds.size.width;
    CGFloat ScreenH = [UIScreen mainScreen].bounds.size.height;
    CGFloat makewidth = width == 0 ? ScreenW : MIN(width, ScreenW);
    CGFloat makeheight = height == 0 ? ScreenH : MIN(height, ScreenH);
    [self.view mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.center.mas_equalTo(0);
        make.size.mas_equalTo(CGSizeMake(makewidth, makeheight));
    }];
    if (ScreenW == makewidth && ScreenH == makeheight) {
        UIWindow *currentWindow = [[XXGWindowManager shared] xxpk_currentWindow];
        UIEdgeInsets safe = currentWindow.safeAreaInsets;
        [self.xxpk_wkview mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.mas_equalTo(safe);
        }];
    }else {
        [self.xxpk_wkview mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.mas_equalTo(UIEdgeInsetsMake(0, 0, 0, 0));
        }];
    }
}


- (void)webView:(WKWebView *)webView didFailProvisionalNavigation:(WKNavigation *)navigation {
    [super webView:webView didFailProvisionalNavigation:navigation];
    self.xxpk_closeButton.hidden = NO;
}

- (void)xxpk_makeMethodActionOfAPPScheme:(NSURL *)URL {
    [super xxpk_makeMethodActionOfAPPScheme:URL];
    
    
    
    
    void (^completionBlock)(BOOL) = self.xxpk_object[XXGUIDriver.xxpk_data_ui.xxpk_p_block_orderExtra];
    if (completionBlock) {
        [[XXGWindowManager shared] xxpk_dismissWindow];
        completionBlock([URL.host isEqualToString:XXGUIDriver.xxpk_data_ui.xxpk_core_mt_continueOrder]);
    }
}

- (void)xxpk_touchesBlank:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    [super xxpk_touchesBlank:touches withEvent:event];
    if ([self.xxpk_object[XXGUIDriver.xxpk_data_ui.xxpk_popup_shade_close] boolValue]) {
        [self xxpk_closeButtonAction:nil];
    }
}
@end
