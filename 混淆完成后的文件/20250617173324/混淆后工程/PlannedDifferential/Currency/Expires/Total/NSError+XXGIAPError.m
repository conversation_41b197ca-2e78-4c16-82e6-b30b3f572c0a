







#import "NSError+XXGIAPError.h"
#import "XXGPlayKitConfig.h"

@implementation NSError (XXGIAPError)
+ (instancetype)errorWithXXGIAPCode:(XXGIAPErrorCode)code{
    NSString *msg = @"";
    switch (code) {
        case XXGIAPErrorCodePaying:
            msg  = __string_core.xxpk_tool_iap_error_paying;
            break;
        case XXGIAPErrorCodeParameter:
            msg  = __string_core.xxpk_tool_iap_error_params;
            break;
        case XXGIAPErrorCodePermission:
            msg  = __string_core.xxpk_tool_iap_error_permission;
            break;
        case XXGIAPErrorCodeProductId:
            msg  = __string_core.xxpk_tool_iap_error_productcode;
            break;
        case XXGIAPErrorCodeReceipt:
            msg  = __string_core.xxpk_tool_iap_error_receipt;
            break;
        case XXGIAPErrorCodeVerifyInvalid:
            msg  = __string_core.xxpk_tool_iap_error_verifyinvalid;
            break;
        case XXGIAPErrorCodeNet:
            msg  = __string_core.xxpk_tool_iap_error_net;
            break;
        case XXGIAPErrorCodeNotRegistered:
            msg  = __string_core.xxpk_tool_iap_error_notregistered;
            break;
        case XXGIAPErrorCodeHasUnfinishedTransaction:
            msg  = __string_core.xxpk_tool_iap_error_hasunfinished;
            break;
    }
    NSError *error = [NSError errorWithDomain:__data_core.xxpk_tools_iap_domain code:code userInfo:@{NSLocalizedDescriptionKey:msg}];
    return  error;
}
@end
