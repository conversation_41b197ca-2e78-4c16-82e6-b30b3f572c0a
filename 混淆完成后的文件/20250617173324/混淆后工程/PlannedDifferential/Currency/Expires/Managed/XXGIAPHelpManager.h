







#import <UIKit/UiKit.h>
#import "XXGIAPPayProtocol.h"
#import "XXGIAPTransactionModel.h"
#import <StoreKit/StoreKit.h>

NS_ASSUME_NONNULL_BEGIN

@class SKProduct;
@interface XXGIAPHelpManager : NSObject



@property (nonatomic,weak)id<XXGIAPPayDelegate> delegate;



+ (instancetype)sharedManager;



- (void)registerPay;



- (void)registerPayWithKeychainService:(NSString *)keychainService
              keychainAccount:(NSString *)keychainAccount;



- (void)buyProductWithUserID:(NSString *)userid
           productIdentifier:(NSString *)productIdentifier
                xxpk_orderId:(NSString *)xxpk_orderId;



- (void)buyProductWithSKPayment:(SKPayment  *)payment;


- (void)fetchProductInfoWithProductIdentifier:(NSString *)productIdentifier;



- (void)restoreProducts;


-(void)checkUnfinishTransaction;

- (NSArray *)getUnfinishTransactions;





- (void)resetKeychainService:( NSString *_Nullable)keychainService
             keychainAccount:( NSString *_Nullable)keychainAccount XXGIAPTransactionModels:(NSArray<XXGIAPTransactionModel *>*)models;




- (void)cleanAllModels;
@end

NS_ASSUME_NONNULL_END
