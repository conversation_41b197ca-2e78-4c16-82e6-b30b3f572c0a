


#import "SDWebImageCompat.h"

@class SDImageCacheConfig;


@protocol SDMemoryCache <NSObject>

@required



- (nonnull instancetype)initWithConfig:(nonnull SDImageCacheConfig *)config;



- (nullable id)objectForKey:(nonnull id)key;



- (void)setObject:(nullable id)object forKey:(nonnull id)key;



- (void)setObject:(nullable id)object forKey:(nonnull id)key cost:(NSUInteger)cost;



- (void)removeObjectForKey:(nonnull id)key;



- (void)removeAllObjects;

@end



@interface SDMemoryCache <KeyType, ObjectType> : NSCache <KeyType, ObjectType> <SDMemoryCache>

@property (nonatomic, strong, nonnull, readonly) SDImageCacheConfig *config;

@end
