


#import <Foundation/Foundation.h>
#import "SDWebImageCompat.h"
#import "SDWebImageDefine.h"
#import "SDImageCacheConfig.h"
#import "SDImageCacheDefine.h"
#import "SDMemoryCache.h"
#import "SDDiskCache.h"


typedef NS_OPTIONS(NSUInteger, SDImageCacheOptions) {
    

    SDImageCacheQueryMemoryData = 1 << 0,
    

    SDImageCacheQueryMemoryDataSync = 1 << 1,
    

    SDImageCacheQueryDiskDataSync = 1 << 2,
    

    SDImageCacheScaleDownLargeImages = 1 << 3,
    

    SDImageCacheAvoidDecodeImage API_DEPRECATED("Use SDWebImageContextImageForceDecodePolicy instead", macos(10.10, 10.10), ios(8.0, 8.0), tvos(9.0, 9.0), watchos(2.0, 2.0)) = 1 << 4,
    

    SDImageCacheDecodeFirstFrameOnly = 1 << 5,
    

    SDImageCachePreloadAllFrames = 1 << 6,
    

    SDImageCacheMatchAnimatedImageClass = 1 << 7,
};



@interface SDImageCacheToken : NSObject <SDWebImageOperation>



- (void)cancel;



@property (nonatomic, strong, nullable, readonly) NSString *key;

@end



@interface SDImageCache : NSObject





@property (nonatomic, copy, nonnull, readonly) SDImageCacheConfig *config;



@property (nonatomic, strong, readonly, nonnull) id<SDMemoryCache> memoryCache;



@property (nonatomic, strong, readonly, nonnull) id<SDDiskCache> diskCache;



@property (nonatomic, copy, nonnull, readonly) NSString *diskCachePath;



@property (nonatomic, copy, nullable) SDImageCacheAdditionalCachePathBlock additionalCachePathBlock;





@property (nonatomic, class, readonly, nonnull) SDImageCache *sharedImageCache;



@property (nonatomic, class, readwrite, null_resettable) NSString *defaultDiskCacheDirectory;



- (nonnull instancetype)initWithNamespace:(nonnull NSString *)ns;



- (nonnull instancetype)initWithNamespace:(nonnull NSString *)ns
                       diskCacheDirectory:(nullable NSString *)directory;



- (nonnull instancetype)initWithNamespace:(nonnull NSString *)ns
                       diskCacheDirectory:(nullable NSString *)directory
                                   config:(nullable SDImageCacheConfig *)config NS_DESIGNATED_INITIALIZER;





- (nullable NSString *)cachePathForKey:(nullable NSString *)key;





- (void)storeImage:(nullable UIImage *)image
            forKey:(nullable NSString *)key
        completion:(nullable SDWebImageNoParamsBlock)completionBlock;



- (void)storeImage:(nullable UIImage *)image
            forKey:(nullable NSString *)key
            toDisk:(BOOL)toDisk
        completion:(nullable SDWebImageNoParamsBlock)completionBlock;



- (void)storeImageData:(nullable NSData *)imageData
                forKey:(nullable NSString *)key
            completion:(nullable SDWebImageNoParamsBlock)completionBlock;



- (void)storeImage:(nullable UIImage *)image
         imageData:(nullable NSData *)imageData
            forKey:(nullable NSString *)key
            toDisk:(BOOL)toDisk
        completion:(nullable SDWebImageNoParamsBlock)completionBlock;



- (void)storeImage:(nullable UIImage *)image
         imageData:(nullable NSData *)imageData
            forKey:(nullable NSString *)key
           options:(SDWebImageOptions)options
           context:(nullable SDWebImageContext *)context
         cacheType:(SDImageCacheType)cacheType
        completion:(nullable SDWebImageNoParamsBlock)completionBlock;



- (void)storeImageToMemory:(nullable UIImage*)image
                    forKey:(nullable NSString *)key;



- (void)storeImageDataToDisk:(nullable NSData *)imageData
                      forKey:(nullable NSString *)key;






- (void)diskImageExistsWithKey:(nullable NSString *)key completion:(nullable SDImageCacheCheckCompletionBlock)completionBlock;



- (BOOL)diskImageDataExistsWithKey:(nullable NSString *)key;





- (nullable NSData *)diskImageDataForKey:(nullable NSString *)key;



- (void)diskImageDataQueryForKey:(nullable NSString *)key completion:(nullable SDImageCacheQueryDataCompletionBlock)completionBlock;



- (nullable SDImageCacheToken *)queryCacheOperationForKey:(nullable NSString *)key done:(nullable SDImageCacheQueryCompletionBlock)doneBlock;



- (nullable SDImageCacheToken *)queryCacheOperationForKey:(nullable NSString *)key options:(SDImageCacheOptions)options done:(nullable SDImageCacheQueryCompletionBlock)doneBlock;



- (nullable SDImageCacheToken *)queryCacheOperationForKey:(nullable NSString *)key options:(SDImageCacheOptions)options context:(nullable SDWebImageContext *)context done:(nullable SDImageCacheQueryCompletionBlock)doneBlock;



- (nullable SDImageCacheToken *)queryCacheOperationForKey:(nullable NSString *)key options:(SDImageCacheOptions)options context:(nullable SDWebImageContext *)context cacheType:(SDImageCacheType)queryCacheType done:(nullable SDImageCacheQueryCompletionBlock)doneBlock;



- (nullable UIImage *)imageFromMemoryCacheForKey:(nullable NSString *)key;



- (nullable UIImage *)imageFromDiskCacheForKey:(nullable NSString *)key;



- (nullable UIImage *)imageFromDiskCacheForKey:(nullable NSString *)key options:(SDImageCacheOptions)options context:(nullable SDWebImageContext *)context;



- (nullable UIImage *)imageFromCacheForKey:(nullable NSString *)key;



- (nullable UIImage *)imageFromCacheForKey:(nullable NSString *)key options:(SDImageCacheOptions)options context:(nullable SDWebImageContext *)context;





- (void)removeImageForKey:(nullable NSString *)key withCompletion:(nullable SDWebImageNoParamsBlock)completion;



- (void)removeImageForKey:(nullable NSString *)key fromDisk:(BOOL)fromDisk withCompletion:(nullable SDWebImageNoParamsBlock)completion;



- (void)removeImageFromMemoryForKey:(nullable NSString *)key;



- (void)removeImageFromDiskForKey:(nullable NSString *)key;





- (void)clearMemory;



- (void)clearDiskOnCompletion:(nullable SDWebImageNoParamsBlock)completion;



- (void)deleteOldFilesWithCompletionBlock:(nullable SDWebImageNoParamsBlock)completionBlock;





- (NSUInteger)totalDiskSize;



- (NSUInteger)totalDiskCount;



- (void)calculateSizeWithCompletionBlock:(nullable SDImageCacheCalculateSizeBlock)completionBlock;

@end



@interface SDImageCache (SDImageCache) <SDImageCache>

@end
