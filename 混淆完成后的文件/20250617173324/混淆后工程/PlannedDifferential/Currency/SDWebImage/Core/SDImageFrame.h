


#import <Foundation/Foundation.h>
#import "SDWebImageCompat.h"



@interface SDImageFrame : NSObject



@property (nonatomic, strong, readonly, nonnull) UIImage *image;


@property (nonatomic, readonly, assign) NSTimeInterval duration;




- (nonnull instancetype)initWithImage:(nonnull UIImage *)image duration:(NSTimeInterval)duration;



+ (nonnull instancetype)frameWithImage:(nonnull UIImage *)image duration:(NSTimeInterval)duration;

- (nonnull instancetype)init NS_UNAVAILABLE;
+ (nonnull instancetype)new  NS_UNAVAILABLE;

@end
