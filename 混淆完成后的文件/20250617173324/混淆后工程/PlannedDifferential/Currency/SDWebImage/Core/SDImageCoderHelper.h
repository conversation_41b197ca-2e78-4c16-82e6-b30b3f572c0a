


#import <ImageIO/ImageIO.h>
#import "SDWebImageCompat.h"
#import "SDImageFrame.h"


typedef NS_ENUM(NSUInteger, SDImageCoderDecodeSolution) {
    
    SDImageCoderDecodeSolutionAutomatic,
    
    SDImageCoderDecodeSolutionCoreGraphics,
    
    SDImageCoderDecodeSolutionUIKit
};





typedef NS_ENUM(NSUInteger, SDImageForceDecodePolicy) {
    
    SDImageForceDecodePolicyAutomatic,
    
    SDImageForceDecodePolicyNever,
    
    SDImageForceDecodePolicyAlways
};



typedef NS_ENUM(NSUInteger, SDImageHDRType) {
    
    SDImageHDRTypeSDR = 0,
    
    SDImageHDRTypeISOHDR = 1,
    
    SDImageHDRTypeISOGainMap = 2,
};





static inline size_t SDByteAlign(size_t size, size_t alignment) {
    return ((size + (alignment - 1)) / alignment) * alignment;
}


typedef struct SDImagePixelFormat {
    
    CGBitmapInfo bitmapInfo;
    
    size_t alignment;
} SDImagePixelFormat;



@interface SDImageCoderHelper : NSObject



+ (UIImage * _Nullable)animatedImageWithFrames:(NSArray<SDImageFrame *> * _Nullable)frames;



+ (NSArray<SDImageFrame *> * _Nullable)framesFromAnimatedImage:(UIImage * _Nullable)animatedImage NS_SWIFT_NAME(frames(from:));






+ (CGColorSpaceRef _Nonnull)colorSpaceGetDeviceRGB CF_RETURNS_NOT_RETAINED;



+ (SDImagePixelFormat)preferredPixelFormat:(BOOL)containsAlpha;



+ (BOOL)CGImageIsHardwareSupported:(_Nonnull CGImageRef)cgImage;



+ (BOOL)CGImageContainsAlpha:(_Nonnull CGImageRef)cgImage;



+ (BOOL)CGImageIsLazy:(_Nonnull CGImageRef)cgImage;



+ (BOOL)CGImageIsHDR:(_Nonnull CGImageRef)cgImage;



+ (CGImageRef _Nullable)CGImageCreateDecoded:(_Nonnull CGImageRef)cgImage CF_RETURNS_RETAINED;



+ (CGImageRef _Nullable)CGImageCreateDecoded:(_Nonnull CGImageRef)cgImage orientation:(CGImagePropertyOrientation)orientation CF_RETURNS_RETAINED;



+ (CGImageRef _Nullable)CGImageCreateScaled:(_Nonnull CGImageRef)cgImage size:(CGSize)size CF_RETURNS_RETAINED;



+ (CGSize)scaledSizeWithImageSize:(CGSize)imageSize scaleSize:(CGSize)scaleSize preserveAspectRatio:(BOOL)preserveAspectRatio shouldScaleUp:(BOOL)shouldScaleUp;






+ (CGSize)scaledSizeWithImageSize:(CGSize)imageSize limitBytes:(NSUInteger)limitBytes bytesPerPixel:(NSUInteger)bytesPerPixel frameCount:(NSUInteger)frameCount;


+ (UIImage * _Nullable)decodedImageWithImage:(UIImage * _Nullable)image;



+ (UIImage * _Nullable)decodedImageWithImage:(UIImage * _Nullable)image policy:(SDImageForceDecodePolicy)policy;



+ (UIImage * _Nullable)decodedAndScaledDownImageWithImage:(UIImage * _Nullable)image limitBytes:(NSUInteger)bytes;



+ (UIImage * _Nullable)decodedAndScaledDownImageWithImage:(UIImage * _Nullable)image limitBytes:(NSUInteger)bytes policy:(SDImageForceDecodePolicy)policy;



@property (class, readwrite) SDImageCoderDecodeSolution defaultDecodeSolution;



@property (class, readwrite) NSUInteger defaultScaleDownLimitBytes;

#if SD_UIKIT || SD_WATCH


+ (UIImageOrientation)imageOrientationFromEXIFOrientation:(CGImagePropertyOrientation)exifOrientation NS_SWIFT_NAME(imageOrientation(from:));



+ (CGImagePropertyOrientation)exifOrientationFromImageOrientation:(UIImageOrientation)imageOrientation;
#endif

@end
