


#import "SDWebImageCompat.h"


FOUNDATION_EXPORT NSErrorDomain const _Nonnull SDWebImageErrorDomain;


FOUNDATION_EXPORT NSErrorUserInfoKey const _Nonnull SDWebImageErrorDownloadResponseKey;

FOUNDATION_EXPORT NSErrorUserInfoKey const _Nonnull SDWebImageErrorDownloadStatusCodeKey;

FOUNDATION_EXPORT NSErrorUserInfoKey const _Nonnull SDWebImageErrorDownloadContentTypeKey;


typedef NS_ERROR_ENUM(SDWebImageErrorDomain, SDWebImageError) {
    SDWebImageErrorInvalidURL = 1000, 
    SDWebImageErrorBadImageData = 1001, 
    SDWebImageErrorCacheNotModified = 1002, 
    SDWebImageErrorBlackListed = 1003, 
    SDWebImageErrorInvalidDownloadOperation = 2000, 
    SDWebImageErrorInvalidDownloadStatusCode = 2001, 
    SDWebImageErrorCancelled = 2002, 
    SDWebImageErrorInvalidDownloadResponse = 2003, 
    SDWebImageErrorInvalidDownloadContentType = 2004, 
};
