


#import <Foundation/Foundation.h>
#import "SDImageCacheDefine.h"


typedef NS_ENUM(NSUInteger, SDImageCachesManagerOperationPolicy) {
    SDImageCachesManagerOperationPolicySerial, 
    SDImageCachesManagerOperationPolicyConcurrent, 
    SDImageCachesManagerOperationPolicyHighestOnly, 
    SDImageCachesManagerOperationPolicyLowestOnly 
};



@interface SDImageCachesManager : NSObject <SDImageCache>



@property (nonatomic, class, readonly, nonnull) SDImageCachesManager *sharedManager;





@property (nonatomic, assign) SDImageCachesManagerOperationPolicy queryOperationPolicy;



@property (nonatomic, assign) SDImageCachesManagerOperationPolicy storeOperationPolicy;



@property (nonatomic, assign) SDImageCachesManagerOperationPolicy removeOperationPolicy;



@property (nonatomic, assign) SDImageCachesManagerOperationPolicy containsOperationPolicy;



@property (nonatomic, assign) SDImageCachesManagerOperationPolicy clearOperationPolicy;



@property (nonatomic, copy, nullable) NSArray<id<SDImageCache>> *caches;



- (void)addCache:(nonnull id<SDImageCache>)cache;



- (void)removeCache:(nonnull id<SDImageCache>)cache;

@end
