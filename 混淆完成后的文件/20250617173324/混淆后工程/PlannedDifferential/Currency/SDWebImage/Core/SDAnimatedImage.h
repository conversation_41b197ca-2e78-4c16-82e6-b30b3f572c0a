


#import "SDWebImageCompat.h"
#import "SDImageCoder.h"




@protocol SDAnimatedImage <SDAnimatedImageProvider>

@required


- (nullable instancetype)initWithData:(nonnull NSData *)data scale:(CGFloat)scale options:(nullable SDImageCoderOptions *)options;



- (nullable instancetype)initWithAnimatedCoder:(nonnull id<SDAnimatedImageCoder>)animatedCoder scale:(CGFloat)scale;

@optional



- (void)preloadAllFrames;



- (void)unloadAllFrames;



@property (nonatomic, assign, readonly, getter=isAllFramesLoaded) BOOL allFramesLoaded;



@property (nonatomic, strong, readonly, nullable) id<SDAnimatedImageCoder> animatedCoder;

@end



NS_SWIFT_NONISOLATED
@interface SDAnimatedImage : UIImage <SDAnimatedImage>





+ (nullable instancetype)imageNamed:(nonnull NSString *)name; 
#if __has_include(<UIKit/UITraitCollection.h>)
+ (nullable instancetype)imageNamed:(nonnull NSString *)name inBundle:(nullable NSBundle *)bundle compatibleWithTraitCollection:(nullable UITraitCollection *)traitCollection; 
#else
+ (nullable instancetype)imageNamed:(nonnull NSString *)name inBundle:(nullable NSBundle *)bundle; 
#endif
+ (nullable instancetype)imageWithContentsOfFile:(nonnull NSString *)path;
+ (nullable instancetype)imageWithData:(nonnull NSData *)data;
+ (nullable instancetype)imageWithData:(nonnull NSData *)data scale:(CGFloat)scale;
- (nullable instancetype)initWithContentsOfFile:(nonnull NSString *)path;
- (nullable instancetype)initWithData:(nonnull NSData *)data;
- (nullable instancetype)initWithData:(nonnull NSData *)data scale:(CGFloat)scale;



@property (nonatomic, assign, readonly) SDImageFormat animatedImageFormat;



@property (nonatomic, copy, readonly, nullable) NSData *animatedImageData;



@property (nonatomic, readonly) CGFloat scale;





- (void)preloadAllFrames;



- (void)unloadAllFrames;


@property (nonatomic, assign, readonly, getter=isAllFramesLoaded) BOOL allFramesLoaded;


@property (nonatomic, strong, readonly, nullable) id<SDAnimatedImageCoder> animatedCoder;

@end
