






#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface XXGLocalizedModel : NSObject

@property(nonatomic, copy) NSString *xxpk_tips;
@property(nonatomic, copy) NSString *xxpk_ok;
@property(nonatomic, copy) NSString *xxpk_cancel;

@property(nonatomic, copy) NSString *xxpk_tool_iap_error_paying;
@property(nonatomic, copy) NSString *xxpk_tool_iap_error_params;
@property(nonatomic, copy) NSString *xxpk_tool_iap_error_permission;
@property(nonatomic, copy) NSString *xxpk_tool_iap_error_productcode;
@property(nonatomic, copy) NSString *xxpk_tool_iap_error_receipt;
@property(nonatomic, copy) NSString *xxpk_tool_iap_error_verifyinvalid;
@property(nonatomic, copy) NSString *xxpk_tool_iap_error_net;
@property(nonatomic, copy) NSString *xxpk_tool_iap_error_notregistered;
@property(nonatomic, copy) NSString *xxpk_tool_iap_error_hasunfinished;
@property(nonatomic, copy) NSString *xxpk_tool_iap_checkingproduct;
@property(nonatomic, copy) NSString *xxpk_tool_iap_paying;
@property(nonatomic, copy) NSString *xxpk_tool_iap_restoring;
@property(nonatomic, copy) NSString *xxpk_tool_iap_verifying;

@end

NS_ASSUME_NONNULL_END
