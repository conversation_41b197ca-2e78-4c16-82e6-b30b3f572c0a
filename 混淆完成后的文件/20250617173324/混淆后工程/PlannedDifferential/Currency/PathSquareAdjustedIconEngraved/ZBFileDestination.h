






#import "ZBBaseDestination.h"

NS_ASSUME_NONNULL_BEGIN

@interface ZBFileDestination : ZBBaseDestination


@property (nonatomic, strong) NSURL *logFileURL;


@property (nonatomic, assign) BOOL syncAfterEachWrite;


@property (nonatomic, assign) NSInteger maxDays;


@property (nonatomic, assign) B<PERSON><PERSON> encryptionEnabled;



- (NSArray<NSURL *> *)allLogFiles;



- (NSString *)readLogFile:(NSURL *)fileURL;



- (NSString *)readAllLogs;



- (NSString *)readAllLogsRaw;



- (NSString *)readLogsForDate:(NSDate *)date;



- (NSArray<NSDate *> *)allLogDates;



- (void)cleanupOldLogs;



- (NSURL *)currentLogFileURL;

@end

NS_ASSUME_NONNULL_END
