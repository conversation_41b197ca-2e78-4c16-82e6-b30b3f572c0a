






#import <Foundation/Foundation.h>

@class ZBBaseDestination;;

NS_ASSUME_NONNULL_BEGIN



typedef NS_OPTIONS(NSUInteger, ZBLogFlag){
    

    ZBLogFlagError      = (1 << 0),

    

    ZBLogFlagWarning    = (1 << 1),

    

    ZBLogFlagInfo       = (1 << 2),

    

    ZBLogFlagDebug      = (1 << 3),

    

    ZBLogFlagVerbose    = (1 << 4)
};



typedef NS_ENUM(NSUInteger, ZBLogLevel){
    

    ZBLogLevelOff       = 0,

    

    ZBLogLevelError     = (ZBLogFlagError),

    

    ZBLogLevelWarning   = (ZBLogLevelError   | ZBLogFlagWarning),

    

    ZBLogLevelInfo      = (ZBLogLevelWarning | ZBLogFlagInfo),

    

    ZBLogLevelDebug     = (ZBLogLevelInfo    | ZBLogFlagDebug),

    

    ZBLogLevelVerbose   = (ZBLogLevelDebug   | ZBLogFlagVerbose),

    

    ZBLogLevelAll       = NSUIntegerMax
};

@interface ZBLog : NSObject



@property (class, nonatomic, strong, readonly) ZBLog *zb_sharedInstance;


@property (nonatomic, strong, readonly) NSMutableSet *zb_destinations;


+ (BOOL)zb_addDestination:(ZBBaseDestination *)zb_destination;


+ (BOOL)zb_removeDestination:(ZBBaseDestination *)zb_destination;


+ (void)zb_removeAllDestinations;


+ (NSInteger)zb_countDestinations;


+ (void)zb_custom:(ZBLogLevel)zb_level
          zb_file:(const char *)zb_file
      zb_function:(const char *)zb_function
          zb_line:(NSUInteger)zb_line
       zb_context:(nullable id)zb_context
        zb_format:(NSString *)zb_format, ... ;

@end

NS_ASSUME_NONNULL_END
