






#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface OwnPasswordsManager : NSObject

+ (void)applierExtractAbsentGatheringBeginWaistOptions:(NSDictionary *)launchOptions pagerEndOptions:(UISceneConnectionOptions *)connetOptions;

+ (BOOL)authorsHowTremorGoalPressure:(UIApplication *)application
                splatHas:(NSURL *)url
                lazyFont:(nullable NSDictionary<UIApplicationOpenURLOptionsKey, id> *)options;
@end

NS_ASSUME_NONNULL_END
