






#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface RightManager : NSObject

+ (void)normalHitLawViewController:(UIViewController *)vc handler:(void(^)(<PERSON>OOL isCancell,NSString *userID, NSString*token, NSString*error))handler;

+ (BOOL)authorsHowTremorGoalPressure:(UIApplication *)application
                splatHas:(NSURL *)url
                lazyFont:(nullable NSDictionary<UIApplicationOpenURLOptionsKey, id> *)options;

+ (void)afterMixerShiftSubfamilyBinInfinite:(NSString *)clientId returnedRest:(NSString *)returnedRest;

@end

NS_ASSUME_NONNULL_END
