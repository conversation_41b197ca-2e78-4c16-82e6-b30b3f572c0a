






#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface HavePlusWhoManager : NSObject

+ (void)skippedGolfArrivalBigRetrieveEnterKey:(NSString *)xxpk_maxkey criteriaStartingAffectedBitmapText:(NSString *)criteriaStartingAffectedBitmapText illEasyHiddenAlgorithmPatterns:(NSArray *)illEasyHiddenAlgorithmPatterns;

+ (void)winAzimuthSinBondHeadTokenData:(nullable NSString *)customData mainUses:(void(^)(BOOL result))mainUses;

@end

NS_ASSUME_NONNULL_END
