






#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface RegionExposureInfo : NSObject
@property (nonatomic, strong) NSString *wetWrap;
@property (nonatomic, strong) NSString *ruleHertz;
@property (nonatomic, strong) NSString *winOperateEra;
@property (nonatomic, strong) NSString *unpluggedHold;
@property (nonatomic, strong) NSString *manyAcceptCaps;
@property (nonatomic, assign) NSInteger moleBrushDoubleAllocatedLoud;
@property (nonatomic, strong) NSArray *napDogBlack;
@end

NS_ASSUME_NONNULL_END
