


#import "SDWebImageCompat.h"
#import "NSData+ImageContentType.h"
#import "SDImageCoder.h"



@interface UIImage (Metadata)



@property (nonatomic, assign) NSUInteger sd_imageLoopCount;



@property (nonatomic, assign, readonly) NSUInteger sd_imageFrameCount;



@property (nonatomic, assign, readonly) BOOL sd_isAnimated;



@property (nonatomic, assign, readonly) BOOL sd_isVector;



@property (nonatomic, assign) SDImageFormat sd_imageFormat;



@property (nonatomic, assign) BOOL sd_isIncremental;



@property (nonatomic, assign) BOOL sd_isTransformed;



@property (nonatomic, assign, readonly) BOOL sd_isThumbnail;



@property (nonatomic, copy) SDImageCoderOptions *sd_decodeOptions;



@property (nonatomic, assign, readonly) BOOL sd_isHighDynamicRange;

@end
