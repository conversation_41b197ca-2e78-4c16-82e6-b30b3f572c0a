


#import "SDWebImageCompat.h"
#import "UIImage+Transform.h"



FOUNDATION_EXPORT NSString * _Nullable SDTransformedKeyForKey(NSString * _Nullable key, NSString * _Nonnull transformerKey);



FOUNDATION_EXPORT NSString * _Nullable SDThumbnailedKeyForKey(NSString * _Nullable key, CGSize thumbnailPixelSize, BOOL preserveAspectRatio);



@protocol SDImageTransformer <NSObject>

@optional



@property (nonatomic, assign, readonly) BOOL preserveImageMetadata;

@required


@property (nonatomic, copy, readonly, nonnull) NSString *transformerKey;



- (nullable UIImage *)transformedImageWithImage:(nonnull UIImage *)image forKey:(nonnull NSString *)key API_DEPRECATED("The key arg will be removed in the future. Update your code and don't rely on that.", macos(10.10, API_TO_BE_DEPRECATED), ios(8.0, API_TO_BE_DEPRECATED), tvos(9.0, API_TO_BE_DEPRECATED), watchos(2.0, API_TO_BE_DEPRECATED));

@end





@interface SDImagePipelineTransformer : NSObject<SDImageTransformer>

@property (nonatomic, assign, readonly) BOOL preserveImageMetadata;


@property (nonatomic, copy, readonly, nonnull) NSArray<id<SDImageTransformer>> *transformers;

- (nonnull instancetype)init NS_UNAVAILABLE;
+ (nonnull instancetype)new  NS_UNAVAILABLE;

+ (nonnull instancetype)transformerWithTransformers:(nonnull NSArray<id<SDImageTransformer>> *)transformers;

@end



@interface SDImageBaseTransformer : NSObject<SDImageTransformer>

@property (nonatomic, assign, readwrite) BOOL preserveImageMetadata;
@end







@interface SDImageRoundCornerTransformer: SDImageBaseTransformer



@property (nonatomic, assign, readonly) CGFloat cornerRadius;



@property (nonatomic, assign, readonly) SDRectCorner corners;



@property (nonatomic, assign, readonly) CGFloat borderWidth;



@property (nonatomic, strong, readonly, nullable) UIColor *borderColor;

- (nonnull instancetype)init NS_UNAVAILABLE;
+ (nonnull instancetype)new  NS_UNAVAILABLE;

+ (nonnull instancetype)transformerWithRadius:(CGFloat)cornerRadius corners:(SDRectCorner)corners borderWidth:(CGFloat)borderWidth borderColor:(nullable UIColor *)borderColor;

@end



@interface SDImageResizingTransformer : SDImageBaseTransformer



@property (nonatomic, assign, readonly) CGSize size;



@property (nonatomic, assign, readonly) SDImageScaleMode scaleMode;

- (nonnull instancetype)init NS_UNAVAILABLE;
+ (nonnull instancetype)new  NS_UNAVAILABLE;

+ (nonnull instancetype)transformerWithSize:(CGSize)size scaleMode:(SDImageScaleMode)scaleMode;

@end



@interface SDImageCroppingTransformer : SDImageBaseTransformer



@property (nonatomic, assign, readonly) CGRect rect;

- (nonnull instancetype)init NS_UNAVAILABLE;
+ (nonnull instancetype)new  NS_UNAVAILABLE;

+ (nonnull instancetype)transformerWithRect:(CGRect)rect;

@end



@interface SDImageFlippingTransformer : SDImageBaseTransformer



@property (nonatomic, assign, readonly) BOOL horizontal;



@property (nonatomic, assign, readonly) BOOL vertical;

- (nonnull instancetype)init NS_UNAVAILABLE;
+ (nonnull instancetype)new  NS_UNAVAILABLE;

+ (nonnull instancetype)transformerWithHorizontal:(BOOL)horizontal vertical:(BOOL)vertical;

@end



@interface SDImageRotationTransformer : SDImageBaseTransformer



@property (nonatomic, assign, readonly) CGFloat angle;



@property (nonatomic, assign, readonly) BOOL fitSize;

- (nonnull instancetype)init NS_UNAVAILABLE;
+ (nonnull instancetype)new  NS_UNAVAILABLE;

+ (nonnull instancetype)transformerWithAngle:(CGFloat)angle fitSize:(BOOL)fitSize;

@end





@interface SDImageTintTransformer : SDImageBaseTransformer



@property (nonatomic, strong, readonly, nonnull) UIColor *tintColor;

@property (nonatomic, assign, readonly) CGBlendMode blendMode;

- (nonnull instancetype)init NS_UNAVAILABLE;
+ (nonnull instancetype)new  NS_UNAVAILABLE;

+ (nonnull instancetype)transformerWithColor:(nonnull UIColor *)tintColor;
+ (nonnull instancetype)transformerWithColor:(nonnull UIColor *)tintColor blendMode:(CGBlendMode)blendMode;

@end





@interface SDImageBlurTransformer : SDImageBaseTransformer



@property (nonatomic, assign, readonly) CGFloat blurRadius;

- (nonnull instancetype)init NS_UNAVAILABLE;
+ (nonnull instancetype)new  NS_UNAVAILABLE;

+ (nonnull instancetype)transformerWithRadius:(CGFloat)blurRadius;

@end

#if SD_UIKIT || SD_MAC


@interface SDImageFilterTransformer: SDImageBaseTransformer



@property (nonatomic, strong, readonly, nonnull) CIFilter *filter;

- (nonnull instancetype)init NS_UNAVAILABLE;
+ (nonnull instancetype)new  NS_UNAVAILABLE;

+ (nonnull instancetype)transformerWithFilter:(nonnull CIFilter *)filter;

@end
#endif
