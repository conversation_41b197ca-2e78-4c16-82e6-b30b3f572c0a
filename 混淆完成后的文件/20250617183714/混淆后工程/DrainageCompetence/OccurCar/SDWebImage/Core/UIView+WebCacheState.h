


#import <Foundation/Foundation.h>
#import "SDWebImageCompat.h"



@interface SDWebImageLoadState : NSObject



@property (nonatomic, strong, nullable) NSURL *url;


@property (nonatomic, strong, nullable) NSProgress *progress;

@end



@interface UIView (WebCacheState)



- (nullable SDWebImageLoadState *)sd_imageLoadStateForKey:(nullable NSString *)key;



- (void)sd_setImageLoadState:(nullable SDWebImageLoadState *)state forKey:(nullable NSString *)key;



- (void)sd_removeImageLoadStateForKey:(nullable NSString *)key;

@end
