


#import <Foundation/Foundation.h>
#import "SDWebImageCompat.h"


typedef NS_ENUM(NSInteger, SDWebImageDownloaderExecutionOrder) {
    

    SDWebImageDownloaderFIFOExecutionOrder,
    
    

    SDWebImageDownloaderLIFOExecutionOrder
};



@interface SDWebImageDownloaderConfig : NSObject <NSCopying>



@property (nonatomic, class, readonly, nonnull) SDWebImageDownloaderConfig *defaultDownloaderConfig;



@property (nonatomic, assign) NSInteger maxConcurrentDownloads;



@property (nonatomic, assign) NSTimeInterval downloadTimeout;



@property (nonatomic, assign) double minimumProgressInterval;



@property (nonatomic, strong, nullable) NSURLSessionConfiguration *sessionConfiguration;



@property (nonatomic, assign, nullable) Class operationClass;



@property (nonatomic, assign) SDWebImageDownloaderExecutionOrder executionOrder;



@property (nonatomic, copy, nullable) NSURLCredential *urlCredential;



@property (nonatomic, copy, nullable) NSString *username;



@property (nonatomic, copy, nullable) NSString *password;



@property (nonatomic, copy, nullable) NSIndexSet *acceptableStatusCodes;



@property (nonatomic, copy, nullable) NSSet<NSString *> *acceptableContentTypes;

@end
