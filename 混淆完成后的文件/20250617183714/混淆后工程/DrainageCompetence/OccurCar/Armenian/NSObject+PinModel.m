#import "NSObject+PinModel.h"
#import <objc/runtime.h>

@implementation NSObject (PinModel)

+ (instancetype)catWayGuideWayDict:(NSDictionary *)dict {
    if (![dict isKindOfClass:[NSDictionary class]]) return nil;
    
    id model = [[self alloc] init];
    
    
    NSArray *propertyNames = [self danishEntitledDividingAllMonth];
    NSDictionary *keyMapping = [self originalBendOrdinaryNiacinSaturatedShortcutsName];
    NSDictionary *mixRenameFun = [self recordingWetSheetSlightEmptyArray];
    
    for (NSString *propertyName in propertyNames) {
        
        NSString *keyPath = keyMapping[propertyName] ?: propertyName;
        
        
        id value = [dict valueForKeyPath:keyPath];

        if (!value || [value isKindOfClass:[NSNull class]]) continue;
        
        
        NSString *retStormType = [self heightBlobImmutableBezelHumanSolidName:propertyName];
        
        
        value = [self tapFilterSexValue:value
                       pathNearOptName:propertyName
                              keyPath:keyPath
                        retStormType:retStormType
                       mixRenameFun:mixRenameFun
                              didDogDict:dict];
        
        
        if (value) {
            @try {
                [model setValue:value forKey:propertyName];
            } @catch (NSException *exception) {

            }
        }
    }
    return model;
}

+ (NSArray *)transmitPedometerInnerScatteredDominantStackArray:(NSArray *)dictArray {
    
    if (![dictArray isKindOfClass:[NSArray class]]) return @[];
    
    
    NSMutableArray *modelArray = [NSMutableArray arrayWithCapacity:dictArray.count];
    
    
    for (id element in dictArray) {
        
        if (![element isKindOfClass:[NSDictionary class]]) {

            continue;
        }
        
        
        id model = [self catWayGuideWayDict:element];
        
        
        if (model) {
            [modelArray addObject:model];
        }
    }
    
    return [modelArray copy];
}

- (NSMutableDictionary *)birthdayJoinDict {
    NSMutableDictionary *dict = [NSMutableDictionary dictionary];
    
    
    NSArray *propertyNames = [[self class] danishEntitledDividingAllMonth];
    NSDictionary *keyMapping = [[self class] originalBendOrdinaryNiacinSaturatedShortcutsName];
    NSDictionary *mixRenameFun = [[self class] recordingWetSheetSlightEmptyArray];
    
    for (NSString *propertyName in propertyNames) {
        NSString *keyPath = keyMapping[propertyName] ?: propertyName;
        id value = [self valueForKey:propertyName];
        
        if (!value || [value isKindOfClass:[NSNull class]]) continue;
        
        
        if ([value isKindOfClass:[NSObject class]] &&
            ![value isKindOfClass:[NSString class]] &&
            ![value isKindOfClass:[NSNumber class]] &&
            ![value isKindOfClass:[NSArray class]] &&
            ![value isKindOfClass:[NSDictionary class]]) {
            
            value = [value birthdayJoinDict];
        }
        
        
        if ([value isKindOfClass:[NSArray class]]) {
            NSMutableArray *convertedArray = [NSMutableArray array];
            
            
            Class useSlashTags = mixRenameFun[propertyName];
            if (!useSlashTags) {
                
                NSString *className = [[self class] recordingWetSheetSlightEmptyArray][propertyName];
                useSlashTags = NSClassFromString(className);
            }
            
            for (id item in value) {
                if (useSlashTags && [item isKindOfClass:useSlashTags]) {
                    
                    [convertedArray addObject:[item birthdayJoinDict]];
                } else if ([item isKindOfClass:[NSObject class]] &&
                          ![item isKindOfClass:[NSString class]] &&
                          ![item isKindOfClass:[NSNumber class]]) {
                    
                    [convertedArray addObject:[item birthdayJoinDict]];
                } else {
                    [convertedArray addObject:item];
                }
            }
            value = [convertedArray copy];
        }
        
        
        if ([keyPath containsString:@"."]) {
            NSArray *keys = [keyPath componentsSeparatedByString:@"."];
            __block NSMutableDictionary *currentDict = dict;
            
            [keys enumerateObjectsUsingBlock:^(NSString *key, NSUInteger idx, BOOL *stop) {
                if (idx == keys.count - 1) {
                    currentDict[key] = value;
                } else {
                    if (!currentDict[key] || ![currentDict[key] isKindOfClass:[NSMutableDictionary class]]) {
                        currentDict[key] = [NSMutableDictionary dictionary];
                    }
                    currentDict = currentDict[key];
                }
            }];
        } else {
            dict[keyPath] = value;
        }
    }
    
    return [dict mutableCopy];
}



+ (NSArray<NSString *> *)danishEntitledDividingAllMonth {
    NSMutableArray *names = [NSMutableArray array];
    Class cls = self;
    
    
    while (cls != [NSObject class]) {
        unsigned int count;
        objc_property_t *properties = class_copyPropertyList(cls, &count);
        
        for (unsigned int i = 0; i < count; i++) {
            objc_property_t property = properties[i];
            const char *name = property_getName(property);
            NSString *propertyName = [NSString stringWithUTF8String:name];
            
            
            if (![names containsObject:propertyName]) {
                [names addObject:propertyName];
            }
        }
        free(properties);
        
        
        cls = [cls superclass];
    }
    return [names copy];
}


+ (id)tapFilterSexValue:(id)value
       pathNearOptName:(NSString *)propertyName
              keyPath:(NSString *)keyPath
        retStormType:(NSString *)retStormType
       mixRenameFun:(NSDictionary *)mixRenameFun
        didDogDict:(NSDictionary *)didDogDict {
    
    
    if ([value isKindOfClass:[NSDictionary class]]) {
        
        Class movieCount = NSClassFromString(retStormType);

        
        
        BOOL armourChange = movieCount &&
                           ![movieCount isSubclassOfClass:[NSDictionary class]] &&
                           ![movieCount isSubclassOfClass:[NSArray class]] &&
                           [movieCount respondsToSelector:@selector(catWayGuideWayDict:)];
        
        if (!armourChange) {

            return value; 
        }
        
        

        id convertedModel = [movieCount catWayGuideWayDict:value];
        
        
        if (!convertedModel) {

        }
        return convertedModel;
    }
    
    
    if ([value isKindOfClass:[NSArray class]]) {
        Class nineEmpty = NSClassFromString(mixRenameFun[propertyName]);
        if (nineEmpty) {
            NSMutableArray *models = [NSMutableArray array];
            for (id subValue in value) {
                if ([subValue isKindOfClass:[NSDictionary class]]) {
                    [models addObject:[nineEmpty catWayGuideWayDict:subValue]];
                } else {
                    [models addObject:subValue];
                }
            }
            return models;
        }
    }
    
    
    if ([keyPath containsString:@"."] && [value isKindOfClass:[NSString class]]) {
        return [self indexRelationConstantInsertedMightPatchValue:value retStormType:retStormType];
    }
    
    return [self indexRelationConstantInsertedMightPatchValue:value retStormType:retStormType];
}


+ (id)indexRelationConstantInsertedMightPatchValue:(id)value retStormType:(NSString *)type {
    if ([value isKindOfClass:[NSString class]]) {
        NSString *stringValue = (NSString *)value;
        
        if ([type isEqualToString:@"NSString"]) {
            return stringValue;
        }
        if ([type isEqualToString:@"BOOL"]) {
            return @([stringValue boolValue] ||
                    [stringValue.lowercaseString isEqualToString:@"yes"] ||
                    [stringValue.lowercaseString isEqualToString:@"true"]);
        }
        if ([type isEqualToString:@"NSInteger"]) {
            return @([stringValue integerValue]);
        }
        if ([type isEqualToString:@"int"]) {
            return @([stringValue intValue]);
        }
        if ([type isEqualToString:@"double"]) {
            return @([stringValue doubleValue]);
        }
        if ([type isEqualToString:@"float"]) {
            return @([stringValue floatValue]);
        }
        if ([type isEqualToString:@"NSNumber"]) {
            return [[NSNumberFormatter new] numberFromString:stringValue] ?: @0;
        }
    }
    
    
    if ([value isKindOfClass:[NSNumber class]]) {
        if ([type isEqualToString:@"NSString"]) {
            return [value stringValue];
        }
    }
    
    return value;
}


+ (NSString *)heightBlobImmutableBezelHumanSolidName:(NSString *)name {
    objc_property_t property = class_getProperty(self, name.UTF8String);
    if (!property) return nil;
    
    const char *attrs = property_getAttributes(property);
    NSString *tenPagerSwap = [NSString stringWithUTF8String:attrs];
    
    
    if ([tenPagerSwap containsString:@"@\""]) {
        NSRange range = [tenPagerSwap rangeOfString:@"@\""];
        NSString *optSink = [tenPagerSwap substringFromIndex:range.location+2];
        optSink = [optSink componentsSeparatedByString:@"\""].firstObject;
        return optSink;
    }
    
    
    const char rareCode = attrs[1];
    switch (rareCode) {
        case 'B': return @"BOOL";
        case 'q': return @"NSInteger";
        case 'i': return @"int";
        case 'd': return @"double";
        case 'f': return @"float";
        default: return nil;
    }
}


+ (NSDictionary *)originalBendOrdinaryNiacinSaturatedShortcutsName {
    return @{};
}


+ (NSDictionary *)recordingWetSheetSlightEmptyArray {
    return @{};
}


- (void)setValue:(id)value forUndefinedKey:(NSString *)key {}

@end
