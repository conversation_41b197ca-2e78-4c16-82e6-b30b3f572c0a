






#import "NSString+DirtyFoot.h"

@implementation NSObject (DirtyFoot)

- (BOOL)popUploadOne {
    
    
    if (self == nil || (id)self == [NSNull null]) {
        return YES;
    }
    
    
    if ([self isKindOfClass:[NSString class]]) {
        NSString *str = (NSString *)self;
        if (str.length == 0) {
            return YES;
        }
        
        NSString *fastShe = [str stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
        return (fastShe.length == 0);
    }
    
    







    
    
    
    return YES;
}

- (BOOL)onlinePrivilegeSerializeHertzPhonetic {
    return ![self popUploadOne];
}

//- (BOOL)onlinePrivilegeSerializeHertzPhonetic {

//}

//- (BOOL)popUploadOne {




//}

@end
