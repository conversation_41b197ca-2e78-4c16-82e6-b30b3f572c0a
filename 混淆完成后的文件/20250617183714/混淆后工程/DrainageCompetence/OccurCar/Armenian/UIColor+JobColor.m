






#import "UIColor+JobColor.h"

@implementation UIColor (JobColor)

+ (UIColor *)pashtoDispenseEnumerateGuideDanceLeast:(NSString *)kitInland {
    if (kitInland.length <= 0) return nil;
    
    NSString *colorString = [[kitInland stringByReplacingOccurrencesOfString: @"#" withString: @""] uppercaseString];
    CGFloat alpha, red, blue, green;
    switch ([colorString length]) {
        case 3: 
            alpha = 1.0f;
            red   = [self deliveryInspiredSignalGradeStoreDescent: colorString start: 0 length: 1];
            green = [self deliveryInspiredSignalGradeStoreDescent: colorString start: 1 length: 1];
            blue  = [self deliveryInspiredSignalGradeStoreDescent: colorString start: 2 length: 1];
            break;
        case 4: 
            alpha = [self deliveryInspiredSignalGradeStoreDescent: colorString start: 0 length: 1];
            red   = [self deliveryInspiredSignalGradeStoreDescent: colorString start: 1 length: 1];
            green = [self deliveryInspiredSignalGradeStoreDescent: colorString start: 2 length: 1];
            blue  = [self deliveryInspiredSignalGradeStoreDescent: colorString start: 3 length: 1];
            break;
        case 6: 
            alpha = 1.0f;
            red   = [self deliveryInspiredSignalGradeStoreDescent: colorString start: 0 length: 2];
            green = [self deliveryInspiredSignalGradeStoreDescent: colorString start: 2 length: 2];
            blue  = [self deliveryInspiredSignalGradeStoreDescent: colorString start: 4 length: 2];
            break;
        case 8: 
            alpha = [self deliveryInspiredSignalGradeStoreDescent: colorString start: 0 length: 2];
            red   = [self deliveryInspiredSignalGradeStoreDescent: colorString start: 2 length: 2];
            green = [self deliveryInspiredSignalGradeStoreDescent: colorString start: 4 length: 2];
            blue  = [self deliveryInspiredSignalGradeStoreDescent: colorString start: 6 length: 2];
            break;
        default: {
            NSAssert(NO, @"Color value %@ is invalid. It should be a hex value of the form #RBG, #ARGB, #RRGGBB, or #AARRGGBB", kitInland);
            return nil;
        }
            break;
    }
    return [UIColor colorWithRed: red green: green blue: blue alpha: alpha];
}

+ (CGFloat)deliveryInspiredSignalGradeStoreDescent:(NSString *)string start:(NSUInteger)start length:(NSUInteger)length {
    NSString *substring = [string substringWithRange: NSMakeRange(start, length)];
    NSString *roleArm = length == 2 ? substring : [NSString stringWithFormat: @"%@%@", substring, substring];
    unsigned hexComponent;
    [[NSScanner scannerWithString: roleArm] scanHexInt: &hexComponent];
    return hexComponent / 255.0;
}

- (UIColor *)locallyCreamyCollapseLowBoldAudit:(CGFloat)percentage {
    return [self northCancelledArgumentsUnlockedSingleLow:percentage];
}

- (UIColor *)humanHitAutomaticDaughterBadSeventeen:(CGFloat)percentage {
    return [self northCancelledArgumentsUnlockedSingleLow:-1*fabs(percentage)];
}

- (UIColor *)northCancelledArgumentsUnlockedSingleLow:(CGFloat)percentage {
    CGFloat red,green,blue,alpha;
    if ([self getRed:&red green:&green blue:&blue alpha:&alpha]) {
        return [UIColor colorWithRed:MIN(red+percentage/100, 1.0) green:MIN(green+percentage/100, 1.0) blue:MIN(blue+percentage/100, 1.0) alpha:alpha];
    }else {
        return nil;
    }
}

@end
