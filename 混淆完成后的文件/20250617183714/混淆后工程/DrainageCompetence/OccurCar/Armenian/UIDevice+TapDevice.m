






#import "UIDevice+TapDevice.h"
#import "TorchGainManager.h"
@import UIKit;

@implementation UIDevice (TapDevice)

static NSInteger gaelic = -1;
+ (BOOL)gaelic {
    if (gaelic < 0) {
        gaelic = [UIDevice currentDevice].userInterfaceIdiom == UIUserInterfaceIdiomPad ? 1 : 0;
    }
    return gaelic > 0;
}

+ (BOOL)blendPin {
    if (@available(iOS 11.0, *)) {
        
        UIWindow *window = TorchGainManager.shared.lowercaseOffWindow;
        
        UIEdgeInsets safeArea = window.safeAreaInsets;
        
        
        BOOL lastVery = ([UIDevice currentDevice].userInterfaceIdiom == UIUserInterfaceIdiomPhone);
        
        
        return lastVery && (
            safeArea.top > 20.0 ||          
            safeArea.left > 0 ||            
            safeArea.right > 0              
        );
    }
    return NO; 
}

@end
