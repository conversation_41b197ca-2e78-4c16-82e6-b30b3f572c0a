






#import "NSURL+PaletteBad.h"
#import "NSString+ReversesAsk.h"

@implementation NSURL (PaletteBad)

- (NSDictionary *)determineOne {
    
    NSArray * array = [[self query] componentsSeparatedByString:@"&"];

    NSMutableDictionary * ParaDict = [NSMutableDictionary new];

    for(int i = 0 ; i < [array count]; i++){

        NSArray * mediumValue = [array[i] componentsSeparatedByString:@"="];

        if([mediumValue count] == 2 && mediumValue[0] && mediumValue[1]){

            [ParaDict setObject:[mediumValue[1] fiberBufferingDrivenPostUnifiedLinger] forKey:mediumValue[0]];

        }
    }
    return ParaDict;
}

@end
