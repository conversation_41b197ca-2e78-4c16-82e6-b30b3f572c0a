






#import "UIImage+YetImage.h"
#import "NSData+CropSum.h"
#import "NSString+DirtyFoot.h"
#import "DarkerButPlusReportStrategy.h"

@implementation UIImage (YetImage)

+ (UIImage *)bondDriveTightColor:(UIColor *)color {
    
    CGRect rect=CGRectMake(0.0f,0.0f, 1.0f,1.0f);
    UIGraphicsBeginImageContext(rect.size);
    CGContextRef context = UIGraphicsGetCurrentContext();
    CGContextSetFillColorWithColor(context, [color CGColor]);
    CGContextFillRect(context, rect);
    UIImage *binImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return binImage;
}

+ (UIImage *)russianTrackingAppendingLemmaThreadedName:(NSString *)imageName {
    
    if (!imageName) {
        return nil;
    }
    
    UIImage *image = nil;
    
    NSString *logPath= [[DarkerButPlusReportStrategy sheTwelve] stringByAppendingPathComponent:imageName];
    
    if (logPath.onlinePrivilegeSerializeHertzPhonetic) {
        
        image = [UIImage imageWithContentsOfFile:logPath];
    }
    
    if (!image) {
        
        NSData *thatTeethData = [NSData dataWithContentsOfFile:logPath];
       
       
        image = [thatTeethData segmentCivilDenyWillOldestMostNode];
    }
    
    return image;
}

- (UIImage *)blueUsagePastSwitchItsColor:(UIColor *)tintColor {
   
    if (!tintColor) return self;
    
    
    UIGraphicsImageRendererFormat *format = [UIGraphicsImageRendererFormat defaultFormat];
    format.scale = self.scale;
    format.opaque = NO;
    
    UIGraphicsImageRenderer *renderer = [[UIGraphicsImageRenderer alloc] initWithSize:self.size format:format];
    
    return [renderer imageWithActions:^(UIGraphicsImageRendererContext * _Nonnull context) {
        
        [tintColor setFill];
        
        
        CGRect bounds = CGRectMake(0, 0, self.size.width, self.size.height);
        [self drawInRect:bounds];
        
        
        CGContextSetBlendMode(context.CGContext, kCGBlendModeSourceIn);
        CGContextFillRect(context.CGContext, bounds);
    }];
}
@end
