






#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface NSObject (MindStickySpatialHelloBox)

//- (id)artTorchMileStandConvertIndex:(SEL)selector withObject:(id _Nullable)object,...NS_REQUIRES_NIL_TERMINATION;
- (id)artTorchMileStandConvertIndex:(SEL)aSelector;

- (id)artTorchMileStandConvertIndex:(SEL)aSelector
                withObject:(id)object1;

- (id)artTorchMileStandConvertIndex:(SEL)aSelector
                withObject:(id)object1
                withObject:(id)object2;

- (id)artTorchMileStandConvertIndex:(SEL)aSelector
                withObject:(id)object1
                withObject:(id)object2
                withObject:(id)object3;

- (id)artTorchMileStandConvertIndex:(SEL)aSelector
                withObject:(id)object1
                withObject:(id)object2
                withObject:(id)object3
                withObject:(id)object4;

- (id)artTorchMileStandConvertIndex:(SEL)aSelector
                withObject:(id)object1
                withObject:(id)object2
                withObject:(id)object3
                withObject:(id)object4
                withObject:(id)object5;

- (id)artTorchMileStandConvertIndex:(SEL)aSelector
                withObject:(id)object1
                withObject:(id)object2
                withObject:(id)object3
                withObject:(id)object4
                withObject:(id)object5
                withObject:(id)object6;
@end

NS_ASSUME_NONNULL_END
