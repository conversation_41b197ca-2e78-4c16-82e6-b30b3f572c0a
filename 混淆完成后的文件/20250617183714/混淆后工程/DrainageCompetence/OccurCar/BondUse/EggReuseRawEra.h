






#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface EggReuseRawEra : NSObject
<
NSURLSessionDelegate,
NSURLSessionTaskDelegate
>

+ (instancetype)shared;

- (void)rateNewsstandRequest:(NSMutableURLRequest *)request
                     process:(NSData * _Nullable (^_Nullable)(NSData * _Nullable rawData))processBlock 
                     success:(void(^_Nullable)(NSDictionary * howLeastTenOff))success
                     implied:(void(^_Nullable)(NSError *error))implied
                  audioCount:(NSInteger)audioCount;

@end

NS_ASSUME_NONNULL_END
