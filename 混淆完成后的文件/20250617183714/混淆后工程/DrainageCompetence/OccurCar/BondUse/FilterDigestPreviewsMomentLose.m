





#import "FilterDigestPreviewsMomentLose.h"
@import Network;

static NSString *outPinPingCert = nil;
static nw_path_monitor_t unionSlashHead = NULL;

@implementation FilterDigestPreviewsMomentLose

+ (BOOL)danishEnsureIcyHostLate {
    return outPinPingCert != nil;
}

+ (NSString *)intentTargetType {
    return outPinPingCert ?: @"none";
}

+ (void)escapedSpineExtendsExpiresTriggersBehaviors:(void (^)(BOOL danishEnsureIcyHostLate))completion {
    
    if (unionSlashHead != NULL) {
        nw_path_monitor_cancel(unionSlashHead);
        unionSlashHead = NULL;
    }
    
    
    unionSlashHead = nw_path_monitor_create();
    nw_path_monitor_set_queue(unionSlashHead, dispatch_get_main_queue());
    
    __block nw_path_monitor_t blockMonitor = unionSlashHead;
    nw_path_monitor_set_update_handler(unionSlashHead, ^(nw_path_t path) {
        nw_path_status_t status = nw_path_get_status(path);
        if (status == nw_path_status_satisfied) {
            if (nw_path_uses_interface_type(path, nw_interface_type_wifi)) {
                outPinPingCert = @"wifi";
            } else if (nw_path_uses_interface_type(path, nw_interface_type_cellular)) {
                outPinPingCert = @"cellular";
            } else {
                
                outPinPingCert = @"other";
            }
            
            
            if (blockMonitor) {
                nw_path_monitor_cancel(blockMonitor);
                blockMonitor = NULL;
                unionSlashHead = NULL;
            }
            
        } else {
            outPinPingCert = nil;
        }
        
        
        if (completion) {
            completion([self danishEnsureIcyHostLate]);
        }
        
    });
    
    
    nw_path_monitor_start(unionSlashHead);
}

@end
