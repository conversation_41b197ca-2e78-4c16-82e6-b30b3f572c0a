






#import "EggReuseRawEra.h"

#define unitPub(obj) __weak typeof(obj) weak##obj = obj;
#define areRetain(obj) __strong typeof(obj) obj = weak##obj;

@interface EggReuseRawEra()

@property (nonatomic,strong) NSURLSession *mainEncipher;

@end

@implementation EggReuseRawEra


+ (instancetype)shared {
    static EggReuseRawEra *shared = nil;
    static dispatch_once_t onlyToken;
    dispatch_once(&onlyToken, ^{
        shared = [[super allocWithZone:NULL] init];
        shared.mainEncipher = [NSURLSession sessionWithConfiguration:[NSURLSessionConfiguration defaultSessionConfiguration] delegate:shared delegateQueue:[[NSOperationQueue alloc] init]];
        shared.mainEncipher.delegateQueue.maxConcurrentOperationCount = 1;
    });
    return shared;
}

- (void)rateNewsstandRequest:(NSMutableURLRequest *)request
                     process:(NSData * _Nullable (^_Nullable)(NSData * _Nullable rawData))processBlock
                     success:(void(^)(NSDictionary * howLeastTenOff))success
                     implied:(void(^)(NSError *error))implied
                  audioCount:(NSInteger)audioCount {

    [self eightTakeRequest:request
                   process:processBlock
                   success:success
                   implied:implied
                audioCount:audioCount
            rangeFileSpell:0];
}


- (void)eightTakeRequest:(NSMutableURLRequest *)request
                 process:(NSData * _Nullable (^_Nullable)(NSData * _Nullable rawData))processBlock
                 success:(void(^)(NSDictionary * howLeastTenOff))success
                 implied:(void(^)(NSError *error))implied
              audioCount:(NSInteger)audioCount
          rangeFileSpell:(NSInteger)rangeFileSpell {

    unitPub(self);
    NSURLSessionDataTask *task = [self.mainEncipher dataTaskWithRequest:request completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
        areRetain(self);
        
        NSError *lazySeeOwn = [self handleError:error response:response data:data];
        if (lazySeeOwn) {
            

            
            if (rangeFileSpell < audioCount) {
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [self eightTakeRequest:request process:processBlock success:success implied:implied audioCount:audioCount rangeFileSpell:rangeFileSpell + 1];
                });
                return;
            }

            
            if (implied) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    implied(lazySeeOwn);
                });
            }
            return;
        }

        
        NSData *processedData = processBlock ? processBlock(data) : data;
        if (!processedData) {
            NSError *processingError = [NSError errorWithDomain:@"NetworkCore"
                                                           code:-30002
                                                       userInfo:@{NSLocalizedDescriptionKey : @"Data processing failed"}];
            if (implied) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    implied(processingError);
                });
            }
            return;
        }

        NSError *jsonError;
        NSDictionary *jsonResponse = [NSJSONSerialization JSONObjectWithData:processedData options:0 error:&jsonError];

        if (!jsonError && [jsonResponse isKindOfClass:[NSDictionary class]]) {
            if (success) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    success(jsonResponse);
                });
            }
        } else {
            
            if (implied) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    implied(jsonError);
                });
            }
        }
    }];

    [task resume];
}


- (NSError *)handleError:(NSError *)error response:(NSURLResponse *)response data:(NSData *)data {
    if (error) {
        return error;
    }

    if (!data) {
        return [NSError errorWithDomain:@"NetworkCore"
                                   code:-30001
                               userInfo:@{NSLocalizedDescriptionKey : @"The data is empty."}];
    }

    NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *)response;
    if (![httpResponse isKindOfClass:[NSHTTPURLResponse class]] || httpResponse.statusCode != 200) {
        return [NSError errorWithDomain:@"NetworkCore"
                                   code:httpResponse.statusCode
                               userInfo:@{NSLocalizedDescriptionKey : [NSString stringWithFormat:@"HTTPError，code: %ld", (long)httpResponse.statusCode]}];
    }

    return nil;
}

@end
