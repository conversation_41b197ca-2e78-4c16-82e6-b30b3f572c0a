

#import "HertzViewController.h"
#import "Color.h"
#import "AgeHardSentinelFaxAgeDiacritic.h"
#import "IdentifyToneAcrossRegistryOccur.h"
#import "InsetTowerConfig.h"

@interface HertzViewController ()
@property (nonatomic, strong) UITextView *textView;
@property (nonatomic, strong) AgeHardSentinelFaxAgeDiacritic *supportedBetweenIdentityExponentsPerfusion;
@property (nonatomic, strong) IdentifyToneAcrossRegistryOccur *prettyLeadFactoryProducingUndone;
@property (nonatomic, strong) NSDate *rareBlueDate; 
@end

static IdentifyToneAcrossRegistryOccur *_sharedFileDestination = nil;
static AgeHardSentinelFaxAgeDiacritic *_sharedConsoleDestination = nil;

@implementation HertzViewController

+ (void)traitStereo {
    
    [Color albumBounceCountingDenyHowBrowse];

    _sharedConsoleDestination = [[AgeHardSentinelFaxAgeDiacritic alloc] init];
    _sharedConsoleDestination.catPubLevel = MenPromptLengthMatchTamil;

    [Color generateExportedWhileRevertingPeer:_sharedConsoleDestination];

    _sharedFileDestination = [[IdentifyToneAcrossRegistryOccur alloc] init];
    _sharedFileDestination.catPubLevel = ArtParseLimit;
    _sharedFileDestination.tipSalt = 7;
    _sharedFileDestination.pasteChinaPreparingWriteLoss = YES;
    [Color generateExportedWhileRevertingPeer:_sharedFileDestination];

    [_sharedFileDestination skinFillEggIts];
}

+ (IdentifyToneAcrossRegistryOccur *)upsidePurpleOutArmenianOuncesGet {
    return _sharedFileDestination;
}
+ (AgeHardSentinelFaxAgeDiacritic *)sandboxPluralRequiringFilteringBaseOffset {
    return _sharedConsoleDestination;
}

- (AgeHardSentinelFaxAgeDiacritic *)supportedBetweenIdentityExponentsPerfusion {
    return _sharedConsoleDestination;
}

+ (void)showFromViewController:(UIViewController *)parentVC {
    HertzViewController *abort = [[HertzViewController alloc] init];
    UINavigationController *net = [[UINavigationController alloc] initWithRootViewController:abort];
    net.modalPresentationStyle = UIModalPresentationFullScreen;
    [parentVC presentViewController:net animated:YES completion:nil];
}

- (void)viewDidLoad {
    [super viewDidLoad];

    self.title = valueSubPop.needProduceOnlyNormalizeShowersEasy;
    self.view.backgroundColor = [UIColor systemBackgroundColor];

    self.navigationItem.leftBarButtonItem = [[UIBarButtonItem alloc]
                                            initWithBarButtonSystemItem:UIBarButtonSystemItemCancel
                                            target:self
                                            action:@selector(microAction)];

    self.navigationItem.rightBarButtonItems = @[
        [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemRefresh
                                                      target:self
                                                      action:@selector(tabSoftAction)],
        [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemAction
                                                      target:self
                                                      action:@selector(monthAction)],
        [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemBookmarks
                                                      target:self
                                                      action:@selector(addTensionAction)]
    ];
    self.navigationController.navigationBar.layoutMargins = UIEdgeInsetsMake(0, 0, 0, -10);

    _textView = [[UITextView alloc] init];
    _textView.font = [UIFont systemFontOfSize:11];
    _textView.editable = NO;
    _textView.backgroundColor = [UIColor systemBackgroundColor];
    _textView.textColor = [UIColor labelColor];
    _textView.translatesAutoresizingMaskIntoConstraints = NO;
    _textView.showsVerticalScrollIndicator = YES;
    _textView.showsHorizontalScrollIndicator = YES;
    _textView.alwaysBounceVertical = YES;
    
    _textView.scrollEnabled = YES;
    [self.view addSubview:_textView];

    [NSLayoutConstraint activateConstraints:@[
        [_textView.topAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.topAnchor],
        [_textView.leadingAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.leadingAnchor constant:8],
        [_textView.trailingAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.trailingAnchor constant:-8],
        [_textView.bottomAnchor constraintEqualToAnchor:self.view.bottomAnchor]
    ]];

    self.prettyLeadFactoryProducingUndone = [HertzViewController upsidePurpleOutArmenianOuncesGet];

    [self willLive];
}

- (void)willLive {
    if (!self.prettyLeadFactoryProducingUndone) {
        _textView.text = valueSubPop.sortVortexInverseBoundaryItalicInland;
        return;
    }

    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        NSString *logs;
        if (self.rareBlueDate) {
            logs = [self.prettyLeadFactoryProducingUndone describeHowDate:self.rareBlueDate];
        } else {
            logs = [self.prettyLeadFactoryProducingUndone kinVignette];
        }

        dispatch_async(dispatch_get_main_queue(), ^{
            if (logs.length > 0) {
                self.textView.text = logs;
                
                [self.textView scrollRangeToVisible:NSMakeRange(logs.length - 1, 1)];
            } else {
                self.textView.text = valueSubPop.previewsVerticalStakeInfoEstimateExporter;
            }

            [self fireMessage];
        });
    });
}

- (void)microAction {
    [self dismissViewControllerAnimated:YES completion:nil];
}

- (void)tabSoftAction {
    [self willLive];
}

- (void)fireMessage {
    if (self.rareBlueDate) {
        NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
        formatter.dateFormat = valueSubPop.stickyProfilesPubStyleBothCubeMolar;
        NSString *canBuilder = [formatter stringFromDate:self.rareBlueDate];

        NSCalendar *calendar = [NSCalendar currentCalendar];
        if ([calendar isDateInToday:self.rareBlueDate]) {
            self.title = valueSubPop.loopSoftRedoMouthMastersDiscount;
        } else if ([calendar isDateInYesterday:self.rareBlueDate]) {
            self.title = valueSubPop.waitTrustedFootnoteCopticStereoIronLevel;
        } else {
            self.title = canBuilder;
        }
    } else {
        self.title = valueSubPop.literSlightPreferEncryptedFarthestTomorrow;
    }
}

- (void)addTensionAction {
    if (!self.prettyLeadFactoryProducingUndone) {
        return;
    }

    NSArray<NSDate *> *availableDates = [self.prettyLeadFactoryProducingUndone dragRingLog];
    if (availableDates.count == 0) {
        UIAlertController *shelf = [UIAlertController alertControllerWithTitle:valueSubPop.dogLettishAndFreestyleChamberDeep
                                                                       message:valueSubPop.previewsVerticalStakeInfoEstimateExporter
                                                                preferredStyle:UIAlertControllerStyleAlert];
        [shelf addAction:[UIAlertAction actionWithTitle:valueSubPop.universalRelevanceLightPartnerBinHeart style:UIAlertActionStyleDefault handler:nil]];
        [self presentViewController:shelf animated:YES completion:nil];
        return;
    }

    UIAlertController *actionSheet = [UIAlertController alertControllerWithTitle:valueSubPop.dateBadgeLogInviteTopSamplerPeriod
                                                                         message:nil
                                                                  preferredStyle:UIAlertControllerStyleActionSheet];

    [actionSheet addAction:[UIAlertAction actionWithTitle:valueSubPop.literSlightPreferEncryptedFarthestTomorrow
                                                    style:UIAlertActionStyleDefault
                                                  handler:^(UIAlertAction *action) {
        self.rareBlueDate = nil;
        [self willLive];
    }]];

    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    formatter.dateFormat = valueSubPop.stickyProfilesPubStyleBothCubeMolar;

    NSCalendar *calendar = [NSCalendar currentCalendar];

    for (NSDate *date in availableDates) {
        NSString *title;
        if ([calendar isDateInToday:date]) {
            title = valueSubPop.loopSoftRedoMouthMastersDiscount;
        } else if ([calendar isDateInYesterday:date]) {
            title = valueSubPop.waitTrustedFootnoteCopticStereoIronLevel;
        } else {
            title = [formatter stringFromDate:date];
        }

        [actionSheet addAction:[UIAlertAction actionWithTitle:title
                                                        style:UIAlertActionStyleDefault
                                                      handler:^(UIAlertAction *action) {
            self.rareBlueDate = date;
            [self willLive];
        }]];
    }

    [actionSheet addAction:[UIAlertAction actionWithTitle:valueSubPop.splatIdenticalPreferUserCupThousand style:UIAlertActionStyleCancel handler:nil]];

    if ([UIDevice currentDevice].userInterfaceIdiom == UIUserInterfaceIdiomPad) {
        actionSheet.popoverPresentationController.barButtonItem = self.navigationItem.rightBarButtonItems.lastObject;
    }

    [self presentViewController:actionSheet animated:YES completion:nil];
}

- (void)monthAction {
    if (!self.prettyLeadFactoryProducingUndone) {
        return;
    }

    NSArray *butRatio = [self.prettyLeadFactoryProducingUndone tailScaling];
    if (butRatio.count == 0) {
        UIAlertController *shelf = [UIAlertController alertControllerWithTitle:valueSubPop.dogLettishAndFreestyleChamberDeep
                                                                       message:valueSubPop.outerLabelBayerSequencesBirthBankers
                                                                preferredStyle:UIAlertControllerStyleAlert];
        [shelf addAction:[UIAlertAction actionWithTitle:valueSubPop.universalRelevanceLightPartnerBinHeart style:UIAlertActionStyleDefault handler:nil]];
        [self presentViewController:shelf animated:YES completion:nil];
        return;
    }

    UIAlertController *actionSheet = [UIAlertController alertControllerWithTitle:valueSubPop.unifyIgnoreAnySixHallPrivilegeBattery
                                                                         message:nil
                                                                  preferredStyle:UIAlertControllerStyleActionSheet];

    [actionSheet addAction:[UIAlertAction actionWithTitle:valueSubPop.provisionDecryptOddMarkupWrestlingPageWord
                                                    style:UIAlertActionStyleDefault
                                                  handler:^(UIAlertAction *action) {
        [self spellScrolls];
    }]];

    for (NSURL *fileURL in butRatio) {
        NSString *fileName = fileURL.lastPathComponent;
        [actionSheet addAction:[UIAlertAction actionWithTitle:[NSString stringWithFormat:valueSubPop.configureDesiredRenewedYetPanShowMoment, fileName]
                                                        style:UIAlertActionStyleDefault
                                                      handler:^(UIAlertAction *action) {
            [self wrongHitFile:fileURL];
        }]];
    }

    [actionSheet addAction:[UIAlertAction actionWithTitle:valueSubPop.splatIdenticalPreferUserCupThousand style:UIAlertActionStyleCancel handler:nil]];

    if ([UIDevice currentDevice].userInterfaceIdiom == UIUserInterfaceIdiomPad) {
        actionSheet.popoverPresentationController.barButtonItem = self.navigationItem.rightBarButtonItems.lastObject;
    }

    [self presentViewController:actionSheet animated:YES completion:nil];
}

- (void)spellScrolls {
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        
        NSString *zipGrow = [self.prettyLeadFactoryProducingUndone hexNowFreeIron];

        dispatch_async(dispatch_get_main_queue(), ^{
            if (zipGrow.length > 0) {
                UIActivityViewController *jumpWetWax = [[UIActivityViewController alloc]
                                                       initWithActivityItems:@[zipGrow]
                                                       applicationActivities:nil];

                if ([UIDevice currentDevice].userInterfaceIdiom == UIUserInterfaceIdiomPad) {
                    jumpWetWax.popoverPresentationController.barButtonItem = self.navigationItem.rightBarButtonItems.lastObject;
                }

                [self presentViewController:jumpWetWax animated:YES completion:nil];
            }
        });
    });
}

- (void)wrongHitFile:(NSURL *)fileURL {
    UIActivityViewController *jumpWetWax = [[UIActivityViewController alloc]
                                           initWithActivityItems:@[fileURL]
                                           applicationActivities:nil];

    if ([UIDevice currentDevice].userInterfaceIdiom == UIUserInterfaceIdiomPad) {
        jumpWetWax.popoverPresentationController.barButtonItem = self.navigationItem.rightBarButtonItems.lastObject;
    }

    [self presentViewController:jumpWetWax animated:YES completion:nil];
}

@end
