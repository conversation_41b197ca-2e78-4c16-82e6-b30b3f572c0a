







#import <Foundation/Foundation.h>
#import "MQTTMessage.h"

static BOOL const MQTT_PERSISTENT = NO;
static NSInteger const MQTT_MAX_SIZE = 64 * 1024 * 1024;
static NSInteger const MQTT_MAX_WINDOW_SIZE = 16;
static NSInteger const MQTT_MAX_MESSAGES = 1024;



 
@protocol MQTTFlow


@property (strong, nonatomic) NSString *clientId;



@property (strong, nonatomic) NSNumber *incomingFlag;



@property (strong, nonatomic) NSNumber *retainedFlag;



@property (strong, nonatomic) NSNumber *commandType;



@property (strong, nonatomic) NSNumber *qosLevel;



@property (strong, nonatomic) NSNumber *messageId;



@property (strong, nonatomic) NSString *topic;



@property (strong, nonatomic) NSData *data;



@property (strong, nonatomic) NSDate *deadline;

@end




@protocol MQTTPersistence



@property (nonatomic) NSUInteger maxWindowSize;



@property (nonatomic) NSUInteger maxMessages;



@property (nonatomic) BOOL persistent;



@property (nonatomic) NSUInteger maxSize;



- (NSUInteger)windowSize:(NSString *)clientId;



- (id<MQTTFlow>)storeMessageForClientId:(NSString *)clientId
                                  topic:(NSString *)topic
                                   data:(NSData *)data
                             retainFlag:(BOOL)retainFlag
                                    qos:(MQTTQosLevel)qos
                                  msgId:(UInt16)msgId
                           incomingFlag:(BOOL)incomingFlag
                            commandType:(UInt8)commandType
                               deadline:(NSDate *)deadline;



- (void)deleteFlow:(id<MQTTFlow>)flow;



- (void)deleteAllFlowsForClientId:(NSString *)clientId;



- (NSArray *)allFlowsforClientId:(NSString *)clientId
                    incomingFlag:(BOOL)incomingFlag;



- (id<MQTTFlow>)flowforClientId:(NSString *)clientId
                   incomingFlag:(BOOL)incomingFlag
                      messageId:(UInt16)messageId;



- (void)sync;

@end
