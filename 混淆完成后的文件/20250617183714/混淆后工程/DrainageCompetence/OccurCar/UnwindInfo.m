






#import "UnwindInfo.h"
#import "InsetTowerConfig.h"
#import "ExcludeSlideExtendsSpaLongest.h"

@import AdSupport;
@import AppTrackingTransparency;
@import UIKit;

#import "sys/utsname.h" //utsname

@implementation UnwindInfo

+ (UIImage *)dayPrefixDesignBounceAdaptiveImage {
    NSDictionary *peerSpace = [[NSBundle mainBundle] infoDictionary];
    NSString *icon = [[peerSpace valueForKeyPath:@"CFBundleIcons.CFBundlePrimaryIcon.CFBundleIconFiles"] lastObject];
    return [UIImage imageNamed:icon];
}

+ (NSString *)scaleIdenticalIdentifier {
    return [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleIdentifier"];
}

+ (NSString *)alphabetNetFunPossibleCursors {
    return [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"];
}

+ (NSString *)toneEastName {
    NSString *displayName = [[NSBundle mainBundle] localizedInfoDictionary][@"CFBundleDisplayName"];

    if (!displayName) {
        displayName = [[NSBundle mainBundle] infoDictionary][@"CFBundleDisplayName"];
    }

    if (!displayName) {
        displayName = [[NSBundle mainBundle] infoDictionary][@"CFBundleName"];
    }

    return displayName;
}

+ (NSString *)diskOverlayName {
    return [UIDevice currentDevice].name;
}

+ (NSString *)sobFolderSpouseStaleAcross {
    return [ASIdentifierManager sharedManager].advertisingIdentifier.UUIDString;
}

+ (NSString *)anchorReclaimCubicForwardsMin {
    return [UIDevice currentDevice].identifierForVendor.UUIDString;
}

+ (NSString *)noticeGreenModel {
    struct utsname systemInfo;
    uname(&systemInfo);
    NSString *deviceModel = [NSString stringWithCString:systemInfo.machine encoding:NSUTF8StringEncoding];
    return deviceModel;
}

+ (NSString *)otherSelectingSuperiorsProcessesSuitable {
    return [UIDevice currentDevice].systemVersion;
}

+ (NSString *)legibleRuleSoftCloudClusterPath {
    return NSHomeDirectory().lastPathComponent;
}

+ (BOOL)bookScripts {
    NSUserDefaults *edgeSayStay = [NSUserDefaults standardUserDefaults];
    return [edgeSayStay boolForKey:valueSubPop.suffixFloatScrollingSmoothManager];
}
+ (void)setBookScripts:(BOOL)bookScripts {
    NSUserDefaults *edgeSayStay = [NSUserDefaults standardUserDefaults];
    [edgeSayStay setBool:bookScripts forKey:valueSubPop.suffixFloatScrollingSmoothManager];
    [edgeSayStay synchronize];
}

+ (void)dailyCurlEncryptContainsDirectionUploading:(void (^)(void))mainUses {
    static dispatch_once_t onlyToken;
    static BOOL optFarGender = NO;

    
    if (optFarGender) {
        LocalInfo(valueSubPop.slopeHandoverReservedTwoMarginsGlucoseInverted);
        return;
    }

    dispatch_once(&onlyToken, ^{
        optFarGender = YES;
        LocalInfo(valueSubPop.romanLaunchedCommonDaysInspiredInfer);

        if (@available(iOS 14, *)) {
            ATTrackingManagerAuthorizationStatus status = [ATTrackingManager trackingAuthorizationStatus];

            NSString *husbandCut = [self hueCanBitTableStatus:status];

            LocalInfo(valueSubPop.turnAndGrandsonHeightNormalCustodian, husbandCut, (long)status);

            switch (status) {
                case ATTrackingManagerAuthorizationStatusAuthorized:
                    LocalInfo(valueSubPop.cupTailParentReversesDependentSpellTerabytes);
                    optFarGender = NO;
                    if (mainUses) {
                        mainUses();
                    }
                    break;

                case ATTrackingManagerAuthorizationStatusDenied:
                    LocalInfo(valueSubPop.maxCommandsPassiveBitFix);
                    optFarGender = NO;
                    if (mainUses) {
                        mainUses();
                    }
                    break;

                case ATTrackingManagerAuthorizationStatusRestricted:
                    LocalInfo(valueSubPop.sawMandatoryTwentyMapSuitableAnchor);
                    optFarGender = NO;
                    if (mainUses) {
                        mainUses();
                    }
                    break;

                case ATTrackingManagerAuthorizationStatusNotDetermined:
                    LocalInfo(valueSubPop.adaptorMidLockSchemeFigureSmart);
                    [self boundForkPairAppearingFlightsQuotesBin:^{
                        optFarGender = NO;
                        if (mainUses) {
                            mainUses();
                        }
                    }];
                    break;
            }
        } else {
            LocalInfo(valueSubPop.restoredMoreTorchKitCellularTry);
            optFarGender = NO;
            if (mainUses) {
                mainUses();
            }
        }
    });
}

+ (void)boundForkPairAppearingFlightsQuotesBin:(void (^)(void))completion {
    LocalInfo(valueSubPop.condensedEjectDefinedDelayTabExclusive);

    
    static int smoothForbid = 6;

    __block id observer = [[NSNotificationCenter defaultCenter]
        addObserverForName:UIApplicationDidBecomeActiveNotification
                    object:nil
                     queue:[NSOperationQueue mainQueue]
                usingBlock:^(NSNotification *notification) {

        LocalInfo(valueSubPop.availMultiplyUnitSearchThumbnailBook, smoothForbid);

        
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(smoothForbid * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{


            UIApplicationState currentState = [UIApplication sharedApplication].applicationState;

            NSString *cardBring = [self quechuaRemovalState:currentState];

            LocalInfo(valueSubPop.areClinicalWasDistortedIronExtras, cardBring);

            if (currentState == UIApplicationStateActive) {
                LocalInfo(valueSubPop.engravedPanTenRetriedJoiningSafeCatalog);
                [self faxBelowBoldRequest:completion];
            } else {

                LocalInfo(valueSubPop.parentalProviderPostalMusicResourceLoss, cardBring);
                LocalInfo(valueSubPop.addSearchContactsTeethBlusteryClimbed);
                observer = [[NSNotificationCenter defaultCenter]
                    addObserverForName:UIApplicationDidBecomeActiveNotification
                                object:nil
                                 queue:[NSOperationQueue mainQueue]
                            usingBlock:^(NSNotification *notification) {
                    
                    LocalInfo(valueSubPop.useFlowAdvancesMarkCelsiusPin);
                    [[NSNotificationCenter defaultCenter] removeObserver:observer];
                    LocalInfo(valueSubPop.pubDeletionItsOffDatabasesConvergedSerial);
                    [self faxBelowBoldRequest:completion];
                }];
            }

        });

        LocalInfo(valueSubPop.generatorMusicMayAppendingSquashQuery);
        
        [[NSNotificationCenter defaultCenter] removeObserver:observer];
    }];
}

+ (void)faxBelowBoldRequest:(void (^)(void))completion {
    if (@available(iOS 14, *)) {
        LocalInfo(valueSubPop.twoMouseGeneratesFocusReturnsBridged);

        [ATTrackingManager requestTrackingAuthorizationWithCompletionHandler:^(ATTrackingManagerAuthorizationStatus status) {
            ATTrackingManagerAuthorizationStatus echoNetStatus = [ATTrackingManager trackingAuthorizationStatus];

            NSString *receiveSearchTopContextLetter = [self hueCanBitTableStatus:status];
            NSString *currentStatusDesc = [self hueCanBitTableStatus:echoNetStatus];

            LocalInfo(valueSubPop.rearrangeSpaMaintainAdvisedBuddyMain);
            LocalInfo(valueSubPop.lettishReaderMoreGestureEmbeddingResponse, receiveSearchTopContextLetter, (long)status);
            LocalInfo(valueSubPop.mustDepthPressureStreamToggleGoogleRectified, currentStatusDesc, (long)echoNetStatus);

            
            
            
            
            BOOL hitSlovakPan = (echoNetStatus == ATTrackingManagerAuthorizationStatusAuthorized) ||
                               (status == ATTrackingManagerAuthorizationStatusAuthorized);

            if (hitSlovakPan) {
                LocalInfo(valueSubPop.capturedFarLookupCatDashBrokenRanging);
                if (completion) {
                    completion();
                }
            } else if (echoNetStatus == ATTrackingManagerAuthorizationStatusNotDetermined) {
                LocalInfo(valueSubPop.stylisticDeleteExpertMinorNapStormBezel);
                [self pintElementsStreamedRematchDayBecomeRendered:completion rangeFileSpell:0];
            } else {
                LocalInfo(valueSubPop.revealKilovoltsExistWristPintCarrierCert);
                if (completion) {
                    completion();
                }
            }
        }];
    }
}


+ (NSString *)hueCanBitTableStatus:(ATTrackingManagerAuthorizationStatus)status  API_AVAILABLE(ios(14)){
    if (@available(iOS 14, *)) {
        switch (status) {
            case ATTrackingManagerAuthorizationStatusNotDetermined:
                return valueSubPop.blackIncrementHerRuleLemmaInstantWhile;
            case ATTrackingManagerAuthorizationStatusRestricted:
                return valueSubPop.interlaceLigaturesAlcoholMusicalResizingFlatness;
            case ATTrackingManagerAuthorizationStatusDenied:
                return valueSubPop.seedChestBirthWrapSubmitNordic;
            case ATTrackingManagerAuthorizationStatusAuthorized:
                return valueSubPop.cellSelfIncorrectTransientAcquireFarsi;
            default:
                return [NSString stringWithFormat:valueSubPop.getKannadaDidEmergencyPlainOrganize, (long)status];
        }
    }
    return valueSubPop.goldenLaunchingOddAgeInstallCookieFix;
}

+ (NSString *)quechuaRemovalState:(UIApplicationState)state {
    switch (state) {
        case UIApplicationStateActive:
            return valueSubPop.pintButFixThreadResultsSymmetric;
        case UIApplicationStateInactive:
            return valueSubPop.insulinFunWeeklyStatementOverallSkip;
        case UIApplicationStateBackground:
            return valueSubPop.sidePlayableGatherThermalArtIodine;
        default:
            return [NSString stringWithFormat:valueSubPop.soloistDublinDashLeaseCutLanguages, (long)state];
    }
}


+ (void)pintElementsStreamedRematchDayBecomeRendered:(void (^)(void))mainUses rangeFileSpell:(NSInteger)rangeFileSpell {
    NSInteger audioCount = 10;

    if (@available(iOS 14, *)) {
        ATTrackingManagerAuthorizationStatus echoNetStatus = [ATTrackingManager trackingAuthorizationStatus];

        NSString *husbandCut = [self hueCanBitTableStatus:echoNetStatus];

        LocalInfo(valueSubPop.previewPubStrongCommitFilePrint,
              (long)(rangeFileSpell + 1), (long)audioCount, husbandCut);

        
        if (echoNetStatus == ATTrackingManagerAuthorizationStatusNotDetermined && rangeFileSpell < audioCount) {
            LocalInfo(valueSubPop.issueGesturesCaretCrossMarkWindow, (long)(rangeFileSpell + 2));

            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0f * NSEC_PER_SEC)),
                          dispatch_get_main_queue(), ^{
                [self pintElementsStreamedRematchDayBecomeRendered:mainUses rangeFileSpell:rangeFileSpell + 1];
            });
            return;
        } else {
            
            
            if (rangeFileSpell >= audioCount) {
                LocalInfo(valueSubPop.willSentRepliesNapEncryptedExclusive, (long)audioCount);
                LocalInfo(valueSubPop.caseLoadReclaimBusIterationHas, husbandCut);
            } else {
                LocalInfo(valueSubPop.tagsOffsetsMidParagraphRarePacket, husbandCut);

                if (echoNetStatus == ATTrackingManagerAuthorizationStatusAuthorized) {
                    LocalInfo(valueSubPop.ruleDitherHyphenObjectCountedSentinel);
                } else if (echoNetStatus == ATTrackingManagerAuthorizationStatusDenied) {
                    LocalInfo(valueSubPop.removalTabularBecomeFactoriesBundleQualifier);
                } else if (echoNetStatus == ATTrackingManagerAuthorizationStatusRestricted) {
                    LocalInfo(valueSubPop.primaryConflictCatFiveStillCyrillic);
                }
            }

            LocalInfo(valueSubPop.visualFlipNativeLateStampWon);
            if (mainUses) {
                mainUses();
            }
        }
    } else {
        LocalInfo(valueSubPop.unsafeManualReasonDialogBiotinEarlyParsing);
        if (mainUses) {
            mainUses();
        }
    }
}
@end
