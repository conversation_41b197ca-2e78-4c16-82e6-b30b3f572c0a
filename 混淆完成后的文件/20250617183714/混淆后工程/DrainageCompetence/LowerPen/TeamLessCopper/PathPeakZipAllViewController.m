






#import "PathPeakZipAllViewController.h"
#import <WebKit/WebKit.h>
#import <WebKit/WKFoundation.h>
#import "NSString+DirtyFoot.h"
#import "NSString+ReversesAsk.h"

@interface PathPeakZipAllViewController ()<UIScrollViewDelegate,WKNavigationDelegate>

@property (nonatomic, strong) UISegmentedControl *earlyDrawStairControl;
@property (nonatomic, strong) UIView * receivePeriodOrnamentsSlovakWork;
@property (nonatomic, strong) UIView * legibleDaughtersDiscoverMindfulZip;

@property (nonatomic, strong) UIScrollView * sleepStaticView;

@end

@implementation PathPeakZipAllViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.refreshUseButton.hidden = YES;
    self.anchorAndButton.hidden = YES;
    
    UISegmentedControl *segmentView = [[UISegmentedControl alloc] initWithItems:@[BurmeseZone.shotCapEastCap.curvePrimariesTripleWidthIcyVertical,BurmeseZone.shotCapEastCap.partPlainMismatchNoneRedFocuses]];
    segmentView.layer.masksToBounds = YES; 
    segmentView.layer.cornerRadius = 2;    
    [segmentView setTitleTextAttributes:@{NSForegroundColorAttributeName:[BurmeseZone oddGaelicColor]} forState:UIControlStateSelected];
    [segmentView setTitleTextAttributes:@{NSForegroundColorAttributeName:[BurmeseZone oddGaelicColor]} forState:UIControlStateNormal];
    [self.view addSubview:segmentView];
    [segmentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.view);
        make.top.mas_equalTo(BurmeseZone.tipColorKind.moreChamber);
    }];
    [segmentView addTarget:self action:@selector(useDayBedTalk:) forControlEvents:UIControlEventValueChanged];
    self.earlyDrawStairControl = segmentView;
    
    _sleepStaticView = [[UIScrollView alloc]init];
    _sleepStaticView.pagingEnabled = YES;
    _sleepStaticView.delegate = self;
    [self.view addSubview:_sleepStaticView];
    [_sleepStaticView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view).offset(BurmeseZone.tipColorKind.valueSeconds);
        make.right.equalTo(self.view).offset(-BurmeseZone.tipColorKind.valueSeconds);
        make.top.equalTo(self.view).offset(BurmeseZone.tipColorKind.urgentAddMin);
        make.bottom.equalTo(self.view).offset(-BurmeseZone.tipColorKind.subscribeHex);
    }];
    
    UIView *containerView = [UIView new];
    containerView.backgroundColor = UIColor.whiteColor;
    [self.sleepStaticView addSubview:containerView];
    [containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.sleepStaticView);
        make.height.equalTo(_sleepStaticView);
    }];
    
    UIView * contentView1 = [self enhanceBarView:[BurmeseZone themePolicyOurEggPolicy]];
    [containerView addSubview:contentView1];
    [contentView1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(0);
        make.top.bottom.equalTo(containerView);
        make.width.mas_equalTo(self.sleepStaticView);
    }];
    self.receivePeriodOrnamentsSlovakWork = contentView1;
    
    UIView * contentView2 = [self enhanceBarView:[BurmeseZone lastPanoramasNotifyCoachedClockwise]];
    [containerView addSubview:contentView2];
    [contentView2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(contentView1.mas_right);
        make.bottom.top.equalTo(containerView);
        make.width.mas_equalTo(self.sleepStaticView);
    }];
    self.legibleDaughtersDiscoverMindfulZip = contentView2;
    
    [containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(contentView2.mas_right);
    }];
    
    if (![self.winParticle boolValue]) {
        UIButton *contactButton = [BurmeseZone hyphensEndCarExpectedSigmaColor:BurmeseZone.shotCapEastCap.emailBookNap];
        [contactButton setBackgroundImage:[UIImage bondDriveTightColor:[[UIColor lightGrayColor] colorWithAlphaComponent:0.5f]] forState:UIControlStateNormal];
        [contactButton addTarget:self action:@selector(flagAutomaticMountedTooFactPitch:) forControlEvents:(UIControlEventTouchUpInside)];
        [self.view addSubview:contactButton];
        [contactButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.view).offset(-BurmeseZone.tipColorKind.heartDryWas);
            make.centerX.equalTo(self.view).multipliedBy(.65);
            make.height.mas_equalTo(BurmeseZone.tipColorKind.formSplitArt);
        }];
    }
    
    UIButton *xxpk_okButton =  [BurmeseZone hyphensEndCarExpectedSigmaColor:BurmeseZone.shotCapEastCap.theWonFire];
    [xxpk_okButton addTarget:self action:@selector(canInvitePieceValidityResultsSimple:) forControlEvents:(UIControlEventTouchUpInside)];
    [self.view addSubview:xxpk_okButton];
    [xxpk_okButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.view).offset(-BurmeseZone.tipColorKind.heartDryWas);
        make.centerX.equalTo(self.view).multipliedBy(![self.winParticle boolValue]?1.35:1);
        make.height.mas_equalTo(BurmeseZone.tipColorKind.formSplitArt);
    }];
    
    segmentView.selectedSegmentIndex = 0;
    [self useDayBedTalk:segmentView];
}

-(void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView{
    [self.earlyDrawStairControl setSelectedSegmentIndex:scrollView.contentOffset.x/self.view.frame.size.width ==0?0:1];
    [self vitaminPeriodGroupSelfGrowText:scrollView.contentOffset.x/self.view.frame.size.width ==0?0:1];
}

- (void)useDayBedTalk:(UISegmentedControl *)sender {
    [self vitaminPeriodGroupSelfGrowText:sender.selectedSegmentIndex == 0?0:1];
    [self.sleepStaticView setContentOffset:CGPointMake(sender.selectedSegmentIndex == 0?0:self.sleepStaticView.frame.size.width, 0) animated:YES];
}

- (void)vitaminPeriodGroupSelfGrowText:(NSInteger)type {
    NSString *contentUrl = nil;
    UIView *contentView = nil;
    contentUrl = type == 0 ? [BurmeseZone themePolicyOurEggPolicy]:[BurmeseZone lastPanoramasNotifyCoachedClockwise];
    contentView = type == 0 ? self.receivePeriodOrnamentsSlovakWork:self.legibleDaughtersDiscoverMindfulZip;
    
    if (contentUrl.popUploadOne) {
        return;
    }
    
    if ([[contentUrl pathExtension] containsString:BurmeseZone.tipColorKind.sessionSpringMemberFlipHer]) {
        UITextView *ctView = (UITextView *)contentView;
        if (ctView.text.length > 0) {
            return;
        }

        
        [MoreVisualView purposeSimpleView:contentView];

        
        NSURL *url = [NSURL URLWithString:contentUrl];
        NSURLSessionDataTask *task = [[NSURLSession sharedSession] dataTaskWithURL:url
                                                                 completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {
            
            dispatch_async(dispatch_get_main_queue(), ^{
                
                [MoreVisualView encryptLowerAboutStepsonRefusedView:contentView];
                
                if (error || data.length == 0) {
                    
                    ctView.text = BurmeseZone.shotCapEastCap.helperLazyBackwardsFavoritesGoalSwimming;
                    return;
                }
                
                
                NSString *trustedSparse = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
                ctView.text = trustedSparse ?: BurmeseZone.shotCapEastCap.sigmaGrammarResourcesSugarDiamondVectorRole;
            });
        }];
        
        [task resume];

    }else {
        WKWebView *browse = (WKWebView *)contentView;
        if (!browse.isLoading && browse.estimatedProgress == 1) {
            [MoreVisualView encryptLowerAboutStepsonRefusedView:contentView];
            return;
        }
        [MoreVisualView purposeSimpleView:contentView];
        NSString *smoothBad =  [contentUrl.fiberBufferingDrivenPostUnifiedLinger stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
        NSURL *url = [NSURL URLWithString:smoothBad];
        NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:url cachePolicy:NSURLRequestReloadIgnoringCacheData timeoutInterval:15.0];
        [browse loadRequest:request];
    }
}

- (void)flagAutomaticMountedTooFactPitch:(id)sender {
    [self removeCycleCapturingAnchoringHockeyAction:nil];
    if (self.artsDecomposeGlyphDepartureExpand) {
        self.artsDecomposeGlyphDepartureExpand(NO);
    }
}

- (void)canInvitePieceValidityResultsSimple:(id)sender {
    [self removeCycleCapturingAnchoringHockeyAction:nil];
    if (self.artsDecomposeGlyphDepartureExpand) {
        self.artsDecomposeGlyphDepartureExpand(YES);
    }
}

- (UIView *)enhanceBarView:(NSString *)string {
    UIView *bitsLibrary = nil;
    if ([[string pathExtension] containsString:BurmeseZone.tipColorKind.sessionSpringMemberFlipHer]) {
        UITextView * carBirth = [UITextView new];
        carBirth.editable = NO;
        carBirth.backgroundColor = UIColor.whiteColor;
        carBirth.textColor = UIColor.grayColor;
        bitsLibrary = carBirth;
    }else {
        WKWebView *bundlesUndo = [[WKWebView alloc] initWithFrame:CGRectZero];
        bundlesUndo.backgroundColor = UIColor.clearColor;
        bundlesUndo.scrollView.backgroundColor = UIColor.lightGrayColor;
        bundlesUndo.opaque = YES;
        bundlesUndo.scrollView.bounces =NO;
        bundlesUndo.scrollView.showsVerticalScrollIndicator = NO;
        bundlesUndo.scrollView.showsHorizontalScrollIndicator = NO;
        bundlesUndo.navigationDelegate = self;
        bitsLibrary = bundlesUndo;
    }
    return bitsLibrary;
}

- (void)webView:(WKWebView *)webView didFinishNavigation:(WKNavigation *)navigation {
    [MoreVisualView encryptLowerAboutStepsonRefusedView:webView];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    UIEdgeInsets expectMust = [[TorchGainManager shared] lowercaseOffWindow].safeAreaInsets;
    expectMust.top    += 10;
    expectMust.left   += 10;
    expectMust.bottom += 10;
    expectMust.right  += 10;

    [self.view mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(expectMust);
    }];
}

@end
