






#import "ReadyHeadViewController.h"
#import "AndToast.h"
#import "ExcludeSlideExtendsSpaLongest.h"
@import WebKit;

@interface ReadyHeadViewController ()

@property (nonatomic, strong) UITextField *teamBinMediaTextField;
@property (nonatomic, strong) UITextField *herOffPanPortTextField;
@property (nonatomic, strong) UITextField *drumThreadsOverallCyclingDeclinedTextField;

@end

@implementation ReadyHeadViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.refreshUseButton.hidden = NO;
    
    
    self.teamBinMediaTextField = [BurmeseZone hyphenNameLatvianCellularCurlAccount];
    self.teamBinMediaTextField.text = [BurmeseZone fastestFreestyleClickDiagnoseUndoneName];
    self.teamBinMediaTextField.enabled = NO;
    [self.view addSubview:self.teamBinMediaTextField];
    [self.teamBinMediaTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(BurmeseZone.tipColorKind.immutableBox);
        make.left.mas_equalTo(BurmeseZone.tipColorKind.friendHerKit);
        make.right.mas_equalTo(-BurmeseZone.tipColorKind.friendHerKit);
        make.height.mas_equalTo(BurmeseZone.tipColorKind.subfamilyDid);
    }];
    
    
    self.herOffPanPortTextField = [BurmeseZone positionTelephonyExternalThreadIllegalPassword:NO];
    [self.view addSubview:self.herOffPanPortTextField];
    [self.herOffPanPortTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.teamBinMediaTextField.mas_bottom).offset(BurmeseZone.tipColorKind.fourTabular);
        make.left.right.equalTo(self.teamBinMediaTextField);
        make.height.mas_equalTo(BurmeseZone.tipColorKind.subfamilyDid);
    }];
    UIButton *acuteButton = self.herOffPanPortTextField.rightView.subviews.firstObject;
    [acuteButton addTarget:self action:@selector(pinGermanUserFactoriesTriggeredSubgroupsHandler:) forControlEvents:(UIControlEventTouchUpInside)];
    
    
    self.drumThreadsOverallCyclingDeclinedTextField = [BurmeseZone positionTelephonyExternalThreadIllegalPassword:YES];
    [self.view addSubview:self.drumThreadsOverallCyclingDeclinedTextField];
    [self.drumThreadsOverallCyclingDeclinedTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.herOffPanPortTextField.mas_bottom).offset(BurmeseZone.tipColorKind.fourTabular);
        make.left.right.equalTo(self.teamBinMediaTextField);
        make.height.mas_equalTo(BurmeseZone.tipColorKind.subfamilyDid);
    }];
    UIButton *nweRightButton = self.drumThreadsOverallCyclingDeclinedTextField.rightView.subviews.firstObject;
    [nweRightButton addTarget:self action:@selector(sixCubePredicateEscapedApplierStreamsRegisterHandler:) forControlEvents:(UIControlEventTouchUpInside)];
    
    
    UIButton *xxpk_changeBoxKeyButton = [BurmeseZone hyphensEndCarExpectedSigmaColor:BurmeseZone.shotCapEastCap.youInnerHumanEnteredVariable];
    [xxpk_changeBoxKeyButton addTarget:self action:@selector(cupUsedCaffeineViewChallengeFirmwareAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:xxpk_changeBoxKeyButton];
    [xxpk_changeBoxKeyButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.drumThreadsOverallCyclingDeclinedTextField.mas_bottom).offset(BurmeseZone.tipColorKind.fourTabular);
        make.left.mas_equalTo(BurmeseZone.tipColorKind.friendHerKit);
        make.right.mas_equalTo(-BurmeseZone.tipColorKind.friendHerKit);
        make.height.mas_equalTo(BurmeseZone.tipColorKind.immutableBox);
    }];
    
    
    UIView *xxpk_tapViewleft = [UIView new];
    xxpk_tapViewleft.userInteractionEnabled = YES;
    xxpk_tapViewleft.backgroundColor = UIColor.clearColor;
    [self.view addSubview:xxpk_tapViewleft];
    [xxpk_tapViewleft mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(BurmeseZone.tipColorKind.immutableBox, BurmeseZone.tipColorKind.immutableBox));
        make.left.bottom.equalTo(self.view);
    }];
    
    UITapGestureRecognizer *xxpk_tapGestureleft = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(sceneFatOldAsk)];
    xxpk_tapGestureleft.numberOfTapsRequired = BurmeseZone.tipColorKind.rawPlainRun;
    [xxpk_tapViewleft addGestureRecognizer:xxpk_tapGestureleft];
}

- (void)sceneFatOldAsk {
    [HertzViewController showFromViewController:self];
}

- (void)pinGermanUserFactoriesTriggeredSubgroupsHandler:(UIButton *)sender {
    sender.selected = !sender.isSelected;
    self.herOffPanPortTextField.secureTextEntry = !self.herOffPanPortTextField.isSecureTextEntry;
}

- (void)sixCubePredicateEscapedApplierStreamsRegisterHandler:(UIButton *)sender {
    sender.selected = !sender.isSelected;
    self.drumThreadsOverallCyclingDeclinedTextField.secureTextEntry = !self.drumThreadsOverallCyclingDeclinedTextField.isSecureTextEntry;
}

- (void)cupUsedCaffeineViewChallengeFirmwareAction:(UIButton *)sender  {
    if (self.herOffPanPortTextField.text.length < BurmeseZone.tipColorKind.traitEggRed ||
        self.drumThreadsOverallCyclingDeclinedTextField.text.length < BurmeseZone.tipColorKind.traitEggRed) {
        [ButAlertView presenceAutoUnifiedMultiplyTruncateFusion:BurmeseZone.shotCapEastCap.helperWax message:BurmeseZone.shotCapEastCap.hectaresSubmittedLogCertKnowVelocity completion:nil];
        return;
    }
    if ([self.oldEarDropIts respondsToSelector:@selector(portRaceAscentWeeklyDisorderClickLenientCalculateKey:balticKey:completion:)]) {
        [MoreVisualView rowsNotMenOurWindow];
        [self.oldEarDropIts portRaceAscentWeeklyDisorderClickLenientCalculateKey:self.herOffPanPortTextField.text balticKey:self.drumThreadsOverallCyclingDeclinedTextField.text completion:^(id object) {
            [MoreVisualView eulerInvertedCacheRowsProcessWindow];
            if ([object boolValue]) {
                [[TorchGainManager shared] reductionPlaybackRoundIgnoreSwapCertViewController:self.navigationController];
                [AndToast redDueGasp:BurmeseZone.shotCapEastCap.prepareMarkLeadAnyCricketMax];
                
                if (self.winParticle && [self.winParticle isKindOfClass:[WKWebView class]]) {
                    WKWebView *sixLanguage = (WKWebView *)self.winParticle;
                    [sixLanguage reload];
                }
            }
        }];
    }
}

@end
