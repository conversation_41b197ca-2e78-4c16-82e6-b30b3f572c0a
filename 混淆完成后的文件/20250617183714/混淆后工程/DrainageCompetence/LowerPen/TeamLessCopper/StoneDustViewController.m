






#import "StoneDustViewController.h"
#import "BuddyIndigoButton.h"
#import "AndToast.h"
#import "PolishFaxTextField.h"
#import "NSString+DirtyFoot.h"

@interface StoneDustViewController ()

@property (nonatomic, strong) PolishFaxTextField *sheMultiplyTextField;
@property (nonatomic, strong) UITextField *debuggingTextField;
@property (nonatomic, strong) UITextField *herOffPanPortTextField;
@property (nonatomic, strong) BuddyIndigoButton *binFinderButton;
@end

@implementation StoneDustViewController

- (BuddyIndigoButton *)binFinderButton {
    if (!_binFinderButton) {
        _binFinderButton = [[BuddyIndigoButton alloc] init];
    }
    return _binFinderButton;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    
    self.sheMultiplyTextField = [[PolishFaxTextField alloc] initWithController:self];
    [self.view addSubview:self.sheMultiplyTextField];
    [self.sheMultiplyTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(BurmeseZone.tipColorKind.urgentAddMin);
        make.left.mas_equalTo(BurmeseZone.tipColorKind.friendHerKit);
        make.right.mas_equalTo(-BurmeseZone.tipColorKind.friendHerKit);
        make.height.mas_equalTo(BurmeseZone.tipColorKind.subfamilyDid);
    }];
    
    
    self.debuggingTextField = [BurmeseZone collectorStrokeCalendarCorruptMisplacedBuddyCode];
    [self.view addSubview:self.debuggingTextField];
    [self.debuggingTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.sheMultiplyTextField.mas_bottom).offset(BurmeseZone.tipColorKind.fourTabular);
        make.left.mas_equalTo(BurmeseZone.tipColorKind.friendHerKit);
        make.height.mas_equalTo(BurmeseZone.tipColorKind.subfamilyDid);
    }];
    
    unitPub(self);
    self.binFinderButton.listGradeLikeAction = ^{
        areRetain(self);
        
        NSString *vowelWeekCode = self.sheMultiplyTextField.typeEveryWrist;
        NSString *pubEntryNow = self.sheMultiplyTextField.locationsYearsRoleRangingCubic;
        if (self.sheMultiplyTextField.sheMultiplyTextField.text.popUploadOne) {
            [self.binFinderButton tabPasswordForeverTenBand];
            [ButAlertView presenceAutoUnifiedMultiplyTruncateFusion:BurmeseZone.shotCapEastCap.helperWax message:BurmeseZone.shotCapEastCap.estonianBrokenRaceHisLowAge completion:nil];
            return;
        }
        if ([self.oldEarDropIts respondsToSelector:@selector(slideBelowWinAcceptedStarRangingBrushType:tenBandWarp:fastCode:completion:)]) {
            [MoreVisualView rowsNotMenOurWindow];
            [self.oldEarDropIts slideBelowWinAcceptedStarRangingBrushType:BurmeseZone.tipColorKind.romanHelloRelationsHostOutdoor tenBandWarp:pubEntryNow fastCode:vowelWeekCode completion:^(id object) {
                [MoreVisualView eulerInvertedCacheRowsProcessWindow];
                if ([object boolValue]) {
                    [AndToast redDueGasp:BurmeseZone.shotCapEastCap.addSkippedSiblingsLongerMusicalDidCode];
                }else {
                    [self.binFinderButton tabPasswordForeverTenBand];
                }
            }];
        }
    };
    [self.view addSubview:self.binFinderButton];
    [self.binFinderButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.debuggingTextField);
        make.height.equalTo(self.debuggingTextField);
        make.left.equalTo(self.debuggingTextField.mas_right).offset(BurmeseZone.tipColorKind.outputOptDue);
        make.right.equalTo(self.sheMultiplyTextField);
    }];
    
    
    [self.binFinderButton setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    
    
    self.herOffPanPortTextField = [BurmeseZone positionTelephonyExternalThreadIllegalPassword:YES];
    [self.view addSubview:self.herOffPanPortTextField];
    [self.herOffPanPortTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.debuggingTextField.mas_bottom).offset(BurmeseZone.tipColorKind.fourTabular);
        make.left.mas_equalTo(BurmeseZone.tipColorKind.friendHerKit);
        make.right.mas_equalTo(-BurmeseZone.tipColorKind.friendHerKit);
        make.height.mas_equalTo(BurmeseZone.tipColorKind.subfamilyDid);
    }];
    UIButton *nweRightButton = self.herOffPanPortTextField.rightView.subviews.firstObject;
    [nweRightButton addTarget:self action:@selector(pinGermanUserFactoriesTriggeredSubgroupsHandler:) forControlEvents:(UIControlEventTouchUpInside)];
    
    
    UIButton *xxpk_forgetButton = [BurmeseZone hyphensEndCarExpectedSigmaColor:BurmeseZone.shotCapEastCap.netSugarEggKey];
    [xxpk_forgetButton addTarget:self action:@selector(givenOpticalAfterProxiesRebusAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:xxpk_forgetButton];
    [xxpk_forgetButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.herOffPanPortTextField.mas_bottom).offset(BurmeseZone.tipColorKind.fourTabular);
        make.left.mas_equalTo(BurmeseZone.tipColorKind.friendHerKit);
        make.right.mas_equalTo(-BurmeseZone.tipColorKind.friendHerKit);
        make.height.mas_equalTo(BurmeseZone.tipColorKind.immutableBox);
    }];
}
- (void)pinGermanUserFactoriesTriggeredSubgroupsHandler:(UIButton *)sender {
    sender.selected = !sender.isSelected;
    self.herOffPanPortTextField.secureTextEntry = !self.herOffPanPortTextField.isSecureTextEntry;
}

- (void)givenOpticalAfterProxiesRebusAction:(id)sender {
    if (self.sheMultiplyTextField.sheMultiplyTextField.text.popUploadOne) {
        [ButAlertView presenceAutoUnifiedMultiplyTruncateFusion:BurmeseZone.shotCapEastCap.helperWax message:BurmeseZone.shotCapEastCap.estonianBrokenRaceHisLowAge completion:nil];
        return;
    }
    if (self.debuggingTextField.text.popUploadOne) {
        [ButAlertView presenceAutoUnifiedMultiplyTruncateFusion:BurmeseZone.shotCapEastCap.helperWax message:BurmeseZone.shotCapEastCap.endStatementRemotelyMenuThreshold completion:nil];
        return;
    }
    if (self.herOffPanPortTextField.text.length < BurmeseZone.tipColorKind.traitEggRed) {
        [ButAlertView presenceAutoUnifiedMultiplyTruncateFusion:BurmeseZone.shotCapEastCap.helperWax message:BurmeseZone.shotCapEastCap.hectaresSubmittedLogCertKnowVelocity completion:nil];
        return;
    }
    NSString *vowelWeekCode = self.sheMultiplyTextField.typeEveryWrist;
    NSString *pubEntryNow = self.sheMultiplyTextField.locationsYearsRoleRangingCubic;
    if ([self.oldEarDropIts respondsToSelector:@selector(softLookCyrillicCardProvidedWorkoutLiner:code:fastCode:eyeKey:completion:)]) {
        [MoreVisualView rowsNotMenOurWindow];
        [self.oldEarDropIts softLookCyrillicCardProvidedWorkoutLiner:pubEntryNow code:self.debuggingTextField.text fastCode:vowelWeekCode eyeKey:self.herOffPanPortTextField.text completion:^(id object) {
            [MoreVisualView eulerInvertedCacheRowsProcessWindow];
            [AndToast redDueGasp:BurmeseZone.shotCapEastCap.equalVisitScaleMeasureAre];
            if (object) {
                [self.polishShelfDelegate nowProduceServicesWideSliceWithName:object musicWayHasPassword:self.herOffPanPortTextField.text];
                [self removeCycleCapturingAnchoringHockeyAction:nil];
            }
        }];
    }
}

- (void)dealloc {
    [self.binFinderButton tabPasswordForeverTenBand];
}
@end
