






#import "TheDarkArmViewController.h"
#import "InsetTowerConfig.h"
#import "NSObject+PinModel.h"
#import "UIColor+JobColor.h"
#import "AndToast.h"

@interface TheDarkArmViewController ()<UITableViewDataSource, UITableViewDelegate>

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) NSMutableArray<NSDictionary *> *terabytesBarCatalogZoneSnapshot; 
@property (nonatomic, strong) NSArray<NSArray<NSString *> *> *standBandNever; 
@property (nonatomic, strong) NSMutableArray<NSString *> *sectionTitles; 

@end

@implementation TheDarkArmViewController


- (void)viewDidLoad {
    [super viewDidLoad];
    
    
    _terabytesBarCatalogZoneSnapshot = [NSMutableArray array];
    _standBandNever = @[];
    _sectionTitles = [NSMutableArray array];
    
    [self burnCookieView];
}

- (void)viewWillAppear:(BOOL)animated {
    
    UIEdgeInsets expectMust = [[TorchGainManager shared] lowercaseOffWindow].safeAreaInsets;
    
    expectMust.top    += 10;
    expectMust.left   += 10;
    expectMust.bottom += 10;
    expectMust.right  += 10;

    [self.view mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(expectMust);
    }];
}


- (void)burnCookieView {
    _tableView = [[UITableView alloc] initWithFrame:self.view.bounds style:UITableViewStyleGrouped];
    _tableView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
    _tableView.dataSource = self;
    _tableView.delegate = self;
    _tableView.backgroundColor = [UIColor clearColor];
    [self.view addSubview:_tableView];
    [_tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.anchorAndButton.mas_bottom);
        make.left.right.bottom.equalTo(self.view);
    }];
    
    
    [_tableView registerClass:[UITableViewCell class] forCellReuseIdentifier:NSStringFromClass(self.class)];
}


- (NSArray<NSString *> *)generateVisualDictionary:(NSDictionary *)dict {
    return [[dict allKeys] sortedArrayUsingSelector:@selector(caseInsensitiveCompare:)];
}

- (void)slantMolarOffInfo:(NSDictionary *)info withTitle:(NSString *)title {
    if (!info || ![info isKindOfClass:[NSDictionary class]]) {
        return;
    }
    
    
    dispatch_async(dispatch_get_main_queue(), ^{
        @synchronized (self) {
            
            [self->_terabytesBarCatalogZoneSnapshot addObject:[info copy]];
            NSArray *forAdapter = [self generateVisualDictionary:info];
            self->_standBandNever = [self->_standBandNever arrayByAddingObject:forAdapter];
            [self->_sectionTitles addObject:title];
            
            
            [self.tableView reloadData];
        }
    });
}


- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return _terabytesBarCatalogZoneSnapshot.count;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return _standBandNever[section].count;
}

- (NSString *)tableView:(UITableView *)tableView titleForHeaderInSection:(NSInteger)section {
    return _sectionTitles[section];
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass(self.class) forIndexPath:indexPath];
    
    NSString *key;
    id value;
    NSInteger sectionIndex = indexPath.section;
    key = _standBandNever[sectionIndex][indexPath.row];
    value = _terabytesBarCatalogZoneSnapshot[sectionIndex][key];
    BOOL areaCard = [value isKindOfClass:[NSDictionary class]] || [value isKindOfClass:[NSArray class]];
    cell.backgroundColor = [UIColor clearColor];
    
    
    for (UIView *subview in cell.contentView.subviews) {
        [subview removeFromSuperview];
    }
    
    
    UILabel *outLabel = [[UILabel alloc] init];
    outLabel.font = [UIFont monospacedSystemFontOfSize:14 weight:UIFontWeightMedium];
    outLabel.textColor = [UIColor darkGrayColor];
    outLabel.text = key;
    outLabel.numberOfLines = 0;
    [cell.contentView addSubview:outLabel];
    
    
    UILabel *blackLabel = [[UILabel alloc] init];
    blackLabel.font = [UIFont monospacedSystemFontOfSize:14 weight:UIFontWeightRegular];
    blackLabel.textColor = [UIColor blackColor];
    blackLabel.numberOfLines = 0;
    blackLabel.textAlignment = NSTextAlignmentRight;
    [cell.contentView addSubview:blackLabel];
    
    
    [outLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(cell.contentView).offset(BurmeseZone.tipColorKind.valueSeconds);
        make.top.equalTo(cell.contentView).offset(BurmeseZone.tipColorKind.outputOptDue);
        make.bottom.equalTo(cell.contentView).offset(-BurmeseZone.tipColorKind.outputOptDue);
        make.width.equalTo(cell.contentView.mas_width).multipliedBy(areaCard?BurmeseZone.tipColorKind.penScrolling:BurmeseZone.tipColorKind.groupParental);
    }];
    
    [blackLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(cell.contentView).offset(-BurmeseZone.tipColorKind.valueSeconds);
        make.top.equalTo(cell.contentView).offset(BurmeseZone.tipColorKind.outputOptDue);
        make.bottom.equalTo(cell.contentView).offset(-BurmeseZone.tipColorKind.outputOptDue);
        make.left.equalTo(outLabel.mas_right).offset(BurmeseZone.tipColorKind.outputOptDue);
    }];
    
    
    if (areaCard) {
        cell.accessoryType = UITableViewCellAccessoryDisclosureIndicator;
    } else {
        blackLabel.text = [value description];
        cell.accessoryType = UITableViewCellAccessoryNone;
    }
    
    return cell;
}


- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    
    id value;
    NSString *key;
    
    NSInteger sectionIndex = indexPath.section;
    key = _standBandNever[sectionIndex][indexPath.row];
    value = _terabytesBarCatalogZoneSnapshot[sectionIndex][key];
    
    
    if ([value isKindOfClass:[NSDictionary class]]) {
        [self sizeSpaMapDictionary:value withTitle:key];
    } else if ([value isKindOfClass:[NSArray class]]) {
        [self spaBothSpaArray:value withTitle:key];
    } else {
        
        UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
        [pasteboard setString:[value description]];
        [AndToast letters:BurmeseZone.tipColorKind.identicalFemaleDetectionBackupUppercaseRemoval];
    }
}


- (void)sizeSpaMapDictionary:(NSDictionary *)dict withTitle:(NSString *)title {
    TheDarkArmViewController *guestFun = [[TheDarkArmViewController alloc] init];
    [self.navigationController pushViewController:guestFun animated:NO];
    [guestFun slantMolarOffInfo:dict withTitle:title];
}

- (void)spaBothSpaArray:(NSArray *)array withTitle:(NSString *)title {
    
    NSMutableDictionary *standDict = [NSMutableDictionary dictionary];
    for (NSInteger i = 0; i < array.count; i++) {
        standDict[[NSString stringWithFormat:@"[%ld]", (long)i]] = array[i];
    }
    
    TheDarkArmViewController *guestFun = [[TheDarkArmViewController alloc] init];
    [self.navigationController pushViewController:guestFun animated:NO];
    [guestFun slantMolarOffInfo:standDict withTitle:[NSString stringWithFormat:@"%@ (Array)", title]];
}

@end
