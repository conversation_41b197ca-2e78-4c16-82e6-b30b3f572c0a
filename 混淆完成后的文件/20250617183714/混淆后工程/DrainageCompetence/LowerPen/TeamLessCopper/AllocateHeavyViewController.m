






#import "AllocateHeavyViewController.h"
#import "BuddyIndigoButton.h"
#import "AndToast.h"
#import "PolishFaxTextField.h"
#import "NSString+DirtyFoot.h"
@import WebKit;

@interface AllocateHeavyViewController ()

@property (nonatomic, strong) PolishFaxTextField *sheMultiplyTextField;
@property (nonatomic, strong) UITextField *debuggingTextField;
@property (nonatomic, strong) BuddyIndigoButton *binFinderButton;

@end

@implementation AllocateHeavyViewController


- (BuddyIndigoButton *)binFinderButton {
    if (!_binFinderButton) {
        _binFinderButton = [[BuddyIndigoButton alloc] init];
    }
    return _binFinderButton;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.refreshUseButton.hidden = [self.winParticle[0] boolValue];
    
    UILabel *tipLabel = [BurmeseZone createPreparingHyphenLogDeclined:BurmeseZone.shotCapEastCap.keysAlcoholPenDecaySuchRemainder];
    tipLabel.numberOfLines = 0;
    [self.view addSubview:tipLabel];
    [tipLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(BurmeseZone.tipColorKind.sonPredicted);
        make.left.mas_equalTo(BurmeseZone.tipColorKind.sonPredicted);
        make.right.mas_equalTo(-BurmeseZone.tipColorKind.sonPredicted);
    }];
    
    
    self.sheMultiplyTextField = [[PolishFaxTextField alloc] initWithController:self];
    [self.view addSubview:self.sheMultiplyTextField];
    [self.sheMultiplyTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(tipLabel.mas_bottom).offset(BurmeseZone.tipColorKind.stampGetZone);
        make.left.mas_equalTo(BurmeseZone.tipColorKind.friendHerKit);
        make.right.mas_equalTo(-BurmeseZone.tipColorKind.friendHerKit);
        make.height.mas_equalTo(BurmeseZone.tipColorKind.subfamilyDid);
    }];
    
    
    self.debuggingTextField = [BurmeseZone collectorStrokeCalendarCorruptMisplacedBuddyCode];
    [self.view addSubview:self.debuggingTextField];
    [self.debuggingTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.sheMultiplyTextField.mas_bottom).offset(BurmeseZone.tipColorKind.indexingFile);
        make.left.mas_equalTo(BurmeseZone.tipColorKind.friendHerKit);
        make.height.mas_equalTo(BurmeseZone.tipColorKind.subfamilyDid);
    }];
    
    unitPub(self);
    self.binFinderButton.listGradeLikeAction = ^{
        areRetain(self);
        NSString *vowelWeekCode = self.sheMultiplyTextField.typeEveryWrist;
        NSString *pubEntryNow = self.sheMultiplyTextField.locationsYearsRoleRangingCubic;
        if (self.sheMultiplyTextField.sheMultiplyTextField.text.popUploadOne) {
            [self.binFinderButton tabPasswordForeverTenBand];
            [ButAlertView presenceAutoUnifiedMultiplyTruncateFusion:BurmeseZone.shotCapEastCap.helperWax message:BurmeseZone.shotCapEastCap.estonianBrokenRaceHisLowAge completion:nil];
            return;
        }
        if ([self.oldEarDropIts respondsToSelector:@selector(slideBelowWinAcceptedStarRangingBrushType:tenBandWarp:fastCode:completion:)]) {
            [MoreVisualView rowsNotMenOurWindow];
            [self.oldEarDropIts slideBelowWinAcceptedStarRangingBrushType:BurmeseZone.tipColorKind.tryNetSunClear tenBandWarp:pubEntryNow fastCode:vowelWeekCode completion:^(id object) {
                [MoreVisualView eulerInvertedCacheRowsProcessWindow];
                if ([object boolValue]) {
                    [AndToast redDueGasp:BurmeseZone.shotCapEastCap.addSkippedSiblingsLongerMusicalDidCode];
                }else {
                    [self.binFinderButton tabPasswordForeverTenBand];
                }
            }];
        }
    };
    [self.view addSubview:self.binFinderButton];
    [self.binFinderButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.debuggingTextField);
        make.height.equalTo(self.debuggingTextField);
        make.left.equalTo(self.debuggingTextField.mas_right).offset(BurmeseZone.tipColorKind.outputOptDue);
        make.right.equalTo(self.sheMultiplyTextField);
    }];
    
    
    [self.binFinderButton setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    
    
    UIButton *xxpk_bindButton = [BurmeseZone hyphensEndCarExpectedSigmaColor:BurmeseZone.shotCapEastCap.enableSpokenGaspStakeMath];
    [xxpk_bindButton addTarget:self action:@selector(deprecateSmallestImplicitUploadedWetAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:xxpk_bindButton];
    [xxpk_bindButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.debuggingTextField.mas_bottom).offset(BurmeseZone.tipColorKind.indexingFile);
        make.left.mas_equalTo(BurmeseZone.tipColorKind.friendHerKit);
        make.right.mas_equalTo(-BurmeseZone.tipColorKind.friendHerKit);
        make.height.mas_equalTo(BurmeseZone.tipColorKind.immutableBox);
    }];
}

- (void)deprecateSmallestImplicitUploadedWetAction:(id)sender {
    if (self.sheMultiplyTextField.sheMultiplyTextField.text.popUploadOne) {
        [ButAlertView presenceAutoUnifiedMultiplyTruncateFusion:BurmeseZone.shotCapEastCap.helperWax message:BurmeseZone.shotCapEastCap.estonianBrokenRaceHisLowAge completion:nil];
        return;
    }
    if (self.debuggingTextField.text.popUploadOne) {
        [ButAlertView presenceAutoUnifiedMultiplyTruncateFusion:BurmeseZone.shotCapEastCap.helperWax message:BurmeseZone.shotCapEastCap.endStatementRemotelyMenuThreshold completion:nil];
        return;
    }
    NSString *vowelWeekCode = self.sheMultiplyTextField.typeEveryWrist;
    NSString *pubEntryNow = self.sheMultiplyTextField.locationsYearsRoleRangingCubic;
    if ([self.oldEarDropIts respondsToSelector:@selector(uniqueDiastolicFairSelectingPopTwoChat:code:fastCode:completion:)]) {
        [MoreVisualView rowsNotMenOurWindow];
        [self.oldEarDropIts uniqueDiastolicFairSelectingPopTwoChat:pubEntryNow code:self.debuggingTextField.text fastCode:vowelWeekCode completion:^(id object) {
            [MoreVisualView eulerInvertedCacheRowsProcessWindow];
            if ([object boolValue]) {
                [[TorchGainManager shared] reductionPlaybackRoundIgnoreSwapCertViewController:self.navigationController];
                [AndToast redDueGasp:BurmeseZone.shotCapEastCap.expiredCloudySeleniumOperationSiteLow];
                if ([self.winParticle[1] isKindOfClass:[WKWebView class]]) {
                    WKWebView *sixLanguage = (WKWebView *)self.winParticle[1];
                    [sixLanguage reload];
                }
            }
        }];
    }
}

- (void)dealloc {
    [self.binFinderButton tabPasswordForeverTenBand];
}

@end
