






#import "OptShowDryViewController.h"
#import "MergeNameViewController.h"
#import "StoneDustViewController.h"

@interface OptShowDryViewController ()<VectorTagDelegate>

@property (nonatomic, strong) UITextField *teamBinMediaTextField;
@property (nonatomic, strong) UITextField *herOffPanPortTextField;
@end

@implementation OptShowDryViewController


- (void)viewDidLoad {
    [super viewDidLoad];
    
    
    self.teamBinMediaTextField = [BurmeseZone hyphenNameLatvianCellularCurlAccount];
    [self.view addSubview:self.teamBinMediaTextField];
    [self.teamBinMediaTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(BurmeseZone.tipColorKind.urgentAddMin);
        make.left.mas_equalTo(BurmeseZone.tipColorKind.friendHerKit);
        make.right.mas_equalTo(-BurmeseZone.tipColorKind.friendHerKit);
        make.height.mas_equalTo(BurmeseZone.tipColorKind.subfamilyDid);
    }];
    
    
    self.herOffPanPortTextField = [BurmeseZone positionTelephonyExternalThreadIllegalPassword:NO];
    [self.view addSubview:self.herOffPanPortTextField];
    [self.herOffPanPortTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.teamBinMediaTextField.mas_bottom).offset(BurmeseZone.tipColorKind.stampGetZone);
        make.left.right.equalTo(self.teamBinMediaTextField);
        make.height.mas_equalTo(BurmeseZone.tipColorKind.subfamilyDid);
    }];
    UIButton *penSolo = self.herOffPanPortTextField.rightView.subviews.firstObject;
    [penSolo addTarget:self action:@selector(pinGermanUserFactoriesTriggeredSubgroupsHandler:) forControlEvents:(UIControlEventTouchUpInside)];
    
    
    UIButton *button = [BurmeseZone hyphensEndCarExpectedSigmaColor:BurmeseZone.shotCapEastCap.guideBaltic];
    [button addTarget:self action:@selector(tomorrowCriticalCapsSettingAdvisedMalformedRowAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:button];
    [button mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.herOffPanPortTextField.mas_bottom).offset(BurmeseZone.tipColorKind.stampGetZone);
        make.left.right.equalTo(self.herOffPanPortTextField);
        make.height.mas_equalTo(BurmeseZone.tipColorKind.immutableBox);
    }];
    
    
    if (![BurmeseZone learnedPostalCertConcludeLeftover]) {
        UIButton *salient = [BurmeseZone hyphensEndCarExpectedSigmaColor:[NSString stringWithFormat:@" %@ ",BurmeseZone.shotCapEastCap.sayHundredHex]];
        [salient addTarget:self action:@selector(otherMenKilogramsCocoaGuideAction:) forControlEvents:UIControlEventTouchUpInside];
        [self.view addSubview:salient];
        [salient mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(button);
            make.top.equalTo(button.mas_bottom).offset(BurmeseZone.tipColorKind.indexingFile);
            make.height.mas_equalTo(BurmeseZone.tipColorKind.burnNowDecay);
        }];
    }
    
    
    UIButton *button2 = [BurmeseZone axialForwardUnwindUnionCharacter:BurmeseZone.shotCapEastCap.netSugarEggKey];
    [button2 addTarget:self action:@selector(givenOpticalAfterProxiesRebusAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:button2];
    [button2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(button);
        make.top.equalTo(button.mas_bottom).offset(BurmeseZone.tipColorKind.indexingFile);
        make.height.mas_equalTo(BurmeseZone.tipColorKind.burnNowDecay);
    }];
}

- (void)pinGermanUserFactoriesTriggeredSubgroupsHandler:(UIButton *)sender {
    sender.selected = !sender.isSelected;
    self.herOffPanPortTextField.secureTextEntry = !self.herOffPanPortTextField.isSecureTextEntry;
}

- (void)tomorrowCriticalCapsSettingAdvisedMalformedRowAction:(UIButton *)sender {
    if (self.teamBinMediaTextField.text.length < BurmeseZone.tipColorKind.traitEggRed) {
        [ButAlertView presenceAutoUnifiedMultiplyTruncateFusion:BurmeseZone.shotCapEastCap.helperWax message:BurmeseZone.shotCapEastCap.drawUnsavedLooperVowelPlayingActivity completion:nil];
        return;
    }
    if (self.herOffPanPortTextField.text.length < BurmeseZone.tipColorKind.traitEggRed) {
        [ButAlertView presenceAutoUnifiedMultiplyTruncateFusion:BurmeseZone.shotCapEastCap.helperWax message:BurmeseZone.shotCapEastCap.hectaresSubmittedLogCertKnowVelocity completion:nil];
        return;
    }
    if ([self.oldEarDropIts respondsToSelector:@selector(wetDigitalSpeakRowWasItsRollName:addKey:completion:)]) {
        [MoreVisualView rowsNotMenOurWindow];
        [self.oldEarDropIts wetDigitalSpeakRowWasItsRollName:self.teamBinMediaTextField.text addKey:self.herOffPanPortTextField.text completion:^(id object) {
            [MoreVisualView eulerInvertedCacheRowsProcessWindow];
        }];
    }
}

- (void)otherMenKilogramsCocoaGuideAction:(UIButton *)sender {
    MergeNameViewController *idleMan = [MergeNameViewController new];
    idleMan.oldEarDropIts = self.oldEarDropIts;
    [self.navigationController pushViewController:idleMan animated:NO];
}

- (void)givenOpticalAfterProxiesRebusAction:(UIButton *)sender {
    StoneDustViewController *idleMan = [StoneDustViewController new];
    idleMan.oldEarDropIts = self.oldEarDropIts;
    idleMan.polishShelfDelegate = self;
    [self.navigationController pushViewController:idleMan animated:NO];
    
}

- (void)nowProduceServicesWideSliceWithName:(NSString *)xxpk_forgetName musicWayHasPassword:(NSString *)musicWayHasPassword {
    self.teamBinMediaTextField.text = xxpk_forgetName;
    self.herOffPanPortTextField.text = musicWayHasPassword;
    UIButton *penSolo = self.herOffPanPortTextField.rightView.subviews.firstObject;
    penSolo.selected = YES;
    self.herOffPanPortTextField.secureTextEntry = NO;
}

@end
