






#import "TextRetOldEggViewController.h"
#import "AndToast.h"
#import "UnwindInfo.h"

@interface TextRetOldEggViewController ()

@property (nonatomic, strong) UIImageView *collisionView;
@property (nonatomic, strong) UIButton *validityWhoButton;
@property (nonatomic, strong) UIView *boxMarginView;
@property (nonatomic, strong) UILabel *youColorLabel;
@property (nonatomic, strong) UITextField *teamBinMediaTextField;
@property (nonatomic, strong) UITextField *herOffPanPortTextField;
@end

@implementation TextRetOldEggViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.refreshUseButton.hidden = YES;
    
    if ([UnwindInfo dayPrefixDesignBounceAdaptiveImage]) {
        self.collisionView = [[UIImageView alloc] initWithImage:[UnwindInfo dayPrefixDesignBounceAdaptiveImage]];
        [self.view addSubview:self.collisionView];
        self.collisionView.hidden = YES;
        [self.collisionView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.mas_equalTo(BurmeseZone.tipColorKind.immutableBox);
            make.left.equalTo(self.anchorAndButton.mas_right);
            make.top.equalTo(self.view).offset(BurmeseZone.tipColorKind.outputOptDue);
        }];
    }
    
    self.boxMarginView = [BurmeseZone boxMarginView];
    self.boxMarginView.hidden = YES;
    [self.view addSubview:self.boxMarginView];
    [self.boxMarginView mas_makeConstraints:^(MASConstraintMaker *make) {
        if ([UnwindInfo dayPrefixDesignBounceAdaptiveImage]) {
            make.centerY.equalTo(self.collisionView);
            make.left.equalTo(self.collisionView.mas_right).offset(BurmeseZone.tipColorKind.fourTabular);
        }else {
            make.top.equalTo(self.view).offset(BurmeseZone.tipColorKind.outputOptDue);
            make.left.equalTo(self.anchorAndButton.mas_right).offset(BurmeseZone.tipColorKind.fourTabular);
        }
        make.right.equalTo(self.refreshUseButton.mas_left);
        make.height.mas_equalTo(BurmeseZone.tipColorKind.immutableBox);
    }];
    
    self.youColorLabel = [BurmeseZone createPreparingHyphenLogDeclined:BurmeseZone.shotCapEastCap.ligatureToleranceAboveBlueHost];
    self.youColorLabel.numberOfLines = 0;
    [self.view addSubview:self.youColorLabel];
    [self.youColorLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(BurmeseZone.tipColorKind.romanAddNine);
        make.left.mas_equalTo(BurmeseZone.tipColorKind.sonPredicted);
        make.right.mas_equalTo(-BurmeseZone.tipColorKind.sonPredicted);
    }];
    
    
    self.teamBinMediaTextField = [BurmeseZone hyphenNameLatvianCellularCurlAccount];
    self.teamBinMediaTextField.enabled = NO;
    self.teamBinMediaTextField.text = self.winParticle[BurmeseZone.tipColorKind.miterEnergyName];
    [self workButOverageNorwegianSaltUnderlineView:self.teamBinMediaTextField text:BurmeseZone.shotCapEastCap.equalitySelectedOrdinaryDarkenInsertion];
    [self.view addSubview:self.teamBinMediaTextField];
    [self.teamBinMediaTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.youColorLabel.mas_bottom).offset(BurmeseZone.tipColorKind.stampGetZone);
        make.left.mas_equalTo(BurmeseZone.tipColorKind.friendHerKit);
        make.right.mas_equalTo(-BurmeseZone.tipColorKind.friendHerKit);
        make.height.mas_equalTo(BurmeseZone.tipColorKind.subfamilyDid);
    }];
    
    
    self.herOffPanPortTextField = [BurmeseZone hyphenNameLatvianCellularCurlAccount];
    self.herOffPanPortTextField.enabled = NO;
    self.herOffPanPortTextField.text = self.winParticle[BurmeseZone.tipColorKind.resultsDragKey];
    [self workButOverageNorwegianSaltUnderlineView:self.herOffPanPortTextField text:BurmeseZone.shotCapEastCap.mealUndefinedArmpitJumpScore];
    [self.view addSubview:self.herOffPanPortTextField];
    [self.herOffPanPortTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.teamBinMediaTextField.mas_bottom).offset(BurmeseZone.tipColorKind.stampGetZone);
        make.left.right.equalTo(self.teamBinMediaTextField);
        make.height.mas_equalTo(BurmeseZone.tipColorKind.subfamilyDid);
    }];
    
    
    UIButton *xxpk_saveButton = [BurmeseZone hyphensEndCarExpectedSigmaColor:BurmeseZone.shotCapEastCap.invalidChinaPlanRelationShare];
    [xxpk_saveButton addTarget:self action:@selector(willSearchingUndoneRepliesBreakingAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:xxpk_saveButton];
    [xxpk_saveButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.herOffPanPortTextField.mas_bottom).offset(BurmeseZone.tipColorKind.stampGetZone);
        make.left.right.equalTo(self.herOffPanPortTextField);
        make.height.mas_equalTo(BurmeseZone.tipColorKind.immutableBox);
    }];
    
    
    self.validityWhoButton = [BurmeseZone axialForwardUnwindUnionCharacter:BurmeseZone.shotCapEastCap.foldElementSummariesElapsedGroupingExponent];
    [self.validityWhoButton setContentEdgeInsets:UIEdgeInsetsMake(0, 0, 0, 0)];
    [self.validityWhoButton addTarget:self action:@selector(controlsRopeHueZipShutdownAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.validityWhoButton];
    [self.validityWhoButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(xxpk_saveButton.mas_bottom).offset(BurmeseZone.tipColorKind.proteinCase);
        make.centerX.equalTo(self.view);
    }];
}

- (void)workButOverageNorwegianSaltUnderlineView:(UITextField *)textField text:(NSString *)text
{
    CGRect frame = {{0,0},CGSizeMake(BurmeseZone.tipColorKind.immutableBox, BurmeseZone.tipColorKind.subfamilyDid)};
    UILabel *leftview = [[UILabel alloc] initWithFrame:frame];
    leftview.text = text;
    leftview.textColor = UIColor.redColor;
    textField.leftViewMode = UITextFieldViewModeAlways;
    textField.leftView = leftview;
}

- (void)controlsRopeHueZipShutdownAction:(UIButton *)sender {
    [[TorchGainManager shared] reductionPlaybackRoundIgnoreSwapCertViewController:self.navigationController];
}

- (void)willSearchingUndoneRepliesBreakingAction:(UIButton *)sender {
    sender.hidden = YES;
    self.youColorLabel.hidden = YES;
    self.boxMarginView.hidden = NO;
    self.collisionView.hidden = NO;
    self.validityWhoButton.hidden = YES;
    
    [self.view mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.center.mas_equalTo(self.view.superview);
        make.size.mas_equalTo(CGSizeMake(BurmeseZone.tipColorKind.rationalDroppedDecomposeDownloadsPullWidth, BurmeseZone.tipColorKind.taggingCompareBundleRearrangeStorylineExits-BurmeseZone.tipColorKind.helloDayWeek));
    }];
    [self.teamBinMediaTextField mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.boxMarginView.mas_bottom).offset(BurmeseZone.tipColorKind.stampGetZone);
    }];
    [self.view layoutIfNeeded];
    
    BOOL front = [[[[NSBundle mainBundle] infoDictionary] allKeys] containsObject:BurmeseZone.tipColorKind.telephoneShortSawImperialHelpNegateOptional];
    if (!front) {
        self.refreshUseButton.hidden = NO;
        [ButAlertView presenceAutoUnifiedMultiplyTruncateFusion:nil message:BurmeseZone.shotCapEastCap.summariesCompositeFinishPashtoDegree completion:nil];
        return;
    }
    CGSize size = self.view.frame.size;
    size.height -= BurmeseZone.tipColorKind.subscribeHex;
    UIGraphicsBeginImageContextWithOptions(size, NO, 0.0);
    [self.view.layer renderInContext:UIGraphicsGetCurrentContext()];
    UIImage *image = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    
    UIImageWriteToSavedPhotosAlbum(image, self, @selector(image:leadBedGoogleCombineMixerDog:contextInfo:), (__bridge void *)self);
}

- (void)image:(UIImage *)image leadBedGoogleCombineMixerDog:(NSError *)error contextInfo:(void *)contextInfo
{
    
    if(!error){
        [[TorchGainManager shared] reductionPlaybackRoundIgnoreSwapCertViewController:self.navigationController];
        [AndToast letters:BurmeseZone.shotCapEastCap.claimSexLogLandmarkCardioid];
    }else {
        self.refreshUseButton.hidden = NO;
        [ButAlertView presenceAutoUnifiedMultiplyTruncateFusion:nil message:BurmeseZone.shotCapEastCap.summariesCompositeFinishPashtoDegree completion:nil];
    }
}

@end
