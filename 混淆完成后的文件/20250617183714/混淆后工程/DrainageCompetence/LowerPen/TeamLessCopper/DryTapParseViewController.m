






#import "DryTapParseViewController.h"
#import "SDWebImageManager.h"
#import "AndToast.h"
#import "NSString+DirtyFoot.h"

@interface DryTapParseViewController ()

@property (nonatomic, strong) UIImageView *chamberBeatImageView;
@property (nonatomic, strong) UITextField *megahertzTextField;
@property (nonatomic, strong) UITextField *cutterCloudTextField;

@end

@implementation DryTapParseViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    
    self.refreshUseButton.hidden = [self.winParticle[0] boolValue];
    
    CGFloat topBottomMargin = BurmeseZone.tipColorKind.stampGetZone;
    
    UILabel *tipLabel = [BurmeseZone createPreparingHyphenLogDeclined:BurmeseZone.shotCapEastCap.lexicalBridgingReuseAltitudeHyphens];
    tipLabel.numberOfLines = 0;
    [self.view addSubview:tipLabel];
    [tipLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(BurmeseZone.tipColorKind.formSplitArt);
        make.left.mas_equalTo(BurmeseZone.tipColorKind.sonPredicted);
        make.right.mas_equalTo(-BurmeseZone.tipColorKind.sonPredicted);
    }];
    
    
    self.megahertzTextField = [BurmeseZone randomTokenField:BurmeseZone.shotCapEastCap.limitedAssumeInstancesMultipleUndoCanon isSecure:NO];
    [self.view addSubview:self.megahertzTextField];
    [self.megahertzTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(tipLabel.mas_bottom).offset(topBottomMargin);
        make.left.mas_equalTo(BurmeseZone.tipColorKind.friendHerKit);
        make.right.mas_equalTo(-BurmeseZone.tipColorKind.friendHerKit);
        make.height.mas_equalTo(BurmeseZone.tipColorKind.subfamilyDid);
    }];
    
    
    self.cutterCloudTextField = [BurmeseZone randomTokenField:BurmeseZone.shotCapEastCap.barEvictQueryingPackCarrierCalling isSecure:NO];;
    [self.view addSubview:self.cutterCloudTextField];
    [self.cutterCloudTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.megahertzTextField.mas_bottom).offset(topBottomMargin);
        make.left.right.equalTo(self.megahertzTextField);
        make.height.mas_equalTo(BurmeseZone.tipColorKind.subfamilyDid);
    }];
    
    
    UIImageView *rewardImageView = nil;
    if ([self.winParticle[1] length] > 0) {
        
        rewardImageView = [[UIImageView alloc] init];
        rewardImageView.backgroundColor = UIColor.lightGrayColor;
        rewardImageView.contentMode = UIViewContentModeScaleAspectFit;
        [self.view addSubview:rewardImageView];
        [rewardImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.cutterCloudTextField.mas_bottom).offset(topBottomMargin);
            make.width.mas_equalTo(self.cutterCloudTextField);
            make.centerX.mas_equalTo(0);
        }];
        self.chamberBeatImageView = rewardImageView;
        
        [[SDWebImageManager sharedManager] loadImageWithURL:[NSURL URLWithString:self.winParticle[1]] options:0 progress:nil completed:^(UIImage * _Nullable image2, NSData * _Nullable data, NSError * _Nullable error, SDImageCacheType cacheType, BOOL finished, NSURL * _Nullable imageURL) {
            dispatch_async(dispatch_get_main_queue(), ^{
                rewardImageView.image = image2;
                CGFloat ratio = image2.size.height / image2.size.width;
                [rewardImageView mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.height.mas_equalTo((BurmeseZone.tipColorKind.rationalDroppedDecomposeDownloadsPullWidth -BurmeseZone.tipColorKind.friendHerKit*2)*ratio);
                }];
                [self.view layoutIfNeeded];
                
                [self.view mas_remakeConstraints:^(MASConstraintMaker *make) {
                    make.center.mas_equalTo(0);
                    CGFloat height = self.chamberBeatImageView ? self.chamberBeatImageView.frame.size.height + BurmeseZone.tipColorKind.stampGetZone +BurmeseZone.tipColorKind.taggingCompareBundleRearrangeStorylineExits : BurmeseZone.tipColorKind.taggingCompareBundleRearrangeStorylineExits;
                    
                    make.size.mas_equalTo(CGSizeMake(BurmeseZone.tipColorKind.rationalDroppedDecomposeDownloadsPullWidth, MIN(height, UIScreen.mainScreen.bounds.size.height)));
                    if (height > UIScreen.mainScreen.bounds.size.height) {
                        [self.chamberBeatImageView mas_updateConstraints:^(MASConstraintMaker *make) {
                            make.height.mas_equalTo(self.chamberBeatImageView.frame.size.height-(height-UIScreen.mainScreen.bounds.size.height));
                        }];
                    }
                }];
            });
        }];
    }
    
    
    UIButton *xxpk_realButton = [BurmeseZone hyphensEndCarExpectedSigmaColor:BurmeseZone.shotCapEastCap.sheRatioEveryFactFiltering];
    [xxpk_realButton addTarget:self action:@selector(proxiesPitchPendingQueueBridgingPipe:) forControlEvents:(UIControlEventTouchUpInside)];
    [self.view addSubview:xxpk_realButton];
    [xxpk_realButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(-BurmeseZone.tipColorKind.indexingFile);
        make.left.right.equalTo(self.megahertzTextField);
        make.height.mas_equalTo(BurmeseZone.tipColorKind.immutableBox);
    }];
    
}

- (void)proxiesPitchPendingQueueBridgingPipe:(id)sender {

}

@end
