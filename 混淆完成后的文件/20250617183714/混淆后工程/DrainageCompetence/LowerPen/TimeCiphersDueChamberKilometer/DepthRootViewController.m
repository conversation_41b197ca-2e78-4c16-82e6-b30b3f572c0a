






#import "DepthRootViewController.h"

@interface DepthRootViewController ()<WKNavigationDelegate,WKUIDelegate,WKScriptMessageHandler>

@end

@implementation DepthRootViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.refreshUseButton.hidden = YES;
    self.anchorAndButton.hidden = YES;
    [self roleAdjectiveView];
}

- (void)roleAdjectiveView
{
    WKUserContentController *askDueLogForce = [[WKUserContentController alloc] init];
    WKUserScript *userScript = [[WKUserScript alloc] initWithSource:BurmeseZone.tipColorKind.mayReplaceTouchesRepeatsBuilt injectionTime:WKUserScriptInjectionTimeAtDocumentEnd forMainFrameOnly:YES];
    [askDueLogForce addUserScript:userScript];
    
    WKWebViewConfiguration * config = [[WKWebViewConfiguration alloc] init];
    WKPreferences *preference = [[WKPreferences alloc]init];
    preference.javaScriptCanOpenWindowsAutomatically = YES;
    preference.minimumFontSize = 40.0;
    preference.javaScriptEnabled = YES;
    config.preferences = preference;
    config.selectionGranularity = WKSelectionGranularityDynamic;
    config.preferences.minimumFontSize = 18;
    config.preferences.javaScriptEnabled = YES;
    config.userContentController = askDueLogForce;
    
    self.bundlesUndo = [[WKWebView alloc] initWithFrame:CGRectZero];
    self.bundlesUndo.backgroundColor = UIColor.clearColor;
    self.bundlesUndo.scrollView.backgroundColor = UIColor.clearColor;
    self.bundlesUndo.navigationDelegate = self;
    self.bundlesUndo.opaque = YES;
    self.bundlesUndo.scrollView.bounces = NO;
    self.bundlesUndo.scrollView.showsVerticalScrollIndicator = NO;
    self.bundlesUndo.scrollView.showsHorizontalScrollIndicator = NO;
    self.bundlesUndo.UIDelegate = self;
    [self.view addSubview:self.bundlesUndo];
    self.bundlesUndo.scrollView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    
    [self.bundlesUndo.configuration.userContentController addScriptMessageHandler:self name:BurmeseZone.tipColorKind.refreshRenderModifierJustifiedOne];
    [self.bundlesUndo.configuration.userContentController addScriptMessageHandler:self name:BurmeseZone.tipColorKind.centralOrangePassiveMemberSobConflictsRadial];
}

- (void)setRollBedMode:(NSString *)rollBedMode {
    _rollBedMode = rollBedMode;
    NSURL *url = [NSURL URLWithString:rollBedMode];
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:url cachePolicy:NSURLRequestReloadIgnoringCacheData timeoutInterval:15.0];
    [request addValue:[BurmeseZone tenEitherCoverMeanIrregularToken] forHTTPHeaderField:BurmeseZone.tipColorKind.preservesCallingArtAssistivePassively];
    [self.bundlesUndo loadRequest:request];
}

- (void)intrinsicQualifierBehaviorAlignmentPresenceMotionIndoor:(NSURL *)URL {
    if ([self.oldEarDropIts respondsToSelector:@selector(rawHomeView:oneComposeAction:arg:)]) {
        [self.oldEarDropIts rawHomeView:self.bundlesUndo oneComposeAction:URL.host arg:URL];
    }
}



- (void)webView:(WKWebView *)webView decidePolicyForNavigationResponse:(WKNavigationResponse *)navigationResponse decisionHandler:(void (^)(WKNavigationResponsePolicy))decisionHandler {
    decisionHandler(WKNavigationResponsePolicyAllow);
}


- (void)webView:(WKWebView *)webView decidePolicyForNavigationAction:(WKNavigationAction *)navigationAction decisionHandler:(void (^)(WKNavigationActionPolicy))decisionHandler {
    NSURL *URL = navigationAction.request.URL;
    
    if ([URL.scheme hasPrefix:BurmeseZone.tipColorKind.idiomMan]) {
        [self intrinsicQualifierBehaviorAlignmentPresenceMotionIndoor:URL];
        decisionHandler(WKNavigationActionPolicyCancel);
        return;
    }
    decisionHandler(WKNavigationActionPolicyAllow);
}


-(void)webView:(WKWebView *)webView didStartProvisionalNavigation:(WKNavigation *)navigation{
    [MoreVisualView purposeSimpleView:self.view];
}


- (void)webView:(WKWebView *)webView didFailProvisionalNavigation:(WKNavigation *)navigation{
    
    [MoreVisualView encryptLowerAboutStepsonRefusedView:self.view];
}


-(void)webView:(WKWebView *)webView didFinishNavigation:(WKNavigation *)navigation {
    
    [webView evaluateJavaScript:BurmeseZone.tipColorKind.archeryAmharicDeveloperBoldExerciseAppear completionHandler:nil];
    
    [webView evaluateJavaScript:BurmeseZone.tipColorKind.armourSplitForwardDrizzleThumbBlink completionHandler:nil];
    [webView evaluateJavaScript:BurmeseZone.tipColorKind.jabberFrontNothingStoreMismatch completionHandler:nil];
    
    [MoreVisualView encryptLowerAboutStepsonRefusedView:self.view];
    while (self.bundlesUndo.isLoading) {
        return;
    }
}


- (void)userContentController:(WKUserContentController *)userContentController didReceiveScriptMessage:(WKScriptMessage *)message {
    
    
    if ([self.oldEarDropIts respondsToSelector:@selector(rawHomeView:oneComposeAction:arg:)]) {
        [self.oldEarDropIts rawHomeView:self.bundlesUndo oneComposeAction:message.name arg:message.body];
    }
}

-(void)webView:(WKWebView *)webView runJavaScriptAlertPanelWithMessage:(NSString *)message initiatedByFrame:(WKFrameInfo *)frame completionHandler:(void (^)(void))completionHandler{
    
    [ButAlertView presenceAutoUnifiedMultiplyTruncateFusion:@"" message:message completion:^(NSInteger buttonIndex) {
        completionHandler();
    }];
}



- (WKWebView *)webView:(WKWebView *)webView createWebViewWithConfiguration:(WKWebViewConfiguration *)configuration forNavigationAction:(WKNavigationAction *)navigationAction windowFeatures:(WKWindowFeatures *)windowFeatures{
    WKFrameInfo *frameInfo = navigationAction.targetFrame;
    if (![frameInfo isMainFrame]) {
        [webView loadRequest:navigationAction.request];
    }
    return nil;
}


- (void)webView:(WKWebView *)webView runJavaScriptTextInputPanelWithPrompt:(NSString *)prompt defaultText:(nullable NSString *)defaultText initiatedByFrame:(WKFrameInfo *)frame completionHandler:(void (^)(NSString * __nullable result))completionHandler{
    completionHandler(BurmeseZone.tipColorKind.picturesMore);
}


- (void)webView:(WKWebView *)webView runJavaScriptConfirmPanelWithMessage:(NSString *)message initiatedByFrame:(WKFrameInfo *)frame completionHandler:(void (^)(BOOL result))completionHandler{
    completionHandler(YES);
}

- (void)dealloc {
    self.bundlesUndo.UIDelegate = nil;
    self.view = nil;
    [self.bundlesUndo.configuration.userContentController removeAllUserScripts];
}

@end
