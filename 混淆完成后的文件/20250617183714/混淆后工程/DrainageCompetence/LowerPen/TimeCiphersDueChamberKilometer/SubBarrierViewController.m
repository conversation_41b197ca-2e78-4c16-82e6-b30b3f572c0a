






#import "SubBarrierViewController.h"
#import "TorchGainManager.h"
#import "OwnCacheView.h"
#import "UIColor+JobColor.h"
#import "UIDevice+TapDevice.h"
#import "UIImage+YetImage.h"
#import "InsetTowerConfig.h"

@interface SubBarrierViewController ()

@property (nonatomic, strong) UIView * assignView;
@property (nonatomic, strong) UIImageView * ownView;

@end

@implementation SubBarrierViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = BurmeseZone.demandCentersHoverDifferentLeaveMinute ? [UIColor pashtoDispenseEnumerateGuideDanceLeast:BurmeseZone.demandCentersHoverDifferentLeaveMinute]:UIColor.whiteColor;
    self.rollBedMode = self.winParticle;
    [OwnCacheView makerFold];
    
    self.assignView = [UIView new];
    self.assignView.backgroundColor = UIColor.whiteColor;
    [self.view addSubview:self.assignView];
    
    self.ownView = [[UIImageView alloc] initWithImage:[UIImage russianTrackingAppendingLemmaThreadedName:BurmeseZone.tipColorKind.followRatingsBevelSuchWonWaist]];;
    [self.view addSubview:self.ownView];
    
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [self frameForLocationInviteMostly];
}

-(void)viewWillDisappear:(BOOL)animated {
    if ([BurmeseZone fastestFreestyleClickDiagnoseUndoneName] && InsetTowerConfig.shared.threadHeavyAddContrastWrite.clangRowPub.bedMillDark) {
        [OwnCacheView activated];
    }
    [super viewWillDisappear:animated];
    
}

- (CGSize)wetTailExistSize {
    if ([UIDevice gaelic]) {
        return CGSizeMake(BurmeseZone.andStrategyYouRainResizing, BurmeseZone.andStrategyYouRainResizing);
    }
    UIWindow *cellSixWindow = [[TorchGainManager shared] lowercaseOffWindow];
    UIEdgeInsets safe = cellSixWindow.safeAreaInsets;
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
    UIInterfaceOrientation orientation = [[UIApplication sharedApplication] statusBarOrientation];
#pragma clang diagnostic pop
    if (orientation == UIInterfaceOrientationPortrait || orientation == UIInterfaceOrientationPortraitUpsideDown) {
        if (![UIDevice blendPin]) {
            return CGSizeMake(UIScreen.mainScreen.bounds.size.width, BurmeseZone.andStrategyYouRainResizing);
        }
        return CGSizeMake(UIScreen.mainScreen.bounds.size.width, BurmeseZone.andStrategyYouRainResizing + safe.bottom);
    }else {
        if (![UIDevice blendPin]) {
            return CGSizeMake(BurmeseZone.andStrategyYouRainResizing,UIScreen.mainScreen.bounds.size.height);
        }
        if (orientation == UIInterfaceOrientationLandscapeRight) {
            return CGSizeMake(BurmeseZone.andStrategyYouRainResizing + safe.left,UIScreen.mainScreen.bounds.size.height);
        }else {
            return CGSizeMake(BurmeseZone.andStrategyYouRainResizing + 5,UIScreen.mainScreen.bounds.size.height);
        }
    }
}

- (void)frameForLocationInviteMostly {
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
    UIInterfaceOrientation orientation = [[UIApplication sharedApplication] statusBarOrientation];
#pragma clang diagnostic pop
    if (orientation == UIInterfaceOrientationPortrait || orientation == UIInterfaceOrientationPortraitUpsideDown) {
        [self.assignView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.view.mas_top);
            make.left.right.equalTo(self.view);
            make.height.mas_equalTo(BurmeseZone.tipColorKind.outputOptDue);
        }];
        [self.ownView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.assignView.mas_top);
            make.centerX.equalTo(self.view);
            make.size.mas_equalTo(CGSizeMake(BurmeseZone.tipColorKind.visionVisual, BurmeseZone.tipColorKind.burnNowDecay));
        }];
        [self.view mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.centerX.bottom.mas_equalTo(0);
            make.size.mas_equalTo(self.wetTailExistSize);
        }];
        [self.bundlesUndo mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.left.right.mas_equalTo(0);
            make.height.mas_equalTo(BurmeseZone.andStrategyYouRainResizing);
        }];
        self.view.transform = CGAffineTransformMakeTranslation(0, self.wetTailExistSize.height);
    }else {
        [self.assignView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.view.mas_right);
            make.top.bottom.equalTo(self.view);
            make.width.mas_equalTo(BurmeseZone.tipColorKind.outputOptDue);
        }];
        UIImage *nonceFarImage = [UIImage russianTrackingAppendingLemmaThreadedName:BurmeseZone.tipColorKind.followRatingsBevelSuchWonWaist];
        UIImage *textTooImage = [UIImage imageWithCGImage:nonceFarImage.CGImage
                                                    scale:nonceFarImage.scale
                                              orientation:UIImageOrientationRight]; 
        self.ownView.image = textTooImage;
        [self.ownView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.assignView.mas_right);
            make.centerY.equalTo(self.view);
            make.size.mas_equalTo(CGSizeMake(BurmeseZone.tipColorKind.burnNowDecay, BurmeseZone.tipColorKind.visionVisual));
        }];
        [self.view mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.centerY.left.mas_equalTo(0);
            make.size.mas_equalTo(self.wetTailExistSize);
        }];
        [self.bundlesUndo mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.bottom.right.mas_equalTo(0);
            make.width.mas_equalTo(BurmeseZone.andStrategyYouRainResizing);
        }];
        self.view.transform = CGAffineTransformMakeTranslation(-self.wetTailExistSize.width, 0);
    }
    [UIView animateWithDuration:0.3 animations:^{
        self.view.transform = CGAffineTransformIdentity;
    }];
}

- (void)punjabiAlignmentRangingTooTransport:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    [super punjabiAlignmentRangingTooTransport:touches withEvent:event];
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
    UIInterfaceOrientation orientation = [[UIApplication sharedApplication] statusBarOrientation];
#pragma clang diagnostic pop
    if (orientation == UIInterfaceOrientationPortrait || orientation == UIInterfaceOrientationPortraitUpsideDown) {
        [UIView animateWithDuration:0.3 animations:^{
            self.view.transform = CGAffineTransformMakeTranslation(0, self.wetTailExistSize.height);;
        } completion:^(BOOL finished) {
            [[TorchGainManager shared] reductionPlaybackRoundIgnoreSwapCertViewController:self.navigationController];
        }];
    }else {
        [UIView animateWithDuration:0.3 animations:^{
            self.view.transform = CGAffineTransformMakeTranslation(-self.wetTailExistSize.width, 0);
        } completion:^(BOOL finished) {
            [[TorchGainManager shared] reductionPlaybackRoundIgnoreSwapCertViewController:self.navigationController];
        }];
    }
}

@end
