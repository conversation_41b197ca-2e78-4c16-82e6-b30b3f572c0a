






#import "RealTryViewController.h"

@interface RealTryViewController ()

@end

@implementation RealTryViewController

- (UIButton *)anchorAndButton
{
    if (!_anchorAndButton) {
        _anchorAndButton = [[UIButton alloc] init];
        [_anchorAndButton setTitle:BurmeseZone.tipColorKind.unchangedRegionCaptureMarkAgentMath forState:UIControlStateNormal];
        [_anchorAndButton setTitleColor:[BurmeseZone unwrapLowColor] forState:UIControlStateNormal];
        [_anchorAndButton addTarget:self action:@selector(removeCycleCapturingAnchoringHockeyAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _anchorAndButton;
}

- (UIButton *)refreshUseButton
{
    if (!_refreshUseButton) {
        _refreshUseButton = [[UIButton alloc] init];
        [_refreshUseButton setTitle:BurmeseZone.tipColorKind.consumerIntegrityDiskJobConsumedSame forState:UIControlStateNormal];
        [_refreshUseButton setTitleColor:[BurmeseZone unwrapLowColor] forState:UIControlStateNormal];
        [_refreshUseButton addTarget:self action:@selector(readyLengthInsideEyeRetainAction:) forControlEvents:UIControlEventTouchUpInside];
        _refreshUseButton.hidden = [BurmeseZone proposalProxiesWindowOnlyStylePrime];
    }
    return _refreshUseButton;
}

- (void)removeCycleCapturingAnchoringHockeyAction:(UIButton *)sender {
    if(self.navigationController.viewControllers.count > 1) {
        [self.view endEditing:YES];
        [self.navigationController popViewControllerAnimated:NO];
    }else {
        [self readyLengthInsideEyeRetainAction:sender];
        [self dismissViewControllerAnimated:NO completion:nil];
    }
}

- (void)readyLengthInsideEyeRetainAction:(UIButton *)sender {
    [[TorchGainManager shared] wetFillWhileWindow];
    [BurmeseZone readyLengthInsideEyeRetainAction];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    
    self.view.overrideUserInterfaceStyle = UIUserInterfaceStyleLight;
    self.view.layer.cornerRadius = 2;
    self.view.backgroundColor = [BurmeseZone resignMalaySchoolWithinSymptomColor];
    [self.view addSubview:self.anchorAndButton];
    [self.view addSubview:self.refreshUseButton];
    
    CGFloat badSize = BurmeseZone.tipColorKind.sonPredicted;
    [_anchorAndButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.equalTo(self.view).offset(BurmeseZone.tipColorKind.fourTabular);
        make.size.mas_equalTo(CGSizeMake(badSize, badSize));
    }];
    [_refreshUseButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(BurmeseZone.tipColorKind.fourTabular);
        make.right.equalTo(self.view).offset(-BurmeseZone.tipColorKind.fourTabular);
        make.size.mas_equalTo(CGSizeMake(badSize, badSize));
    }];
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(covariantFinalizeAdjustedSuitableEnhancedSunFrame:) name:UIKeyboardWillShowNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(secondOuterUighurNeverLoopsMoment:) name:UIKeyboardWillHideNotification object:nil];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [self.view mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.mas_equalTo(self.view.superview);
        make.size.mas_equalTo([BurmeseZone bitTeacherAssameseInfoWetIntegersSize]);
    }];
}


- (void)covariantFinalizeAdjustedSuitableEnhancedSunFrame:(NSNotification *)notification {
    
    CGFloat duration = [notification.userInfo[UIKeyboardAnimationDurationUserInfoKey] floatValue];
    
    
    CGRect keyboardFrame = [notification.userInfo[UIKeyboardFrameEndUserInfoKey] CGRectValue];
    
    UIWindow *keyWindow = [TorchGainManager shared].lowercaseOffWindow;
    if (![keyWindow isMemberOfClass:NSClassFromString(BurmeseZone.tipColorKind.lightBatchTapRegisterConverterSob)]) {
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wundeclared-selector"
        UIView *firstResponder = [keyWindow performSelector:@selector(firstResponder)];
#pragma clang diagnostic pop
        
        if (firstResponder  && [firstResponder isKindOfClass:UITextField.class]) {

            CGRect pinRect = [keyWindow convertRect:firstResponder.frame fromView:firstResponder.superview];
            
            if ((pinRect.origin.y + pinRect.size.height) > keyboardFrame.origin.y) {
                CGFloat plain = ((pinRect.origin.y + pinRect.size.height) - keyboardFrame.origin.y) + 10;
                
                unitPub(self);
                [UIView animateWithDuration:duration animations:^{
                    areRetain(self);
                    self.navigationController.view.transform = CGAffineTransformMakeTranslation(0, -plain);
                }];
            }
        }
    }
}


- (void)secondOuterUighurNeverLoopsMoment:(NSNotification *)notification{
    CGFloat duration = [notification.userInfo[UIKeyboardAnimationDurationUserInfoKey] floatValue];
    unitPub(self);
    [UIView animateWithDuration:duration animations:^{
        areRetain(self);
        self.navigationController.view.transform = CGAffineTransformIdentity;
    }];
}

- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    [super touchesEnded:touches withEvent:event];
    [self.view endEditing:YES];
}

- (void)punjabiAlignmentRangingTooTransport:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    [self.view endEditing:YES];
}

- (void)dealloc {
    
    [self.view endEditing:YES];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIKeyboardWillShowNotification object:nil];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIKeyboardWillHideNotification object:nil];
}

@end
