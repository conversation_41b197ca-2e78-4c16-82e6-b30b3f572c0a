






#import "HoldBigBoxNapController.h"
#import "RealTryViewController.h"

@interface HoldBigBoxNapController ()

@end

@implementation HoldBigBoxNapController


- (BOOL)shouldAutorotate {
    return self.topViewController.shouldAutorotate;
}

- (UIInterfaceOrientationMask)supportedInterfaceOrientations {
    return self.topViewController.supportedInterfaceOrientations;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.interactivePopGestureRecognizer.enabled = NO;
    [self setNavigationBarHidden:YES];
    self.view.backgroundColor = [UIColor.blackColor colorWithAlphaComponent:.3];
}

- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    
    CGPoint point = [[touches anyObject] locationInView:self.view];
    
    UIView *redView = self.topViewController.view;
    
    point = [redView.layer convertPoint:point fromLayer:self.view.layer];
    
    RealTryViewController *vc = (RealTryViewController *)self.topViewController;
    if (![redView.layer containsPoint:point]) {
        [vc punjabiAlignmentRangingTooTransport:touches withEvent:event];
    }else{  
        [super touchesBegan:touches withEvent:event];
    }
}

- (void)pushViewController:(RealTryViewController *)viewController animated:(BOOL)animated
{
    if (self.childViewControllers.count > 0) {
        viewController.anchorAndButton.hidden = NO;
    }else {
        viewController.anchorAndButton.hidden = YES;
    }
    [super pushViewController:viewController animated:animated];
    
}
- (void)dealloc {
    
}
@end
