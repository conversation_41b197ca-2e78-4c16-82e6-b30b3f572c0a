






#import "TorchGainManager.h"
#import "TapCardMenArtsViewController.h"
#import "InsetTowerConfig.h"

@interface TorchGainManager()
@property (nonatomic, strong) NSMutableArray<UIWindow *> *oppositeStreet;  
@property (nonatomic, strong) NSMutableArray<UIWindow *> *skinBounding;  
@end

@implementation TorchGainManager

- (instancetype)init {
    self = [super init];
    if (self) {
        _oppositeStreet = [NSMutableArray array];
        _skinBounding = [NSMutableArray array];
    }
    return self;
}

+ (instancetype)shared {
    static id shared = nil;
    static dispatch_once_t onlyToken;
    dispatch_once(&onlyToken, ^{
        shared = [[super alloc] init];
    });
    return shared;
}


- (UIWindow *)addBothBarWindow {
    UIWindow *firstWindow = nil;
    
    if (@available(iOS 13.0, *)) {
        
        NSSet<UIScene *> *connectedScenes = [UIApplication sharedApplication].connectedScenes;
        for (UIScene *scene in connectedScenes) {
            
            if (scene.activationState == UISceneActivationStateForegroundActive &&
                [scene isKindOfClass:[UIWindowScene class]]) {
                
                UIWindowScene *windowScene = (UIWindowScene *)scene;
                
                if (windowScene.windows.count > 0) {
                    firstWindow = windowScene.windows.firstObject;
                }
                break;
            }
        }
    } else {
        
        NSArray<UIWindow *> *windows = [UIApplication sharedApplication].windows;
        if (windows.count > 0) {
            firstWindow = windows.firstObject;
        }
    }
    
    
    if (!firstWindow) {
        NSArray<UIWindow *> *windows = [UIApplication sharedApplication].windows;
        if (windows.count > 0) {
            firstWindow = windows.firstObject;
        }
    }
    
    return firstWindow;
}


- (UIWindow *)lowercaseOffWindow {
    
    UIWindow *cellSixWindow = nil;
    
    if (@available(iOS 13.0, *)) {
        NSSet<UIScene *> *connectedScenes = [UIApplication sharedApplication].connectedScenes;
        for (UIScene *scene in connectedScenes) {
            if (scene.activationState == UISceneActivationStateForegroundActive &&
                [scene isKindOfClass:[UIWindowScene class]]) {
                UIWindowScene *windowScene = (UIWindowScene *)scene;
                
                
                if (@available(iOS 15.0, *)) {
                    cellSixWindow = windowScene.keyWindow;
                }
                
                else {
                    for (UIWindow *window in windowScene.windows) {
                        if (window.isKeyWindow) {
                            cellSixWindow = window;
                            break;
                        }
                    }
                }
                break;
            }
        }
    } else {
        
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
        cellSixWindow = [UIApplication sharedApplication].keyWindow;
#pragma clang diagnostic pop
    }
    
    
    if (!cellSixWindow) {
        NSArray<UIWindow *> *windows = [UIApplication sharedApplication].windows;
        for (UIWindow *window in windows) {
            if (window.isKeyWindow) {
                cellSixWindow = window;
                break;
            }
        }
    }
    
    return cellSixWindow;
}


- (void)wonRareGenreLatvianPrepareInventorySawViewController:(UIViewController *)radial{
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if ([UIApplication sharedApplication].applicationState == UIApplicationStateActive) {
            
            UIWindow *newWindow = [self recentShortcutsOperandModifierWarpWrestling:radial];
            
            
            [self containerIllPlusEsperantoAlphabetWin:newWindow];
            
            [self.oppositeStreet addObject:newWindow];
        } else {
            
            __weak typeof(self) weakSelf = self;
            
            __block __weak id wax = nil;
            
            wax = [[NSNotificationCenter defaultCenter] addObserverForName:UIApplicationDidBecomeActiveNotification
                                                                       object:nil
                                                                        queue:[NSOperationQueue mainQueue]
                                                                   usingBlock:^(NSNotification *note) {
                
                [[NSNotificationCenter defaultCenter] removeObserver:wax];
                [weakSelf wonRareGenreLatvianPrepareInventorySawViewController:radial];
            }];
        }
    });
}

- (void)cellBuddyAwakeHandshakeArrangerSynthesisViewController:(UIViewController *)radial {
    dispatch_async(dispatch_get_main_queue(), ^{
        if ([UIApplication sharedApplication].applicationState == UIApplicationStateActive) {
            [self driveReceivingDetailProvidingStripMousePin:radial];
        } else {
            
            __weak typeof(self) weakSelf = self;
            
            __block __weak id wax = nil;
            
            wax = [[NSNotificationCenter defaultCenter] addObserverForName:UIApplicationDidBecomeActiveNotification
                                                                       object:nil
                                                                        queue:[NSOperationQueue mainQueue]
                                                                   usingBlock:^(NSNotification *note) {
                
                [[NSNotificationCenter defaultCenter] removeObserver:wax];
                
                [weakSelf driveReceivingDetailProvidingStripMousePin:radial];
            }];
        }
    });
}

- (void)momentaryEncodingsSockCapacityIcyThinView:(UIView *)view {
    dispatch_async(dispatch_get_main_queue(), ^{
        if ([UIApplication sharedApplication].applicationState == UIApplicationStateActive) {
            [self driveReceivingDetailProvidingStripMousePin:view];
        } else {
            
            __weak typeof(self) weakSelf = self;
            
            __block __weak id wax = nil;
            
            wax = [[NSNotificationCenter defaultCenter] addObserverForName:UIApplicationDidBecomeActiveNotification
                                                                       object:nil
                                                                        queue:[NSOperationQueue mainQueue]
                                                                   usingBlock:^(NSNotification *note) {
                
                [[NSNotificationCenter defaultCenter] removeObserver:wax];
                
                [weakSelf driveReceivingDetailProvidingStripMousePin:view];
            }];
        }
    });
}

- (void)driveReceivingDetailProvidingStripMousePin:(id)object {
    UIViewController *radial = nil;
    
        
    if ([object isKindOfClass:[UIViewController class]]) {
        radial = object;
    }
    
    if ([object isKindOfClass:[UIView class]]) {
        radial = [TapCardMenArtsViewController new];
        radial.view = object;
    }
    
    
    UIWindow *newWindow = [self recentShortcutsOperandModifierWarpWrestling:radial];
    
    
    [self containerIllPlusEsperantoAlphabetWin:newWindow];
    
    
    [self.skinBounding addObject:newWindow];
}

- (void)tapLeapSandboxBrowseOfferMid:(NSNotification *)note {
    
    [[NSNotificationCenter defaultCenter] removeObserver:self
                                                    name:UIApplicationDidBecomeActiveNotification
                                                  object:nil];
    [self momentaryEncodingsSockCapacityIcyThinView:note.object];
}

- (void)wetFillWhileWindow {
    [self proceedInsulinModernStreamControlsWindow];
}

- (void)proceedInsulinModernStreamControlsWindow {
    dispatch_async(dispatch_get_main_queue(), ^{
        if (self.skinBounding.count == 0) return;

        
        UIWindow *mindWindow = [self.skinBounding lastObject];
        [self.skinBounding removeLastObject];

        
        if (mindWindow.isKeyWindow) {
            [self tableEndsBleedWindow];
        }

        
        mindWindow.hidden = YES;
    });
}

- (void)reductionPlaybackRoundIgnoreSwapCertViewController:(UIViewController *)rootViewController {
    dispatch_async(dispatch_get_main_queue(), ^{
        NSEnumerator *butRepairSnapBlusteryHer = [self.skinBounding reverseObjectEnumerator];
        UIWindow *window = nil;
        
        
        while ((window = [butRepairSnapBlusteryHer nextObject])) {
            if (window.rootViewController == rootViewController) {
                
                if (window.isKeyWindow) {
                    [self tableEndsBleedWindow];
                }
                
                
                window.hidden = YES;
                [self.skinBounding removeObject:window];
                
                
                butRepairSnapBlusteryHer = [self.skinBounding reverseObjectEnumerator];
            }
        }
    });
}

- (void)twistReachedSmoothingFlipDigitizedSex {
    dispatch_async(dispatch_get_main_queue(), ^{
        
        for (UIWindow *window in [self.skinBounding reverseObjectEnumerator]) {
            if (window.isKeyWindow) {
                [self tableEndsBleedWindow];
            }
            window.hidden = YES;
        }
        [self.skinBounding removeAllObjects];
    });
}


- (UIWindow *)recentShortcutsOperandModifierWarpWrestling:(UIViewController *)radial {
    UIWindow *window = nil;
    
    
    if (@available(iOS 13.0, *)) {
        for (UIScene *scene in [UIApplication sharedApplication].connectedScenes) {
            if (scene.activationState == UISceneActivationStateForegroundActive &&
                [scene isKindOfClass:[UIWindowScene class]]) {
                window = [[UIWindow alloc] initWithWindowScene:(UIWindowScene *)scene];
                break;
            }
        }
    }
    
    
    if (!window) {
        window = [[UIWindow alloc] initWithFrame:[UIScreen mainScreen].bounds];
    }
    
    
    window.backgroundColor = [UIColor clearColor];
    window.rootViewController = radial;
    return window;
}

- (void)containerIllPlusEsperantoAlphabetWin:(UIWindow *)window {
    

    window.windowLevel = UIWindowLevelStatusBar + 100;
    [window makeKeyAndVisible];
}


- (void)tableEndsBleedWindow {
    UIWindow *mostWindow = [self auditEnergyWindow];
    [mostWindow makeKeyWindow];
    if (!mostWindow.isKeyWindow) {
        [mostWindow becomeKeyWindow];
    }
}

- (UIWindow *)auditEnergyWindow {
    __block UIWindow *mostWindow = nil;
    
    
    if (@available(iOS 13.0, *)) {
        NSArray<UIWindowScene *> *windowScenes = [self ukrainianUseExposuresPortraitsEldest];
        [windowScenes enumerateObjectsUsingBlock:^(UIWindowScene * _Nonnull scene, NSUInteger idx, BOOL * _Nonnull stop) {
            
            if (@available(iOS 15.0, *)) {
                mostWindow = scene.keyWindow;
            }
            
            if (!mostWindow) {
                mostWindow = [scene.windows firstObject];
            }
            if (mostWindow) *stop = YES;
        }];
    }
    
    else {
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
        mostWindow = [UIApplication sharedApplication].keyWindow;
#pragma clang diagnostic pop
    }
    
    
    if (!mostWindow) {
        mostWindow = [UIApplication sharedApplication].windows.firstObject;
    }
    
    return mostWindow;
}

- (NSArray<UIWindowScene *> *)ukrainianUseExposuresPortraitsEldest {
    NSPredicate *predicate = [NSPredicate predicateWithBlock:^BOOL(UIScene * _Nullable scene, NSDictionary<NSString *,id> * _Nullable bindings) {
        return scene.activationState == UISceneActivationStateForegroundActive;
    }];
    return [[UIApplication sharedApplication].connectedScenes filteredSetUsingPredicate:predicate].allObjects;
}


- (UIWindow *)lowWindow {
    return [self.skinBounding lastObject];
}

- (NSInteger)promptCount {
    return self.skinBounding.count;
}


@end
