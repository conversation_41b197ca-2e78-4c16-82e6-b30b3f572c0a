






#import "BuddyIndigoButton.h"
#import "BurmeseZone.h"
#import "UIImage+YetImage.h"

@interface BuddyIndigoButton ()


@property (nonatomic, strong) NSTimer *signalingWorldTimer;

@property (nonatomic, assign) NSInteger unlockWorkoutsHertzArmenianTempEast;

@property (nonatomic, copy) NSString *standardDeviceThermalStrongestSpan;

@end

@implementation BuddyIndigoButton

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self indicesAccurateDarkerPullPermanent];
    }
    return self;
}

- (instancetype)initWithCoder:(NSCoder *)coder {
    self = [super initWithCoder:coder];
    if (self) {
        [self indicesAccurateDarkerPullPermanent];
    }
    return self;
}


- (void)indicesAccurateDarkerPullPermanent {
    
    self.blackPackLacrosseKeyEasyOptimize = 60;
    self.standardDeviceThermalStrongestSpan = BurmeseZone.shotCapEastCap.retBezelVirtualDogRecursivePositionCode;
    [self setTitle:self.standardDeviceThermalStrongestSpan forState:UIControlStateNormal];
    [self setBackgroundImage:[UIImage bondDriveTightColor:BurmeseZone.oddGaelicColor] forState:UIControlStateNormal];
    [self setBackgroundImage:[UIImage bondDriveTightColor:[[UIColor lightGrayColor] colorWithAlphaComponent:0.5f]] forState:UIControlStateHighlighted];
    self.titleLabel.font = [UIFont systemFontOfSize:16];
    self.layer.cornerRadius = 2.f;
    self.layer.masksToBounds = YES;
    
    self.contentEdgeInsets = UIEdgeInsetsMake(0, 5, 0, 5);
    
    [self sizeToFit];
    
    
    [self addTarget:self action:@selector(netPastSlowClicked) forControlEvents:UIControlEventTouchUpInside];
}


- (void)netPastSlowClicked {
    [self bitViabilityForPreparingUpload];
    if (self.listGradeLikeAction) {
        self.listGradeLikeAction();
    }
}


- (void)bitViabilityForPreparingUpload {
    self.enabled = NO;
    self.unlockWorkoutsHertzArmenianTempEast = self.blackPackLacrosseKeyEasyOptimize;
    [self operateTryLinerGroupDigitizedAbove];
    
    
    self.signalingWorldTimer = [NSTimer scheduledTimerWithTimeInterval:1.0
                                                                 target:self
                                                               selector:@selector(cameraPredictedSelectorsMemberNarrative:)
                                                               userInfo:nil
                                                                repeats:YES];
}


- (void)cameraPredictedSelectorsMemberNarrative:(NSTimer *)timer {
    self.unlockWorkoutsHertzArmenianTempEast--;
    if (self.unlockWorkoutsHertzArmenianTempEast <= 0) {
        [self tabPasswordForeverTenBand];
    } else {
        [self operateTryLinerGroupDigitizedAbove];
    }
}


- (void)operateTryLinerGroupDigitizedAbove {
    NSString *title = [NSString stringWithFormat:@"%@(%ld)",BurmeseZone.shotCapEastCap.blueMayAskTightSwashesWeeklyCode, (long)self.unlockWorkoutsHertzArmenianTempEast];
    [self setTitle:title forState:UIControlStateDisabled];
}


- (void)tabPasswordForeverTenBand {
    [self.signalingWorldTimer invalidate];
    self.signalingWorldTimer = nil;
    self.enabled = YES;
    [self setTitle:self.standardDeviceThermalStrongestSpan forState:UIControlStateNormal];
}

- (void)dealloc {
    
    [self.signalingWorldTimer invalidate];
    self.signalingWorldTimer = nil;
}

@end
