






#import "AndToast.h"
#import "TorchGainManager.h"


static UIColor *clientDogUnlearnCanadianFeaturesColor = nil;
static UIColor *clearHueSameColor = nil;
static UIFont *busyPinAudit = nil;
static CGFloat rootPubRopeDueRadius = 6.0;
static UIEdgeInsets mostMaleTurkmenPartEstimatedFinal = {10, 16, 10, 16};

@interface AndToast()
@property (nonatomic, strong) UILabel *saveAllLabel;
@property (nonatomic, strong) NSTimer *opacityTimer;
@property (nonatomic, assign) TodaySexRenewedPrecisionReal position;
@end

@implementation AndToast


- (instancetype)initModeMessage:(NSString *)message {
    self = [super initWithFrame:CGRectZero];
    if (self) {
        self.userInteractionEnabled = NO;
        self.backgroundColor = UIColor.clearColor;
        
        
        UIView *container = [UIView new];
        container.backgroundColor = clientDogUnlearnCanadianFeaturesColor ?:
            [[UIColor blackColor] colorWithAlphaComponent:0.85];
        container.layer.cornerRadius = rootPubRopeDueRadius;
        container.clipsToBounds = YES;
        container.translatesAutoresizingMaskIntoConstraints = NO;
        [self addSubview:container];
        
        
        _saveAllLabel = [UILabel new];
        _saveAllLabel.text = message;
        _saveAllLabel.textColor = clearHueSameColor ?: UIColor.whiteColor;
        _saveAllLabel.font = busyPinAudit ?: [UIFont systemFontOfSize:14];
        _saveAllLabel.textAlignment = NSTextAlignmentCenter;
        _saveAllLabel.numberOfLines = 0;
        _saveAllLabel.translatesAutoresizingMaskIntoConstraints = NO;
        [container addSubview:_saveAllLabel];
        
        
        [NSLayoutConstraint activateConstraints:@[
            
            [container.leadingAnchor constraintEqualToAnchor:_saveAllLabel.leadingAnchor
                                                   constant:-mostMaleTurkmenPartEstimatedFinal.left],
            [container.trailingAnchor constraintEqualToAnchor:_saveAllLabel.trailingAnchor
                                                    constant:mostMaleTurkmenPartEstimatedFinal.right],
            [container.topAnchor constraintEqualToAnchor:_saveAllLabel.topAnchor
                                              constant:-mostMaleTurkmenPartEstimatedFinal.top],
            [container.bottomAnchor constraintEqualToAnchor:_saveAllLabel.bottomAnchor
                                                 constant:mostMaleTurkmenPartEstimatedFinal.bottom],
            
            
            [container.widthAnchor constraintLessThanOrEqualToConstant:
                [UIScreen mainScreen].bounds.size.width - 40]
        ]];
    }
    return self;
}


+ (void)show:(NSString *)message
    duration:(NSTimeInterval)duration
    position:(TodaySexRenewedPrecisionReal)position
{
    
    dispatch_async(dispatch_get_main_queue(), ^{
        AndToast *built = [[AndToast alloc] initModeMessage:message];
        built.position = position;
        [built subLocatorShowersFifteenWhile];
        [built bandSmoothingLowSummariesPrep:duration];
    });
}

- (void)bandSmoothingLowSummariesPrep:(NSTimeInterval)duration {
    UIWindow *window = [TorchGainManager.shared lowercaseOffWindow];
    [window addSubview:self];
    
    
    self.translatesAutoresizingMaskIntoConstraints = NO;
    [NSLayoutConstraint activateConstraints:@[
        [self.leadingAnchor constraintEqualToAnchor:window.leadingAnchor],
        [self.trailingAnchor constraintEqualToAnchor:window.trailingAnchor],
        [self.topAnchor constraintEqualToAnchor:window.topAnchor],
        [self.bottomAnchor constraintEqualToAnchor:window.bottomAnchor]
    ]];
    
    
    [self driveProducingServicesNoiseProvideAnimation];
    
    
    if (duration > 0) {
        __weak typeof(self) weakSelf = self;
        self.opacityTimer = [NSTimer scheduledTimerWithTimeInterval:duration repeats:YES block:^(NSTimer * _Nonnull timer) {
            [weakSelf dismiss];
        }];
    }
}

- (void)dismiss {
    [self.opacityTimer invalidate];
    [self kurdishQuarterContextsCourseCertModifiersNone:^{
        [self removeFromSuperview];
    }];
}


- (void)driveProducingServicesNoiseProvideAnimation {
    CGAffineTransform transform;
    switch (self.position) {
        case SourcesPrimeSnapshotLearnDelivery:
            transform = CGAffineTransformMakeTranslation(0, -100);
            break;
        case StoppedExpiredZipNeedHailChroma:
            transform = CGAffineTransformMakeTranslation(0, 100);
            break;
        default:
            transform = CGAffineTransformMakeScale(0.8, 0.8);
            break;
    }
    
    self.alpha = 0;
    self.saveAllLabel.superview.transform = transform;
    
    [UIView animateWithDuration:0.3
                          delay:0
         usingSpringWithDamping:0.7
          initialSpringVelocity:0.1
                        options:UIViewAnimationOptionCurveEaseOut
                     animations:^{
        self.alpha = 1;
        self.saveAllLabel.superview.transform = CGAffineTransformIdentity;
    } completion:nil];
}

- (void)kurdishQuarterContextsCourseCertModifiersNone:(void(^)(void))completion {
    CGAffineTransform transform;
    switch (self.position) {
        case SourcesPrimeSnapshotLearnDelivery:
            transform = CGAffineTransformMakeTranslation(0, -self.saveAllLabel.superview.frame.size.height - 50);
            break;
        case StoppedExpiredZipNeedHailChroma:
            transform = CGAffineTransformMakeTranslation(0, self.saveAllLabel.superview.frame.size.height + 50);
            break;
        default:
            transform = CGAffineTransformMakeScale(0.8, 0.8);
            break;
    }
    
    [UIView animateWithDuration:0.25
                     animations:^{
        self.alpha = 0;
        self.saveAllLabel.superview.transform = transform;
    } completion:^(BOOL finished) {
        if (completion) completion();
    }];
}


- (void)subLocatorShowersFifteenWhile {
    UIView *container = self.saveAllLabel.superview;
    
    
    switch (self.position) {
        case SourcesPrimeSnapshotLearnDelivery: {
            [container.topAnchor constraintEqualToAnchor:self.safeAreaLayoutGuide.topAnchor
                                               constant:30].active = YES;
            break;
        }
        case AskIntegrityNorthSpacePartlyCenter: {
            [container.centerYAnchor constraintEqualToAnchor:self.centerYAnchor].active = YES;
            break;
        }
        case StoppedExpiredZipNeedHailChroma: {
            [container.bottomAnchor constraintEqualToAnchor:self.safeAreaLayoutGuide.bottomAnchor
                                                  constant:-30].active = YES;
            break;
        }
    }
    
    
    [container.centerXAnchor constraintEqualToAnchor:self.centerXAnchor].active = YES;
}


+ (void)epsilonCheckingSphereBlackMolarLineColor:(UIColor *)color {
    clientDogUnlearnCanadianFeaturesColor = color;
}

+ (void)renewTapSinBigColor:(UIColor *)color {
    clearHueSameColor = color;
}

+ (void)redFootSendMap:(UIFont *)font {
    busyPinAudit = font;
}

+ (void)armVariablesEulerLoopSetupRadius:(CGFloat)radius {
    rootPubRopeDueRadius = radius;
}


+ (void)letters:(NSString *)message {
    [self show:message duration:2.0 position:SourcesPrimeSnapshotLearnDelivery];
}

+ (void)moleCenter:(NSString *)message {
    [self show:message duration:2.0 position:AskIntegrityNorthSpacePartlyCenter];
}

+ (void)redDueGasp:(NSString *)message {
    [self show:message duration:2.0 position:StoppedExpiredZipNeedHailChroma];
}

- (void)dealloc {
    
}

@end
