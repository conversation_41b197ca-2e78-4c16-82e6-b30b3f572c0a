






#import "PenGramLossZipButton.h"
#import "BurmeseZone.h"
#import "UIImage+YetImage.h"
#import "HoldBigBoxNapController.h"
#import "UIImage+YetImage.h"
#import "UIColor+JobColor.h"

@implementation PenGramLossZipButton

- (instancetype)initBinFairWakeViewController:(UIViewController *)viewController {
    self = [super init];
    if (self) {
        self.catUndoChainViewController = viewController;
        [self indicesAccurateDarkerPullPermanent];
    }
    return self;
}


- (void)indicesAccurateDarkerPullPermanent {
    
    NSArray *blendIndirectEllipseGroupReceipt = [DarkerButPlusReportStrategy croppingPrimariesCompileLandmarkIodine:[PickReason class]];
    
    
    NSString *currentCountryCode = [[NSLocale currentLocale] objectForKey:NSLocaleCountryCode];
    
    __block PickReason *matchedCountry = nil;
    [blendIndirectEllipseGroupReceipt enumerateObjectsUsingBlock:^(PickReason *country, NSUInteger idx, BOOL *stop) {
        if ([country.flowFaxErrorCode caseInsensitiveCompare:currentCountryCode] == NSOrderedSame) {
            matchedCountry = country;
            *stop = YES; 
        }
    }];
    self.squaresNaturalExceededFlightsFilm = matchedCountry;
    
    
    NSString *title = [NSString stringWithFormat:@"%@%@",BurmeseZone.tipColorKind.secondsGaelic, matchedCountry.vowelWeekCode];
    [self setTitle:title forState:UIControlStateNormal];
    
    
    UIImage *nonceFarImage = [UIImage russianTrackingAppendingLemmaThreadedName:BurmeseZone.tipColorKind.dustResourceClipFeatureTorchStair];
    
    
    CGSize targetImageSize = CGSizeMake(13, 13); 
    
    
    UIImage *scaledImage = [self scopeSubscriptImage:nonceFarImage armSevenSize:targetImageSize];
    
    
    [self setImage:scaledImage forState:UIControlStateNormal];
    [self setImage:[scaledImage imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate] forState:UIControlStateHighlighted]; 
    
    
    self.imageView.contentMode = UIViewContentModeScaleAspectFit;
    
    
    self.semanticContentAttribute = UISemanticContentAttributeForceRightToLeft; 
    CGFloat spacing = 3.0; 
    self.imageEdgeInsets = UIEdgeInsetsMake(0, spacing, 0, -spacing);  
    self.titleEdgeInsets = UIEdgeInsetsMake(0, -spacing, 0, spacing);   
    
    
    [self setBackgroundImage:[UIImage bondDriveTightColor:[BurmeseZone.oddGaelicColor locallyCreamyCollapseLowBoldAudit:8]] forState:UIControlStateNormal];
    [self setBackgroundImage:[UIImage bondDriveTightColor:[[UIColor lightGrayColor] colorWithAlphaComponent:0.5f]]
                   forState:UIControlStateHighlighted];
    self.titleLabel.font = [UIFont systemFontOfSize:16];
    self.layer.cornerRadius = 2.f;
    self.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMinXMaxYCorner;
    self.layer.masksToBounds = YES;
    
    
    self.contentEdgeInsets = UIEdgeInsetsMake(8, 12, 8, 12); 
    
    
    [self sizeToFit];
    
    
    [self addTarget:self action:@selector(netPastSlowClicked) forControlEvents:UIControlEventTouchUpInside];
}


- (UIImage *)scopeSubscriptImage:(UIImage *)image armSevenSize:(CGSize)targetSize {
    
    UIGraphicsBeginImageContextWithOptions(targetSize, NO, 0.0);
    
    
    CGFloat useUnified = targetSize.width / image.size.width;
    CGFloat featureHail = targetSize.height / image.size.height;
    CGFloat scaleFactor = MIN(useUnified, featureHail);
    
    
    CGRect fitMidRect = CGRectMake(0, 0,
                                  image.size.width * scaleFactor,
                                  image.size.height * scaleFactor);
    
    
    CGPoint origin = CGPointMake((targetSize.width - fitMidRect.size.width) / 2.0,
                               (targetSize.height - fitMidRect.size.height) / 2.0);
    [image drawInRect:CGRectMake(origin.x, origin.y,
                                fitMidRect.size.width,
                                fitMidRect.size.height)];
    
    UIImage *kitImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    
    return kitImage;
}


- (void)netPastSlowClicked {
    ProjectsCapturedArtCommitRedTraveledViewController *vc = [ProjectsCapturedArtCommitRedTraveledViewController new];
    vc.leakyLoseDelegate = self;
    [self.catUndoChainViewController.navigationController pushViewController:vc animated:NO];
}

- (void)requestFilenamesBadBigRunBouncingProcessedHierarchy:(PickReason *)country {
    NSString *title = [NSString stringWithFormat:@"%@%@",BurmeseZone.tipColorKind.secondsGaelic, country.vowelWeekCode];
    [self setTitle:title forState:UIControlStateNormal];
    self.squaresNaturalExceededFlightsFilm = country;
}

- (void)dealloc {
    
}
@end
