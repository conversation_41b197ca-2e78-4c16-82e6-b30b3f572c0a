






#import "RealTryViewController.h"
#import "PickReason.h"
#import "DarkerButPlusReportStrategy.h"

NS_ASSUME_NONNULL_BEGIN

@protocol ButSamplesAnnotatedClampLacrosseFeaturesDelegate <NSObject>
- (void)requestFilenamesBadBigRunBouncingProcessedHierarchy:(PickReason *)country;
@end

@interface ProjectsCapturedArtCommitRedTraveledViewController : RealTryViewController

@property (nonatomic, weak) id<ButSamplesAnnotatedClampLacrosseFeaturesDelegate> leakyLoseDelegate;

@end

NS_ASSUME_NONNULL_END
