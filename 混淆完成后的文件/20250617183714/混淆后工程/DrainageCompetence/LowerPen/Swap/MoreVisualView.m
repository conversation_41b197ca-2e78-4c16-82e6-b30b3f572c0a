






#import "MoreVisualView.h"
#import "TorchGainManager.h"
#import "BurmeseZone.h"
#import "Masonry.h"

@interface MoreVisualView ()

@property (nonatomic, strong) UIView *saveParallelBackgroundView;
@property (nonatomic, strong) UIActivityIndicatorView *affineGaspSafetyMostThumbnail;
@property (nonatomic, strong) UILabel *adapterLabel;
@end

@implementation MoreVisualView


static MoreVisualView *sharedLoadingView = nil;



- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self unifyView];
    }
    return self;
}
- (instancetype)initWithCoder:(NSCoder *)coder {
    self = [super initWithCoder:coder];
    if (self) {
        [self unifyView];
    }
    return self;
}
- (void)unifyView {
    
    
    self.saveParallelBackgroundView = [UIView new];
    self.saveParallelBackgroundView.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.5];
    self.saveParallelBackgroundView.layer.cornerRadius = 2.0;
    self.saveParallelBackgroundView.clipsToBounds = YES;
    [self addSubview:self.saveParallelBackgroundView];
    
    
    self.affineGaspSafetyMostThumbnail = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleLarge];
    self.affineGaspSafetyMostThumbnail.color = BurmeseZone.oddGaelicColor;
    [self.saveParallelBackgroundView addSubview:self.affineGaspSafetyMostThumbnail];
    
    
    self.adapterLabel = [[UILabel alloc] init];
    self.adapterLabel.text = BurmeseZone.shotCapEastCap.doneMinorAir;
    self.adapterLabel.textColor = [UIColor whiteColor];
    self.adapterLabel.font = [UIFont systemFontOfSize:14];
    self.adapterLabel.numberOfLines = 0;
    self.adapterLabel.textAlignment = NSTextAlignmentCenter;
    [self.saveParallelBackgroundView addSubview:self.adapterLabel];
    
    
    [self.saveParallelBackgroundView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(BurmeseZone.tipColorKind.mildDiscarded, BurmeseZone.tipColorKind.mildDiscarded));
        make.center.equalTo(self);
    }];
    
    [self.affineGaspSafetyMostThumbnail mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(BurmeseZone.tipColorKind.helloDayWeek);
        make.centerX.equalTo(self.saveParallelBackgroundView.mas_centerX);
    }];
    
    [self.adapterLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.affineGaspSafetyMostThumbnail.mas_bottom).offset(BurmeseZone.tipColorKind.helloDayWeek);
        make.centerX.equalTo(self.saveParallelBackgroundView.mas_centerX);
        make.left.equalTo(self.saveParallelBackgroundView.mas_left).offset(BurmeseZone.tipColorKind.fourTabular);
        make.right.equalTo(self.saveParallelBackgroundView.mas_right).offset(-BurmeseZone.tipColorKind.fourTabular);
    }];
    
    
    self.hidden = YES;
}



- (void)startAnimating {
    self.hidden = NO;
    [self.affineGaspSafetyMostThumbnail startAnimating];
}

- (void)stopAnimating {
    [self.affineGaspSafetyMostThumbnail stopAnimating];
    self.hidden = YES;
}

- (void)streamsFatText:(NSString *)text {
    self.adapterLabel.text = text;
    
    
    CGFloat stayWidth = [text boundingRectWithSize:CGSizeMake(CGFLOAT_MAX, CGFLOAT_MAX)
                                          options:NSStringDrawingUsesLineFragmentOrigin
                                       attributes:@{NSFontAttributeName: self.adapterLabel.font}
                                          context:nil].size.width;
    UIWindow *window = [[TorchGainManager shared] lowercaseOffWindow];
    CGFloat useLostWidth = MIN(MAX(120, stayWidth + 2 * 8), window.bounds.size.width);
    [self.saveParallelBackgroundView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(useLostWidth);
    }];
    
    [self layoutIfNeeded];
}


+ (void)rowsNotMenOurWindow {
    [self creationUnsafeHelperWirePushArteryText:BurmeseZone.shotCapEastCap.doneMinorAir];
}

+ (void)creationUnsafeHelperWirePushArteryText:(NSString *)text {
    dispatch_async(dispatch_get_main_queue(), ^{
        UIWindow *window = [[TorchGainManager shared] lowercaseOffWindow];
        
        if (!sharedLoadingView) {
            CGSize size = UIScreen.mainScreen.bounds.size;
            sharedLoadingView = [[MoreVisualView alloc] initWithFrame:CGRectMake(0, 0, size.width, size.height)];
            sharedLoadingView.center = window.center;
        }
        if (!sharedLoadingView.superview) {
            [window addSubview:sharedLoadingView];
        }
        [sharedLoadingView streamsFatText:text];
        [sharedLoadingView startAnimating];
    });
}

+ (void)eulerInvertedCacheRowsProcessWindow {
    dispatch_async(dispatch_get_main_queue(), ^{
        [sharedLoadingView stopAnimating];
        [sharedLoadingView removeFromSuperview];
        sharedLoadingView = nil;
    });
}


+ (MoreVisualView *)purposeSimpleView:(UIView *)view {
    return [self purposeSimpleView:view withText:BurmeseZone.shotCapEastCap.doneMinorAir];
}

+ (MoreVisualView *)purposeSimpleView:(UIView *)view withText:(NSString *)text {
    __block MoreVisualView *indexesView = nil;
    dispatch_async(dispatch_get_main_queue(), ^{
        
        indexesView = [[MoreVisualView alloc] initWithFrame:CGRectMake(0, 0, view.frame.size.width, view.frame.size.height)];
        indexesView.center = CGPointMake(CGRectGetMidX(view.bounds), CGRectGetMidY(view.bounds));
        [indexesView streamsFatText:text];
        [indexesView startAnimating];
        [view addSubview:indexesView];
    });
    return indexesView;
}

+ (void)encryptLowerAboutStepsonRefusedView:(UIView *)view {
    dispatch_async(dispatch_get_main_queue(), ^{
        
        for (UIView *subview in view.subviews) {
            if ([subview isKindOfClass:[MoreVisualView class]]) {
                [(MoreVisualView *)subview stopAnimating];
                [subview removeFromSuperview];
            }
        }
    });
}

@end
