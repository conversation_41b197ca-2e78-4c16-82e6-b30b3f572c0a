






#import "ButAlertView.h"
#import "BurmeseZone.h"
#import "TorchGainManager.h"
#import "Masonry.h"

#define unitPub(obj) __weak typeof(obj) weak##obj = obj;
#define areRetain(obj) __strong typeof(obj) obj = weak##obj;

@interface ButAlertView()

@property (nonatomic, strong) UIView *freeStreetFairView;
@property (nonatomic, copy) CatCarKeyboardDifferentWakeSomali completion;
@property (nonatomic, strong) UIStackView *fireOffBringView;

@end

@implementation ButAlertView

- (void)dealloc {
    
}

- (instancetype)initWithFrame:(CGRect)frame
                          title:(NSString *)title
                        message:(NSString *)message
                   sexShotMonth:(NSArray<NSString *> *)sexShotMonth
                     completion:(CatCarKeyboardDifferentWakeSomali)completion {
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.5];
        self.completion = completion;
        
        
        self.freeStreetFairView = [[UIView alloc] init];
        self.freeStreetFairView.backgroundColor = [BurmeseZone oddGaelicColor];
        self.freeStreetFairView.layer.cornerRadius = 8.0;
        self.freeStreetFairView.clipsToBounds = YES;
        self.freeStreetFairView.translatesAutoresizingMaskIntoConstraints = NO;
        [self addSubview:self.freeStreetFairView];
        
        
        [NSLayoutConstraint activateConstraints:@[
            [self.freeStreetFairView.centerXAnchor constraintEqualToAnchor:self.centerXAnchor],
            [self.freeStreetFairView.centerYAnchor constraintEqualToAnchor:self.centerYAnchor],
            [self.freeStreetFairView.widthAnchor constraintEqualToConstant:270]
        ]];
        
        
        UIView *previousView = nil;
        CGFloat semaphoreAwayFeaturePercentOrnaments = 20;
        
        
        if (title.length > 0) {
            UILabel *titleLabel = [[UILabel alloc] init];
            titleLabel.text = title;
            titleLabel.textColor = UIColor.whiteColor;
            titleLabel.font = [UIFont boldSystemFontOfSize:18];
            titleLabel.textAlignment = NSTextAlignmentCenter;
            titleLabel.numberOfLines = 0;
            titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
            [self.freeStreetFairView addSubview:titleLabel];
            
            [NSLayoutConstraint activateConstraints:@[
                [titleLabel.topAnchor constraintEqualToAnchor:self.freeStreetFairView.topAnchor constant:semaphoreAwayFeaturePercentOrnaments],
                [titleLabel.leadingAnchor constraintEqualToAnchor:self.freeStreetFairView.leadingAnchor constant:16],
                [titleLabel.trailingAnchor constraintEqualToAnchor:self.freeStreetFairView.trailingAnchor constant:-16]
            ]];
            
            previousView = titleLabel;
        }
        
        
        if (message.length > 0) {
            UILabel *saveAllLabel = [[UILabel alloc] init];
            saveAllLabel.text = message;
            saveAllLabel.textColor = UIColor.whiteColor;
            saveAllLabel.font = [UIFont systemFontOfSize:15];
            saveAllLabel.textAlignment = NSTextAlignmentCenter;
            saveAllLabel.numberOfLines = 0;
            saveAllLabel.translatesAutoresizingMaskIntoConstraints = NO;
            [self.freeStreetFairView addSubview:saveAllLabel];
            
            NSLayoutYAxisAnchor *topAnchor = previousView ? previousView.bottomAnchor : self.freeStreetFairView.topAnchor;
            CGFloat trapDryBit = previousView ? 10 : semaphoreAwayFeaturePercentOrnaments;
            [NSLayoutConstraint activateConstraints:@[
                [saveAllLabel.topAnchor constraintEqualToAnchor:topAnchor constant:trapDryBit],
                [saveAllLabel.leadingAnchor constraintEqualToAnchor:self.freeStreetFairView.leadingAnchor constant:16],
                [saveAllLabel.trailingAnchor constraintEqualToAnchor:self.freeStreetFairView.trailingAnchor constant:-16]
            ]];
            previousView = saveAllLabel;
        }
        
        
        self.fireOffBringView = [[UIStackView alloc] init];
        self.fireOffBringView.axis = UILayoutConstraintAxisVertical;
        self.fireOffBringView.spacing = 1;  
        self.fireOffBringView.distribution = UIStackViewDistributionFillEqually;
        self.fireOffBringView.translatesAutoresizingMaskIntoConstraints = NO;
        [self.freeStreetFairView addSubview:self.fireOffBringView];
        
        
        NSLayoutYAxisAnchor *orderSelectedCreatorLowercaseExtern = previousView ? previousView.bottomAnchor : self.freeStreetFairView.topAnchor;
        CGFloat buttonsTopPadding = previousView ? semaphoreAwayFeaturePercentOrnaments : semaphoreAwayFeaturePercentOrnaments;
        
        [NSLayoutConstraint activateConstraints:@[
            [self.fireOffBringView.topAnchor constraintEqualToAnchor:orderSelectedCreatorLowercaseExtern constant:buttonsTopPadding],
            [self.fireOffBringView.leadingAnchor constraintEqualToAnchor:self.freeStreetFairView.leadingAnchor],
            [self.fireOffBringView.trailingAnchor constraintEqualToAnchor:self.freeStreetFairView.trailingAnchor],
            [self.fireOffBringView.bottomAnchor constraintEqualToAnchor:self.freeStreetFairView.bottomAnchor]
        ]];
        
        
       
       if (sexShotMonth.count == 2) {
           
           self.fireOffBringView = [[UIStackView alloc] init];
           self.fireOffBringView.axis = UILayoutConstraintAxisHorizontal;
           self.fireOffBringView.distribution = UIStackViewDistributionFillEqually;
           self.fireOffBringView.spacing = 1;  
           self.fireOffBringView.translatesAutoresizingMaskIntoConstraints = NO;
           [self.freeStreetFairView addSubview:self.fireOffBringView];
           
           NSLayoutYAxisAnchor *orderSelectedCreatorLowercaseExtern = previousView ? previousView.bottomAnchor : self.freeStreetFairView.topAnchor;
           [NSLayoutConstraint activateConstraints:@[
               [self.fireOffBringView.topAnchor constraintEqualToAnchor:orderSelectedCreatorLowercaseExtern constant:semaphoreAwayFeaturePercentOrnaments],
               [self.fireOffBringView.leadingAnchor constraintEqualToAnchor:self.freeStreetFairView.leadingAnchor],
               [self.fireOffBringView.trailingAnchor constraintEqualToAnchor:self.freeStreetFairView.trailingAnchor],
               [self.fireOffBringView.bottomAnchor constraintEqualToAnchor:self.freeStreetFairView.bottomAnchor]
           ]];
           
           
           for (NSInteger i = 0; i < sexShotMonth.count; i++) {
               NSString *mapLossy = sexShotMonth[i];
               UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
               [button setTitle:mapLossy forState:UIControlStateNormal];
               button.titleLabel.font = [UIFont systemFontOfSize:17];
               [button setTitleColor:[BurmeseZone oddGaelicColor] forState:UIControlStateNormal];
               [button setTitleColor:UIColor.lightGrayColor forState:UIControlStateHighlighted];
               button.backgroundColor = [UIColor whiteColor];
               button.tag = i;
               [button addTarget:self action:@selector(proposedBack:) forControlEvents:UIControlEventTouchUpInside];
               button.translatesAutoresizingMaskIntoConstraints = NO;
               [button.heightAnchor constraintEqualToConstant:40].active = YES;
               [self.fireOffBringView addArrangedSubview:button];
           }
       } else {
           
           self.fireOffBringView = [[UIStackView alloc] init];
           self.fireOffBringView.axis = UILayoutConstraintAxisVertical;
           self.fireOffBringView.spacing = 1;
           self.fireOffBringView.distribution = UIStackViewDistributionFillEqually;
           self.fireOffBringView.translatesAutoresizingMaskIntoConstraints = NO;
           [self.freeStreetFairView addSubview:self.fireOffBringView];
           
           NSLayoutYAxisAnchor *orderSelectedCreatorLowercaseExtern = previousView ? previousView.bottomAnchor : self.freeStreetFairView.topAnchor;
           [NSLayoutConstraint activateConstraints:@[
               [self.fireOffBringView.topAnchor constraintEqualToAnchor:orderSelectedCreatorLowercaseExtern constant:semaphoreAwayFeaturePercentOrnaments],
               [self.fireOffBringView.leadingAnchor constraintEqualToAnchor:self.freeStreetFairView.leadingAnchor],
               [self.fireOffBringView.trailingAnchor constraintEqualToAnchor:self.freeStreetFairView.trailingAnchor],
               [self.fireOffBringView.bottomAnchor constraintEqualToAnchor:self.freeStreetFairView.bottomAnchor]
           ]];
           
           for (NSInteger i = 0; i < sexShotMonth.count; i++) {
               NSString *mapLossy = sexShotMonth[i];
               UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
               [button setTitle:mapLossy forState:UIControlStateNormal];
               button.titleLabel.font = [UIFont systemFontOfSize:17];
               [button setTitleColor:[BurmeseZone oddGaelicColor] forState:UIControlStateNormal];
               [button setTitleColor:UIColor.lightGrayColor forState:UIControlStateHighlighted];
               button.backgroundColor = [UIColor whiteColor];
               button.tag = i;
               [button addTarget:self action:@selector(proposedBack:) forControlEvents:UIControlEventTouchUpInside];
               button.translatesAutoresizingMaskIntoConstraints = NO;
               [button.heightAnchor constraintEqualToConstant:40].active = YES;
               [self.fireOffBringView addArrangedSubview:button];
           }
       }
    }
    return self;
}

- (void)proposedBack:(UIButton *)sender {
    if (self.completion) {
        self.completion(sender.tag);
    }
    
    
    [UIView animateWithDuration:0.25 animations:^{
        self.alpha = 0;
    } completion:^(BOOL finished) {
        [TorchGainManager.shared wetFillWhileWindow];
    }];
}

+ (void)presenceAutoUnifiedMultiplyTruncateFusion:(NSString *)title
                        message:(NSString *)message
                   sexShotMonth:(NSArray<NSString *> *)sexShotMonth
                     completion:(CatCarKeyboardDifferentWakeSomali)completion {
    
    ButAlertView *shelf = [[ButAlertView alloc] initWithFrame:[UIScreen mainScreen].bounds
                                                 title:title
                                               message:message
                                          sexShotMonth:sexShotMonth
                                            completion:completion];
    
    
    [TorchGainManager.shared momentaryEncodingsSockCapacityIcyThinView:shelf];
    
    
    shelf.alpha = 0.0;
    [UIView animateWithDuration:0.25 animations:^{
        shelf.alpha = 1.0;
    }];
}

+ (void)presenceAutoUnifiedMultiplyTruncateFusion:(NSString *)title message:(NSString *)message completion:(CatCarKeyboardDifferentWakeSomali)completion {
    [self presenceAutoUnifiedMultiplyTruncateFusion:title message:message sexShotMonth:@[BurmeseZone.shotCapEastCap.extends] completion:completion];
}

@end
