






#import "ThirdCivilCell.h"
#import "BurmeseZone.h"
#import "Masonry.h"
#import "UIImage+YetImage.h"
#import "UIImageView+WebCache.h"
#import "NSString+DirtyFoot.h"

@interface ThirdCivilCell()


@property (nonatomic,strong) NSString * topTapDecline;


@property (nonatomic,strong) UIImageView * hashFixEndView;


@property (nonatomic,strong) UILabel * nowTenFormLabel;


@property (nonatomic,strong) UILabel * asleepCarLabel;

@property (nonatomic, strong) UIButton * flightButton;

@end

@implementation ThirdCivilCell

- (id)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if(self){
        
        self.clipsToBounds = YES;
        self.layer.cornerRadius = BurmeseZone.tipColorKind.heartDryWas;
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        
        self.hashFixEndView = [UIImageView new];
        self.hashFixEndView.tintColor = [BurmeseZone oddGaelicColor];
        self.hashFixEndView.layer.cornerRadius = BurmeseZone.tipColorKind.formSplitArt;
        [self.contentView addSubview:self.hashFixEndView];
        [self.hashFixEndView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.contentView).offset(BurmeseZone.tipColorKind.fourTabular);
            make.centerY.mas_equalTo(self.contentView);
            make.width.height.mas_equalTo(BurmeseZone.tipColorKind.toneChecking);
        }];
        
        self.nowTenFormLabel = [UILabel new];
        self.nowTenFormLabel.font = [UIFont boldSystemFontOfSize:BurmeseZone.tipColorKind.holdPhaseOdd];
        self.nowTenFormLabel.textColor = UIColor.darkGrayColor;
        [self.contentView addSubview:self.nowTenFormLabel];
        
        self.asleepCarLabel = [UILabel new];
        self.asleepCarLabel.font = [UIFont boldSystemFontOfSize:BurmeseZone.tipColorKind.otherAlcohol];
        self.asleepCarLabel.textColor = UIColor.darkGrayColor;
        [self.contentView addSubview:self.asleepCarLabel];
        
        [self.nowTenFormLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.hashFixEndView.mas_right).offset(BurmeseZone.tipColorKind.outputOptDue);
            make.centerY.equalTo(self.contentView);
        }];
        
        [self.asleepCarLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.nowTenFormLabel);
            make.top.equalTo(self.nowTenFormLabel.mas_bottom).offset(BurmeseZone.tipColorKind.proteinCase);
        }];
        
        self.flightButton = [[UIButton alloc] init];
        _flightButton.userInteractionEnabled = NO;
        
        UIImage *image = [[UIImage russianTrackingAppendingLemmaThreadedName:BurmeseZone.tipColorKind.touchesLossMultipleBitmapCapturing] imageWithRenderingMode:(UIImageRenderingModeAlwaysTemplate)];
        [_flightButton setBackgroundImage:[UIImage russianTrackingAppendingLemmaThreadedName:BurmeseZone.tipColorKind.sawMetricsLanguagesResolvedGrammar] forState: UIControlStateNormal];
        [_flightButton setBackgroundImage:image forState: UIControlStateSelected];
        _flightButton.tintColor = [BurmeseZone oddGaelicColor];
        [self.contentView addSubview:_flightButton];
        [_flightButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.offset(0);
            make.right.offset(-BurmeseZone.tipColorKind.outputOptDue);
            make.size.mas_equalTo(CGSizeMake(BurmeseZone.tipColorKind.floatAskForm, BurmeseZone.tipColorKind.floatAskForm));
        }];
    }
    return self;
}

- (void)setSelected:(BOOL)selected {
    _flightButton.selected = selected;
    self.layer.borderWidth = selected ? 1:0;
    self.layer.borderColor = [BurmeseZone oddGaelicColor].CGColor;
}

- (void)setFrame:(CGRect)frame {
    frame.origin.x = BurmeseZone.tipColorKind.fourTabular;
    frame.size.width -= BurmeseZone.tipColorKind.holdPhaseOdd;
    frame.origin.y += BurmeseZone.tipColorKind.fourTabular;
    frame.size.height -= BurmeseZone.tipColorKind.fourTabular;
    [super setFrame:frame];
}

-(void)setTopTapDecline:(NSString *)topTapDecline {
    _topTapDecline = topTapDecline;
    [self.hashFixEndView sd_setImageWithURL:[NSURL URLWithString:topTapDecline] placeholderImage:nil];
}


- (void)setOurCountry:(ArraySawSequencerReceiveCutPacket *)ourCountry {
    _ourCountry= ourCountry;
    self.topTapDecline = ourCountry.modernTag;
    self.nowTenFormLabel.text = ourCountry.longGroup;
    NSString *note = ourCountry.autoFarsi?:@"";
    if (note.popUploadOne) {
        self.asleepCarLabel.hidden = YES;
        [self.nowTenFormLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.hashFixEndView.mas_right).offset(BurmeseZone.tipColorKind.outputOptDue);
            make.centerY.equalTo(self.contentView);
        }];
    }else {
        self.asleepCarLabel.hidden = NO;
        NSRange range1 = [note rangeOfString:BurmeseZone.tipColorKind.directoryMidCityTexturedGaelicStrength];
        NSRange range2 = [note rangeOfString:BurmeseZone.tipColorKind.allergyAboutPrinterCandidateLearnHigh];
        
        if (range1.length == 0 && range2.length == 0) {
            self.asleepCarLabel.text = note;
            self.asleepCarLabel.font = [UIFont systemFontOfSize:BurmeseZone.tipColorKind.otherAlcohol];
            self.asleepCarLabel.textColor = UIColor.lightGrayColor;
        }else {
            NSRange download = NSMakeRange(range1.location+range1.length, range2.location-(range1.location+range1.length));
            NSString *wetAsk = [note substringWithRange:download];
            NSString *forRest = [note stringByReplacingOccurrencesOfString:BurmeseZone.tipColorKind.directoryMidCityTexturedGaelicStrength withString:@""];
            forRest = [forRest stringByReplacingOccurrencesOfString:BurmeseZone.tipColorKind.allergyAboutPrinterCandidateLearnHigh withString:@""];
            
            download = [forRest rangeOfString:wetAsk];
            NSMutableAttributedString *tenPagerSwap = [[NSMutableAttributedString alloc] initWithString:forRest];
            [tenPagerSwap addAttribute:NSForegroundColorAttributeName value:[UIColor lightGrayColor] range:NSMakeRange(0, forRest.length)];
            [tenPagerSwap addAttribute:NSFontAttributeName value:[UIFont systemFontOfSize:14] range:NSMakeRange(0, forRest.length)];
            [tenPagerSwap addAttribute:NSForegroundColorAttributeName value:[BurmeseZone oddGaelicColor] range:download];
            [tenPagerSwap addAttribute:NSFontAttributeName value:[UIFont boldSystemFontOfSize:14] range:download];
            
            self.asleepCarLabel.attributedText = tenPagerSwap;
        }
        
        [self.nowTenFormLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.hashFixEndView.mas_right).offset(BurmeseZone.tipColorKind.outputOptDue);
            make.top.equalTo(self.hashFixEndView).offset(BurmeseZone.tipColorKind.rawPlainRun);
        }];
    }
}

@end
