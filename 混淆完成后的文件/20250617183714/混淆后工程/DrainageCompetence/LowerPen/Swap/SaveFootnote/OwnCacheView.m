






#import "OwnCacheView.h"
#import "UIImageView+WebCache.h"
#import "UIImage+YetImage.h"
#import "BurmeseZone.h"
#import "NSString+DirtyFoot.h"
#import "ButAlertView.h"
#import "GainPassGetDogWindow.h"
#import "TapCardMenArtsViewController.h"
#import "UIDevice+TapDevice.h"
#import "TorchGainManager.h"

@interface OwnCacheView()  <UIGestureRecognizerDelegate> {
    CGPoint youAreInsertDependingMouthAttach;
    BOOL leadPolicyMealRawYet;
    BOOL barOneWrappedUploadingManagersEdge; 
    BOOL arrayWidgetWelshUnwindNot; 
}


@property (nonatomic, strong) GainPassGetDogWindow *leadCanSunWindow;
@property (nonatomic, weak) UIWindow *airAsleepRowWindow;


@property (nonatomic, strong) UIImageView *collisionView;
@property (nonatomic, strong) UIView *actionsLikeView;


@property (nonatomic, strong) UIView *socialKeyView;
@property (nonatomic, strong) UILabel *numberAreLabel;
@property (nonatomic, assign) BOOL vitalInitialAlignedYearsLacrosse;


@property (nonatomic, strong) NSTimer *growMenKinSeeTimer;
@property (nonatomic, assign) UIEdgeInsets zipSectionUnpluggedResumeYahoo;
@property (nonatomic, assign) CGRect negotiateUplinkNineScannerNominallyPointer;


@property (nonatomic, strong) UIImage *planBuilderImage;
@property (nonatomic, copy) NSString *softnessMalayalamDocumentsIndirectEast;
@property (nonatomic, strong) UIImage *valueLockImage;
@property (nonatomic, assign) CGFloat sumMindCurlBar;
@property (nonatomic, assign) OwnIndianFarEdge anyAxesDenseEdge;
@property (nonatomic, assign) NSTimeInterval sentReversesLanguageLearnChanged;
@property (nonatomic, assign) BOOL settlingFinnishTorqueTonePackAgreement;
@end

@implementation OwnCacheView


+ (instancetype)shared {
    static OwnCacheView *instance = nil;
    static dispatch_once_t onlyToken;
    dispatch_once(&onlyToken, ^{
        instance = [[super alloc] initWithFrame:CGRectZero];
        [instance actionsMoleRectumNineUnion];
    });
    return instance;
}

- (UIView *)actionsLikeView {
    if (!_actionsLikeView) {
        _actionsLikeView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 8, 8)];
        _actionsLikeView.backgroundColor = UIColor.redColor;
        _actionsLikeView.layer.cornerRadius = 4;
        _actionsLikeView.hidden = YES;
    }
    return _actionsLikeView;
}

- (void)actionsMoleRectumNineUnion {
    self.sumMindCurlBar = 10.0;
    self.sentReversesLanguageLearnChanged = 3.0;
    self.settlingFinnishTorqueTonePackAgreement = YES;
    
    
    self.collisionView = [[UIImageView alloc] init];
    self.collisionView.contentMode = UIViewContentModeScaleAspectFit;
    [self addSubview:self.collisionView];
    
    self.socialKeyView = [[UIView alloc] init];
    self.socialKeyView.backgroundColor = [UIColor colorWithWhite:0 alpha:0.7];
    self.socialKeyView.layer.cornerRadius = 20;
    self.socialKeyView.layer.masksToBounds = YES;
    self.socialKeyView.alpha = 0.0;
    
    self.numberAreLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 80, 40)];
    self.numberAreLabel.text = BurmeseZone.shotCapEastCap.selectorsEscapedConfigureForcePlanShowers;
    self.numberAreLabel.numberOfLines = 0;
    self.numberAreLabel.textColor = [UIColor whiteColor];
    self.numberAreLabel.textAlignment = NSTextAlignmentCenter;
    self.numberAreLabel.font = [UIFont systemFontOfSize:14];
    [self.socialKeyView addSubview:self.numberAreLabel];
    
    
    UIPanGestureRecognizer *spa = [[UIPanGestureRecognizer alloc]
                                   initWithTarget:self
                                   action:@selector(operatingElder:)];
    spa.delegate = self;
    [self addGestureRecognizer:spa];
    
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc]
                                   initWithTarget:self
                                   action:@selector(artsBadAwayNap)];
    [self addGestureRecognizer:tap];
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(decigramsServicesTeamAspectHandoverPerforms)
                                                 name:UIApplicationDidChangeStatusBarOrientationNotification
                                               object:nil];
#pragma clang diagnostic pop
}

- (void)setFixBedHourJson:(NSDictionary *)fixBedHourJson {
    _fixBedHourJson = fixBedHourJson;
    if (fixBedHourJson && self.leadCanSunWindow != nil) {
        NSString *action = fixBedHourJson[BurmeseZone.tipColorKind.allowTransport];
        if ([action isEqualToString:BurmeseZone.tipColorKind.orderedToday]) {
            self.actionsLikeView.hidden = NO;
        }else if ([action isEqualToString:BurmeseZone.tipColorKind.fatPrintable]) {
            self.actionsLikeView.hidden = YES;
        }else if ([action isEqualToString:BurmeseZone.tipColorKind.ductilityCheckedPlaneSwedishOur]) {
            self.actionsLikeView.hidden = NO;
        }
    }
}


+ (void)activated {
    [self.shared mixSpaceFileGregorianEqualityProceedImage:[UIImage russianTrackingAppendingLemmaThreadedName:BurmeseZone.tipColorKind.pastCenteredExpandingAscenderString] scopeTry:BurmeseZone.jumpSummariesCommandsExclusionPending nextImage:nil];
}

+ (void)availReturnedImage:(UIImage *)image {
    [[self shared] mixSpaceFileGregorianEqualityProceedImage:image nextImage:nil];
}

+ (void)rightRevertImage:(UIImage *)normalImage nextImage:(nullable UIImage *)nextImage {
    OwnCacheView *instance = [self shared];
    instance.planBuilderImage = normalImage;
    instance.softnessMalayalamDocumentsIndirectEast = nil;
    instance.valueLockImage = nextImage;
    instance.collisionView.image = normalImage;
}

+ (void)makerFold {
    [[self shared] softSubjectLargestElevenReason];
}

+ (BOOL)minorParseCase {
    return [self shared].leadCanSunWindow != nil;
}


- (void)mixSpaceFileGregorianEqualityProceedImage:(UIImage *)image nextImage:(nullable UIImage *)nextImage {
    [self mixSpaceFileGregorianEqualityProceedImage:image scopeTry:nil nextImage:nextImage];
}

- (void)mixSpaceFileGregorianEqualityProceedImage:(UIImage *)image scopeTry:(NSString *)scopeTry nextImage:(nullable UIImage *)nextImage {
    dispatch_async(dispatch_get_main_queue(), ^{
        self.planBuilderImage = image;
        self.softnessMalayalamDocumentsIndirectEast = scopeTry;
        self.valueLockImage = nextImage;
        
        if (!self.leadCanSunWindow) {
            [self illMirroringFreestyleButterflyBlueWindow];
            [self curveArmourBoldfaceUnifyAirline];
            [self cameraAlpineSafeUnderlineColumnBoundary];
            [self veryFootersLinearWinCaseJoining]; 
        }
        
        [self.leadCanSunWindow makeKeyAndVisible];
        [self.airAsleepRowWindow makeKeyWindow];
        
        [self sheAlertRedoDayUseHeartAnimation:YES];
        [self guideMoveRepublicSubYouTimer];
    });
}

- (void)softSubjectLargestElevenReason {
    dispatch_async(dispatch_get_main_queue(), ^{
        [self.leadCanSunWindow resignKeyWindow];
        self.leadCanSunWindow.hidden = YES;
        self.leadCanSunWindow = nil;
    });
}


- (void)illMirroringFreestyleButterflyBlueWindow {
    
    self.airAsleepRowWindow = [self encryptedHasCropFailureDayWindow];
    
    
    GainPassGetDogWindow *window = nil;
    
    
    if (@available(iOS 13.0, *)) {
        for (UIScene *scene in [UIApplication sharedApplication].connectedScenes) {
            if (scene.activationState == UISceneActivationStateForegroundActive &&
                [scene isKindOfClass:[UIWindowScene class]]) {
                window = [[GainPassGetDogWindow alloc] initWithWindowScene:(UIWindowScene *)scene];
                break;
            }
        }
    }
    
    
    if (!window) {
        window = [[GainPassGetDogWindow alloc] initWithFrame:[UIScreen mainScreen].bounds];
    }
    
    
    window.backgroundColor = [UIColor clearColor];
    window.clipsToBounds = YES; 
    window.windowLevel = UIWindowLevelAlert + 1000;
    window.backgroundColor = [UIColor clearColor];
    window.rootViewController = [[TapCardMenArtsViewController alloc] init];
    window.hidden = NO;
    self.leadCanSunWindow = window;
    
    
    [self.leadCanSunWindow resignKeyWindow];
    [self.airAsleepRowWindow makeKeyWindow];
    
    
    [self addSubview:self.actionsLikeView];
    
    
    self.frame = CGRectMake(0, 0, 60, 60);
    if (self.softnessMalayalamDocumentsIndirectEast) {
        [self.collisionView sd_setImageWithURL:[NSURL URLWithString:self.softnessMalayalamDocumentsIndirectEast] placeholderImage
                                              :[UIImage russianTrackingAppendingLemmaThreadedName:BurmeseZone.tipColorKind.pastCenteredExpandingAscenderString]
                                       options:(SDWebImageDelayPlaceholder)];
    }else {
        self.collisionView.image = self.planBuilderImage;
    }
    self.collisionView.frame = self.bounds;
    
    [self.leadCanSunWindow addSubview:self];
    [self.leadCanSunWindow addSubview:self.socialKeyView];
}


- (void)veryFootersLinearWinCaseJoining {
    CGRect swipeWinRoll = self.negotiateUplinkNineScannerNominallyPointer;
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
    UIInterfaceOrientation orientation = [UIApplication sharedApplication].statusBarOrientation;
#pragma clang diagnostic pop
    
    if (UIInterfaceOrientationIsLandscape(orientation)) {
        CGFloat merge = 180;
        self.socialKeyView.frame = CGRectMake(
         (swipeWinRoll.size.width - merge)/2,
          swipeWinRoll.size.height - merge/2,
          merge,
          merge
        );
        self.socialKeyView.layer.masksToBounds = YES;
        self.socialKeyView.layer.cornerRadius = merge/2;
        self.numberAreLabel.center = CGPointMake(merge/2, merge/4);
    }
    
    else {
        CGFloat merge = 240;
        self.socialKeyView.frame = CGRectMake(

          (swipeWinRoll.size.width - merge/2),
          swipeWinRoll.size.height - merge/2,
          merge,
          merge
        );
        self.socialKeyView.layer.masksToBounds = YES;
        self.socialKeyView.layer.cornerRadius = merge/2;
        self.numberAreLabel.center = CGPointMake(merge/3, merge/4);
    }
}


- (void)artsBadAwayNap {
    if (self.fixBedHourJson) {
        !self.audioFixHandler ?: self.audioFixHandler(self.fixBedHourJson[BurmeseZone.tipColorKind.reachedFlag]);
        if ([self.fixBedHourJson[BurmeseZone.tipColorKind.allowTransport] isEqualToString:BurmeseZone.tipColorKind.ductilityCheckedPlaneSwedishOur]) {
            self.actionsLikeView.hidden = YES;
            _fixBedHourJson = nil;
        }
    }else {
        !self.audioFixHandler ?: self.audioFixHandler(nil);
    }
}

- (void)operatingElder:(UIPanGestureRecognizer *)gesture {
    if (leadPolicyMealRawYet) return;
        
    CGPoint translation = [gesture translationInView:self.superview];
    
    switch (gesture.state) {
        case UIGestureRecognizerStateBegan:
            youAreInsertDependingMouthAttach = self.center;
            _collisionView.alpha = 1;
            [self sectionsSwashesSiteNeedCoalesced];
            barOneWrappedUploadingManagersEdge = NO; 
            arrayWidgetWelshUnwindNot = NO; 
            
            
            [self.layer removeAllAnimations];
            [self.socialKeyView.layer removeAllAnimations];
            
            
            self.socialKeyView.alpha = 0.0;
            self.socialKeyView.transform = CGAffineTransformIdentity;
            break;
            
        case UIGestureRecognizerStateChanged:{
            
            self.center = [self documentsTenSpaIncludingMediaBigTransitCenter:
                           CGPointMake(youAreInsertDependingMouthAttach.x + translation.x,
                                       youAreInsertDependingMouthAttach.y + translation.y)];
            
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
            
            BOOL MandarinIts = UIInterfaceOrientationIsLandscape([UIApplication sharedApplication].statusBarOrientation);
#pragma clang diagnostic pop
            CGRect hintFrame = self.socialKeyView.frame;
            CGRect touchArea = CGRectInset(hintFrame, -280, MandarinIts?-100:-280); 
            BOOL isInHideArea = CGRectContainsPoint(touchArea, self.center);
            
            
            
            
            if (isInHideArea != arrayWidgetWelshUnwindNot) {
                arrayWidgetWelshUnwindNot = isInHideArea;
                
                
                [UIView animateWithDuration:0.3
                                      delay:0
                                    options:UIViewAnimationOptionBeginFromCurrentState
                                 animations:^{
                    self.socialKeyView.alpha = isInHideArea ? 1.0 : 0.0;
                    self.socialKeyView.transform = isInHideArea ? CGAffineTransformMakeScale(1.2, 1.2) : CGAffineTransformIdentity;
                } completion:nil];
            }
            
            
            isInHideArea = CGRectContainsPoint(CGRectInset(hintFrame, 0, 0), self.center);
            if (isInHideArea && !barOneWrappedUploadingManagersEdge) {
                UIImpactFeedbackGenerator *plusPage = [[UIImpactFeedbackGenerator alloc] initWithStyle:UIImpactFeedbackStyleMedium];
                [plusPage prepare]; 
                [plusPage impactOccurred];
                barOneWrappedUploadingManagersEdge = YES;
                
                
                [UIView animateWithDuration:0.3
                                      delay:0
                                    options:UIViewAnimationOptionBeginFromCurrentState
                                 animations:^{
                    self.socialKeyView.transform = CGAffineTransformMakeScale(1.3, 1.3);
                } completion:nil];
            } else if (!isInHideArea) {
                if (barOneWrappedUploadingManagersEdge) {
                    self.socialKeyView.transform = CGAffineTransformMakeScale(1.2, 1.2);
                }
                barOneWrappedUploadingManagersEdge = NO;
            }
            
            
            touchArea = CGRectInset(hintFrame, 0, 0);
            _vitalInitialAlignedYearsLacrosse = CGRectContainsPoint(touchArea, self.center);
            break;
        }
            
        case UIGestureRecognizerStateEnded:
        case UIGestureRecognizerStateCancelled: {
            
            [UIView animateWithDuration:0.3 animations:^{
                self.socialKeyView.alpha = 0.0;
                self.socialKeyView.transform = CGAffineTransformIdentity;
            }];
            
            if (_vitalInitialAlignedYearsLacrosse) {
                [ButAlertView presenceAutoUnifiedMultiplyTruncateFusion:nil message:BurmeseZone.shotCapEastCap.enteredPreferOffExistScanWake sexShotMonth:@[BurmeseZone.shotCapEastCap.tryBlockBus, BurmeseZone.shotCapEastCap.extends] completion:^(NSInteger buttonIndex) {
                    if (buttonIndex ==1) {
                        [self softSubjectLargestElevenReason];
                    }else {
                        [self sheAlertRedoDayUseHeartAnimation:YES];
                        [self guideMoveRepublicSubYouTimer];
                    }
                }];
            } else {
                [self sheAlertRedoDayUseHeartAnimation:YES];
                [self guideMoveRepublicSubYouTimer];
            }
            barOneWrappedUploadingManagersEdge = NO;
            arrayWidgetWelshUnwindNot = NO;
            break;
        }

        default: break;
    }
}


- (void)sheAlertRedoDayUseHeartAnimation:(BOOL)animate {
    if (!_settlingFinnishTorqueTonePackAgreement) return;
    
    
    if (leadPolicyMealRawYet && animate) return;
    
    CGRect safeFrame = [self gatewaysPlusReportingDisallowMapFrame];
    CGPoint center = self.center;
    
    CGFloat shot = safeFrame.origin.x;
    CGFloat swap = safeFrame.origin.x + safeFrame.size.width;
    CGFloat pack = safeFrame.origin.y;
    CGFloat star = safeFrame.origin.y + safeFrame.size.height;
    
    
    OwnIndianFarEdge targetEdge = TrainingTamilAlcoholSubscriptOptNet;
    CGFloat minDistance = CGFLOAT_MAX;
    
    
    CGFloat toLeft = center.x - shot;
    CGFloat toRight = swap - center.x;
    CGFloat toTop = center.y - pack;
    CGFloat toBottom = star - center.y;
    
    NSArray *distances = @[@(toLeft), @(toRight), @(toTop), @(toBottom)];
    NSArray *edges = @[@(RemembersForceCousinItalicManSalient), @(DetailedIconPivotFusionInitiatedResponds),
                       @(ExpiredWillProxiesTiedRegistry), @(WrappedVarianceCreditsBrokenDateSmoothed)];
    
    for (NSInteger i = 0; i < distances.count; i++) {
        CGFloat distance = [distances[i] floatValue];
        if (distance < minDistance) {
            minDistance = distance;
            targetEdge = [edges[i] integerValue];
        }
    }
    
    
    if (targetEdge == self.anyAxesDenseEdge) {
        CGPoint parsingCenter = self.center;
        CGPoint sindhiCenter = [self serverIntervalsCombiningDroppedFinishingDiastolicEdge:targetEdge];
        CGFloat distance = hypot(parsingCenter.x - sindhiCenter.x, parsingCenter.y - sindhiCenter.y);
        if (distance < 5.0) { 
            return;
        }
    }
    
    self.anyAxesDenseEdge = targetEdge;
    
    
    CGPoint sindhiCenter = [self serverIntervalsCombiningDroppedFinishingDiastolicEdge:targetEdge];
    CGPoint underCenter = [self kernelMongolianSyntaxCapPullGradientDueEdge:targetEdge];
    
    
    leadPolicyMealRawYet = YES;
    
    
    [CATransaction begin];
    [CATransaction setCompletionBlock:^{
        self->leadPolicyMealRawYet = NO;
    }];
    
    [UIView animateWithDuration:animate ? 0.3 : 0
                     animations:^{
        self.center = sindhiCenter;
        self.actionsLikeView.center = underCenter;
    }];
    
    [CATransaction commit];
}


- (CGPoint)serverIntervalsCombiningDroppedFinishingDiastolicEdge:(OwnIndianFarEdge)edge {
    CGRect safeFrame = [self gatewaysPlusReportingDisallowMapFrame];
    CGPoint center = self.center;
    
    CGFloat shot = safeFrame.origin.x;
    CGFloat swap = safeFrame.origin.x + safeFrame.size.width;
    CGFloat pack = safeFrame.origin.y;
    CGFloat star = safeFrame.origin.y + safeFrame.size.height;
    
    CGPoint sindhiCenter = center;
    
    switch (edge) {
        case RemembersForceCousinItalicManSalient:
            sindhiCenter.x = shot + self.bounds.size.width/2 + _sumMindCurlBar;
            break;
        case DetailedIconPivotFusionInitiatedResponds:
            sindhiCenter.x = swap - self.bounds.size.width/2 - _sumMindCurlBar;
            break;
        case ExpiredWillProxiesTiedRegistry:
            sindhiCenter.y = pack + self.bounds.size.height/2 + _sumMindCurlBar;
            break;
        case WrappedVarianceCreditsBrokenDateSmoothed:
            sindhiCenter.y = star - self.bounds.size.height/2 - _sumMindCurlBar;
            break;
        default:
            break;
    }
    
    
    return [self documentsTenSpaIncludingMediaBigTransitCenter:sindhiCenter];
}


- (CGPoint)kernelMongolianSyntaxCapPullGradientDueEdge:(OwnIndianFarEdge)edge {
    CGPoint underCenter = CGPointMake(0, 0);
    
    switch (edge) {
        case RemembersForceCousinItalicManSalient:
            underCenter.x = self.bounds.size.width;
            break;
        case DetailedIconPivotFusionInitiatedResponds:
            
            break;
        case ExpiredWillProxiesTiedRegistry:
            underCenter.x = self.bounds.size.width;
            underCenter.y = self.bounds.size.height;
            break;
        case WrappedVarianceCreditsBrokenDateSmoothed:
            underCenter.x = self.bounds.size.width;
            break;
        default:
            break;
    }
    
    return underCenter;
}


- (void)guideMoveRepublicSubYouTimer {
    if (_sentReversesLanguageLearnChanged <= 0) return;
    
    [self sectionsSwashesSiteNeedCoalesced];
    _growMenKinSeeTimer = [NSTimer scheduledTimerWithTimeInterval:_sentReversesLanguageLearnChanged
                                                     target:self
                                                   selector:@selector(reusableSensitiveTapsActualShowers)
                                                   userInfo:nil
                                                    repeats:NO];
}

- (void)sectionsSwashesSiteNeedCoalesced {
    [_growMenKinSeeTimer invalidate];
    _growMenKinSeeTimer = nil;
}

- (void)reusableSensitiveTapsActualShowers {
    [UIView animateWithDuration:0.3 animations:^{
        self.collisionView.alpha = 0.5;
        
        CGRect frame = self.frame;
        switch (self.anyAxesDenseEdge) {
            case RemembersForceCousinItalicManSalient:
                frame.origin.x -= self.sumMindCurlBar;
                break;
            case DetailedIconPivotFusionInitiatedResponds:
                frame.origin.x += self.sumMindCurlBar;
                break;
            case ExpiredWillProxiesTiedRegistry:
                frame.origin.y -= self.sumMindCurlBar;
                break;
            case WrappedVarianceCreditsBrokenDateSmoothed:
                frame.origin.y += self.sumMindCurlBar;
                break;
            default:
                break;
        }
        self.frame = frame;
    }];
}


- (void)decigramsServicesTeamAspectHandoverPerforms {
    [self curveArmourBoldfaceUnifyAirline];
    [self veryFootersLinearWinCaseJoining]; 
    [self sheAlertRedoDayUseHeartAnimation:YES];
}


- (void)curveArmourBoldfaceUnifyAirline {
    UIWindow *keyWindow = TorchGainManager.shared.lowercaseOffWindow; //self.airAsleepRowWindow;
    UIEdgeInsets safeArea = UIEdgeInsetsZero;
    if (![UIDevice blendPin]) {
        safeArea = UIEdgeInsetsZero;
    }else if([UIDevice gaelic]) {
        safeArea = UIEdgeInsetsMake(0, 0, 20, 0);
    }else {
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
        UIInterfaceOrientation orientation = [[UIApplication sharedApplication] statusBarOrientation];
#pragma clang diagnostic pop
        safeArea = keyWindow.safeAreaInsets;
        switch (orientation) {
            case UIInterfaceOrientationPortrait:
                safeArea = UIEdgeInsetsMake(safeArea.top-10, 5, 15, 5);
                break;
            case UIInterfaceOrientationPortraitUpsideDown:
                safeArea = UIEdgeInsetsMake(15, 5, safeArea.bottom-10, 5);
                break;
            case UIInterfaceOrientationLandscapeRight:
                safeArea = UIEdgeInsetsMake(5, safeArea.right-10, 15, 5);
                break;
            case UIInterfaceOrientationLandscapeLeft:
                safeArea = UIEdgeInsetsMake(5, 5, 15, safeArea.left-10);
                break;
            case UIInterfaceOrientationUnknown:
            default:
                safeArea = safeArea;
        }
    }
    
    self.zipSectionUnpluggedResumeYahoo = safeArea;
    self.negotiateUplinkNineScannerNominallyPointer = keyWindow.bounds;
}

- (CGRect)gatewaysPlusReportingDisallowMapFrame {
    
    return CGRectMake(
        self.negotiateUplinkNineScannerNominallyPointer.origin.x + self.zipSectionUnpluggedResumeYahoo.left,
        self.negotiateUplinkNineScannerNominallyPointer.origin.y + self.zipSectionUnpluggedResumeYahoo.top,
        self.negotiateUplinkNineScannerNominallyPointer.size.width - (self.zipSectionUnpluggedResumeYahoo.left + self.zipSectionUnpluggedResumeYahoo.right),
        self.negotiateUplinkNineScannerNominallyPointer.size.height - (self.zipSectionUnpluggedResumeYahoo.top + self.zipSectionUnpluggedResumeYahoo.bottom)
    );
}


- (void)cameraAlpineSafeUnderlineColumnBoundary {
    NSString *netPitchFlow = [[NSUserDefaults standardUserDefaults] valueForKey:BurmeseZone.tipColorKind.extractReferentSlowUseLeakyGrade];
    if (netPitchFlow) {
        self.center = CGPointFromString(netPitchFlow);
    }else {
        
        CGRect safeFrame = [self gatewaysPlusReportingDisallowMapFrame];
        self.center = CGPointMake(safeFrame.origin.x + safeFrame.size.width - self.bounds.size.width/2 - _sumMindCurlBar,
                                  safeFrame.origin.y + safeFrame.size.height/2);
    }
}


- (UIWindow *)encryptedHasCropFailureDayWindow {
    if (@available(iOS 13.0, *)) {
        NSSet<UIScene *> *scenes = [UIApplication sharedApplication].connectedScenes;
        for (UIScene *scene in scenes) {
            if (scene.activationState == UISceneActivationStateForegroundActive &&
                [scene isKindOfClass:[UIWindowScene class]]) {
                UIWindowScene *windowScene = (UIWindowScene *)scene;
                return windowScene.windows.firstObject;
            }
        }
    }
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
    return [UIApplication sharedApplication].keyWindow;
#pragma clang diagnostic pop
}

- (CGPoint)documentsTenSpaIncludingMediaBigTransitCenter:(CGPoint)proposedCenter {
    CGRect safeFrame = [self gatewaysPlusReportingDisallowMapFrame];
    CGSize redTagSize = self.bounds.size;
    
    CGFloat shot = safeFrame.origin.x + redTagSize.width/2;
    CGFloat swap = safeFrame.origin.x + safeFrame.size.width - redTagSize.width/2;
    CGFloat pack = safeFrame.origin.y + redTagSize.height/2;
    CGFloat star = safeFrame.origin.y + safeFrame.size.height - redTagSize.height/2;
    
    return CGPointMake(
        MAX(shot, MIN(proposedCenter.x, swap)),
        MAX(pack, MIN(proposedCenter.y, star))
    );
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

@end
