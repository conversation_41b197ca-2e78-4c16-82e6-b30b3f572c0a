






#import "BinLawParseThickMixCell.h"
#import "BurmeseZone.h"
#import "Masonry.h"

@implementation BinLawParseThickMixCell

-(id)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if(self){
        
        self.backgroundColor = UIColor.whiteColor;
        self.contentView.backgroundColor = UIColor.whiteColor;
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        
        
        self.hashFixEndView = [UIImageView new];
        self.hashFixEndView.tintColor = [BurmeseZone oddGaelicColor];
        self.hashFixEndView.layer.cornerRadius = 15;
        [self.contentView addSubview:self.hashFixEndView];
        [self.hashFixEndView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(BurmeseZone.tipColorKind.outputOptDue);
            make.centerY.mas_equalTo(self);
            make.width.height.mas_equalTo(BurmeseZone.tipColorKind.sonPredicted);
        }];
        
        
        self.zeroNearName = [UILabel new];
        self.zeroNearName.font = [UIFont boldSystemFontOfSize:16];
        self.zeroNearName.textColor = UIColor.darkGrayColor;
        [self.contentView addSubview:self.zeroNearName];
        [self.zeroNearName mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.hashFixEndView.mas_right).offset(BurmeseZone.tipColorKind.outputOptDue);
            make.bottom.equalTo(self.contentView.mas_centerY);
        }];
        
        
        self.lastKitOnlyTime = [UILabel new];
        self.lastKitOnlyTime.font =  [UIFont systemFontOfSize:11];
        self.lastKitOnlyTime.textColor = UIColor.grayColor;
        [self.contentView addSubview:self.lastKitOnlyTime];
        [self.lastKitOnlyTime mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.contentView.mas_centerY).offset(BurmeseZone.tipColorKind.rawPlainRun);
            make.left.equalTo(self.zeroNearName);
        }];
    }
    return self;
}

@end
