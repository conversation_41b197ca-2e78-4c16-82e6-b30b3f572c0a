







#import <UIKit/UIKit.h>
#import "ManAllMildCaseCell.h"

@protocol MakerTwistLateDelegate;


@interface ShortHoverWide : UIView


@property (weak, nonatomic) id<MakerTwistLateDelegate> delegate;


- (void)sixBigMilesSee:(NSArray <ManAllMildCaseCell *> *)barrages;


- (void)start;


- (void)stop;

@end


@protocol MakerTwistLateDelegate <NSObject>

@optional


- (void)filtersStandView:(ShortHoverWide *)speakerView cloudMidMapCell:(ManAllMildCaseCell *)cell;


- (void)pointVelocityWorkSecondsArtSeeBriefExemplar:(ShortHoverWide *)speakerView;


- (void)filtersStandView:(ShortHoverWide *)speakerView willDisplayCell:(ManAllMildCaseCell *)cell;


- (void)filtersStandView:(ShortHoverWide *)speakerView didEndDisplayingCell:(ManAllMildCaseCell *)cell;

@end
