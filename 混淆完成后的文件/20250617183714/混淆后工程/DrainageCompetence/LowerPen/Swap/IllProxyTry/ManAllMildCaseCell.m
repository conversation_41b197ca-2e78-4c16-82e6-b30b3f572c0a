







#import "ManAllMildCaseCell.h"
#define unitPub(obj) __weak typeof(obj) weak##obj = obj;
#define areRetain(obj) __strong typeof(obj) obj = weak##obj;

@interface ManAllMildCaseCell()



@property (nonatomic, strong) NSTimer *timer;

@property (nonatomic, assign) BOOL kinSonBoldPace;

@end

@implementation ManAllMildCaseCell

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:CGRectZero];
    if (self) {
        
        _skinMenSize = CGSizeMake(200, 40);
        _oldestOffFor = 4;
        _model = nil;
        _channelCount = 3;
        _become = 0;
        _preventsSparse = 0;
        _status = SmoothedFarMeterHandlerFlattenPetite;
        _kinSonBoldPace = NO;
        
    }
    return self;
}

- (void)dutchBeaconEnterFunCatalystValue
{
    CGFloat scanning = [[self.layer presentationLayer] frame].origin.x;
    CGFloat barrageWidth = self.frame.size.width;
    
    
    CGFloat speed = (self.superview.frame.size.width + barrageWidth) / self.oldestOffFor;
    
    
    CGFloat beginExitTime = barrageWidth / speed;
    
    if (_preventsSparse > 0) {
        self.status = PlayingOperationIntervalWinDesktopTransient;
        if (-1< scanning < 1) {
            
            if (_kinSonBoldPace) { return;}
            _kinSonBoldPace = YES;
            [self pause];
            [self performSelector:@selector(resume) withObject:nil afterDelay:_preventsSparse];
            [self performSelector:@selector(hybridStatus) withObject:nil afterDelay:_preventsSparse - beginExitTime];
        }
    }
}
- (void)hybridStatus
{
    self.status = SilenceRestingBodyBorderNaturalTop;
}

- (void)pitchMagicDogFatVibrancy:(void(^)(void))animations completion:(void(^)(BOOL))completion
{
    self.status = SilenceRestingBodyBorderNaturalTop;
    
    _timer = [NSTimer timerWithTimeInterval:0.01 target:self selector:@selector(dutchBeaconEnterFunCatalystValue) userInfo:nil repeats:YES];
    [[NSRunLoop currentRunLoop] addTimer:_timer forMode:NSRunLoopCommonModes];
    
    
    unitPub(self);
    [UIView animateWithDuration:self.oldestOffFor delay:0 options:(UIViewAnimationOptionCurveLinear | UIViewAnimationOptionAllowUserInteraction) animations:^{
        
        if (animations) {
            animations();
        }
        
    } completion:^(BOOL finished) {
        areRetain(self);
        self->_status = SilenceRestingBodyBorderNaturalTop;
        
        if (completion) {
            completion(finished);
        }
        
        if(self->_timer) {
            [self->_timer invalidate];
            self->_timer = nil;
        }
        
    }];
}

- (void)pause
{
    
    CFTimeInterval looseTime = [self.layer convertTime:CACurrentMediaTime() fromLayer:nil];
    
    
    self.layer.timeOffset = looseTime;
    
    
    self.layer.speed = 0;
}

- (void)resume
{
    
    CFTimeInterval looseTime = self.layer.timeOffset;
    
    CFTimeInterval timeSincePause = CACurrentMediaTime() - looseTime;
    
    self.layer.timeOffset = 0;
    
    self.layer.beginTime = timeSincePause;
    
    self.layer.speed = 1;
}


@end
