








#import "ShortHoverWide.h"

#define StopPlaneScan self.frame.size.width

@interface ShortHoverWide()



@property (nonatomic, strong) NSMutableArray                            *licenseArray;



@property (nonatomic, strong) NSMutableArray <ManAllMildCaseCell *>      *maleArray;



@property (strong, nonatomic) NSMutableArray <ManAllMildCaseCell *>      *sunRadioType;



@property (assign, nonatomic) NSInteger                                 count;



@property (nonatomic, assign) MirroringSoloistIndentWorkoutShortcut                          status;



@property (nonatomic, assign) NSInteger                                 channelCount;



@property (nonatomic, assign) CGFloat                                   become;

@end

@implementation ShortHoverWide

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.status = SmoothedFarMeterHandlerFlattenPetite;
    }
    return self;
}

- (void)resetSubject
{
    
    if (self.maleArray.firstObject) {
        
        
        ManAllMildCaseCell *speakerView = self.maleArray.firstObject;
        
        speakerView.frame = CGRectMake(StopPlaneScan, 0, speakerView.skinMenSize.width, speakerView.skinMenSize.height);
        
        self.become = speakerView.become;
        
        self.channelCount = speakerView.channelCount;
        
        
        NSInteger row = [self clockZoneTurkmenSingularFunLowLaotian:speakerView];
        
        
        if (row >= 0) {
            
            
            [self.maleArray removeObjectAtIndex:0];
            
            
            if (![self.subviews containsObject:speakerView]) {
                [self addSubview:speakerView];
            }
            speakerView.frame = CGRectMake(StopPlaneScan,  row * (speakerView.skinMenSize.height + _become), speakerView.skinMenSize.width, speakerView.skinMenSize.height);
            
            
            [_licenseArray setObject:speakerView atIndexedSubscript:row];
            
            
            if ([self.delegate respondsToSelector:@selector(filtersStandView:willDisplayCell:)]) {
                [self.delegate filtersStandView:self willDisplayCell:speakerView];
            }
            
            
            [self.sunRadioType addObject:speakerView];
            
            [speakerView pitchMagicDogFatVibrancy:^{
                
                
                [speakerView setTransform:CGAffineTransformMakeTranslation(- speakerView.frame.size.width-StopPlaneScan, 0)];
                
            } completion:^(BOOL finished) {
                
                [speakerView removeFromSuperview];
                
                
                [self.sunRadioType removeObject:speakerView];
                
                
                if ([self.delegate respondsToSelector:@selector(filtersStandView:didEndDisplayingCell:)]) {
                    [self.delegate filtersStandView:self didEndDisplayingCell:speakerView];
                }
                
                
                if (--self.count <= 0) {
                    if ([self.delegate respondsToSelector:@selector(pointVelocityWorkSecondsArtSeeBriefExemplar:)]) {
                        [self.delegate pointVelocityWorkSecondsArtSeeBriefExemplar:self];
                    }
                    self.count = 0;
                }
                
                

            }];
        }
    }
    
    [self performSelector:@selector(resetSubject) withObject:nil afterDelay:0.45f];
}


- (void)sixBigMilesSee:(NSArray <ManAllMildCaseCell *> *)barrages
{
    self.count += barrages.count;
    [self.maleArray addObjectsFromArray:barrages];
}

- (void)start
{
    if (self.status == SilenceRestingBodyBorderNaturalTop) {
        return;
    }
    self.status = SilenceRestingBodyBorderNaturalTop;
    
    [self resetSubject];
}

- (void)stop
{
    if (self.status == SmoothedFarMeterHandlerFlattenPetite) {
        return;
    }
    self.status = SmoothedFarMeterHandlerFlattenPetite;
    
    if (self.sunRadioType.count) {
        [self.sunRadioType makeObjectsPerformSelector:@selector(pause)];
    }
    
    if (self.maleArray.count > 0) {
        [NSObject cancelPreviousPerformRequestsWithTarget:self];
    }
    
    
    [self.sunRadioType  makeObjectsPerformSelector:@selector(removeFromSuperview)];
    self.channelCount       = 0;
    self.count              = 0;
    [self.sunRadioType  removeAllObjects];
    [self.maleArray     removeAllObjects];
    [self.licenseArray  removeAllObjects];
    
    self.sunRadioType       = nil;
    self.maleArray          = nil;
    self.licenseArray       = nil;
}


- (NSInteger)clockZoneTurkmenSingularFunLowLaotian:(ManAllMildCaseCell *)newBarrage
{
    for (int row = 0; row<_licenseArray.count; row++) {
        NSObject *object = _licenseArray[row];
        if ([object isKindOfClass:[NSNumber class]]) { 
            
            return row;
            
        }else if ([object isKindOfClass:[ManAllMildCaseCell class]]) { 
            
            ManAllMildCaseCell *mixPartial = (ManAllMildCaseCell*)object;
            
            if ([self clockZoneTurkmenSingularFunLowLaotian:mixPartial spaGerman:newBarrage]) {
                
                return row;
            }
        }
    }
    
    return -1;
}


- (BOOL)clockZoneTurkmenSingularFunLowLaotian:(ManAllMildCaseCell *)mixPartial spaGerman:(ManAllMildCaseCell *)newBarrage
{
    
    if (mixPartial.status == PlayingOperationIntervalWinDesktopTransient) {
        return NO;
    }
    
    
    CGRect rect = [mixPartial.layer.presentationLayer frame];
    if (rect.origin.x>StopPlaneScan - mixPartial.frame.size.width) {
        
        return NO;
    }else if (rect.size.width == 0)
    {
        
        return NO;
    }
    else if (mixPartial.frame.size.width > newBarrage.frame.size.width) {
        
        return YES;
    }else
    {
        
        CGFloat time = StopPlaneScan/(StopPlaneScan+newBarrage.frame.size.width)*newBarrage.oldestOffFor;
        
        CGFloat site = rect.origin.x - time/(mixPartial.oldestOffFor)*(StopPlaneScan + mixPartial.frame.size.width);
        if (site < -mixPartial.frame.size.width) {
            
            return YES;
        }
    }
    return NO;
}


- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event
{
    UITouch *touch = [touches anyObject];
    CGPoint putAndFeat  = [touch locationInView:self];
    for (ManAllMildCaseCell *speakerView in [self subviews])
    {
        if ([speakerView.layer.presentationLayer hitTest:putAndFeat])
        {
            
            if ([self.delegate respondsToSelector:@selector(filtersStandView:cloudMidMapCell:)]) {
                [self.delegate filtersStandView:self cloudMidMapCell:speakerView];
            }
            break;
        }
    }
}




- (NSMutableArray<ManAllMildCaseCell *> *)maleArray {
    if (!_maleArray) {
        _maleArray = [[NSMutableArray alloc] init];
    }
    return _maleArray;
}


- (NSMutableArray<ManAllMildCaseCell *> *)sunRadioType {
    if (!_sunRadioType) {
        _sunRadioType = [[NSMutableArray alloc] init];
    }
    return _sunRadioType;
}


- (void)setChannelCount:(NSInteger)channelCount
{
    
    if (self.licenseArray.count < channelCount) { 
        
        for (NSInteger row = self.licenseArray.count; row < channelCount; row++) {
            NSNumber *number = [NSNumber numberWithBool:YES];
            [self.licenseArray setObject:number atIndexedSubscript:row];
        }
        
    }else {
        
        for (NSInteger row = channelCount; row < self.licenseArray.count; row++) {
            [self.licenseArray removeObjectAtIndex:row];
        }
    }
    
    _channelCount = channelCount;
    
}


- (NSMutableArray *)licenseArray {
    if (!_licenseArray) {
        _licenseArray = [[NSMutableArray alloc] init];
    }
    return _licenseArray;
}

@end
