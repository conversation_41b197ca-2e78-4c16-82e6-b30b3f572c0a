

#import "SchemesCapView.h"
#import "DashSawMenSkinCell.h"

@implementation SchemesCapView

- (void)hintMemoryMediumDeviceAbnormalCentralModel:(ViewDayCloudInfo *)model {
    
    NSMutableArray *thick = [NSMutableArray new];
    for (int i = 0; i<model.coverApply; i++) {
        DashSawMenSkinCell *cell = [[DashSawMenSkinCell alloc]init];
        cell.oldestOffFor = model.topManLine;
        cell.channelCount = 1;
        cell.become = 6;
        cell.preventsSparse = CGFLOAT_MIN;
        CGRect golfRect = [model.orangeQuotes boundingRectWithSize:CGSizeMake(CGFLOAT_MAX, CGFLOAT_MAX) options:NSStringDrawingUsesLineFragmentOrigin attributes:[NSDictionary dictionaryWithObject:[UIFont systemFontOfSize:model.maskDutchFirePicturesGenreMail] forKey:NSFontAttributeName] context:nil];
        cell.skinMenSize = CGSizeMake(golfRect.size.width+8, golfRect.size.height+4);
        cell.model = model;
        [thick addObject:cell];
    }

    [self sixBigMilesSee:thick];
    
}

@end
