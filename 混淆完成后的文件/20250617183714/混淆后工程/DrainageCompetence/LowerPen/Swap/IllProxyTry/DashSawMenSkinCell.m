






#import "DashSawMenSkinCell.h"
#import "ViewDayCloudInfo.h"
#import "Masonry.h"
#import "UIColor+JobColor.h"

@implementation DashSawMenSkinCell

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        
        self.backgroundColor = [UIColor clearColor];
        
        self.clipsToBounds = YES;
        
        [self addSubview:self.saveAllLabel];
        
        [self.saveAllLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.mas_equalTo(UIEdgeInsetsMake(0, 4, 0, 0));
        }];
        
    }
    return self;
}

- (void)setModel:(ViewDayCloudInfo *)model
{
    _model = model;
    _saveAllLabel.text = model.orangeQuotes;
    _saveAllLabel.font = [UIFont systemFontOfSize:model.maskDutchFirePicturesGenreMail];
    _saveAllLabel.textColor = [UIColor pashtoDispenseEnumerateGuideDanceLeast:model.insertCoachedOutletPintDigitalYet];
    self.backgroundColor = [[UIColor pashtoDispenseEnumerateGuideDanceLeast:model.finderAboveDescendedDeletingMalayOrange] colorWithAlphaComponent:model.finishOutEscapedLaunchAchievedThread];
    self.layer.cornerRadius = 2;
}

- (UILabel *)saveAllLabel {
    if (!_saveAllLabel) {
        _saveAllLabel = [[UILabel alloc] init];
        _saveAllLabel.backgroundColor = [UIColor clearColor];
        _saveAllLabel.textAlignment = NSTextAlignmentLeft;
    }
    return _saveAllLabel;
}

@end
