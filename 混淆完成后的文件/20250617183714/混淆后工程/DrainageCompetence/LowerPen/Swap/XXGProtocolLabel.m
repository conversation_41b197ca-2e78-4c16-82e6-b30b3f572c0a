






#import "XXGProtocolLabel.h"
#import "BurmeseZone.h"
#import "UIImage+YetImage.h"

@implementation XXGProtocolLabel

+ (XXGProtocolLabel *)wayRotorLabelLabel {
    return [self wayRotorLabelLabel:YES];
}

+ (XXGProtocolLabel *)wayRotorLabelLabel:(BOOL)isCheckBox {
    
    XXGProtocolLabel *label = [[XXGProtocolLabel alloc] init];
    label.numberOfLines = 0;
    label.textAlignment = NSTextAlignmentCenter;
    label.textColor = [UIColor lightGrayColor];
    label.font = [UIFont systemFontOfSize:12];
    label.userInteractionEnabled = YES; 

    NSAttributedString *specifiedSwipeSixSphericalWrong = nil;
    if (isCheckBox) {
        
        NSTextAttachment *attachment = [[NSTextAttachment alloc] init];
        UIImage *styleFitImage = [[UIImage russianTrackingAppendingLemmaThreadedName:BurmeseZone.tipColorKind.assertIgnoreTrapDiphthongSheIndirect] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        attachment.image = styleFitImage; 
        
        attachment.bounds = CGRectMake(0, -5, 20, 20);
        specifiedSwipeSixSphericalWrong = [NSAttributedString attributedStringWithAttachment:attachment];
    }

    
    NSString *text = BurmeseZone.shotCapEastCap.kilohertzUse;
    NSMutableAttributedString *itemLawCapWork = [[NSMutableAttributedString alloc] initWithString:text];
    
    
    NSRange getCupOceanFix = [text rangeOfString:BurmeseZone.shotCapEastCap.bendOnceEyeSin];
    if (getCupOceanFix.location != NSNotFound) {
        [itemLawCapWork addAttribute:NSForegroundColorAttributeName value:[BurmeseZone oddGaelicColor] range:getCupOceanFix];
        [itemLawCapWork addAttribute:NSUnderlineStyleAttributeName value:@(NSUnderlineStyleSingle) range:getCupOceanFix];
    }

    
    NSMutableAttributedString *behaviorsChildrenDeletingPubButterfly = [[NSMutableAttributedString alloc] init];
    if (specifiedSwipeSixSphericalWrong) {
        [behaviorsChildrenDeletingPubButterfly appendAttributedString:specifiedSwipeSixSphericalWrong];
    }
    [behaviorsChildrenDeletingPubButterfly appendAttributedString:itemLawCapWork];
    
    label.attributedText = behaviorsChildrenDeletingPubButterfly;
    
    
    UITapGestureRecognizer *moleHybrid = [[UITapGestureRecognizer alloc] initWithTarget:label action:@selector(tiedTaggerNorwegianBestSense:)];
    [label addGestureRecognizer:moleHybrid];
    
    return label;
}

- (void)setScanHexBitSalt:(BOOL)scanHexBitSalt {
    _scanHexBitSalt = !scanHexBitSalt;
    [self treeAllNoneConsumedStreamNordicLabel:self];
}

- (void)tiedTaggerNorwegianBestSense:(UITapGestureRecognizer *)moleHybrid {
    XXGProtocolLabel *label = (XXGProtocolLabel *)moleHybrid.view;
    if (!label.attributedText) return;
    
    
    NSTextStorage *textStorage = [[NSTextStorage alloc] initWithAttributedString:label.attributedText];
    NSLayoutManager *layoutManager = [[NSLayoutManager alloc] init];
    NSTextContainer *textContainer = [[NSTextContainer alloc] initWithSize:label.bounds.size];
    
    textContainer.lineFragmentPadding = 0;
    textContainer.maximumNumberOfLines = label.numberOfLines;
    textContainer.lineBreakMode = label.lineBreakMode;
    
    [textStorage addLayoutManager:layoutManager];
    [layoutManager addTextContainer:textContainer];
    
    
    [layoutManager ensureLayoutForTextContainer:textContainer];
    
    
    CGPoint foodFactRun = [moleHybrid locationInView:label];
    CGRect usedRect = [layoutManager usedRectForTextContainer:textContainer];
    CGPoint textContainerOrigin = CGPointMake(
        (label.bounds.size.width - usedRect.size.width) / 2,   
        (label.bounds.size.height - usedRect.size.height) / 2  
    );
    
    
    CGPoint locationInTextContainer = CGPointMake(
        foodFactRun.x - textContainerOrigin.x,
        foodFactRun.y - textContainerOrigin.y
    );
    
    
    __block BOOL isImageTapped = NO;
    [label.attributedText enumerateAttribute:NSAttachmentAttributeName
                                    inRange:NSMakeRange(0, label.attributedText.length)
                                    options:0
                                 usingBlock:^(id value, NSRange range, BOOL *stop) {
        if ([value isKindOfClass:[NSTextAttachment class]]) {
            
            NSRange glyphRange;
            [layoutManager glyphRangeForCharacterRange:range actualCharacterRange:&glyphRange];
            
            
            CGRect sheetRect = [layoutManager boundingRectForGlyphRange:glyphRange
                                                      inTextContainer:textContainer];
            
            
            CGRect optAchievedRect = CGRectOffset(sheetRect, textContainerOrigin.x, textContainerOrigin.y);
            
            
            if (CGRectContainsPoint(optAchievedRect, foodFactRun)) {
                isImageTapped = YES;
                *stop = YES;
            }
        }
    }];
    
    if (isImageTapped) {
        
        
        [self treeAllNoneConsumedStreamNordicLabel:label];
        return;
    }
    
    
    NSUInteger characterIndex = [layoutManager characterIndexForPoint:locationInTextContainer
                                                    inTextContainer:textContainer
                           fractionOfDistanceBetweenInsertionPoints:NULL];
    
    NSString *fullText = label.attributedText.string;
    NSRange getCupOceanFix = [fullText rangeOfString:BurmeseZone.shotCapEastCap.bendOnceEyeSin];
    
    if (characterIndex != NSNotFound && NSLocationInRange(characterIndex, getCupOceanFix)) {
        
        
        if (self.contextTeethTiedFinnishRestClose) {
            self.contextTeethTiedFinnishRestClose();
        }
    }
}


- (void)treeAllNoneConsumedStreamNordicLabel:(XXGProtocolLabel *)label {
    NSMutableAttributedString *attributedText = [label.attributedText mutableCopy];
    __block BOOL waxBestSpa = NO;
    
    [attributedText enumerateAttribute:NSAttachmentAttributeName
                             inRange:NSMakeRange(0, attributedText.length)
                             options:0
                          usingBlock:^(NSTextAttachment *oldAttachment, NSRange range, BOOL *stop) {
        if (![oldAttachment isKindOfClass:[NSTextAttachment class]]) return;
        
        
        BOOL aliveRoll = !_scanHexBitSalt;
        
        
        NSTextAttachment *dutchNearRest = [[NSTextAttachment alloc] init];
        
        
        UIColor *unloadColor = aliveRoll ? [BurmeseZone oddGaelicColor]: UIColor.lightGrayColor;
        UIImage *nonceFarImage = [UIImage russianTrackingAppendingLemmaThreadedName:aliveRoll ? BurmeseZone.tipColorKind.keyEscapedRoomBypassNoneEcho :BurmeseZone.tipColorKind.assertIgnoreTrapDiphthongSheIndirect];
        
        
        dutchNearRest.image = [[nonceFarImage imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate]
                                imageWithTintColor:unloadColor];
        dutchNearRest.bounds = oldAttachment.bounds;
        
        
        [attributedText removeAttribute:NSAttachmentAttributeName range:range];
        [attributedText addAttribute:NSAttachmentAttributeName value:dutchNearRest range:range];
        
        _scanHexBitSalt = aliveRoll;
        waxBestSpa = YES;
        *stop = YES;
    }];
    
    if (waxBestSpa) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [UIView transitionWithView:label
                              duration:0.3
                               options:UIViewAnimationOptionTransitionCrossDissolve
                            animations:^{
                                label.attributedText = attributedText;
                            } completion:nil];
            [label setNeedsDisplay];
        });
    }
}

@end
