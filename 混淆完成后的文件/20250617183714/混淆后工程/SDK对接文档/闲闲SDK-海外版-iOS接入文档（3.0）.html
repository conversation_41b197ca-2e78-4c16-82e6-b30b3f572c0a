<!DOCTYPE html>
<html lang="zh-CN">
 <head>
  <meta charset="utf-8"/>
  <meta content="width=device-width, initial-scale=1.0" name="viewport"/>
  <title>
   闲闲SDK-海外版-iOS接入文档（3.0）
  </title>
  <style>
   body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            padding: 0;
            margin: 0;
            color: #333;
            display: flex;
        }
        
        /* 目录样式 */
        .toc-container {
            width: 320px;
            height: 100vh;
            overflow-y: auto;
            position: sticky;
            top: 0;
            padding: 20px;
            background-color: #f8f9fa;
            border-right: 1px solid #e1e4e8;
            box-sizing: border-box;
        }
        
        .toc-container .toc {
            margin: 0;
            padding: 0;
        }
        
        .toc-container .toc ul {
            list-style-type: none;
            padding-left: 20px;
        }
        
        .toc-container .toc > ul {
            padding-left: 0;
        }
        
        .toc-container .toc li {
            margin: 8px 0;
        }
        
        .toc-container .toc a {
            color: #4a86e8;
            text-decoration: none;
            font-size: 14px;
            display: block;
            padding: 3px 0;
            border-left: 2px solid transparent;
            padding-left: 10px;
            transition: all 0.2s ease;
        }
        
        .toc-container .toc a:hover {
            border-left-color: #4a86e8;
            background-color: rgba(74, 134, 232, 0.1);
        }
        
        /* 顶部链接样式 */
        .toc-container .toc .toc-top-link {
            font-weight: bold;
            font-size: 16px;
            color: #333;
            padding: 5px 0;
            margin-bottom: 10px;
            border-bottom: 1px solid #e1e4e8;
        }
        
        .toc-container .toc .toc-top-link:hover {
            color: #4a86e8;
        }
        
        /* 内容样式 */
        .content-container {
            flex: 1;
            padding: 20px;
            max-width: 950px;
            margin: 0 auto;
            box-sizing: border-box;
        }
        
        /* 响应式布局 */
        @media (max-width: 768px) {
            body {
                flex-direction: column;
            }
            
            .toc-container {
                width: 100%;
                height: auto;
                max-height: 300px;
                position: relative;
            }
            
            .content-container {
                width: 100%;
            }
        }
        
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        
        code {
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            background-color: #f5f5f5;
            padding: 2px 4px;
            border-radius: 3px;
        }
        
        table {
            border-collapse: collapse;
            width: 100%;
            margin-bottom: 20px;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        
        th {
            background-color: #f2f2f2;
        }
        
        img {
            max-width: 100%;
            height: auto;
        }
        
        blockquote {
            border-left: 4px solid #ddd;
            padding-left: 16px;
            margin-left: 0;
            color: #666;
        }
        
        blockquote pre {
            background-color: #f0f0f0;
            border-left: 3px solid #ccc;
        }
        
        /* 标题样式优化 */
        h2 {
            font-size: 1.5em;
            border-bottom: 1px solid #eaecef;
            padding-bottom: 0.3em;
            text-decoration: underline;  /* 为 h2 添加下划线 */
        }
        
        /* 超链接颜色优化 */
        a {
            color: #4a86e8;  /* 更淡的浅蓝色 */
            text-decoration: none;
        }
        
        a:hover {
            text-decoration: underline;
        }
        
        /* 代码高亮样式 */
        pre { line-height: 125%; }
td.linenos .normal { color: inherit; background-color: transparent; padding-left: 5px; padding-right: 5px; }
span.linenos { color: inherit; background-color: transparent; padding-left: 5px; padding-right: 5px; }
td.linenos .special { color: #000000; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }
span.linenos.special { color: #000000; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }
.highlight .hll { background-color: #ffffcc }
.highlight { background: #f8f8f8; }
.highlight .c { color: #3D7B7B; font-style: italic } /* Comment */
.highlight .err { border: 1px solid #FF0000 } /* Error */
.highlight .k { color: #008000; font-weight: bold } /* Keyword */
.highlight .o { color: #666666 } /* Operator */
.highlight .ch { color: #3D7B7B; font-style: italic } /* Comment.Hashbang */
.highlight .cm { color: #3D7B7B; font-style: italic } /* Comment.Multiline */
.highlight .cp { color: #9C6500 } /* Comment.Preproc */
.highlight .cpf { color: #3D7B7B; font-style: italic } /* Comment.PreprocFile */
.highlight .c1 { color: #3D7B7B; font-style: italic } /* Comment.Single */
.highlight .cs { color: #3D7B7B; font-style: italic } /* Comment.Special */
.highlight .gd { color: #A00000 } /* Generic.Deleted */
.highlight .ge { font-style: italic } /* Generic.Emph */
.highlight .ges { font-weight: bold; font-style: italic } /* Generic.EmphStrong */
.highlight .gr { color: #E40000 } /* Generic.Error */
.highlight .gh { color: #000080; font-weight: bold } /* Generic.Heading */
.highlight .gi { color: #008400 } /* Generic.Inserted */
.highlight .go { color: #717171 } /* Generic.Output */
.highlight .gp { color: #000080; font-weight: bold } /* Generic.Prompt */
.highlight .gs { font-weight: bold } /* Generic.Strong */
.highlight .gu { color: #800080; font-weight: bold } /* Generic.Subheading */
.highlight .gt { color: #0044DD } /* Generic.Traceback */
.highlight .kc { color: #008000; font-weight: bold } /* Keyword.Constant */
.highlight .kd { color: #008000; font-weight: bold } /* Keyword.Declaration */
.highlight .kn { color: #008000; font-weight: bold } /* Keyword.Namespace */
.highlight .kp { color: #008000 } /* Keyword.Pseudo */
.highlight .kr { color: #008000; font-weight: bold } /* Keyword.Reserved */
.highlight .kt { color: #B00040 } /* Keyword.Type */
.highlight .m { color: #666666 } /* Literal.Number */
.highlight .s { color: #BA2121 } /* Literal.String */
.highlight .na { color: #687822 } /* Name.Attribute */
.highlight .nb { color: #008000 } /* Name.Builtin */
.highlight .nc { color: #0000FF; font-weight: bold } /* Name.Class */
.highlight .no { color: #880000 } /* Name.Constant */
.highlight .nd { color: #AA22FF } /* Name.Decorator */
.highlight .ni { color: #717171; font-weight: bold } /* Name.Entity */
.highlight .ne { color: #CB3F38; font-weight: bold } /* Name.Exception */
.highlight .nf { color: #0000FF } /* Name.Function */
.highlight .nl { color: #767600 } /* Name.Label */
.highlight .nn { color: #0000FF; font-weight: bold } /* Name.Namespace */
.highlight .nt { color: #008000; font-weight: bold } /* Name.Tag */
.highlight .nv { color: #19177C } /* Name.Variable */
.highlight .ow { color: #AA22FF; font-weight: bold } /* Operator.Word */
.highlight .w { color: #bbbbbb } /* Text.Whitespace */
.highlight .mb { color: #666666 } /* Literal.Number.Bin */
.highlight .mf { color: #666666 } /* Literal.Number.Float */
.highlight .mh { color: #666666 } /* Literal.Number.Hex */
.highlight .mi { color: #666666 } /* Literal.Number.Integer */
.highlight .mo { color: #666666 } /* Literal.Number.Oct */
.highlight .sa { color: #BA2121 } /* Literal.String.Affix */
.highlight .sb { color: #BA2121 } /* Literal.String.Backtick */
.highlight .sc { color: #BA2121 } /* Literal.String.Char */
.highlight .dl { color: #BA2121 } /* Literal.String.Delimiter */
.highlight .sd { color: #BA2121; font-style: italic } /* Literal.String.Doc */
.highlight .s2 { color: #BA2121 } /* Literal.String.Double */
.highlight .se { color: #AA5D1F; font-weight: bold } /* Literal.String.Escape */
.highlight .sh { color: #BA2121 } /* Literal.String.Heredoc */
.highlight .si { color: #A45A77; font-weight: bold } /* Literal.String.Interpol */
.highlight .sx { color: #008000 } /* Literal.String.Other */
.highlight .sr { color: #A45A77 } /* Literal.String.Regex */
.highlight .s1 { color: #BA2121 } /* Literal.String.Single */
.highlight .ss { color: #19177C } /* Literal.String.Symbol */
.highlight .bp { color: #008000 } /* Name.Builtin.Pseudo */
.highlight .fm { color: #0000FF } /* Name.Function.Magic */
.highlight .vc { color: #19177C } /* Name.Variable.Class */
.highlight .vg { color: #19177C } /* Name.Variable.Global */
.highlight .vi { color: #19177C } /* Name.Variable.Instance */
.highlight .vm { color: #19177C } /* Name.Variable.Magic */
.highlight .il { color: #666666 } /* Literal.Number.Integer.Long */
        
        .highlight {
            background: #f8f8f8;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .highlight pre {
            background: transparent;
            margin: 0;
            padding: 10px;
            border: none;
            font-size: 0.85em;  /* 缩小代码块字体 */
            line-height: 1.5;  /* 增加代码块行间距 */
        }
        
        .highlight code {
            line-height: 1.5;  /* 增加代码行间距 */
        }
        
        /* 代码行样式 */
        .highlight .line {
            line-height: 1.5;  /* 增加代码行间距 */
            margin-bottom: 2px;  /* 行之间添加间距 */
            display: block;  /* 确保每行都是块级元素 */
        }
        
        /* 减弱蓝色代码的强度 */
        .highlight .n, .highlight .na, .highlight .nb, .highlight .bp, .highlight .nc, .highlight .no, .highlight .nd, .highlight .ni, .highlight .ne, .highlight .nf, .highlight .nl, .highlight .nn, .highlight .nx, .highlight .py, .highlight .nt, .highlight .nv, .highlight .ow, .highlight .o, .highlight .oi, .highlight .om {
            color: #5c8db8 !important;  /* 减弱蓝色代码的强度 */
        }
        
        /* Objective-C 语法高亮 */
        .language-objectivec .highlight .k, .language-objectivec .highlight .kt {
            color: #008 !important;  /* 关键字 - 深蓝色 */
        }
        
        .language-objectivec .highlight .nc {
            color: #b06 !important;  /* 类名 - 紫色 */
        }
        
        .language-objectivec .highlight .mi {
            color: #099 !important;  /* 数字 - 青色 */
        }
        
        .language-objectivec .highlight .s, .language-objectivec .highlight .s1, .language-objectivec .highlight .s2 {
            color: #d14 !important;  /* 字符串 - 红色 */
        }
        
        .language-objectivec .highlight .cp {
            color: #999 !important;  /* 预处理指令 - 灰色 */
        }
        
        /* XML 标签颜色 */
        .language-xml .highlight .nt {
            color: #905 !important;  /* XML标签名称 - 暗红色 */
        }
        
        .language-xml .highlight .na {
            color: #690 !important;  /* XML属性名称 - 绿色 */
        }
        
        .language-xml .highlight .s, .language-xml .highlight .s1, .language-xml .highlight .s2 {
            color: #07a !important;  /* XML字符串 - 蓝色 */
        }
        
        /* JSON 语法高亮 */
        .language-json .highlight .p {
            color: #333 !important;  /* JSON标点符号 */
        }
        
        .language-json .highlight .s2 {
            color: #d14 !important;  /* JSON字符串 - 红色 */
        }
        
        .language-json .highlight .kc {
            color: #099 !important;  /* JSON关键字(true/false/null) - 青色 */
        }
        
        .language-json .highlight .mi {
            color: #099 !important;  /* JSON数字 - 青色 */
        }
        
        .language-label {
            display: block;
            background: #e0e0e0;
            padding: 3px 10px;
            font-size: 12px;
            font-family: sans-serif;
            color: #666;
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
        }
        
        .language-label {
            display: block;
            background: #e0e0e0;
            padding: 3px 10px;
            font-size: 12px;
            font-family: sans-serif;
            color: #666;
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
        }
        
        /* 引用块中的代码样式 */
        blockquote .highlight {
            background: #f0f0f0;
        }
        
        /* 隐藏原始 TOC */
        .content-container .toc {
            display: none;
        }
  </style>
 </head>
 <body>
  <div class="toc-container">
   <h2>
    目录
   </h2>
   <div class="toc">
    <ul>
     <li>
      <a href="#sdk-ios">
       闲闲SDK-海外版-iOS接入文档¶
      </a>
     </li>
     <ul>
      <li>
       <a href="#1">
        1. 更新日志¶
       </a>
      </li>
      <li>
       <a href="#2">
        2.环境支持¶
       </a>
      </li>
      <li>
       <a href="#3sdk">
        3.集成SDK¶
       </a>
      </li>
      <ul>
       <li>
        <a href="#30">
         3.0 参数准备¶
        </a>
       </li>
       <li>
        <a href="#31">
         3.1 接入方式¶
        </a>
       </li>
       <ul>
        <li>
         <a href="#311">
          3.1.1 静态接入¶
         </a>
        </li>
        <li>
         <a href="#312-swift">
          3.1.2 添加 swift文件¶
         </a>
        </li>
       </ul>
       <li>
        <a href="#32">
         3.2 添加依赖库（必要）¶
        </a>
       </li>
       <li>
        <a href="#33-other-linker-flags">
         3.3 Other Linker Flags（必要）¶
        </a>
       </li>
       <li>
        <a href="#34-infoplist">
         3.4 设置Info.plist¶
        </a>
       </li>
       <ul>
        <li>
         <a href="#341-url-schemes">
          3.4.1 URL Schemes¶
         </a>
        </li>
        <ul>
         <li>
          <a href="#3411-url-schemes-appappid">
           3.4.1.1 URL Schemes = "app"+"appid" （必要）¶
          </a>
         </li>
         <li>
          <a href="#3412-url-schemes-fbfacebookappid">
           3.4.1.2 URL Schemes = "fb"+"FacebookAppID" （根据需求选接）¶
          </a>
         </li>
         <li>
          <a href="#3413-url-schemes-vkvk_appid">
           3.4.1.3 URL Schemes = "vk"+"VK_APPID" （根据需求选接）¶
          </a>
         </li>
        </ul>
        <li>
         <a href="#342">
          3.4.2 权限（必要）¶
         </a>
        </li>
        <ul>
         <li>
          <a href="#3421-idfa">
           ******* 配置获取IDFA权限（必要）¶
          </a>
         </li>
         <li>
          <a href="#3422-photo">
           ******* 配置获取Photo权限（必要）¶
          </a>
         </li>
         <li>
          <a href="#3423">
           ******* 配置获取相机权限（按需选接）¶
          </a>
         </li>
        </ul>
        <li>
         <a href="#343-queried-url-schemes">
          3.4.3 白名单（Queried URL Schemes）¶
         </a>
        </li>
        <ul>
         <li>
          <a href="#3431-facebook">
           ******* Facebook（按需选接）¶
          </a>
         </li>
         <li>
          <a href="#3432-vk">
           3.4.3.2 VK登录（按需选接）¶
          </a>
         </li>
        </ul>
        <li>
         <a href="#344">
          3.4.4 其他配置¶
         </a>
        </li>
        <ul>
         <li>
          <a href="#3441-facebook">
           3.4.4.1 Facebook（按需选接）¶
          </a>
         </li>
         <li>
          <a href="#3442-appsflyer">
           ******* AppsFlyer（按需选接）¶
          </a>
         </li>
         <li>
          <a href="#3443-applovin">
           ******* AppLovin（按需选接）¶
          </a>
         </li>
        </ul>
       </ul>
       <li>
        <a href="#35-privacyinfoxcprivacy">
         3.5 PrivacyInfo.xcprivacy¶
        </a>
       </li>
      </ul>
      <li>
       <a href="#4sdk">
        4.SDK使用方式¶
       </a>
      </li>
      <ul>
       <li>
        <a href="#41">
         4.1 启动（必要）¶
        </a>
       </li>
       <li>
        <a href="#42-app">
         4.2 App通用链接回调（必要）¶
        </a>
       </li>
       <li>
        <a href="#43-sdk">
         4.3 设置SDK代理（必要）¶
        </a>
       </li>
       <li>
        <a href="#44">
         4.4 登录（必要）¶
        </a>
       </li>
       <li>
        <a href="#45">
         4.5 退出登录¶
        </a>
       </li>
       <li>
        <a href="#46">
         4.6 支付（必要）¶
        </a>
       </li>
       <li>
        <a href="#47">
         4.7 上报角色信息（必要）¶
        </a>
       </li>
       <li>
        <a href="#48-facebook">
         4.8 绑定Facebook（选接）¶
        </a>
       </li>
       <li>
        <a href="#49-vk">
         4.9 绑定VK（选接）¶
        </a>
       </li>
       <li>
        <a href="#410">
         4.10 事件打点（选接）¶
        </a>
       </li>
       <li>
        <a href="#411">
         4.11 激励广告（选接）¶
        </a>
       </li>
       <li>
        <a href="#412">
         4.12 上报日志（选接）¶
        </a>
       </li>
       <li>
        <a href="#412_1">
         4.12.其他配置¶
        </a>
       </li>
       <ul>
        <li>
         <a href="#4121">
          4.12.1 登录界面关闭接口（可选接口）¶
         </a>
        </li>
       </ul>
      </ul>
     </ul>
    </ul>
   </div>
  </div>
  <div class="content-container">
   <div class="markdown-body">
    <h1 id="sdk-ios" style="text-align: center;">
     闲闲SDK-海外版-iOS接入文档
     <a class="headerlink" href="#sdk-ios" title="Permanent link">
      ¶
     </a>
    </h1>
    <p>
     <center>
      当前版本 v3.2.0
     </center>
    </p>
    <blockquote>
     <p>
      🌍 闲闲SDK-海外版Server接入文档：
      <a href="https://psm4i21lig.feishu.cn/docx/EEundeNEHoIm7Rx7IlkcoOyGnBf">
       https://psm4i21lig.feishu.cn/docx/EEundeNEHoIm7Rx7IlkcoOyGnBf
      </a>
     </p>
    </blockquote>
    <h2 id="1">
     1. 更新日志
     <a class="headerlink" href="#1" title="Permanent link">
      ¶
     </a>
    </h2>
    <ul>
     <li>
      v3.2.0：2025.5.15
     </li>
     <li>
      修复已知bug，完善功能
     </li>
    </ul>
    <h2 id="2">
     2.环境支持
     <a class="headerlink" href="#2" title="Permanent link">
      ¶
     </a>
    </h2>
    <ul>
     <li>
      iOS:
      <code>
       13.0
      </code>
      及以上
     </li>
     <li>
      架构:
      <code>
       arm64
      </code>
     </li>
     <li>
      Xcode:
      <code>
       15.2
      </code>
      及以上
     </li>
     <li>
      上传AppStoreConnect 所需 Xcode版本参考Apple官方文档
      <a href="https://developer.apple.com/ios/submit/">
       https://developer.apple.com/ios/submit/
      </a>
      <code>
       实时更新
      </code>
     </li>
    </ul>
    <h2 id="3sdk">
     3.集成SDK
     <a class="headerlink" href="#3sdk" title="Permanent link">
      ¶
     </a>
    </h2>
    <h3 id="30">
     3.0 参数准备
     <a class="headerlink" href="#30" title="Permanent link">
      ¶
     </a>
    </h3>
    <p>
     集成所需参数如下：
    </p>
    <ul>
     <li>
      <p>
       SDK App ID
      </p>
     </li>
     <li>
      <p>
       appid  // 配置
       <code>
        URL Scheme
       </code>
       处使用
      </p>
     </li>
     <li>
      <p>
       第三方SDK - Facebook
      </p>
     </li>
     <li>
      <p>
       FacebookAppID
      </p>
     </li>
     <li>
      <p>
       FacebookDisplayName
      </p>
     </li>
     <li>
      <p>
       FacebookClientToken
      </p>
     </li>
     <li>
      <p>
       第三方SDK - VK（按需选接）
      </p>
     </li>
     <li>
      <p>
       VK_APPID // 配置
       <code>
        URL Scheme
       </code>
       处使用
      </p>
     </li>
     <li>
      第三方SDK - AppLovin（按需选接）
     </li>
     <li>
      GADApplicationIdentifier
     </li>
    </ul>
    <h3 id="31">
     3.1 接入方式
     <a class="headerlink" href="#31" title="Permanent link">
      ¶
     </a>
    </h3>
    <h4 id="311">
     3.1.1 静态接入
     <a class="headerlink" href="#311" title="Permanent link">
      ¶
     </a>
    </h4>
    <blockquote>
     <p>
      【SDK】目前只支持静态库接入方式
     </p>
    </blockquote>
    <p>
     将所有文件【SDK】添加到Xcode工程中，确保在工程的主目录下
    </p>
    <h4 id="312-swift">
     3.1.2 添加 swift文件
     <a class="headerlink" href="#312-swift" title="Permanent link">
      ¶
     </a>
    </h4>
    <p>
     由于SDK使用到包含swift的第三方库，所以项目中需包含一个swift文件，达到重新检查并修复模块间的依赖关系
    </p>
    <p>
     如果Xcode工程中已经存在swift文件可不添加，否则请直接在Xcode工程中任意位置创建任意一个swift文件
    </p>
    <h3 id="32">
     3.2 添加依赖库（必要）
     <a class="headerlink" href="#32" title="Permanent link">
      ¶
     </a>
    </h3>
    <p>
     <strong>
      配置规则：
     </strong>
    </p>
    <blockquote>
     <p>
      在Xcode工程中，选择你的工程设置项--&gt;选中“TARGETS”一栏对应的Target--&gt;在“Build Phases”标签栏的--&gt;"Link Binary With Libraries"添加
     </p>
    </blockquote>
    <ul>
     <li>
      无
     </li>
    </ul>
    <h3 id="33-other-linker-flags">
     3.3 Other Linker Flags（必要）
     <a class="headerlink" href="#33-other-linker-flags" title="Permanent link">
      ¶
     </a>
    </h3>
    <p>
     <strong>
      配置规则：
     </strong>
    </p>
    <blockquote>
     <p>
      在Xcode工程中，选择你的工程设置项--&gt;选中“TARGETS”一栏对应的Target--&gt;在“Build Phases”标签栏的--&gt;"Build Settings"搜索 Other Linker Flags 并添加
     </p>
     <p>
      <img alt="linker" src="https://cdn.jsdelivr.net/gh/zhmbo/static@master/img/202504091756997.png"/>
     </p>
    </blockquote>
    <ul>
     <li>
      <strong>
       -ObjC
      </strong>
     </li>
    </ul>
    <h3 id="34-infoplist">
     3.4 设置Info.plist
     <a class="headerlink" href="#34-infoplist" title="Permanent link">
      ¶
     </a>
    </h3>
    <p>
     <strong>
      配置规则：
     </strong>
    </p>
    <blockquote>
     <p>
      在Xcode工程中，选择你的工程设置项--&gt;选中“TARGETS”一栏对应的Target--&gt;在“info”标签栏的
     </p>
    </blockquote>
    <h4 id="341-url-schemes">
     3.4.1 URL Schemes
     <a class="headerlink" href="#341-url-schemes" title="Permanent link">
      ¶
     </a>
    </h4>
    <p>
     <strong>
      配置规则：
     </strong>
    </p>
    <blockquote>
     <p>
      在Xcode工程中，选择你的工程设置项--&gt;选中“TARGETS”一栏对应的Target--&gt;在“info”标签栏的--&gt;"URL Types"
     </p>
    </blockquote>
    <h5 id="3411-url-schemes-appappid">
     3.4.1.1 URL Schemes = "app"+"appid" （必要）
     <a class="headerlink" href="#3411-url-schemes-appappid" title="Permanent link">
      ¶
     </a>
    </h5>
    <blockquote>
     <p>
      例如：appid 为：352e8711bcca025e07230a8402f03d09
     </p>
     <p>
      则配置对应的 URL schemes：app352e8711bcca025e07230a8402f03d09
     </p>
     <p>
      <img alt="app352e8711bcca025e07230a8402f03d09" src="https://cdn.jsdelivr.net/gh/zhmbo/static@master/img/202504081827815.png"/>
     </p>
    </blockquote>
    <div class="highlight">
     <div class="highlight language-xml">
      <div class="language-label">
       xml
      </div>
      <div class="highlight">
       <pre><span></span><span class="nt">&lt;key&gt;</span>CFBundleURLTypes<span class="nt">&lt;/key&gt;</span>
<span class="nt">&lt;array&gt;</span>
<span class="w">  </span><span class="nt">&lt;dict&gt;</span>
<span class="w">    </span><span class="nt">&lt;key&gt;</span>CFBundleTypeRole<span class="nt">&lt;/key&gt;</span>
<span class="w">    </span><span class="nt">&lt;string&gt;</span>Editor<span class="nt">&lt;/string&gt;</span>
<span class="w">    </span><span class="nt">&lt;key&gt;</span>CFBundleURLSchemes<span class="nt">&lt;/key&gt;</span>
<span class="w">    </span><span class="nt">&lt;array&gt;</span>
<span class="w">      </span><span class="nt">&lt;string&gt;</span>app352e8711bcca025e07230a8402f03d09<span class="nt">&lt;/string&gt;</span>
<span class="w">    </span><span class="nt">&lt;/array&gt;</span>
<span class="w">  </span><span class="nt">&lt;/dict&gt;</span>
<span class="nt">&lt;/array&gt;</span>
</pre>
      </div>
     </div>
    </div>
    <h5 id="3412-url-schemes-fbfacebookappid">
     3.4.1.2 URL Schemes = "fb"+"FacebookAppID" （根据需求选接）
     <a class="headerlink" href="#3412-url-schemes-fbfacebookappid" title="Permanent link">
      ¶
     </a>
    </h5>
    <p>
     <strong>
      选接规则：
     </strong>
     如果SDK文件中包含FacebookSDK,并且我方运营提供了Facebook相关参数，则需要添加下面 scheme
    </p>
    <blockquote>
     <p>
      例如：FacebookAppID 为：804492424288162
     </p>
     <p>
      则配置对应的 URL schemes：fb804492424288162
     </p>
     <p>
      <img alt="fbscheme" src="https://cdn.jsdelivr.net/gh/zhmbo/static@master/img/202504111222958.png"/>
     </p>
    </blockquote>
    <div class="highlight">
     <div class="highlight language-xml">
      <div class="language-label">
       xml
      </div>
      <div class="highlight">
       <pre><span></span><span class="nt">&lt;key&gt;</span>CFBundleURLTypes<span class="nt">&lt;/key&gt;</span>
<span class="nt">&lt;array&gt;</span>
<span class="w">  </span><span class="nt">&lt;dict&gt;</span>
<span class="w">    </span><span class="nt">&lt;key&gt;</span>CFBundleTypeRole<span class="nt">&lt;/key&gt;</span>
<span class="w">    </span><span class="nt">&lt;string&gt;</span>Editor<span class="nt">&lt;/string&gt;</span>
<span class="w">    </span><span class="nt">&lt;key&gt;</span>CFBundleURLSchemes<span class="nt">&lt;/key&gt;</span>
<span class="w">    </span><span class="nt">&lt;array&gt;</span>
<span class="w">      </span><span class="nt">&lt;string&gt;</span>fb804492424288162<span class="nt">&lt;/string&gt;</span>
<span class="w">    </span><span class="nt">&lt;/array&gt;</span>
<span class="w">  </span><span class="nt">&lt;/dict&gt;</span>
<span class="nt">&lt;/array&gt;</span>
</pre>
      </div>
     </div>
    </div>
    <h5 id="3413-url-schemes-vkvk_appid">
     3.4.1.3 URL Schemes = "vk"+"VK_APPID" （根据需求选接）
     <a class="headerlink" href="#3413-url-schemes-vkvk_appid" title="Permanent link">
      ¶
     </a>
    </h5>
    <p>
     <strong>
      选接规则：
     </strong>
     如果SDK文件中包含VKSDK,并且我方运营提供了VK相关参数，则需要添加下面 scheme
    </p>
    <blockquote>
     <p>
      例如：VK_APPID 为：52832469
     </p>
     <p>
      则配置对应的 URL schemes：vk52832469
     </p>
     <p>
      <img alt="vkscheme" src="https://cdn.jsdelivr.net/gh/zhmbo/static@master/img/202504111224219.png"/>
     </p>
    </blockquote>
    <div class="highlight">
     <div class="highlight language-xml">
      <div class="language-label">
       xml
      </div>
      <div class="highlight">
       <pre><span></span><span class="nt">&lt;key&gt;</span>CFBundleURLTypes<span class="nt">&lt;/key&gt;</span>
<span class="nt">&lt;array&gt;</span>
<span class="w">  </span><span class="nt">&lt;dict&gt;</span>
<span class="w">    </span><span class="nt">&lt;key&gt;</span>CFBundleTypeRole<span class="nt">&lt;/key&gt;</span>
<span class="w">    </span><span class="nt">&lt;string&gt;</span>Editor<span class="nt">&lt;/string&gt;</span>
<span class="w">    </span><span class="nt">&lt;key&gt;</span>CFBundleURLSchemes<span class="nt">&lt;/key&gt;</span>
<span class="w">    </span><span class="nt">&lt;array&gt;</span>
<span class="w">      </span><span class="nt">&lt;string&gt;</span>vk52832469<span class="nt">&lt;/string&gt;</span>
<span class="w">    </span><span class="nt">&lt;/array&gt;</span>
<span class="w">  </span><span class="nt">&lt;/dict&gt;</span>
<span class="nt">&lt;/array&gt;</span>
</pre>
      </div>
     </div>
    </div>
    <h4 id="342">
     3.4.2 权限（必要）
     <a class="headerlink" href="#342" title="Permanent link">
      ¶
     </a>
    </h4>
    <p>
     <strong>
      配置规则：
     </strong>
    </p>
    <blockquote>
     <p>
      在Xcode工程中，选择你的工程设置项--&gt;选中“TARGETS”一栏对应的Target--&gt;在“info”标签栏的--&gt;"URL Types"--&gt;“”Custom iOS Target Properties”
     </p>
     <p>
      以下两个是SDK所需权限，根据产品发售地区请按需选择当地语言进行翻译，以下是其他（权限包括以下两种权限）的所有地区翻译：
     </p>
     <p>
      <strong>
       闲闲SDK-海外版-IOS-Info.plist权限多语言
      </strong>
      ：
      <a href="https://obqf4llj8m.feishu.cn/docx/Z7bBdu5jVoA64jxkXmrcw3UKnOb">
       https://obqf4llj8m.feishu.cn/docx/Z7bBdu5jVoA64jxkXmrcw3UKnOb
      </a>
     </p>
    </blockquote>
    <h5 id="3421-idfa">
     ******* 配置获取IDFA权限（必要）
     <a class="headerlink" href="#3421-idfa" title="Permanent link">
      ¶
     </a>
    </h5>
    <blockquote>
     <p>
      <strong>
       NSUserTrackingUsageDescription
      </strong>
     </p>
     <p>
      <img alt="idfa" src="https://cdn.jsdelivr.net/gh/zhmbo/static@master/img/202504081919728.png"/>
     </p>
    </blockquote>
    <div class="highlight">
     <div class="highlight language-xml">
      <div class="language-label">
       xml
      </div>
      <div class="highlight">
       <pre><span></span><span class="nt">&lt;key&gt;</span>NSUserTrackingUsageDescription<span class="nt">&lt;/key&gt;</span>
<span class="nt">&lt;string&gt;</span>App需要访问您的设备标识符(IDFA)，用于提供更契合您兴趣的内容，减少无关广告推荐<span class="nt">&lt;/string&gt;</span>
</pre>
      </div>
     </div>
    </div>
    <h5 id="3422-photo">
     ******* 配置获取Photo权限（必要）
     <a class="headerlink" href="#3422-photo" title="Permanent link">
      ¶
     </a>
    </h5>
    <blockquote>
     <p>
      <strong>
       NSPhotoLibraryAddUsageDescription
      </strong>
     </p>
     <p>
      <img alt="photo" src="https://cdn.jsdelivr.net/gh/zhmbo/static@master/img/202504081924488.png"/>
     </p>
    </blockquote>
    <div class="highlight">
     <div class="highlight language-xml">
      <div class="language-label">
       xml
      </div>
      <div class="highlight">
       <pre><span></span><span class="nt">&lt;key&gt;</span>NSPhotoLibraryAddUsageDescription<span class="nt">&lt;/key&gt;</span>
<span class="nt">&lt;string&gt;</span>APP需要您的同意，才能访问相册进行保存账号密码截图到您的相册，如禁止将无法完成保存操作<span class="nt">&lt;/string&gt;</span>
</pre>
      </div>
     </div>
    </div>
    <h5 id="3423">
     ******* 配置获取相机权限（按需选接）
     <a class="headerlink" href="#3423" title="Permanent link">
      ¶
     </a>
    </h5>
    <p>
     <strong>
      选接规则：
     </strong>
     如果SDK文件中包含JYouLoginKit（Poopo渠道）,并且我方运营提供了Poopo渠道相关参数，则需要添加下面 scheme
    </p>
    <blockquote>
     <p>
      <strong>
       NSCameraUsageDescription
      </strong>
     </p>
     <p>
      <img alt="camera" src="https://cdn.jsdelivr.net/gh/zhmbo/static@master/img/202505071706365.png"/>
     </p>
    </blockquote>
    <div class="highlight">
     <div class="highlight language-xml">
      <div class="language-label">
       xml
      </div>
      <div class="highlight">
       <pre><span></span><span class="nt">&lt;key&gt;</span>NSCameraUsageDescription<span class="nt">&lt;/key&gt;</span>
<span class="nt">&lt;string&gt;</span>APP需要您的同意，才能访问您的相机权限，如禁止将不能在游戏内提供照相功能<span class="nt">&lt;/string&gt;</span>
</pre>
      </div>
     </div>
    </div>
    <h4 id="343-queried-url-schemes">
     3.4.3 白名单（Queried URL Schemes）
     <a class="headerlink" href="#343-queried-url-schemes" title="Permanent link">
      ¶
     </a>
    </h4>
    <p>
     <strong>
      配置规则：
     </strong>
    </p>
    <blockquote>
     <p>
      在Xcode工程中，选择你的工程设置项--&gt;选中“TARGETS”一栏对应的Target--&gt;在“info”标签栏的--&gt;"URL Types"--&gt;“”Custom iOS Target Properties”
     </p>
     <p>
      <strong>
       LSApplicationQueriesSchemes
      </strong>
     </p>
    </blockquote>
    <h5 id="3431-facebook">
     ******* Facebook（按需选接）
     <a class="headerlink" href="#3431-facebook" title="Permanent link">
      ¶
     </a>
    </h5>
    <p>
     <strong>
      选接规则：
     </strong>
     如果SDK文件中包含FacebookSDK,或我方运营提供了Facebook相关参数，则需要添加下面白名单
    </p>
    <blockquote>
     <p>
      Facebook登录、分享等功能使用
     </p>
     <p>
      <img alt="白名单fb" src="https://cdn.jsdelivr.net/gh/zhmbo/static@master/img/202504111617967.png"/>
     </p>
    </blockquote>
    <div class="highlight">
     <div class="highlight language-xml">
      <div class="language-label">
       xml
      </div>
      <div class="highlight">
       <pre><span></span><span class="nt">&lt;key&gt;</span>LSApplicationQueriesSchemes<span class="nt">&lt;/key&gt;</span>
<span class="nt">&lt;array&gt;</span>
<span class="w">  </span><span class="nt">&lt;string&gt;</span>fb<span class="nt">&lt;/string&gt;</span>
<span class="w">  </span><span class="nt">&lt;string&gt;</span>fbapi<span class="nt">&lt;/string&gt;</span>
<span class="w">  </span><span class="nt">&lt;string&gt;</span>fb-messenger-share-api<span class="nt">&lt;/string&gt;</span>
<span class="w">  </span><span class="nt">&lt;string&gt;</span>fbauth2<span class="nt">&lt;/string&gt;</span>
<span class="w">  </span><span class="nt">&lt;string&gt;</span>fbshareextension<span class="nt">&lt;/string&gt;</span>
<span class="nt">&lt;/array&gt;</span>
</pre>
      </div>
     </div>
    </div>
    <h5 id="3432-vk">
     3.4.3.2 VK登录（按需选接）
     <a class="headerlink" href="#3432-vk" title="Permanent link">
      ¶
     </a>
    </h5>
    <p>
     <strong>
      选接规则：
     </strong>
     如果SDK文件中包含VKSDK,或我方运营提供了VK相关参数，则需要添加下面白名单
    </p>
    <blockquote>
     <p>
      VK白名单
     </p>
     <p>
      <img alt="白名单vk" src="https://cdn.jsdelivr.net/gh/zhmbo/static@master/img/202504111618111.png"/>
     </p>
    </blockquote>
    <div class="highlight">
     <div class="highlight language-xml">
      <div class="language-label">
       xml
      </div>
      <div class="highlight">
       <pre><span></span><span class="nt">&lt;key&gt;</span>LSApplicationQueriesSchemes<span class="nt">&lt;/key&gt;</span>
<span class="nt">&lt;array&gt;</span>
<span class="w">  </span><span class="nt">&lt;string&gt;</span>vkauthorize-silent<span class="nt">&lt;/string&gt;</span>
<span class="nt">&lt;/array&gt;</span>
</pre>
      </div>
     </div>
    </div>
    <h4 id="344">
     3.4.4 其他配置
     <a class="headerlink" href="#344" title="Permanent link">
      ¶
     </a>
    </h4>
    <h5 id="3441-facebook">
     3.4.4.1 Facebook（按需选接）
     <a class="headerlink" href="#3441-facebook" title="Permanent link">
      ¶
     </a>
    </h5>
    <p>
     <strong>
      选接规则：
     </strong>
     如果SDK文件中包含FacebookSDK,或我方运营提供了Facebook相关参数，则需要添加下面参数
    </p>
    <blockquote>
     <p>
      用于FacebookSDK初始化使用
     </p>
     <p>
      <img alt="fb" src="https://cdn.jsdelivr.net/gh/zhmbo/static@master/img/202505151324950.png"/>
     </p>
    </blockquote>
    <div class="highlight">
     <div class="highlight language-xml">
      <div class="language-label">
       xml
      </div>
      <div class="highlight">
       <pre><span></span><span class="nt">&lt;key&gt;</span>FacebookAppID<span class="nt">&lt;/key&gt;</span>
<span class="nt">&lt;string&gt;</span>我方运营提供<span class="nt">&lt;/string&gt;</span>
<span class="nt">&lt;key&gt;</span>FacebookDisplayName<span class="nt">&lt;/key&gt;</span>
<span class="nt">&lt;string&gt;</span>我方运营提供或跟应用名称一致<span class="nt">&lt;/string&gt;</span>
<span class="nt">&lt;key&gt;</span>FacebookClientToken<span class="nt">&lt;/key&gt;</span>
<span class="nt">&lt;string&gt;</span>我方运营提供<span class="nt">&lt;/string&gt;</span>
<span class="nt">&lt;key&gt;</span>FacebookAutoLogAppEventsEnabled<span class="nt">&lt;/key&gt;</span>
<span class="nt">&lt;true/&gt;</span>
<span class="nt">&lt;key&gt;</span>FacebookAdvertiserIDCollectionEnabled<span class="nt">&lt;/key&gt;</span>
<span class="nt">&lt;true/&gt;</span>
</pre>
      </div>
     </div>
    </div>
    <h5 id="3442-appsflyer">
     ******* AppsFlyer（按需选接）
     <a class="headerlink" href="#3442-appsflyer" title="Permanent link">
      ¶
     </a>
    </h5>
    <blockquote>
     <p>
      归因 App Clip
     </p>
     <p>
      <img alt="af" src="https://cdn.jsdelivr.net/gh/zhmbo/static@master/img/202504111657571.png"/>
     </p>
    </blockquote>
    <div class="highlight">
     <div class="highlight language-xml">
      <div class="language-label">
       xml
      </div>
      <div class="highlight">
       <pre><span></span><span class="nt">&lt;key&gt;</span>NSAdvertisingAttributionReportEndpoint<span class="nt">&lt;/key&gt;</span>
<span class="nt">&lt;string&gt;</span>https://appsflyer-skadnetwork.com/<span class="nt">&lt;/string&gt;</span>
</pre>
      </div>
     </div>
    </div>
    <h5 id="3443-applovin">
     ******* AppLovin（按需选接）
     <a class="headerlink" href="#3443-applovin" title="Permanent link">
      ¶
     </a>
    </h5>
    <p>
     <strong>
      选接规则：
     </strong>
     如果SDK文件中包含AppLovin,或我方运营提供了AppLovin相关参数，则需要添加下面参数
    </p>
    <blockquote>
     <p>
      <strong>
       GADApplicationIdentifier
      </strong>
     </p>
     <p>
      Value：ADMOB_APP_ID
     </p>
     <p>
      <img alt="GADApplicationIdentifier" src="https://cdn.jsdelivr.net/gh/zhmbo/static@master/img/202505081421096.png"/>
     </p>
    </blockquote>
    <div class="highlight">
     <div class="highlight language-xml">
      <div class="language-label">
       xml
      </div>
      <div class="highlight">
       <pre><span></span><span class="nt">&lt;key&gt;</span>GADApplicationIdentifier<span class="nt">&lt;/key&gt;</span>
<span class="nt">&lt;string&gt;</span>我方运营提供<span class="nt">&lt;/string&gt;</span>
</pre>
      </div>
     </div>
    </div>
    <p>
     <strong>
      如果接入AppLovin还需以下配置：
     </strong>
    </p>
    <ol>
     <li>
      接入 AppLovin 需要使用包管理工具 CocoaPods 接入，将下面脚本复制到Podfile。如果未提供了GADApplicationIdentifier 参数则 pod 'AppLovinMediationGoogleAdapter'  可以去除
     </li>
    </ol>
    <blockquote>
     <p>
      这里pod install，需要给终端搭梯子，命令中端口号需要更换为电脑本地开启的代理端口例如：7890。
     </p>
     <p>
      如何查看代理端口？打开电脑设置---网络（WIFI）---详情信息---代理---查看，然后更换成对应代理，执行下面命令
     </p>
     <p>
      export https_proxy=http://127.0.0.1:7890 http_proxy=http://127.0.0.1:7890 all_proxy=socks5://127.0.0.1:7890
     </p>
     <p>
      再终端执行下面命令
      <code>
       pod install --repo-update
      </code>
     </p>
    </blockquote>
    <div class="highlight">
     <div class="highlight language-ruby">
      <div class="language-label">
       ruby
      </div>
      <div class="highlight">
       <pre><span></span><span class="n">platform</span><span class="w"> </span><span class="ss">:ios</span><span class="p">,</span><span class="w"> </span><span class="s1">'13.0'</span>

<span class="n">use_frameworks!</span>
<span class="n">inhibit_all_warnings!</span>

<span class="n">target</span><span class="w"> </span><span class="s1">'YOUR_PROJECT_NAME'</span><span class="w"> </span><span class="k">do</span>
<span class="w">  </span><span class="n">pod</span><span class="w"> </span><span class="s1">'AppLovinSDK'</span>
<span class="w">  </span><span class="n">pod</span><span class="w"> </span><span class="s1">'AppLovinMediationGoogleAdapter'</span>
<span class="w">  </span><span class="n">pod</span><span class="w"> </span><span class="s1">'AppLovinMediationIronSourceAdapter'</span>
<span class="w">  </span><span class="n">pod</span><span class="w"> </span><span class="s1">'AppLovinMediationFacebookAdapter'</span>
<span class="w">  </span><span class="n">pod</span><span class="w"> </span><span class="s1">'AppLovinMediationMintegralAdapter'</span>
<span class="w">  </span><span class="n">pod</span><span class="w"> </span><span class="s1">'AppLovinMediationByteDanceAdapter'</span>
<span class="w">  </span><span class="n">pod</span><span class="w"> </span><span class="s1">'AppLovinMediationUnityAdsAdapter'</span>
<span class="w">  </span><span class="n">pod</span><span class="w"> </span><span class="s1">'AppLovinMediationYandexAdapter'</span>
<span class="k">end</span>
</pre>
      </div>
     </div>
    </div>
    <ol start="2">
     <li>
      工程
      <strong>
       Build Setting
      </strong>
      搜索
      <strong>
       ENABLE_USER_SCRIPT_SANDBOXING
      </strong>
      设置为
      <strong>
       NO
      </strong>
     </li>
    </ol>
    <p>
     <img alt="ENABLE_USER_SCRIPT_SANDBOXING" src="https://cdn.jsdelivr.net/gh/zhmbo/static@master/img/202505081717401.png"/>
    </p>
    <ol start="3">
     <li>
      在
      <strong>
       Info.plist
      </strong>
      添加
      <strong>
       SKAdNetworkItems
      </strong>
     </li>
    </ol>
    <blockquote>
     <p>
      下面链接是
      <strong>
       闲闲SDK-海外版-IOS-Info.plist-SKAdNetworkItems
      </strong>
      完整代码：
     </p>
     <p>
      点击链接查看并复制到工程中：
      <a href="https://obqf4llj8m.feishu.cn/docx/Aj4Zd9fxeoC4N2x8CIxcFumGnme?from=from_copylink">
       https://obqf4llj8m.feishu.cn/docx/Aj4Zd9fxeoC4N2x8CIxcFumGnme?from=from_copylink
      </a>
     </p>
    </blockquote>
    <p>
     <img alt="SKAdNetworkItems" src="https://cdn.jsdelivr.net/gh/zhmbo/static@master/img/202505081804623.png"/>
    </p>
    <h3 id="35-privacyinfoxcprivacy">
     3.5 PrivacyInfo.xcprivacy
     <a class="headerlink" href="#35-privacyinfoxcprivacy" title="Permanent link">
      ¶
     </a>
    </h3>
    <p>
     根据2024年5月1日Apple新隐私条款：
    </p>
    <p>
     <a href="https://developer.apple.com/documentation/bundleresources/privacy-manifest-files?language=objc">
      https://developer.apple.com/documentation/bundleresources/privacy-manifest-files?language=objc
     </a>
    </p>
    <p>
     <a href="https://developer.apple.com/support/third-party-SDK-requirements/">
      https://developer.apple.com/support/third-party-SDK-requirements/
     </a>
    </p>
    <p>
     做哪些：
    </p>
    <ul>
     <li>
      工程中添加 PrivacyInfo.xcprivacy
     </li>
     <li>
      第三方库中添加 PrivacyInfo.xcprivacy
     </li>
    </ul>
    <p>
     参考获取文件方式：
    </p>
    <ul>
     <li>
      <p>
       <a href="https://github.com/kimbely0320/update_privacy_info.py">
        https://github.com/kimbely0320/update_privacy_info.py
       </a>
      </p>
     </li>
     <li>
      <p>
       <a href="https://www.privacymanifest.dev/">
        https://www.privacymanifest.dev/
       </a>
      </p>
     </li>
    </ul>
    <h2 id="4sdk">
     4.SDK使用方式
     <a class="headerlink" href="#4sdk" title="Permanent link">
      ¶
     </a>
    </h2>
    <h3 id="41">
     4.1 启动（必要）
     <a class="headerlink" href="#41" title="Permanent link">
      ¶
     </a>
    </h3>
    <p>
     如使用AppDelegate：
    </p>
    <div class="highlight">
     <div class="highlight language-Objective-C">
      <div class="language-label">
       Objective-C
      </div>
      <div class="highlight">
       <pre><span></span><span class="cp">#import &lt;DrainageCompetence/DrainageCompetence.h&gt;</span>

<span class="p">-</span> <span class="p">(</span><span class="kt">BOOL</span><span class="p">)</span><span class="nf">application:</span><span class="p">(</span><span class="bp">UIApplication</span><span class="w"> </span><span class="o">*</span><span class="p">)</span><span class="nv">application</span><span class="w"> </span><span class="nf">didFinishLaunchingWithOptions:</span><span class="p">(</span><span class="bp">NSDictionary</span><span class="w"> </span><span class="o">*</span><span class="p">)</span><span class="nv">launchOptions</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// 如果您的应用程序使用UIScene ，请从UISceneDelegate实现以下方法，这里无需实现</span>
<span class="w">    </span><span class="p">[</span><span class="n">BrokenDid</span><span class="w"> </span><span class="n">applierExtractAbsentGatheringBeginWaistOptions</span><span class="o">:</span><span class="n">launchOptions</span><span class="w"> </span><span class="n">pagerEndOptions</span><span class="o">:</span><span class="nb">nil</span><span class="p">];</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="nb">YES</span><span class="p">;</span>
<span class="p">}</span>
</pre>
      </div>
     </div>
    </div>
    <p>
     如使用SceneDelegate：
    </p>
    <div class="highlight">
     <div class="highlight language-Objective-C">
      <div class="language-label">
       Objective-C
      </div>
      <div class="highlight">
       <pre><span></span><span class="cp">#import &lt;DrainageCompetence/DrainageCompetence.h&gt;</span>

<span class="p">-</span> <span class="p">(</span><span class="kt">void</span><span class="p">)</span><span class="nf">scene:</span><span class="p">(</span><span class="bp">UIScene</span><span class="w"> </span><span class="o">*</span><span class="p">)</span><span class="nv">scene</span><span class="w"> </span><span class="nf">willConnectToSession:</span><span class="p">(</span><span class="bp">UISceneSession</span><span class="w"> </span><span class="o">*</span><span class="p">)</span><span class="nv">session</span><span class="w"> </span><span class="nf">options:</span><span class="p">(</span><span class="bp">UISceneConnectionOptions</span><span class="w"> </span><span class="o">*</span><span class="p">)</span><span class="nv">connectionOptions</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// 如果应用程序使用的 UIScene，下面方法无需在AppDelegate里实现，只在这里实现</span>
<span class="w">    </span><span class="p">[</span><span class="n">BrokenDid</span><span class="w"> </span><span class="n">applierExtractAbsentGatheringBeginWaistOptions</span><span class="o">:</span><span class="nb">nil</span><span class="w"> </span><span class="n">pagerEndOptions</span><span class="o">:</span><span class="n">connectionOptions</span><span class="p">];</span>
<span class="p">}</span>
</pre>
      </div>
     </div>
    </div>
    <p>
     <strong>
      <em>
       <font color="red">
        注意：
       </font>
      </em>
     </strong>
     <strong>
      ！！需要注意的是，以上两种方式，根据接入工程时机情况，选择对应方案即可，且需要注意，两种方案透传给SDK的数据结构不同，AppDelegate方案需要传launchOptions，SceneDelegate方案需要传connectionOptions。
     </strong>
    </p>
    <h3 id="42-app">
     4.2 App通用链接回调（必要）
     <a class="headerlink" href="#42-app" title="Permanent link">
      ¶
     </a>
    </h3>
    <p>
     如使用AppDelegate：
    </p>
    <div class="highlight">
     <div class="highlight language-Objective-C">
      <div class="language-label">
       Objective-C
      </div>
      <div class="highlight">
       <pre><span></span><span class="cp">#import &lt;DrainageCompetence/DrainageCompetence.h&gt;</span>

<span class="p">-</span> <span class="p">(</span><span class="kt">BOOL</span><span class="p">)</span><span class="nf">application:</span><span class="p">(</span><span class="bp">UIApplication</span><span class="w"> </span><span class="o">*</span><span class="p">)</span><span class="nv">app</span><span class="w"> </span><span class="nf">openURL:</span><span class="p">(</span><span class="bp">NSURL</span><span class="w"> </span><span class="o">*</span><span class="p">)</span><span class="nv">url</span><span class="w"> </span><span class="nf">options:</span><span class="p">(</span><span class="bp">NSDictionary</span><span class="o">&lt;</span><span class="n">UIApplicationOpenURLOptionsKey</span><span class="p">,</span><span class="kt">id</span><span class="o">&gt;</span><span class="w"> </span><span class="o">*</span><span class="p">)</span><span class="nv">options</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="c1">// 如果您的应用程序使用UIScene ，请从UISceneDelegate实现以下方法，这里无需实现</span>
<span class="w">    </span><span class="p">[</span><span class="n">BrokenDid</span><span class="w"> </span><span class="n">postalCompressExecutionNowDictationIntro</span><span class="o">:</span><span class="n">url</span><span class="w"> </span><span class="n">lazyFont</span><span class="o">:</span><span class="n">options</span><span class="w"> </span><span class="n">cupGetPolish</span><span class="o">:</span><span class="nb">nil</span><span class="p">];</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="nb">YES</span><span class="p">;</span>
<span class="p">}</span>
</pre>
      </div>
     </div>
    </div>
    <p>
     如使用SceneDelegate：
    </p>
    <div class="highlight">
     <div class="highlight language-Objective-C">
      <div class="language-label">
       Objective-C
      </div>
      <div class="highlight">
       <pre><span></span><span class="cp">#import &lt;DrainageCompetence/DrainageCompetence.h&gt;</span>

<span class="p">-</span> <span class="p">(</span><span class="kt">void</span><span class="p">)</span><span class="nf">scene:</span><span class="p">(</span><span class="bp">UIScene</span><span class="w"> </span><span class="o">*</span><span class="p">)</span><span class="nv">scene</span><span class="w"> </span><span class="nf">openURLContexts:</span><span class="p">(</span><span class="bp">NSSet</span><span class="o">&lt;</span><span class="bp">UIOpenURLContext</span><span class="w"> </span><span class="o">*&gt;</span><span class="w"> </span><span class="o">*</span><span class="p">)</span><span class="nv">URLContexts</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="c1">// 如果应用程序使用的 UIScene，下面方法无需在AppDelegate里实现，只在这里实现</span>
<span class="w">    </span><span class="p">[</span><span class="n">BrokenDid</span><span class="w"> </span><span class="n">postalCompressExecutionNowDictationIntro</span><span class="o">:</span><span class="n">URLContexts</span><span class="p">.</span><span class="n">allObjects</span><span class="p">.</span><span class="n">firstObject</span><span class="p">.</span><span class="n">URL</span><span class="w"> </span><span class="n">lazyFont</span><span class="o">:</span><span class="nb">nil</span><span class="w"> </span><span class="n">cupGetPolish</span><span class="o">:</span><span class="n">URLContexts</span><span class="p">];</span>
<span class="p">}</span>
</pre>
      </div>
     </div>
    </div>
    <h3 id="43-sdk">
     4.3 设置SDK代理（必要）
     <a class="headerlink" href="#43-sdk" title="Permanent link">
      ¶
     </a>
    </h3>
    <blockquote>
     <p>
      <strong>
       设置代理用于接收登录、退出、支付、上报角色结果
      </strong>
     </p>
    </blockquote>
    <div class="highlight">
     <pre><span></span><code><span class="c1">// 1.引入</span>
<span class="cp">#import &lt;DrainageCompetence/DrainageCompetence.h&gt;</span>
<span class="c1">// 2.遵循协议</span>
<span class="k">@interface</span> <span class="nc">ViewController</span><span class="w"> </span><span class="p">()</span><span class="o">&lt;</span><span class="n">ConvertDelegate</span><span class="o">&gt;</span>
<span class="c1">// 3.设置代理</span>
<span class="p">[</span><span class="n">BrokenDid</span><span class="w"> </span><span class="n">successMatchDelegate</span><span class="o">:</span><span class="nb">self</span><span class="p">];</span>

<span class="c1">// 4.实现代理方法</span>
<span class="c1">// MARK: ConvertDelegate</span>
<span class="c1">///登录回调（必接）</span>
<span class="p">-</span> <span class="p">(</span><span class="kt">void</span><span class="p">)</span><span class="nf">settingOffSnowSayRetried:</span><span class="p">(</span><span class="bp">NSDictionary</span><span class="w"> </span><span class="o">*</span><span class="p">)</span><span class="nv">box</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="n">NSLog</span><span class="p">(</span><span class="s">@"登录成功 - %@"</span><span class="p">,</span><span class="w"> </span><span class="n">box</span><span class="p">);</span>
<span class="p">}</span>

<span class="c1">//退出回调（必接）</span>
<span class="c1">// 必接 - 对接方需在此回调中调用游戏切换账号接口,退到游戏【登录界面】</span>
<span class="c1">// SDK个人中心有切换账号功能，所以用户在个人中心操作切换账号需游戏一并退出</span>
<span class="c1">// 另外游戏内退出同时调用SDK的logout:函数也会走此回调，</span>
<span class="c1">// 所以建议游戏的退出接口直接调用SDK的logout:在此回调中一并退出，不然游戏会重复退出2次</span>
<span class="p">-</span> <span class="p">(</span><span class="kt">void</span><span class="p">)</span><span class="nf">minTabAskWeek</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="n">NSLog</span><span class="p">(</span><span class="s">@"退出成功"</span><span class="p">);</span>
<span class="p">}</span>

<span class="c1">//支付回调</span>
<span class="p">-</span> <span class="p">(</span><span class="kt">void</span><span class="p">)</span><span class="nf">organizeRetComposerAnyBend:</span><span class="p">(</span><span class="kt">BOOL</span><span class="p">)</span><span class="nv">isSuc</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="n">NSLog</span><span class="p">(</span><span class="s">@"支付%@"</span><span class="p">,</span><span class="w"> </span><span class="n">isSuc</span><span class="o">?</span><span class="s">@"成功"</span><span class="o">:</span><span class="s">@"失败"</span><span class="p">);</span>
<span class="p">}</span>

<span class="c1">//上报角色回调</span>
<span class="p">-</span> <span class="p">(</span><span class="kt">void</span><span class="p">)</span><span class="nf">tenEntitiesPitchInfinityRemainingSpacing:</span><span class="p">(</span><span class="kt">BOOL</span><span class="p">)</span><span class="nv">isSuc</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="n">NSLog</span><span class="p">(</span><span class="s">@"上报角色%@"</span><span class="p">,</span><span class="w"> </span><span class="n">isSuc</span><span class="o">?</span><span class="s">@"成功"</span><span class="o">:</span><span class="s">@"失败"</span><span class="p">);</span>
<span class="p">}</span>
</code></pre>
    </div>
    <h3 id="44">
     4.4 登录（必要）
     <a class="headerlink" href="#44" title="Permanent link">
      ¶
     </a>
    </h3>
    <ul>
     <li>
      在游戏登录界面加载完成后调用及自动登录.(也可通过登录按钮让用户点击完成登录操作)
     </li>
     <li>
      登录完成后会通过代理方法
      <strong>
       “settingOffSnowSayRetried: ”
      </strong>
      返回用户信息，类型为NSDictionary
     </li>
    </ul>
    <div class="highlight">
     <div class="highlight language-Objective-C">
      <div class="language-label">
       Objective-C
      </div>
      <div class="highlight">
       <pre><span></span><span class="cp">#import &lt;DrainageCompetence/DrainageCompetence.h&gt;</span>

<span class="cm">/**</span>
<span class="cm"> * 在游戏登录界面加载完成后调用及自动登录.(也可通过登录按钮让用户点击完成登录操作)</span>
<span class="cm"> * 登录完成后会通过代理方法：settingOffSnowSayRetried: 返回用户信息</span>
<span class="cm"> */</span>
<span class="c1">// 登录接口</span>
<span class="p">[</span><span class="n">BrokenDid</span><span class="w"> </span><span class="n">guideBaltic</span><span class="p">];</span>
</pre>
      </div>
     </div>
    </div>
    <ul>
     <li>
      settingOffSnowSayRetried:回调返回参数示例：
     </li>
    </ul>
    <div class="highlight">
     <div class="highlight language-json">
      <div class="language-label">
       json
      </div>
      <div class="highlight">
       <pre><span></span><span class="p">{</span>
<span class="w">    </span><span class="nt">"id"</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="s2">"62072919"</span><span class="p">,</span>
<span class="w">    </span><span class="nt">"name"</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="s2">"G25040962072919"</span><span class="p">,</span>
<span class="w">    </span><span class="nt">"time"</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="s2">"1744192861"</span><span class="p">,</span>
<span class="w">    </span><span class="nt">"token"</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="s2">"7fdf2ff6b4ba44f0284b2adada2652bb"</span><span class="p">,</span>
<span class="w">    </span><span class="nt">"type"</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="mi">0</span>
<span class="p">}</span>
</pre>
      </div>
     </div>
    </div>
    <h3 id="45">
     4.5 退出登录
     <a class="headerlink" href="#45" title="Permanent link">
      ¶
     </a>
    </h3>
    <div class="highlight">
     <div class="highlight language-Objective-C">
      <div class="language-label">
       Objective-C
      </div>
      <div class="highlight">
       <pre><span></span><span class="cp">#import &lt;DrainageCompetence/DrainageCompetence.h&gt;</span>

<span class="c1">// 退出接口</span>
<span class="p">[</span><span class="n">BrokenDid</span><span class="w"> </span><span class="n">badGramLeft</span><span class="p">];</span>
</pre>
      </div>
     </div>
    </div>
    <ul>
     <li>
      minTabAskWeek 方法回调退出
     </li>
    </ul>
    <h3 id="46">
     4.6 支付（必要）
     <a class="headerlink" href="#46" title="Permanent link">
      ¶
     </a>
    </h3>
    <blockquote>
     <p>
      订单模型,具体查看属性注释和文档说明
      <br/>
      这里注意：确保传进的参数都为
      <strong>
       NSString
      </strong>
      类型，不接受NSNumber和其他类型
      <br/>
      支付结果通过代理方法
      <strong>
       “xxpk_pFinished:”
      </strong>
      返回 YES为成功，NO为失败
     </p>
    </blockquote>
    <div class="highlight">
     <div class="highlight language-Objective-C">
      <div class="language-label">
       Objective-C
      </div>
      <div class="highlight">
       <pre><span></span><span class="cp">#import &lt;DrainageCompetence/DrainageCompetence.h&gt;</span>

<span class="bp">NSString</span><span class="w"> </span><span class="o">*</span><span class="n">cpOrderId</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="bp">NSUUID</span><span class="p">.</span><span class="n">UUID</span><span class="p">.</span><span class="n">UUIDString</span><span class="p">;</span><span class="w">        </span><span class="c1">// 游戏生成的订单号 (必传)</span>
<span class="bp">NSString</span><span class="w"> </span><span class="o">*</span><span class="n">productCode</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">@"com.xxgame.sdk.demo.test"</span><span class="p">;</span><span class="w"> </span><span class="c1">// 商品标识(苹果商品标识)(必传)</span>
<span class="bp">NSString</span><span class="w"> </span><span class="o">*</span><span class="n">amount</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">@"6"</span><span class="p">;</span><span class="w">                             </span><span class="c1">// 商品金额（单位：元） (必传)</span>
<span class="bp">NSString</span><span class="w"> </span><span class="o">*</span><span class="n">productName</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">@"6元套餐"</span><span class="p">;</span><span class="w">                   </span><span class="c1">// 商品名称、例：60元宝 (必传)</span>
<span class="bp">NSString</span><span class="w"> </span><span class="o">*</span><span class="n">serverId</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">@"20190927001"</span><span class="p">;</span><span class="w">                 </span><span class="c1">// 用户游戏角色所在的服务器id (必传)</span>
<span class="bp">NSString</span><span class="w"> </span><span class="o">*</span><span class="n">roleId</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">@"100001"</span><span class="p">;</span><span class="w">                        </span><span class="c1">// 用户游戏角色ID (选传)</span>
<span class="bp">NSString</span><span class="w"> </span><span class="o">*</span><span class="n">roleName</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">@"角色-XXGameSDK"</span><span class="p">;</span><span class="w">               </span><span class="c1">// 用户游戏角色名称 (选传)</span>
<span class="bp">NSString</span><span class="w"> </span><span class="o">*</span><span class="n">roleLevel</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">@"99"</span><span class="p">;</span><span class="w">                          </span><span class="c1">// 用户游戏角色等级 (选传)</span>
<span class="bp">NSString</span><span class="w"> </span><span class="o">*</span><span class="n">extraInfo</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">@"2019"</span><span class="p">;</span><span class="w">                        </span><span class="c1">// 订单额外信息，最终将回传给游戏服务器 (选传)</span>

<span class="cm">/**</span>
<span class="cm"> * 支付,传入订单模型</span>
<span class="cm"> * 异步回调支付结果</span>
<span class="cm"> */</span>
<span class="p">[</span><span class="n">BrokenDid</span><span class="w"> </span><span class="n">mustMandarinHerSpouseFont</span><span class="o">:</span><span class="n">cpOrderId</span>
<span class="w">         </span><span class="nl">rejectSmoothCode</span><span class="p">:</span><span class="n">productCode</span>
<span class="w">              </span><span class="nl">earMajorSex</span><span class="p">:</span><span class="n">amount</span>
<span class="w">         </span><span class="nl">invokeItsDidName</span><span class="p">:</span><span class="n">productName</span>
<span class="w">            </span><span class="nl">sumSayWetDrum</span><span class="p">:</span><span class="n">serverId</span>
<span class="w">           </span><span class="nl">ourDrawFixInfo</span><span class="p">:</span><span class="n">extraInfo</span>
<span class="w">              </span><span class="nl">indicesArea</span><span class="p">:</span><span class="n">roleId</span>
<span class="w">            </span><span class="nl">deepLooseName</span><span class="p">:</span><span class="n">roleName</span>
<span class="w">           </span><span class="nl">containerLevel</span><span class="p">:</span><span class="n">roleLevel</span><span class="p">];</span>
</pre>
      </div>
     </div>
    </div>
    <h3 id="47">
     4.7 上报角色信息（必要）
     <a class="headerlink" href="#47" title="Permanent link">
      ¶
     </a>
    </h3>
    <p>
     如服务端已对接此接口客户端无需对接
    </p>
    <blockquote>
     <p>
      调用时机(重要!!只在以下三个场景需调用):
      <br/>
      1、玩家在选择区服进入游戏时调用该接口
      <br/>
      2、创建角色时调用该接口
      <br/>
      3、角色升级或其他角色汇报信息发生变化时调用该接
     </p>
    </blockquote>
    <div class="highlight">
     <div class="highlight language-Objective-C">
      <div class="language-label">
       Objective-C
      </div>
      <div class="highlight">
       <pre><span></span><span class="cp">#import &lt;DrainageCompetence/DrainageCompetence.h&gt;</span>

<span class="cm">/**</span>
<span class="cm"> * 必要！！！（有 3 处需要调用此接口）</span>
<span class="cm"> * 必要！！！（有 3 处需要调用此接口）</span>
<span class="cm"> * 必要！！！（有 3 处需要调用此接口） </span>
<span class="cm"> * 注意：确保传进的参数都为NSString类型，不接受NSNumber类型，extend字段类型为字典</span>
<span class="cm"> * 调用时机(重要!!只在以下三个场景需调用):</span>
<span class="cm"> * 1. 玩家在选择区服进入游戏时调用该接口。</span>
<span class="cm"> * 2. 创建角色时调用该接口</span>
<span class="cm"> * 3. 角色升级或其他角色汇报信息发生变化时调用该接</span>
<span class="cm"> */</span>
<span class="c1">// 角色所在服务器id (必传)</span>
<span class="bp">NSString</span><span class="w"> </span><span class="o">*</span><span class="n">serverId</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">"9001"</span><span class="p">;</span>
<span class="c1">// 区服名称 (必传)</span>
<span class="bp">NSString</span><span class="w"> </span><span class="o">*</span><span class="n">serverName</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">@"XXGame"</span><span class="p">;</span>
<span class="c1">// 用户游戏角色ID (必传)</span>
<span class="bp">NSString</span><span class="w"> </span><span class="o">*</span><span class="n">roleId</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">"xx100921"</span><span class="p">;</span>
<span class="c1">// 角色名称 (必传)</span>
<span class="bp">NSString</span><span class="w"> </span><span class="o">*</span><span class="n">roleName</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">"阿斯蒂芬"</span><span class="p">;</span>
<span class="c1">// 角色等级 (必传)</span>
<span class="bp">NSString</span><span class="w"> </span><span class="o">*</span><span class="n">roleLevel</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">"109"</span><span class="p">;</span>
<span class="c1">// 角色扩展信息（选填）类型：字典</span>
<span class="bp">NSDictionary</span><span class="w"> </span><span class="o">*</span><span class="n">extend</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="l">@{</span>
<span class="w">    </span><span class="s">@"pet"</span><span class="o">:</span><span class="w"> </span><span class="s">"5"</span><span class="p">,</span><span class="w">          </span><span class="c1">//宠物等级（5）</span>
<span class="w">    </span><span class="s">@"horse"</span><span class="o">:</span><span class="w"> </span><span class="s">"1"</span><span class="p">,</span><span class="w">        </span><span class="c1">//坐骑等级（1）</span>
<span class="w">    </span><span class="s">@"power"</span><span class="o">:</span><span class="w"> </span><span class="s">"10092"</span><span class="p">,</span><span class="w">        </span><span class="c1">//战力（100）</span>
<span class="w">    </span><span class="s">@"promote"</span><span class="o">:</span><span class="w"> </span><span class="s">"2"</span><span class="p">,</span><span class="w">      </span><span class="c1">//转职（2转）</span>
<span class="w">    </span><span class="s">@"married"</span><span class="o">:</span><span class="w"> </span><span class="s">"0"</span><span class="p">,</span><span class="w">      </span><span class="c1">//'0': 未婚, '1': 已婚</span>
<span class="w">    </span><span class="s">@"liveness"</span><span class="o">:</span><span class="w"> </span><span class="s">"2000"</span><span class="p">),</span><span class="w">     </span><span class="c1">//活跃度 (2000)</span>
<span class="w">    </span><span class="s">@"hero_level"</span><span class="o">:</span><span class="w"> </span><span class="s">@"98"</span><span class="p">,</span><span class="w">               </span><span class="c1">//英雄等级(98级)</span>
<span class="w">    </span><span class="s">@"trumps"</span><span class="o">:</span><span class="w"> </span><span class="l">@[</span><span class="w"> </span><span class="c1">//已激活的法宝列表</span>
<span class="w">         </span><span class="s">@"fabao1"</span><span class="p">,</span><span class="w">   </span><span class="c1">//法宝1</span>
<span class="w">         </span><span class="s">@"fabao2"</span><span class="w">    </span><span class="c1">//法宝2</span>
<span class="w">    </span><span class="l">]</span><span class="p">,</span>
<span class="w">    </span><span class="s">@"wings"</span><span class="o">:</span><span class="w"> </span><span class="l">@[</span><span class="w">  </span><span class="c1">//已激活的翅膀列表</span>
<span class="w">         </span><span class="s">@"wing1"</span><span class="p">,</span><span class="w">    </span><span class="c1">//翅膀1</span>
<span class="w">         </span><span class="s">@"wing2"</span><span class="w">     </span><span class="c1">//翅膀2</span>
<span class="w">    </span><span class="l">]</span><span class="p">,</span>
<span class="w">    </span><span class="s">@"artifacts"</span><span class="o">:</span><span class="w"> </span><span class="l">@[</span><span class="w">  </span><span class="c1">//已激活的神器列表</span>
<span class="w">         </span><span class="s">@"artifact1"</span><span class="p">,</span><span class="w">    </span><span class="c1">//神器1</span>
<span class="w">         </span><span class="s">@"artifact2"</span><span class="p">,</span><span class="w">    </span><span class="c1">//神器2</span>
<span class="w">    </span><span class="l">]</span><span class="p">,</span>
<span class="w">    </span><span class="c1">//@"xxx":@"xxx"                     //其他自定义信息</span>
<span class="w">    </span><span class="c1">//@"xxx":@"xxx"                     //其他自定义信息</span>
<span class="w">    </span><span class="c1">//@"xxx":@"xxx"                     //其他自定义信息</span>
<span class="w">    </span><span class="c1">//...</span>
<span class="l">}</span><span class="p">;</span>

<span class="cm">/**</span>
<span class="cm"> 上报角色信息</span>
<span class="cm"> */</span>
<span class="p">[</span><span class="n">BrokenDid</span><span class="w"> </span><span class="n">winPasswordsCombiningTagalogRemoteInfo</span><span class="o">:</span><span class="n">serverId</span>
<span class="w">               </span><span class="nl">bitsContainName</span><span class="p">:</span><span class="n">serverName</span>
<span class="w">                   </span><span class="nl">indicesArea</span><span class="p">:</span><span class="n">roleId</span>
<span class="w">                 </span><span class="nl">deepLooseName</span><span class="p">:</span><span class="n">roleName</span>
<span class="w">                </span><span class="nl">containerLevel</span><span class="p">:</span><span class="n">roleLevel</span>
<span class="w">                   </span><span class="nl">pauseStrict</span><span class="p">:</span><span class="n">extend</span><span class="p">];</span>
</pre>
      </div>
     </div>
    </div>
    <h3 id="48-facebook">
     4.8 绑定Facebook（选接）
     <a class="headerlink" href="#48-facebook" title="Permanent link">
      ¶
     </a>
    </h3>
    <div class="highlight">
     <div class="highlight language-Objective-C">
      <div class="language-label">
       Objective-C
      </div>
      <div class="highlight">
       <pre><span></span><span class="cp">#import &lt;DrainageCompetence/DrainageCompetence.h&gt;</span>

<span class="p">[</span><span class="n">BrokenDid</span><span class="w"> </span><span class="n">beatLossWordAccountSymbols</span><span class="o">:^</span><span class="p">(</span><span class="bp">NSDictionary</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="n">_Nullable</span><span class="w"> </span><span class="n">userInfo</span><span class="p">,</span><span class="w"> </span><span class="bp">NSString</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="n">_Nonnull</span><span class="w"> </span><span class="n">errorMsg</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">userInfo</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// 绑定成功</span>
<span class="w">        </span><span class="n">NSLog</span><span class="p">(</span><span class="s">@"绑定 Facebook 成功！</span><span class="se">\n</span><span class="s">userInfo:%@"</span><span class="p">,</span><span class="n">userInfo</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span><span class="k">else</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// 绑定失败</span>
<span class="w">        </span><span class="n">NSLog</span><span class="p">(</span><span class="s">@"绑定 Facebook 失败! </span><span class="se">\n</span><span class="s">errorMsg:%@"</span><span class="p">,</span><span class="w"> </span><span class="n">errorMsg</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}];</span>
</pre>
      </div>
     </div>
    </div>
    <h3 id="49-vk">
     4.9 绑定VK（选接）
     <a class="headerlink" href="#49-vk" title="Permanent link">
      ¶
     </a>
    </h3>
    <div class="highlight">
     <div class="highlight language-Objective-C">
      <div class="language-label">
       Objective-C
      </div>
      <div class="highlight">
       <pre><span></span><span class="cp">#import &lt;DrainageCompetence/DrainageCompetence.h&gt;</span>

<span class="p">[</span><span class="n">BrokenDid</span><span class="w"> </span><span class="n">dirtyAlways</span><span class="o">:^</span><span class="p">(</span><span class="bp">NSDictionary</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="n">_Nullable</span><span class="w"> </span><span class="n">userInfo</span><span class="p">,</span><span class="w"> </span><span class="bp">NSString</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="n">_Nonnull</span><span class="w"> </span><span class="n">errorMsg</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">userInfo</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// 绑定成功</span>
<span class="w">        </span><span class="n">NSLog</span><span class="p">(</span><span class="s">@"绑定 VK 成功！userInfo:%@"</span><span class="p">,</span><span class="n">userInfo</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span><span class="k">else</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// 绑定失败</span>
<span class="w">        </span><span class="n">NSLog</span><span class="p">(</span><span class="s">@"绑定 VK 失败! errorMsg:%@"</span><span class="p">,</span><span class="w"> </span><span class="n">errorMsg</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}];</span>
</pre>
      </div>
     </div>
    </div>
    <h3 id="410">
     4.10 事件打点（选接）
     <a class="headerlink" href="#410" title="Permanent link">
      ¶
     </a>
    </h3>
    <blockquote>
     <p>
      根据运营提供第三方打点平台事件，选择接入对应第三方平台接口，平台：AppFlyer、Facebook、Firebase、Adjust
     </p>
     <p>
      例如：如果运营只提供了AppFlyer和Firebase的打点参数 则：
     </p>
     <p>
      [BrokenDid putAllWrappersTensionKilometerBypassed:@"eventName" params:params];
     </p>
     <p>
      [BrokenDid bringLengthReorderConsumedGenericsRepair:@"eventName" params:nil]; // params未要求提供可传nil
     </p>
    </blockquote>
    <ul>
     <li>
      <p>
       <strong>
        event
       </strong>
       ：为事件标识，类型：NSString
      </p>
     </li>
     <li>
      <p>
       <strong>
        params
       </strong>
       ：透传额外参数，类型：NSDictionary 例如：@{@"param_key":@"param_value"} 未要求提供可传nil
      </p>
     </li>
    </ul>
    <div class="highlight">
     <div class="highlight language-Objective-C">
      <div class="language-label">
       Objective-C
      </div>
      <div class="highlight">
       <pre><span></span><span class="cp">#import &lt;DrainageCompetence/DrainageCompetence.h&gt;</span>

<span class="bp">NSDictionary</span><span class="w"> </span><span class="o">*</span><span class="n">params</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="l">@{</span><span class="s">@"param_key"</span><span class="o">:</span><span class="s">@"param_value"</span><span class="l">}</span><span class="p">;</span>
<span class="c1">// AppFlyer</span>
<span class="p">[</span><span class="n">BrokenDid</span><span class="w"> </span><span class="n">putAllWrappersTensionKilometerBypassed</span><span class="o">:</span><span class="s">@"demo_eventName"</span><span class="w"> </span><span class="n">params</span><span class="o">:</span><span class="n">params</span><span class="p">];</span>
<span class="c1">// Facebook</span>
<span class="p">[</span><span class="n">BrokenDid</span><span class="w"> </span><span class="n">lengthConfirmBinLostBuffersMillibars</span><span class="o">:</span><span class="s">@"demo_eventName"</span><span class="w"> </span><span class="n">params</span><span class="o">:</span><span class="n">params</span><span class="p">];</span>
<span class="c1">// Firebase</span>
<span class="p">[</span><span class="n">BrokenDid</span><span class="w"> </span><span class="n">bringLengthReorderConsumedGenericsRepair</span><span class="o">:</span><span class="s">@"demo_eventName"</span><span class="w"> </span><span class="n">params</span><span class="o">:</span><span class="n">params</span><span class="p">];</span>
<span class="c1">// Adjust</span>
<span class="p">[</span><span class="n">BrokenDid</span><span class="w"> </span><span class="n">hallSmallerDownhillRejectNear</span><span class="o">:</span><span class="s">@"demo_eventName"</span><span class="w"> </span><span class="n">params</span><span class="o">:</span><span class="n">params</span><span class="p">];</span>
</pre>
      </div>
     </div>
    </div>
    <h3 id="411">
     4.11 激励广告（选接）
     <a class="headerlink" href="#411" title="Permanent link">
      ¶
     </a>
    </h3>
    <blockquote>
     <p>
      为了防止激励广告音频干扰应用的背景音频，AppLovin 建议您在展示广告之前停止应用的背景音频。 关闭广告后，您可以恢复应用的背景音频。
     </p>
     <p>
      @param
      <strong>
       customData
      </strong>
      自定义参数，可通过服务端接口获取
     </p>
    </blockquote>
    <div class="highlight">
     <div class="highlight language-Objective-C">
      <div class="language-label">
       Objective-C
      </div>
      <div class="highlight">
       <pre><span></span><span class="cp">#import &lt;DrainageCompetence/DrainageCompetence.h&gt;</span>

<span class="p">[</span><span class="n">BrokenDid</span><span class="w"> </span><span class="n">winAzimuthSinBondHeadTokenData</span><span class="o">:</span><span class="s">@"customData"</span><span class="w"> </span><span class="n">mainUses</span><span class="o">:^</span><span class="p">(</span><span class="kt">BOOL</span><span class="w"> </span><span class="n">result</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">result</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="c1">// 完成激励广告任务回调</span>
<span class="w">    </span><span class="p">}</span><span class="k">else</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="c1">// 未完成激励广告</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}];</span>
</pre>
      </div>
     </div>
    </div>
    <h3 id="412">
     4.12 上报日志（选接）
     <a class="headerlink" href="#412" title="Permanent link">
      ¶
     </a>
    </h3>
    <blockquote>
     <p>
      type:任意String类型
     </p>
     <p>
      content:任意String类型
     </p>
    </blockquote>
    <div class="highlight">
     <div class="highlight language-Objective-C">
      <div class="language-label">
       Objective-C
      </div>
      <div class="highlight">
       <pre><span></span><span class="cp">#import &lt;DrainageCompetence/DrainageCompetence.h&gt;</span>

<span class="bp">NSString</span><span class="w"> </span><span class="o">*</span><span class="n">type</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">@"login"</span><span class="p">;</span>
<span class="bp">NSString</span><span class="w"> </span><span class="o">*</span><span class="n">content</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">@"success"</span><span class="p">;</span>
<span class="p">[</span><span class="n">BrokenDid</span><span class="w"> </span><span class="n">removalDrainCookieMainOutType</span><span class="o">:</span><span class="n">type</span><span class="w"> </span><span class="n">linkageWrist</span><span class="o">:</span><span class="n">content</span><span class="p">];</span>
</pre>
      </div>
     </div>
    </div>
    <h3 id="412_1">
     4.12.其他配置
     <a class="headerlink" href="#412_1" title="Permanent link">
      ¶
     </a>
    </h3>
    <h4 id="4121">
     4.12.1 登录界面关闭接口（可选接口）
     <a class="headerlink" href="#4121" title="Permanent link">
      ¶
     </a>
    </h4>
    <blockquote>
     <p>
      隐藏SDK界面右上角关闭按钮默认为：YES 隐藏状态
     </p>
     <p>
      如果游戏登录界面没有登录按钮用来拉起SDK登录界面，不用调用或设置为YES，以免用户关闭后无法重新拉起登录界面
     </p>
    </blockquote>
    <div class="highlight">
     <div class="highlight language-Objective-C">
      <div class="language-label">
       Objective-C
      </div>
      <div class="highlight">
       <pre><span></span><span class="cp">#import &lt;DrainageCompetence/DrainageCompetence.h&gt;</span>

<span class="p">[</span><span class="n">OldestLink</span><span class="w"> </span><span class="n">uplinkUkrainianAirControlLooseBlink</span><span class="o">:</span><span class="nb">YES</span><span class="p">];</span>
</pre>
      </div>
     </div>
    </div>
   </div>
  </div>
 </body>
</html>
