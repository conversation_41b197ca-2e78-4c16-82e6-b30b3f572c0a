/*********************************
 *********************************
 ***当前文件为Demo界面设置，请忽略
 *********************************
 *********************************
 **/





























































































































































































































































































































#import "_.h"
#import <DrainageCompetence/DrainageCompetence.h>

@implementation ViewController (Demo)

- (UIInterfaceOrientationMask)supportedInterfaceOrientations {
    return UIInterfaceOrientationMaskPortrait;
}
- (BOOL)prefersHomeIndicatorAutoHidden {
    return NO;
}
- (UIRectEdge)preferredScreenEdgesDeferringSystemGestures {
    return UIRectEdgeAll;
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    self.demo_logs = [NSMutableArray new];
    [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(underageMatrixInsertingEncodeHexDigest) name:OldestLink.EighteenSessionsCollapsedBeenDifferentThreadedCascadePreviews object:nil];
    self.demo_sdk_version.text = [NSString stringWithFormat:@"SDK VERSION：%@",OldestLink.wetOuterSink];
    [self sentencesHisRetGradeLookSlovenian];
    [self binNeverJustReaderReportsEulerArmpit:self.view.subviews];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    [self stakePatchExecRoundSliceAnimation];
}

- (void)sentencesHisRetGradeLookSlovenian {
    UIStoryboard *everyNonceTargetComponentWire = [UIStoryboard storyboardWithName:@"LaunchScreen" bundle:nil];
    UIViewController *echoBeat = [everyNonceTargetComponentWire instantiateInitialViewController];
    UIView *sonCarView = echoBeat.view;
    sonCarView.frame = self.view.bounds;
    sonCarView.tag = 99;
    [self.view addSubview:sonCarView];
    self.demo_logo.hidden = YES;
}

- (void)stakePatchExecRoundSliceAnimation {
    UIView *sonCarView = [self.view viewWithTag:99];
    UIView *logoView = [sonCarView viewWithTag:100];
    CGPoint sindhiCenter = [self.view convertPoint:self.demo_logo.center
                                         fromView:self.demo_logo.superview];
    UIView *movingLogo = [logoView snapshotViewAfterScreenUpdates:YES];
    movingLogo.frame = [self.view convertRect:logoView.frame fromView:logoView.superview];
    [self.view addSubview:movingLogo];
    logoView.hidden = YES;
    self.demo_login_status.hidden = YES;
    // 用dispatch_after实现真正的延迟
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [UIView animateWithDuration:1.1
                              delay:0
                            options:UIViewAnimationOptionCurveEaseInOut
                         animations:^{
            sonCarView.alpha = 0;
            movingLogo.center = sindhiCenter;
            movingLogo.transform = CGAffineTransformMakeScale(self.demo_logo.bounds.size.width/movingLogo.bounds.size.width,
                                                             self.demo_logo.bounds.size.height/movingLogo.bounds.size.height);
        } completion:^(BOOL finished) {
            [UIView animateWithDuration:0.5 animations:^{
                self.demo_logo.hidden = NO;
                self.demo_login_status.hidden = NO;
            }];
            [sonCarView removeFromSuperview];
            [movingLogo removeFromSuperview];
            [self.demo_logo.layer addAnimation:[self ownSeeAnimation] forKey:nil];
            // 动画结束后依次显示按钮
            [self distortedScoreGradientWonOrangeBigGerman:self.view.subviews];
        }];
    });
}

- (CABasicAnimation *)ownSeeAnimation {
    CABasicAnimation *tied = [CABasicAnimation animationWithKeyPath:@"opacity"];
    tied.fromValue = @0;
    tied.toValue = @1;
    tied.duration = 0.3;
    tied.removedOnCompletion = NO;
    tied.fillMode = kCAFillModeForwards;
    return tied;
}

- (void)underageMatrixInsertingEncodeHexDigest {
    switch (OldestLink.outStoneMayStatus) {
        case VersionsEnteredLowercaseIdiomBrandPopover:
            self.demo_login_status.text = @"○ 未登录";
            self.demo_login_status.textColor = UIColor.grayColor;
            break;
        case ResizeContinuedStillJumpDenseDividingOwnership:
            self.demo_login_status.text = @"○ 准备登录";
            self.demo_login_status.textColor = UIColor.yellowColor;
            break;
        case DuplicateModernFireOutlineLawInsertedPhysical:
            self.demo_login_status.text = @"○ 登录中...";
            self.demo_login_status.textColor = UIColor.blueColor;
            break;
        case StrongPrefersBeforePlatformCiphersScheme:
            self.demo_login_status.text = @"● 已登录";
            self.demo_login_status.textColor = UIColor.greenColor;
            break;
    }
}

- (void)binNeverJustReaderReportsEulerArmpit:(NSArray *)subviews {
    for (UIView *subview in subviews) {
        [self binNeverJustReaderReportsEulerArmpit:subview.subviews];
        if ([subview isKindOfClass:[UIButton class]]) {
            subview.layer.cornerRadius = 5.0;
            subview.layer.shadowColor = [UIColor lightGrayColor].CGColor;
            subview.layer.shadowOffset = CGSizeMake(0, 3);
            subview.layer.shadowOpacity = 0.5;
            subview.layer.shadowRadius = 4.0;
            subview.clipsToBounds = NO;
            subview.alpha = 0;
        }
   }
}

- (void)distortedScoreGradientWonOrangeBigGerman:(NSArray *)subviews {
    static NSTimeInterval callStand = 0.08;
    static NSTimeInterval badClampWarn = 0.25;
    __block NSInteger btnIndex = 0;
    for (UIView *subview in subviews) {
        [self distortedScoreGradientWonOrangeBigGerman:subview.subviews];
        if ([subview isKindOfClass:[UIButton class]]) {
            subview.alpha = 0;
            [UIView animateWithDuration:badClampWarn
                                  delay:btnIndex * callStand
                                options:UIViewAnimationOptionCurveEaseInOut
                             animations:^{
                subview.alpha = 1;
            } completion:nil];
            btnIndex++;
        }
    }
}

- (void)demo_log:(NSString *)logMessage {
    NSLog(@"%@",logMessage);
    dispatch_async(dispatch_get_main_queue(), ^{
        [self.demo_logs addObject:logMessage];
        NSIndexPath *newIndexPath = [NSIndexPath indexPathForRow:self.demo_logs.count - 1 inSection:0];
        [self.demo_tableView insertRowsAtIndexPaths:@[newIndexPath] withRowAnimation:UITableViewRowAnimationAutomatic];
        [self.demo_tableView scrollToRowAtIndexPath:newIndexPath
                             atScrollPosition:UITableViewScrollPositionBottom
                                     animated:YES];
    });
}

#pragma mark - UITableViewDataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.demo_logs.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"LogCell" forIndexPath:indexPath];
    cell.textLabel.text = self.demo_logs[indexPath.row];
    cell.textLabel.numberOfLines = 0;
    return cell;
}
@end
