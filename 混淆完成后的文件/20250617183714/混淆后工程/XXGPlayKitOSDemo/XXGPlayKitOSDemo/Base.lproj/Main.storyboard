<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="23504" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="BYZ-38-t0r">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23506"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="tne-QT-ifu">
            <objects>
                <viewController id="BYZ-38-t0r" customClass="ViewController" sceneMemberID="viewController">
                    <view key="view" clipsSubviews="YES" contentMode="scaleToFill" id="8bC-Xf-vdC">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillProportionally" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="HJb-4M-srF">
                                <rect key="frame" x="36.666666666666657" y="79" width="320" height="40"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" verticalHuggingPriority="251" image="logo" translatesAutoresizingMaskIntoConstraints="NO" id="vb5-NI-K1t">
                                        <rect key="frame" x="0.0" y="0.0" width="40" height="40"/>
                                        <color key="tintColor" red="0.11764705882352941" green="0.62352941176470589" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="40" id="kaV-WD-9Bb"/>
                                        </constraints>
                                        <preferredSymbolConfiguration key="preferredSymbolConfiguration" scale="small"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                <integer key="value" value="6"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                    </imageView>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="1000" verticalHuggingPriority="251" horizontalCompressionResistancePriority="1000" text="闲闲SDK-OS" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="xvt-wJ-8ed">
                                        <rect key="frame" x="50.000000000000014" y="0.0" width="163.66666666666669" height="40"/>
                                        <fontDescription key="fontDescription" type="system" weight="medium" pointSize="28"/>
                                        <color key="textColor" red="0.11764705882352941" green="0.62352941176470589" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="1000" verticalHuggingPriority="251" horizontalCompressionResistancePriority="1000" text="○ 未登录" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="b6g-U2-8oy">
                                        <rect key="frame" x="223.66666666666666" y="0.0" width="96.333333333333343" height="40"/>
                                        <fontDescription key="fontDescription" type="system" weight="light" pointSize="12"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="RUs-O6-Vyc" userLabel="height = 40"/>
                                    <constraint firstAttribute="width" constant="320" id="Z3Y-sF-aaQ"/>
                                </constraints>
                            </stackView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="核心功能" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="4Sb-3K-k3R">
                                <rect key="frame" x="36.666666666666657" y="134" width="320" height="15"/>
                                <constraints>
                                    <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="320" id="Nkz-sM-xDP"/>
                                    <constraint firstAttribute="height" constant="15" id="yIg-b3-cl8"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" weight="ultraLight" pointSize="12"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillProportionally" alignment="center" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="HQG-B1-cAu" userLabel="btns1 Stack View">
                                <rect key="frame" x="36.666666666666657" y="151" width="320" height="44"/>
                                <subviews>
                                    <button autoresizesSubviews="NO" opaque="NO" clearsContextBeforeDrawing="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Hdl-DJ-T26">
                                        <rect key="frame" x="0.0" y="7" width="53.333333333333336" height="30"/>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <fontDescription key="fontDescription" type="system" weight="medium" pointSize="15"/>
                                        <color key="tintColor" systemColor="systemBackgroundColor"/>
                                        <inset key="imageEdgeInsets" shot="0.0" pack="0.0" swap="2.2250738585072014e-308" star="0.0"/>
                                        <state key="normal" title="登录">
                                            <attributedString key="attributedTitle">
                                                <fragment content="登录"/>
                                            </attributedString>
                                            <color key="titleColor" red="0.12941176470588234" green="0.12941176470588234" blue="0.12941176470588234" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        </state>
                                        <connections>
                                            <action selector="demo_login:" destination="BYZ-38-t0r" eventType="touchUpInside" id="hA4-3A-2wa"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="LoJ-Y0-HMu">
                                        <rect key="frame" x="63.333333333333329" y="7" width="53.333333333333329" height="30"/>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <fontDescription key="fontDescription" type="system" weight="medium" pointSize="15"/>
                                        <color key="tintColor" systemColor="systemBackgroundColor"/>
                                        <inset key="imageEdgeInsets" shot="0.0" pack="0.0" swap="2.2250738585072014e-308" star="0.0"/>
                                        <state key="normal" title="退出">
                                            <attributedString key="attributedTitle">
                                                <fragment content="退出"/>
                                            </attributedString>
                                            <color key="titleColor" red="0.12941176470588234" green="0.12941176470588234" blue="0.12941176470588234" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        </state>
                                        <connections>
                                            <action selector="demo_logout:" destination="BYZ-38-t0r" eventType="touchUpInside" id="8oN-qk-gFa"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="3yc-G7-fkA">
                                        <rect key="frame" x="126.66666666666667" y="7" width="53.333333333333329" height="30"/>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <fontDescription key="fontDescription" type="system" weight="medium" pointSize="15"/>
                                        <color key="tintColor" systemColor="systemBackgroundColor"/>
                                        <inset key="imageEdgeInsets" shot="0.0" pack="0.0" swap="2.2250738585072014e-308" star="0.0"/>
                                        <state key="normal" title="支付">
                                            <attributedString key="attributedTitle">
                                                <fragment content="支付"/>
                                            </attributedString>
                                            <color key="titleColor" red="0.12941176470588234" green="0.12941176470588234" blue="0.12941176470588234" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        </state>
                                        <connections>
                                            <action selector="demo_pay:" destination="BYZ-38-t0r" eventType="touchUpInside" id="FMc-ex-44i"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="BO4-m5-hOj">
                                        <rect key="frame" x="190" y="7" width="130" height="30"/>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <fontDescription key="fontDescription" type="system" weight="medium" pointSize="15"/>
                                        <color key="tintColor" systemColor="systemBackgroundColor"/>
                                        <inset key="imageEdgeInsets" shot="0.0" pack="0.0" swap="2.2250738585072014e-308" star="0.0"/>
                                        <state key="normal" title="上报角色">
                                            <attributedString key="attributedTitle">
                                                <fragment content="上报角色"/>
                                            </attributedString>
                                            <color key="titleColor" red="0.12941176470588234" green="0.12941176470588234" blue="0.12941176470588234" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        </state>
                                        <connections>
                                            <action selector="demo_uploadRoleInfo:" destination="BYZ-38-t0r" eventType="touchUpInside" id="Q96-Nw-8D5"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="width" constant="320" id="Ix5-ps-1Cl"/>
                                    <constraint firstAttribute="height" constant="44" id="ltQ-H5-gf9"/>
                                </constraints>
                            </stackView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="扩展功能" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="BGQ-Um-dgZ">
                                <rect key="frame" x="36.666666666666657" y="210" width="320" height="15"/>
                                <constraints>
                                    <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="320" id="83Y-Jx-GQD"/>
                                    <constraint firstAttribute="height" constant="15" id="DTv-0g-V6m"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" weight="ultraLight" pointSize="12"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillProportionally" alignment="center" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="kqL-kl-mwE" userLabel="btns2 Stack View">
                                <rect key="frame" x="36.666666666666657" y="227" width="320" height="44"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="249" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="RHf-ez-vLy">
                                        <rect key="frame" x="0.0" y="7" width="86.333333333333329" height="30"/>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <fontDescription key="fontDescription" type="system" weight="medium" pointSize="15"/>
                                        <color key="tintColor" systemColor="systemBackgroundColor"/>
                                        <inset key="imageEdgeInsets" shot="0.0" pack="0.0" swap="2.2250738585072014e-308" star="0.0"/>
                                        <state key="normal" title="个人中心">
                                            <color key="titleColor" red="0.12941176470588234" green="0.12941176470588234" blue="0.12941176470588234" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        </state>
                                        <connections>
                                            <action selector="demo_openUserCenter:" destination="BYZ-38-t0r" eventType="touchUpInside" id="wPi-E0-eN0"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="249" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="fWC-Sb-W7g">
                                        <rect key="frame" x="96.333333333333343" y="7" width="72" height="30"/>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <fontDescription key="fontDescription" type="system" weight="medium" pointSize="15"/>
                                        <color key="tintColor" systemColor="systemBackgroundColor"/>
                                        <inset key="imageEdgeInsets" shot="0.0" pack="0.0" swap="2.2250738585072014e-308" star="0.0"/>
                                        <state key="normal" title="绑定FB">
                                            <color key="titleColor" red="0.12941176470588234" green="0.12941176470588234" blue="0.12941176470588234" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        </state>
                                        <connections>
                                            <action selector="demo_bindFacebook:" destination="BYZ-38-t0r" eventType="touchUpInside" id="Sah-Nj-a4n"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="w9I-4L-iFd">
                                        <rect key="frame" x="178.33333333333334" y="7" width="75.333333333333343" height="30"/>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <fontDescription key="fontDescription" type="system" weight="medium" pointSize="15"/>
                                        <color key="tintColor" systemColor="systemBackgroundColor"/>
                                        <inset key="imageEdgeInsets" shot="0.0" pack="0.0" swap="2.2250738585072014e-308" star="0.0"/>
                                        <state key="normal" title="绑定VK">
                                            <color key="titleColor" red="0.12941176470588234" green="0.12941176470588234" blue="0.12941176470588234" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        </state>
                                        <connections>
                                            <action selector="demo_bindFVK:" destination="BYZ-38-t0r" eventType="touchUpInside" id="bXS-Zm-Q8q"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" id="mqq-mc-2XE">
                                        <rect key="frame" x="263.66666666666669" y="7" width="56.333333333333314" height="30"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <fontDescription key="fontDescription" type="system" weight="medium" pointSize="15"/>
                                        <color key="tintColor" systemColor="systemBackgroundColor"/>
                                        <inset key="imageEdgeInsets" shot="0.0" pack="0.0" swap="2.2250738585072014e-308" star="0.0"/>
                                        <state key="normal" title="打点">
                                            <color key="titleColor" red="0.12941176470588234" green="0.12941176470588234" blue="0.12941176470588234" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        </state>
                                        <connections>
                                            <action selector="demo_logEvent" destination="BYZ-38-t0r" eventType="touchUpInside" id="odz-qp-W1c"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="height" constant="44" id="Sxx-H2-4fU"/>
                                    <constraint firstAttribute="width" constant="320" id="Y3b-Uf-Qg8"/>
                                </constraints>
                            </stackView>
                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillProportionally" alignment="center" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="Z9u-4E-FSZ" userLabel="btns3 Stack View">
                                <rect key="frame" x="36.666666666666657" y="279" width="320" height="44"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="249" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="IjV-RD-12g">
                                        <rect key="frame" x="0.0" y="7" width="96" height="30"/>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <fontDescription key="fontDescription" type="system" weight="medium" pointSize="15"/>
                                        <color key="tintColor" systemColor="systemBackgroundColor"/>
                                        <inset key="imageEdgeInsets" shot="0.0" pack="0.0" swap="2.2250738585072014e-308" star="0.0"/>
                                        <state key="normal" title="激励广告">
                                            <color key="titleColor" red="0.12941176469999999" green="0.12941176469999999" blue="0.12941176469999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        </state>
                                        <connections>
                                            <action selector="demo_showRewaredAd:" destination="BYZ-38-t0r" eventType="touchUpInside" id="AcC-SG-cGT"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="249" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="3o4-Dm-g7u">
                                        <rect key="frame" x="106" y="7" width="96" height="30"/>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <fontDescription key="fontDescription" type="system" weight="medium" pointSize="15"/>
                                        <color key="tintColor" systemColor="systemBackgroundColor"/>
                                        <inset key="imageEdgeInsets" shot="0.0" pack="0.0" swap="2.2250738585072014e-308" star="0.0"/>
                                        <state key="normal" title="上报日志">
                                            <color key="titleColor" red="0.12941176469999999" green="0.12941176469999999" blue="0.12941176469999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        </state>
                                        <connections>
                                            <action selector="demo_reportlog:" destination="BYZ-38-t0r" eventType="touchUpInside" id="WpG-4k-rbT"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="249" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="1GH-CV-vdN" userLabel="内购修复">
                                        <rect key="frame" x="212" y="7" width="108" height="30"/>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <fontDescription key="fontDescription" type="system" weight="medium" pointSize="15"/>
                                        <color key="tintColor" systemColor="systemBackgroundColor"/>
                                        <inset key="imageEdgeInsets" shot="0.0" pack="0.0" swap="2.2250738585072014e-308" star="0.0"/>
                                        <state key="normal" title="内购修复">
                                            <color key="titleColor" red="0.12941176469999999" green="0.12941176469999999" blue="0.12941176469999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        </state>
                                        <connections>
                                            <action selector="demo_iapRepair:" destination="BYZ-38-t0r" eventType="touchUpInside" id="KpB-nX-63x"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="width" constant="320" id="9cK-P2-s1K"/>
                                    <constraint firstAttribute="height" constant="44" id="Suf-Bi-I7a"/>
                                </constraints>
                            </stackView>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="default" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="-1" estimatedSectionHeaderHeight="-1" sectionFooterHeight="-1" estimatedSectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="e6X-r1-3uq">
                                <rect key="frame" x="36.666666666666657" y="623.66666666666663" width="320" height="160"/>
                                <color key="backgroundColor" red="0.94509803921568625" green="0.94509803921568625" blue="0.94509803921568625" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="160" id="cIX-W2-drs"/>
                                    <constraint firstAttribute="width" constant="320" id="m8L-LW-YLd"/>
                                </constraints>
                                <color key="separatorColor" red="0.8784313725490196" green="0.8784313725490196" blue="0.8784313725490196" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <prototypes>
                                    <tableViewCell clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" reuseIdentifier="LogCell" textLabel="A2d-pH-PYk" style="IBUITableViewCellStyleDefault" id="Lkq-6p-Xvc">
                                        <rect key="frame" x="0.0" y="50" width="320" height="43.666667938232422"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="Lkq-6p-Xvc" id="wXn-w4-S6h">
                                            <rect key="frame" x="0.0" y="0.0" width="320" height="43.666667938232422"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <label opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="left" insetsLayoutMarginsFromSafeArea="NO" text="Title" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" id="A2d-pH-PYk">
                                                    <rect key="frame" x="16" y="0.0" width="288" height="43.666667938232422"/>
                                                    <autoresizingMask key="autoresizingMask"/>
                                                    <fontDescription key="fontDescription" type="system" weight="light" pointSize="12"/>
                                                    <nil key="textColor"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                            </subviews>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" red="0.94509803921568625" green="0.94509803921568625" blue="0.94509803921568625" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </tableViewCell>
                                </prototypes>
                                <connections>
                                    <outlet property="dataSource" destination="BYZ-38-t0r" id="ilh-lD-dgC"/>
                                    <outlet property="delegate" destination="BYZ-38-t0r" id="CBP-Ld-aUe"/>
                                </connections>
                            </tableView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="SDK VERSION：3.0" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="IGX-bP-eM4">
                                <rect key="frame" x="142.66666666666666" y="793.66666666666663" width="107.66666666666666" height="14.333333333333371"/>
                                <fontDescription key="fontDescription" type="system" weight="light" pointSize="12"/>
                                <color key="textColor" red="0.12941176469999999" green="0.12941176469999999" blue="0.12941176469999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="DEMOLOG" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="3SZ-Bd-aft" userLabel="DEMO日志">
                                <rect key="frame" x="36.666666666666657" y="598.66666666666663" width="320" height="15"/>
                                <constraints>
                                    <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="320" id="KIn-0E-45b"/>
                                    <constraint firstAttribute="height" constant="15" id="adL-6W-7tR"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" weight="ultraLight" pointSize="12"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="6Tk-OE-BBY"/>
                        <color key="backgroundColor" red="0.96078431372549022" green="0.96078431372549022" blue="0.96078431372549022" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstItem="6Tk-OE-BBY" firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="3SZ-Bd-aft" secondAttribute="trailing" symbolic="YES" id="0Re-3V-fQx"/>
                            <constraint firstItem="BGQ-Um-dgZ" firstAttribute="top" secondItem="HQG-B1-cAu" secondAttribute="bottom" constant="15" id="1VV-Qp-kSl"/>
                            <constraint firstItem="4Sb-3K-k3R" firstAttribute="centerX" secondItem="8bC-Xf-vdC" secondAttribute="centerX" id="1Zk-Is-ZvK"/>
                            <constraint firstItem="e6X-r1-3uq" firstAttribute="top" secondItem="3SZ-Bd-aft" secondAttribute="bottom" constant="10" id="1ye-dY-xdt"/>
                            <constraint firstItem="e6X-r1-3uq" firstAttribute="centerX" secondItem="8bC-Xf-vdC" secondAttribute="centerX" id="2DC-rp-hw9"/>
                            <constraint firstItem="e6X-r1-3uq" firstAttribute="bottom" secondItem="IGX-bP-eM4" secondAttribute="top" constant="-10" id="2fj-r3-iGd"/>
                            <constraint firstItem="Z9u-4E-FSZ" firstAttribute="centerX" secondItem="6Tk-OE-BBY" secondAttribute="centerX" id="3Up-AB-ckk"/>
                            <constraint firstItem="kqL-kl-mwE" firstAttribute="top" secondItem="BGQ-Um-dgZ" secondAttribute="bottom" constant="2" id="Eri-Ka-z7Q"/>
                            <constraint firstItem="6Tk-OE-BBY" firstAttribute="bottom" secondItem="IGX-bP-eM4" secondAttribute="bottom" constant="10" id="JsB-ad-UBh"/>
                            <constraint firstItem="HJb-4M-srF" firstAttribute="centerX" secondItem="8bC-Xf-vdC" secondAttribute="centerX" id="LBG-KP-k9o"/>
                            <constraint firstItem="HQG-B1-cAu" firstAttribute="top" secondItem="4Sb-3K-k3R" secondAttribute="bottom" constant="2" id="Lc8-mD-eOq"/>
                            <constraint firstItem="BGQ-Um-dgZ" firstAttribute="centerX" secondItem="8bC-Xf-vdC" secondAttribute="centerX" id="Ln3-cI-REm"/>
                            <constraint firstItem="kqL-kl-mwE" firstAttribute="centerX" secondItem="6Tk-OE-BBY" secondAttribute="centerX" id="PW0-Jn-Cty"/>
                            <constraint firstItem="3SZ-Bd-aft" firstAttribute="leading" secondItem="e6X-r1-3uq" secondAttribute="leading" id="cZZ-vl-pdX"/>
                            <constraint firstItem="HJb-4M-srF" firstAttribute="top" secondItem="6Tk-OE-BBY" secondAttribute="top" constant="20" id="hJv-yD-zIx"/>
                            <constraint firstItem="IGX-bP-eM4" firstAttribute="centerX" secondItem="8bC-Xf-vdC" secondAttribute="centerX" id="hU7-rT-RNr"/>
                            <constraint firstItem="HQG-B1-cAu" firstAttribute="centerX" secondItem="6Tk-OE-BBY" secondAttribute="centerX" id="jFH-Wg-Mpc"/>
                            <constraint firstItem="4Sb-3K-k3R" firstAttribute="top" secondItem="HJb-4M-srF" secondAttribute="bottom" constant="15" id="oWv-Lo-udu"/>
                            <constraint firstItem="Z9u-4E-FSZ" firstAttribute="top" secondItem="kqL-kl-mwE" secondAttribute="bottom" constant="8" symbolic="YES" id="pqO-no-Z4A"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="demo_login_status" destination="b6g-U2-8oy" id="pl5-Gy-JAo"/>
                        <outlet property="demo_logo" destination="vb5-NI-K1t" id="SlS-p7-jG6"/>
                        <outlet property="demo_sdk_version" destination="IGX-bP-eM4" id="5XJ-I0-8BG"/>
                        <outlet property="demo_tableView" destination="e6X-r1-3uq" id="QcY-8J-hsM"/>
                        <outlet property="logoStackView" destination="HJb-4M-srF" id="4xC-pW-wki"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="dkx-z0-nzr" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="480" y="-91"/>
        </scene>
    </scenes>
    <resources>
        <image name="logo" width="83.5" height="83.5"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
