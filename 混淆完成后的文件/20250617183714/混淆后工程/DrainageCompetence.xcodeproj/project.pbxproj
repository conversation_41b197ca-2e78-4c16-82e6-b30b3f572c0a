// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		004992F8C46044A6E0218E49 /* TenStartupModel.h in Headers */ = {isa = PBXBuildFile; fileRef = A0F68E19E078298D207D9DEA /* TenStartupModel.h */; };
		00A2B3DA7D509FE3F6170928 /* UIImage+GIF.m in Sources */ = {isa = PBXBuildFile; fileRef = 12BB866ADF7B159434867E4B /* UIImage+GIF.m */; };
		00C95D2D1B1DDEBBF744CAEB /* PickReason.m in Sources */ = {isa = PBXBuildFile; fileRef = AA003FEBDC1C982818B996D8 /* PickReason.m */; };
		010DE514579BD0515A1E7141 /* OptShowDryViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 58DAFC70F7CC443A9185E72C /* OptShowDryViewController.h */; };
		0194AFB3CFC6DE4595CC0400 /* TapForHasRenew.h in Headers */ = {isa = PBXBuildFile; fileRef = C36E019F800F08CE1B488A1B /* TapForHasRenew.h */; };
		01B2ABAFD7B0C4AF8637EC1C /* SDWebImageDownloaderResponseModifier.m in Sources */ = {isa = PBXBuildFile; fileRef = E39D3C020942D666B6351FE1 /* SDWebImageDownloaderResponseModifier.m */; };
		024F008A387BE8E59E99360E /* SDAnimatedImagePlayer.h in Headers */ = {isa = PBXBuildFile; fileRef = F357E3CB7A3362D73871A15E /* SDAnimatedImagePlayer.h */; };
		02A12221F802EE3E91B5A31B /* ClickTokenViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 88D1429EB67A73102B4CDEEC /* ClickTokenViewController.m */; };
		0545A1BE5E9EEC69813C04FB /* DarkerButPlusReportStrategy.m in Sources */ = {isa = PBXBuildFile; fileRef = 5C0870BFCF52BB2921ED157B /* DarkerButPlusReportStrategy.m */; };
		0553AF32D6EE9BA48B425853 /* MoreVisualView.m in Sources */ = {isa = PBXBuildFile; fileRef = C0D05E2E15455D4F0C4BBC3E /* MoreVisualView.m */; };
		056E206AB2CF344D27E736CE /* Masonry.h in Headers */ = {isa = PBXBuildFile; fileRef = 6962C188A317A22A2210DAF2 /* Masonry.h */; };
		056EA36B1E03CDB2F5562EEF /* SDImageCoderHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = EE5EDFE175C4517CA55F2904 /* SDImageCoderHelper.m */; };
		06B351EFCB02FC813907FDDE /* OwnCacheView.h in Headers */ = {isa = PBXBuildFile; fileRef = 68F99AB8880BE0D4B84970A9 /* OwnCacheView.h */; };
		08725C6258842C5E3AD505D5 /* MQTTSSLSecurityPolicyTransport.m in Sources */ = {isa = PBXBuildFile; fileRef = F5BB5BD4EEB0BF69DBAE7BB5 /* MQTTSSLSecurityPolicyTransport.m */; };
		08DB5AAC1E9FF0E9A463A5CF /* SDWebImageManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 461D5508A8C868DF16263E99 /* SDWebImageManager.h */; };
		0958F4AC58B7295BBC4A54F0 /* ShortHoverWide.h in Headers */ = {isa = PBXBuildFile; fileRef = AA177D0A45478CDA5E870059 /* ShortHoverWide.h */; };
		099B6F1A9D4853518BDF2F02 /* AgeHardSentinelFaxAgeDiacritic.h in Headers */ = {isa = PBXBuildFile; fileRef = 674F8BEC1F9724A3F9F53F8C /* AgeHardSentinelFaxAgeDiacritic.h */; };
		09CCF99EC59546ADE48E3EAF /* DarkerButPlusReportStrategy.h in Headers */ = {isa = PBXBuildFile; fileRef = D9DA5B376458049AD09186DA /* DarkerButPlusReportStrategy.h */; };
		0A7833B477D00DA691C72DAA /* MQTTSessionManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 4D14F18C9AB27109CE7F0920 /* MQTTSessionManager.m */; };
		0B1CEC1F7800F835A1846653 /* SubBarrierViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 561A55C2F2A1BF3F41349143 /* SubBarrierViewController.m */; };
		0B99476F4ED1FF77CFDF20B6 /* MQTTSSLSecurityPolicyDecoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 4C35DA85AA4B46ADB67714C9 /* MQTTSSLSecurityPolicyDecoder.m */; };
		0CA44CE636D333123E615697 /* SDDiskCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 707D8F31DAD892B1A16330AF /* SDDiskCache.h */; };
		0CD3DD885486906F76D4DA4A /* WayTipManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 4AE24056D587AABB1648A602 /* WayTipManager.h */; };
		0CD71E3ABC40F192936C1AEB /* SDCallbackQueue.m in Sources */ = {isa = PBXBuildFile; fileRef = 91A6B4D732FB66EE5AE2FD94 /* SDCallbackQueue.m */; };
		0D9377216C87DCFB89DAD456 /* InsetTowerConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = 36B7F1C4E6EA67ABDCB2C051 /* InsetTowerConfig.m */; };
		0DC61F93D52046DE02F32032 /* MQTTSessionManager.h in Headers */ = {isa = PBXBuildFile; fileRef = A19DCB32C93BB93869146621 /* MQTTSessionManager.h */; };
		0E37E95CB61BB40CD718632F /* MQTTProperties.h in Headers */ = {isa = PBXBuildFile; fileRef = 29E260077ABAD4F5965CEF7A /* MQTTProperties.h */; };
		0F03555E8BE60DAA625E7464 /* TabNumberRevisionMegahertzPasteViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 5EF79911837971D4166F0BB5 /* TabNumberRevisionMegahertzPasteViewController.m */; };
		0F3B5952477440229E9C941B /* SDAssociatedObject.m in Sources */ = {isa = PBXBuildFile; fileRef = 28C13AA26ADCAF466F6E9390 /* SDAssociatedObject.m */; };
		0F3EB36B380A509387326709 /* NSData+ImageContentType.h in Headers */ = {isa = PBXBuildFile; fileRef = 5EB8AFFE87F67BA2683C8A6E /* NSData+ImageContentType.h */; };
		0FF47C0DDF52328BB16B41CD /* TabNumberRevisionMegahertzPasteViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 301DBB4D473811DD79AAC0D1 /* TabNumberRevisionMegahertzPasteViewController.h */; };
		111888313EA32D75BB0D90C5 /* ForegroundReconnection.h in Headers */ = {isa = PBXBuildFile; fileRef = 8A96A847DCEA35250F3E0226 /* ForegroundReconnection.h */; };
		142C9919F33B16F16F811D21 /* AllocateHeavyViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 0A972F5AE62B93436B077CD2 /* AllocateHeavyViewController.h */; };
		14B20922F1E79D33F0522765 /* BetterEyeIncrementLoopsSummaryTool.h in Headers */ = {isa = PBXBuildFile; fileRef = FD5A648198380875F8C946CE /* BetterEyeIncrementLoopsSummaryTool.h */; };
		15F6E0A67C95E4F0982B08DA /* MQTTSSLSecurityPolicy.h in Headers */ = {isa = PBXBuildFile; fileRef = B61192AE53DF957EE7E5D4E7 /* MQTTSSLSecurityPolicy.h */; };
		16ADBD1D625E5B4AD94DB96C /* StorageNumberSmallestProjectLaw.m in Sources */ = {isa = PBXBuildFile; fileRef = 221B6924E8C79E1C3840FBD2 /* StorageNumberSmallestProjectLaw.m */; };
		16FB8B6E0AE1B7B79BA3A5B6 /* AnswerConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = E5D31FF1D3555DE38C2CAF70 /* AnswerConfig.h */; };
		175794F698A8F5A7CB120718 /* UIImage+Transform.m in Sources */ = {isa = PBXBuildFile; fileRef = 9F102264BDA22857C9253CA6 /* UIImage+Transform.m */; };
		17F7DAB92A1C6CB8F4C36A26 /* ArraySawSequencerReceiveCutPacket.m in Sources */ = {isa = PBXBuildFile; fileRef = EF3B8804964E3252F001DA75 /* ArraySawSequencerReceiveCutPacket.m */; };
		18248CD5A29C137486E5C696 /* SDMemoryCache.h in Headers */ = {isa = PBXBuildFile; fileRef = F85D7C276EEBB7432213A19D /* SDMemoryCache.h */; };
		19D25C663DE3BC38B9FCFE20 /* NSError+YoungestTwo.h in Headers */ = {isa = PBXBuildFile; fileRef = C462B961D67182D3EAECBE8F /* NSError+YoungestTwo.h */; };
		1AB863F62AC08D6BDFDBEAED /* MQTTSessionSynchron.h in Headers */ = {isa = PBXBuildFile; fileRef = 9403E740B0E24B8CA0E11F19 /* MQTTSessionSynchron.h */; };
		1AC0F8379BC58A679E84EE94 /* RealTryViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 71A835FC472D9461C6D15E07 /* RealTryViewController.m */; };
		1B218D7DFB9FA638DD821DFB /* MASViewConstraint.h in Headers */ = {isa = PBXBuildFile; fileRef = B061FF0400777705C4335609 /* MASViewConstraint.h */; };
		1B5B134ACE3BF77F3C8EFB30 /* MQTTCFSocketEncoder.h in Headers */ = {isa = PBXBuildFile; fileRef = FB6193C8671A84748D419B8D /* MQTTCFSocketEncoder.h */; };
		1B657304D7DBC9F2A54532BA /* UIView+WebCacheOperation.h in Headers */ = {isa = PBXBuildFile; fileRef = 619E970B0C355455C4769687 /* UIView+WebCacheOperation.h */; };
		1C2DCEC5998E2544054524BD /* UIViewController+DueViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 214E31A52D5FCEB4A06D72D6 /* UIViewController+DueViewController.m */; };
		1D4E669C85A1EB5628FF26F2 /* SDImageAWebPCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = D1A538F3EEA1BD72C5FDE2B6 /* SDImageAWebPCoder.h */; };
		1D576136C534FA7F0A045F13 /* SDWebImageCacheKeyFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = 9B971DA05B7315364EE79A30 /* SDWebImageCacheKeyFilter.m */; };
		1EB0517B87FF755283A0F26F /* UnwindInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = FF88A68EBD984FE98C96DFBC /* UnwindInfo.h */; };
		1F41C8D1D352BE190BA9A7BE /* NSArray+MASShorthandAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = 285CA7B4F2071C82F53BA420 /* NSArray+MASShorthandAdditions.h */; };
		1F7D575CB11DE963E1991F68 /* XXGProtocolLabel.h in Headers */ = {isa = PBXBuildFile; fileRef = D0F596A92578CF0DD8E19C46 /* XXGProtocolLabel.h */; };
		1F8AA79007210BF92B5C86C8 /* NSImage+Compatibility.h in Headers */ = {isa = PBXBuildFile; fileRef = CFC99165FA77CB55E4951B41 /* NSImage+Compatibility.h */; };
		1FE2B4B6EB667560A1A97C10 /* TerabytesMapManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 1C83FFA711772A628FE88312 /* TerabytesMapManager.h */; };
		2058C6AF3A82E71A34FE8311 /* SDWebImageDownloaderOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = B9CDF03919FFAA5626F16554 /* SDWebImageDownloaderOperation.m */; };
		20AAFEC59FA67C474554A4B3 /* DashSawMenSkinCell.h in Headers */ = {isa = PBXBuildFile; fileRef = 4537D4140214C7B34392810F /* DashSawMenSkinCell.h */; };
		20F10E5E6675608EA406DDCB /* ArbiterLocal.h in Headers */ = {isa = PBXBuildFile; fileRef = D37DBC6F6601E02448363C39 /* ArbiterLocal.h */; };
		2169F9AE990D6A39EB30749F /* InheritedViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 9CB1C1A76C4274A79EA46856 /* InheritedViewController.h */; };
		21CDCD8BEDE806DAC53D9FAF /* NSBezierPath+SDRoundedCorners.m in Sources */ = {isa = PBXBuildFile; fileRef = FBE1B7EA846AC338DF199043 /* NSBezierPath+SDRoundedCorners.m */; };
		23F5C7BE536A99DB9B6DD123 /* KilobitsMenGallonWorkoutsDueSpherical.h in Headers */ = {isa = PBXBuildFile; fileRef = D9072A9CE4C558909BC257B2 /* KilobitsMenGallonWorkoutsDueSpherical.h */; };
		23FA74E4DF619A23B41EBE81 /* SDAnimatedImage.m in Sources */ = {isa = PBXBuildFile; fileRef = 770EA2FCD5B8C7B604FAE7EA /* SDAnimatedImage.m */; };
		241C24F2F8D23DDCB9BABA13 /* SDWebImageDefine.m in Sources */ = {isa = PBXBuildFile; fileRef = 3494C66CB3AFF6B3B043E1FC /* SDWebImageDefine.m */; };
		257C6E0C9F0B9224F1673034 /* UseEditorInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 63047D7E585286AAD12E9B44 /* UseEditorInfo.m */; };
		259B24EE17B678F7F306E950 /* AnswerConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = DBD8DCAEBEDD5875983DF880 /* AnswerConfig.m */; };
		26718807568F09EF9978C68A /* DanceMidHisManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 5BD709CC82DEE6BBCCDB8AE1 /* DanceMidHisManager.h */; };
		26D14C0DF534C010FBCDEDE7 /* NSObject+MindStickySpatialHelloBox.m in Sources */ = {isa = PBXBuildFile; fileRef = 4E370C834396373B2EA33F90 /* NSObject+MindStickySpatialHelloBox.m */; };
		27198752C999BD4CE988505F /* ClickTokenViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 4F16B6C98C67CF40BFAB0CD6 /* ClickTokenViewController.h */; };
		277ABF6F652A8C36E4488737 /* SDWebImageOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = B3EBA07E9160BBCF2E37D426 /* SDWebImageOperation.m */; };
		27ECDFCEEF79B3BBE4E55200 /* UIImage+ForceDecode.h in Headers */ = {isa = PBXBuildFile; fileRef = 4C59431ED98C4E0FB36F1F2B /* UIImage+ForceDecode.h */; };
		28ED03D440BF7ED2EBBC7222 /* TurnGenericManager.h in Headers */ = {isa = PBXBuildFile; fileRef = E9DF1FDA5205150BBBF9EE94 /* TurnGenericManager.h */; };
		29DBAC86F330C8FE91AFA917 /* LessRaceProtocol.h in Headers */ = {isa = PBXBuildFile; fileRef = ED41E2FB31B281B8349E222E /* LessRaceProtocol.h */; };
		2A5D9C86CD7DD1AECAC0E0DB /* MASConstraint.h in Headers */ = {isa = PBXBuildFile; fileRef = C14558FFFCADF1D00588FE90 /* MASConstraint.h */; };
		2A7A0925E169377FADF2BF8C /* ModelHexModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 6C5169F9C162417452724FEA /* ModelHexModel.m */; };
		2A92A25637B6FB3935431E62 /* MQTTSSLSecurityPolicyEncoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 789ABCAAE7DBC3F2A5C8780D /* MQTTSSLSecurityPolicyEncoder.m */; };
		2B03D9BDC54B69CA19AD8787 /* SurgeBreakStay.m in Sources */ = {isa = PBXBuildFile; fileRef = C2578D8E772EE6FEFDDD005F /* SurgeBreakStay.m */; };
		2C2F59FE0B13B6E219F4EE18 /* SDAnimatedImage.h in Headers */ = {isa = PBXBuildFile; fileRef = 99442D6AB4024F590B88BD49 /* SDAnimatedImage.h */; };
		2D9FAC5006E9EDD7DD134C00 /* SDImageIOCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 428A2AC8E86D38E09AA9799A /* SDImageIOCoder.m */; };
		2E40D3843E73B6C038808CC2 /* SDDeviceHelper.h in Headers */ = {isa = PBXBuildFile; fileRef = C4B69F4CA964495D2C9F82BB /* SDDeviceHelper.h */; };
		2F169AFD22A63A983A64F1CE /* SDWebImageError.h in Headers */ = {isa = PBXBuildFile; fileRef = F5E0E3218F91B623C041274E /* SDWebImageError.h */; };
		2F20E58A20E7737FDA9F5125 /* NSLayoutConstraint+MASDebugAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 5A2A32E140658F6194CEFFDB /* NSLayoutConstraint+MASDebugAdditions.m */; };
		306330C90A447C68C8D70031 /* MASConstraintMaker.h in Headers */ = {isa = PBXBuildFile; fileRef = FD1B865DA0BE4D1CDE671EE0 /* MASConstraintMaker.h */; };
		30F2B1E45F5F652C8EA384F6 /* PenGramLossZipButton.h in Headers */ = {isa = PBXBuildFile; fileRef = E5E17E9C167143CE89EF377C /* PenGramLossZipButton.h */; };
		3137309C93AA6BFC14CBAA62 /* NSLayoutConstraint+MASDebugAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = FE4FC49FE5C2249593BF5397 /* NSLayoutConstraint+MASDebugAdditions.h */; };
		330FB4BDAED0C97E3D008209 /* MQTTSessionSynchron.m in Sources */ = {isa = PBXBuildFile; fileRef = 64D0438572649BF01494ECA6 /* MQTTSessionSynchron.m */; };
		335DE796147EBAF35CF6E2AA /* DiacriticVital.h in Headers */ = {isa = PBXBuildFile; fileRef = 13D2D8E4AF12E9A22AE4A50A /* DiacriticVital.h */; };
		338ED543808EDDC6F64CF209 /* SDWebImageDownloader.h in Headers */ = {isa = PBXBuildFile; fileRef = 6E5A95B489F4EDF6FC75A31A /* SDWebImageDownloader.h */; };
		346716248BA129E598129BB5 /* SDImageFrame.h in Headers */ = {isa = PBXBuildFile; fileRef = 7170C7C6D65FDDC9CAAAD5AB /* SDImageFrame.h */; };
		347C2E6BE120407F7E516BF4 /* TextRetOldEggViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 2BF27640E3B9EE27FC013493 /* TextRetOldEggViewController.m */; };
		3494358BEC07AC17CBED6304 /* NSObject+MindStickySpatialHelloBox.h in Headers */ = {isa = PBXBuildFile; fileRef = 025D4EA91DD0D6615AC9AC66 /* NSObject+MindStickySpatialHelloBox.h */; };
		3595EFFBAE0B96E8127CF122 /* UseEditorInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = 5492263F6C441C489C5AAB83 /* UseEditorInfo.h */; };
		36BCDDF653383C01B678C44A /* UIView+WebCacheState.m in Sources */ = {isa = PBXBuildFile; fileRef = B473112AE44CE908095B2087 /* UIView+WebCacheState.m */; };
		374F71824E57DC716C2FE452 /* SDImageCacheConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 50BF3455CE6E3F90858F857E /* SDImageCacheConfig.h */; };
		3AD309169261C55DF7735C38 /* Recovery.m in Sources */ = {isa = PBXBuildFile; fileRef = A8E25CF84D2DA0B13AEE7E78 /* Recovery.m */; };
		3AD31B5A4FDEA85632371472 /* RegisterColor.h in Headers */ = {isa = PBXBuildFile; fileRef = 60013077CCB32C161D404D18 /* RegisterColor.h */; };
		3B79C35550DDD01323A9BF6E /* MQTTSessionLegacy.m in Sources */ = {isa = PBXBuildFile; fileRef = A167DA7DF6402395CDBAA033 /* MQTTSessionLegacy.m */; };
		3BBF52D74B1D03B04EABE819 /* NSObject+PinModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 8A93DDB3A534F8FF14734D97 /* NSObject+PinModel.m */; };
		3C32F44AC2A9D4CE86AD3BF7 /* RealTryViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = AE03CE9BCBFE592F547AD01C /* RealTryViewController.h */; };
		3C5AC37EE64412FB6FA4ECC5 /* StoneDustViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 0ADB12C79A21A6C2F5694F6F /* StoneDustViewController.h */; };
		3C7A15BCDAAC548FC744DDF5 /* SDAssociatedObject.h in Headers */ = {isa = PBXBuildFile; fileRef = 4F1C65776340C2F0646BCE2D /* SDAssociatedObject.h */; };
		3CA441359D169956E197ED45 /* MQTTCFSocketTransport.m in Sources */ = {isa = PBXBuildFile; fileRef = F20593849D706C33221490B1 /* MQTTCFSocketTransport.m */; };
		3D00230108C61A0CE7CD3B87 /* FilterDigestPreviewsMomentLose.m in Sources */ = {isa = PBXBuildFile; fileRef = D83F01DFFB3186A3D2F34386 /* FilterDigestPreviewsMomentLose.m */; };
		3D446B0B9296637FC5A5EE41 /* AwayFullySpa.h in Headers */ = {isa = PBXBuildFile; fileRef = CEA0ADC3A77AC7590FFBECA2 /* AwayFullySpa.h */; };
		3D45789DAB5DB4D517089BD7 /* ButAlertView.m in Sources */ = {isa = PBXBuildFile; fileRef = 64BF842715FBF1E2431B77DC /* ButAlertView.m */; };
		3DC68219FF03D1143858E18F /* SDWeakProxy.h in Headers */ = {isa = PBXBuildFile; fileRef = 5F94E754532B117A09000DD3 /* SDWeakProxy.h */; };
		3E78C0D7DD8DD09ACC250E91 /* MQTTMessage.h in Headers */ = {isa = PBXBuildFile; fileRef = CC461ED6776ED50BBA883B5D /* MQTTMessage.h */; };
		3F8683A9908ABAE90B703188 /* MASViewAttribute.m in Sources */ = {isa = PBXBuildFile; fileRef = 888EFAE2562BCB3212DF42D0 /* MASViewAttribute.m */; };
		404B16462538FF1079FEEA88 /* UIButton+WebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 1ABBCE8B5A63BEB6F2A7EA58 /* UIButton+WebCache.m */; };
		40546FD8EA8DD0F9655385D8 /* NSString+ReversesAsk.h in Headers */ = {isa = PBXBuildFile; fileRef = 8B1298CB6CAA81B5C3773D19 /* NSString+ReversesAsk.h */; };
		4074F142F051C1D89C899D45 /* RightManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 6BC70D59E929673660CA2312 /* RightManager.m */; };
		4137B3057BEEFD9CCE9972B7 /* HoldBigBoxNapController.m in Sources */ = {isa = PBXBuildFile; fileRef = A304BB7F1BA5FE9E4DEA5799 /* HoldBigBoxNapController.m */; };
		415ABD1F2667FCA4C4BF15DD /* DanceMidHisManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 579D62EB17AD9CF4C5297F26 /* DanceMidHisManager.m */; };
		4198878735CCDEB687D3E5DB /* UIImage+YetImage.m in Sources */ = {isa = PBXBuildFile; fileRef = 4E40A90867F47FB556496429 /* UIImage+YetImage.m */; };
		41D84487FD7919A99F66E261 /* UIColor+SDHexString.h in Headers */ = {isa = PBXBuildFile; fileRef = 7A4DAE610336C2D92B286E13 /* UIColor+SDHexString.h */; };
		427D1F638F09434C22ADF85A /* SlowDetails.h in Headers */ = {isa = PBXBuildFile; fileRef = E1D03C1903AC3AB1A8B27626 /* SlowDetails.h */; };
		430ABDFCC4F26AA0350C7790 /* SDWebImageTransition.m in Sources */ = {isa = PBXBuildFile; fileRef = 635E90D7F86630F66D3F5992 /* SDWebImageTransition.m */; };
		4380BBC5C614AF7076C171F1 /* BrokenDid.h in Headers */ = {isa = PBXBuildFile; fileRef = 3F335B5A4FC2E0FC90F2F0F1 /* BrokenDid.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4505EC90A88A21335BB37C9C /* ReadyHeadViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 719CF62CFCE6532DE861AF5B /* ReadyHeadViewController.m */; };
		45ADED2116875FC725D5295F /* HyphenPhone.h in Headers */ = {isa = PBXBuildFile; fileRef = F5C2783E04C75E202B32D6AD /* HyphenPhone.h */; };
		45CB7677B6AD33BD9EA61AC5 /* SDAsyncBlockOperation.h in Headers */ = {isa = PBXBuildFile; fileRef = D2B51A3B6F499C51FB0831F0 /* SDAsyncBlockOperation.h */; };
		45DDFFB2B9C0C186A0D049FB /* UIImageView+HighlightedWebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = F1377CE9BCD5E119A62EBCBC /* UIImageView+HighlightedWebCache.m */; };
		4716EFB6045CA31FE1070DF6 /* DiacriticVital.m in Sources */ = {isa = PBXBuildFile; fileRef = 7A943BA18A5259AA0CCCBA05 /* DiacriticVital.m */; };
		47F622E2803F962F53F3E037 /* SchemesCapView.h in Headers */ = {isa = PBXBuildFile; fileRef = 563D6A4F6886020425E107C1 /* SchemesCapView.h */; };
		48DE3B6148A14CD4BC5A737F /* SDGraphicsImageRenderer.m in Sources */ = {isa = PBXBuildFile; fileRef = FA9D86B65EF76E6378A194D2 /* SDGraphicsImageRenderer.m */; };
		4923166191F9088023F9226A /* SDDisplayLink.m in Sources */ = {isa = PBXBuildFile; fileRef = 1AE178CA2C98B55CC2D4774A /* SDDisplayLink.m */; };
		4988A84B37A59B0F3F0A3CEF /* ChatMidProtocol.h in Headers */ = {isa = PBXBuildFile; fileRef = 629EB1FEE21834C1BB2583D4 /* ChatMidProtocol.h */; settings = {ATTRIBUTES = (Public, ); }; };
		4993CEE69EC746952931CADF /* TapDenseManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 1D08F2B44A5F2ABFD38D2EAB /* TapDenseManager.h */; };
		4AEB228E8D8B73427EA1B65B /* SDWebImageDownloaderConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 67D2F42472DB75D33504FE9A /* SDWebImageDownloaderConfig.h */; };
		4B2A0D1AD9F8E51BBEC73542 /* MASConstraintMaker.m in Sources */ = {isa = PBXBuildFile; fileRef = 5C90151EF14C456DA0C7FDFC /* MASConstraintMaker.m */; };
		4B904F9F5455C1C5DADBEB50 /* TapForHasRenew+PopRed.h in Headers */ = {isa = PBXBuildFile; fileRef = 8334EB457B033DBA7209659E /* TapForHasRenew+PopRed.h */; };
		4C0DF51797385C7BE63910BB /* SDWebImagePrefetcher.m in Sources */ = {isa = PBXBuildFile; fileRef = C4254BAF95A9B5C140E3E1AE /* SDWebImagePrefetcher.m */; };
		4C6011F16A6DAFC682103CCC /* MASLayoutConstraint.h in Headers */ = {isa = PBXBuildFile; fileRef = 4ED616A355CAEE027B60E185 /* MASLayoutConstraint.h */; };
		4D6FF90E7CBD29FA3B0ABE1B /* SDImageIOAnimatedCoderInternal.h in Headers */ = {isa = PBXBuildFile; fileRef = EE034196822ED15D8E97655D /* SDImageIOAnimatedCoderInternal.h */; };
		4E12B00CE833D393C2F6E5DE /* SexNetwork.m in Sources */ = {isa = PBXBuildFile; fileRef = D5E8309DB0D0912DDBB24D79 /* SexNetwork.m */; };
		4EDF56A392DA205731E6CE1B /* RegisterColor.m in Sources */ = {isa = PBXBuildFile; fileRef = 5F86C344EE6D82073E5987B8 /* RegisterColor.m */; };
		4FA51D85E021D11180D98E05 /* SDAsyncBlockOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = 1897AD37369E10C5F04F10D3 /* SDAsyncBlockOperation.m */; };
		5063F8B4D5F51C95B8445A3B /* UnwindInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = B1EB7EFC95DEB8B9B14C8EFC /* UnwindInfo.m */; };
		51133415D4D0D5E270401530 /* MQTTCoreDataPersistence.m in Sources */ = {isa = PBXBuildFile; fileRef = A47D2222AEC3C8C1D637985A /* MQTTCoreDataPersistence.m */; };
		518950E76A20566994CCA0D6 /* MQTTCoreDataPersistence.h in Headers */ = {isa = PBXBuildFile; fileRef = B5E9F2C4B7820624331E260A /* MQTTCoreDataPersistence.h */; };
		51E004BFB8117638A792FF37 /* SDImageIOAnimatedCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 1EC4B504B74161DCD91901D5 /* SDImageIOAnimatedCoder.h */; };
		529CBBF1835C6C52A0AB0AF6 /* ViewController+MASAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 3F96F9AB4AE4A0A8F1516551 /* ViewController+MASAdditions.m */; };
		537B99887E92975407DD9572 /* SDImageCachesManagerOperation.h in Headers */ = {isa = PBXBuildFile; fileRef = B9BA07E83ABC4553BBEBF4DB /* SDImageCachesManagerOperation.h */; };
		53EBF25E79E55BB646057BD7 /* LinearManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 330DC7E92F0EE4BF881CFD31 /* LinearManager.m */; };
		540A10DCB48AA5A4AA91A2A3 /* MQTTPersistence.h in Headers */ = {isa = PBXBuildFile; fileRef = 7E00617224339495E59B0951 /* MQTTPersistence.h */; };
		54397A075691CF20589B824C /* IdentifyToneAcrossRegistryOccur.m in Sources */ = {isa = PBXBuildFile; fileRef = F34D19F61C6B498859370CCD /* IdentifyToneAcrossRegistryOccur.m */; };
		5442CB6C9ABDF6B72EC1B810 /* SDAnimatedImageView.m in Sources */ = {isa = PBXBuildFile; fileRef = 141BB055207E906290B82B44 /* SDAnimatedImageView.m */; };
		54CED9B79CD271C3C7CA56BF /* SDImageCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 1615062F148B71D24026EAF8 /* SDImageCache.m */; };
		552D582FC0A85F8D0BE5C7F5 /* OptPaceSuchAction.h in Headers */ = {isa = PBXBuildFile; fileRef = 55F2DCDB1FD433FA366CC0C7 /* OptPaceSuchAction.h */; };
		554340FB46402C64BC115649 /* LogoSent.m in Sources */ = {isa = PBXBuildFile; fileRef = 50A435C06DC74595FDE21BFD /* LogoSent.m */; };
		566319D6F64DF42AC73B6461 /* HavePlusWhoManager.m in Sources */ = {isa = PBXBuildFile; fileRef = AF8A49318FDC6F175C8BA081 /* HavePlusWhoManager.m */; };
		56C72AE88C8C8671153FBE2D /* TheDarkArmViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = B7E9C6DCBE31069AF0FCEA75 /* TheDarkArmViewController.h */; };
		572B546C90C4B620ECE99D1E /* InheritedViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = BA89D2A090F394B409558A23 /* InheritedViewController.m */; };
		576EA78FFDDC9BFC29126B51 /* ManAllMildCaseCell.h in Headers */ = {isa = PBXBuildFile; fileRef = 12F1A6F272FA259940309903 /* ManAllMildCaseCell.h */; };
		57B0AFAF04E15CCF02BED583 /* HertzViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 457B3033135C517A729D3535 /* HertzViewController.m */; };
		58E06C41D673C5B8A1FDE442 /* MergeNameViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 7854180F821EDEB33E03A7E3 /* MergeNameViewController.h */; };
		59C6C653E9F35ADEAD00BB7D /* SDImageLoader.m in Sources */ = {isa = PBXBuildFile; fileRef = 384391EB0CD3C9AE3CFFFFF8 /* SDImageLoader.m */; };
		5A5D5FD26EF6866A6C09DA4D /* SDImageCachesManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 15C01B225EDACCED1BBAABEA /* SDImageCachesManager.h */; };
		5B4633C69BBEE214EB8379E7 /* Color.h in Headers */ = {isa = PBXBuildFile; fileRef = D39400BF47E742D3A1A25765 /* Color.h */; };
		5BE6D23C8E7D84EB9621D705 /* AwayFullySpa.m in Sources */ = {isa = PBXBuildFile; fileRef = D99B852B6FC88CD752CC27D9 /* AwayFullySpa.m */; };
		5C67CF5AB524F2AAA903F073 /* Color.m in Sources */ = {isa = PBXBuildFile; fileRef = F6859DB3D21E61EBB4A54248 /* Color.m */; };
		5D7AFF8C1331ACC9650C87BD /* ViewDayCloudInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = 10F8CE1926F0C25AB8F81165 /* ViewDayCloudInfo.h */; };
		5E1C8DE570C6C6ACEE73FD06 /* UIImage+ExtendedCacheData.m in Sources */ = {isa = PBXBuildFile; fileRef = A462FA611F18A8120FBA57F6 /* UIImage+ExtendedCacheData.m */; };
		5E469FD31C6B7A0621B6462B /* UIImage+YetImage.h in Headers */ = {isa = PBXBuildFile; fileRef = C3051CC331618DAB930D6A21 /* UIImage+YetImage.h */; };
		5E5EC9419627988BC677F964 /* SDImageIOCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = FAB61A4666E33303B97B1251 /* SDImageIOCoder.h */; };
		5F29BD6B62651075C399220B /* SDImageAssetManager.h in Headers */ = {isa = PBXBuildFile; fileRef = F8EED95EB1B6ABF06B4FD3B6 /* SDImageAssetManager.h */; };
		5FF24A54E7D290436399D6DA /* SDImageCacheDefine.h in Headers */ = {isa = PBXBuildFile; fileRef = D8298597DCE164D09A88C5EF /* SDImageCacheDefine.h */; };
		618FDE21D706075482C8BB19 /* SDWebImageCacheSerializer.h in Headers */ = {isa = PBXBuildFile; fileRef = F5BFCAD14F8CD43F9A74BC77 /* SDWebImageCacheSerializer.h */; };
		620D63201039A0F59A4BBFCA /* LinearManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 8DF20259B4832036D2C8B8B6 /* LinearManager.h */; };
		6328D31F37C58FC63976925B /* PinPartModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9B3B10493135E58622EADAC6 /* PinPartModel.m */; };
		634447714772958696BB2AAE /* LogoSent.h in Headers */ = {isa = PBXBuildFile; fileRef = 44EA56130C211DB8938398B5 /* LogoSent.h */; };
		635E7AE0C254D7C84B196806 /* TerabytesMapManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 5109F642D16ED1F7C56C5741 /* TerabytesMapManager.m */; };
		638235AD6D8A8E4293470022 /* SDImageAPNGCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 86CD3D05951C3A4E7D908BF9 /* SDImageAPNGCoder.h */; };
		63985111A732546BF8FEDE81 /* MQTTStrict.h in Headers */ = {isa = PBXBuildFile; fileRef = 11331CA51275FA728B67D9B5 /* MQTTStrict.h */; };
		64AE7FC24CBED5B5761D5E5D /* SDWebImageDownloaderResponseModifier.h in Headers */ = {isa = PBXBuildFile; fileRef = D58DE27181558E81D87B10A5 /* SDWebImageDownloaderResponseModifier.h */; };
		64B322F7CC12DF6AF0ECC90B /* HertzViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 12941105536073ED4DA9CF4C /* HertzViewController.h */; };
		65ABD2DCAD74EECA30263F1D /* UIView+WebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 3F50C3FDDDA683A6949E470B /* UIView+WebCache.m */; };
		666776D76919EA96E4D468D8 /* MQTTSSLSecurityPolicy.m in Sources */ = {isa = PBXBuildFile; fileRef = A1A6A01995B67A2FD246044C /* MQTTSSLSecurityPolicy.m */; };
		68385A102FF10764E6F31E17 /* MQTTTransportProtocol.h in Headers */ = {isa = PBXBuildFile; fileRef = 51595B2BCF7B15480E544D73 /* MQTTTransportProtocol.h */; };
		68A78BACCD1D2E905BC80F23 /* ItalianLog.h in Headers */ = {isa = PBXBuildFile; fileRef = 5B2D01B7123CC70085061273 /* ItalianLog.h */; };
		6ABC128C77714CA9A0A21180 /* CharShiftDark.m in Sources */ = {isa = PBXBuildFile; fileRef = B5A8DA6599FDD3354A6844C8 /* CharShiftDark.m */; };
		6B6D0177EC5A1CD45EE8B479 /* ArbiterLocal.m in Sources */ = {isa = PBXBuildFile; fileRef = 1179D1421A8477D0A4BD88B6 /* ArbiterLocal.m */; };
		6B8AC851D829A54F4B29A1B9 /* PenGramLossZipButton.m in Sources */ = {isa = PBXBuildFile; fileRef = 4D23922F4F59E3B4A4DF4D6C /* PenGramLossZipButton.m */; };
		6B971071CA0E66A173475F99 /* ItalianLog.m in Sources */ = {isa = PBXBuildFile; fileRef = 65457C6C6C6C1C847438ECE9 /* ItalianLog.m */; };
		6C5D0EBD9F22BD41F3798BFC /* SDFileAttributeHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = 5C84898857E25713DDC55B13 /* SDFileAttributeHelper.m */; };
		6C69CAADA794547ADB9CF85D /* ProjectsCapturedArtCommitRedTraveledViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = B19FEED26F1FF046AF323554 /* ProjectsCapturedArtCommitRedTraveledViewController.h */; };
		6C979E44214FD2D2168EF54F /* IllDayBodyRule.h in Headers */ = {isa = PBXBuildFile; fileRef = 36360B33FB1DE372DC1412F0 /* IllDayBodyRule.h */; };
		6CA26C88F422F813394AE4FE /* HyphenPhone.m in Sources */ = {isa = PBXBuildFile; fileRef = 6A6EE765D0CAE0631BC80AE4 /* HyphenPhone.m */; };
		6CACD27CA00FAA7690C9AFD0 /* InsetTowerConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = 02D6CB81DE39FF902CA72F77 /* InsetTowerConfig.h */; };
		6D83D69635E3C175A921DFE7 /* BezelItsManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 89D7EA6EB29A0EF7C22C936A /* BezelItsManager.m */; };
		6D975F2B45791F29D259A827 /* TenStartupModel.m in Sources */ = {isa = PBXBuildFile; fileRef = E34CAA9B03D2A0E183EEFCAD /* TenStartupModel.m */; };
		6DE79D7E062FB4F95831F800 /* NSString+ReversesAsk.m in Sources */ = {isa = PBXBuildFile; fileRef = D3B2EC10DE17B83C3B30B81C /* NSString+ReversesAsk.m */; };
		6DEEB88C4AD4F01E59E87F73 /* RetryAwakeSentencesBarsAll.m in Sources */ = {isa = PBXBuildFile; fileRef = A9A8C76EEBD22D9546EDF17C /* RetryAwakeSentencesBarsAll.m */; };
		6EE0434775314FB460D7A4AF /* NSArray+MASAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 015CA462C5ABE9B6F9E515A3 /* NSArray+MASAdditions.m */; };
		6F454CA909E992FC7BC27147 /* HailSheCanadianLooperBefore.h in Headers */ = {isa = PBXBuildFile; fileRef = D5686184C9322B36826099F2 /* HailSheCanadianLooperBefore.h */; };
		6FF6BABE0E7A8709AB4A68C7 /* UIImage+ExtendedCacheData.h in Headers */ = {isa = PBXBuildFile; fileRef = 35F81EEAD28CF085ED745CA9 /* UIImage+ExtendedCacheData.h */; };
		71E145D21C04466DCE08F3DC /* PolishFaxTextField.h in Headers */ = {isa = PBXBuildFile; fileRef = D787C0D5D22457B253F60AAD /* PolishFaxTextField.h */; };
		727F940EBBBEB915F3EF7374 /* TapForHasRenew.m in Sources */ = {isa = PBXBuildFile; fileRef = CC9E25F523DD9B4ECD0ECD42 /* TapForHasRenew.m */; };
		72FA66D465A938D1BCBE0EDE /* TapForHasRenew+PopRed.m in Sources */ = {isa = PBXBuildFile; fileRef = F498C1D1D26B88EB35C7161D /* TapForHasRenew+PopRed.m */; };
		7389E044544E949F3EF51676 /* DryTapParseViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 78CD62031381714D0BB7365B /* DryTapParseViewController.h */; };
		73E93012EF9D2844E4197D62 /* BagCheckoutNarrativeArmGain.m in Sources */ = {isa = PBXBuildFile; fileRef = B688CC3387810DE0D6154B2B /* BagCheckoutNarrativeArmGain.m */; };
		74236E8798FD752C07CAD312 /* ProjectsCapturedArtCommitRedTraveledViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = FDB00E0424953F1A2D040295 /* ProjectsCapturedArtCommitRedTraveledViewController.m */; };
		74918C378CE04F9052136E70 /* TextRetOldEggViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = B2FE53FBC85EBA73B8B18E55 /* TextRetOldEggViewController.h */; };
		75640C7FAACBB35C2B89BC79 /* NSButton+WebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 9578E6C06480B2755F8694A0 /* NSButton+WebCache.m */; };
		75B4431184EAEB54EABD93DE /* TapCardMenArtsViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0B8EB28663F6E7429D894D75 /* TapCardMenArtsViewController.m */; };
		75D4D7712D86D60A6F871BB4 /* PathPeakZipAllViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = D49BE0E05EBD2856C0520098 /* PathPeakZipAllViewController.h */; };
		75E490305D44A8ADDC1BEBF4 /* MQTTDecoder.m in Sources */ = {isa = PBXBuildFile; fileRef = B038B18BE450ABD3CAC5BDC3 /* MQTTDecoder.m */; };
		767CB79A118BDCFB3847E0D1 /* FilterDigestPreviewsMomentLose.h in Headers */ = {isa = PBXBuildFile; fileRef = 6190538599F552A6EBFFF634 /* FilterDigestPreviewsMomentLose.h */; };
		7713A06BC38749EDC04700B5 /* SDWebImageError.m in Sources */ = {isa = PBXBuildFile; fileRef = 6E83DCEDDBC6B3AA04820720 /* SDWebImageError.m */; };
		77F7105E2234B918EED1477E /* RegionExposureInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 8053B4F96E759F64786592B8 /* RegionExposureInfo.m */; };
		7933AEC5D926D1F3431BFCB2 /* ViewDayCloudInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 23E1AC2347E7F860D03624D7 /* ViewDayCloudInfo.m */; };
		79467E37DB8D58D5404A42F8 /* SDMemoryCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 08CDE16A6483EB1478F73093 /* SDMemoryCache.m */; };
		7982D8A5AC4089F421944102 /* SDInternalMacros.m in Sources */ = {isa = PBXBuildFile; fileRef = 477A16D57919C65EF81D07B8 /* SDInternalMacros.m */; };
		79A67F799BBD296323E2BDB8 /* LossWayDiskViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = C9176FE819F488CEC97A1E38 /* LossWayDiskViewController.h */; };
		7A1FA3F2769C9ED69A22EF4E /* ReconnectTimer.h in Headers */ = {isa = PBXBuildFile; fileRef = E94FB146C5FE700D7E2AC820 /* ReconnectTimer.h */; };
		7A5E57749931C1352047753C /* CharShiftDark.h in Headers */ = {isa = PBXBuildFile; fileRef = 9E6A3EE76DDAF3ACECF5251C /* CharShiftDark.h */; };
		7AD73911C67B893F84BF1235 /* FourMinFoundModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 9475DFABB47529406BB6A139 /* FourMinFoundModel.m */; };
		7AD78C5A55B5745CBC2CDBD4 /* UIColor+JobColor.h in Headers */ = {isa = PBXBuildFile; fileRef = B456BA7BDC7AA9AD448685A4 /* UIColor+JobColor.h */; };
		7B62B11507717C5BB655F5FF /* SDWebImageDownloaderRequestModifier.m in Sources */ = {isa = PBXBuildFile; fileRef = 2E25D0CE0EEA78F54AFE1663 /* SDWebImageDownloaderRequestModifier.m */; };
		7BB9CFEC8C52C2714CF2265D /* NSError+YoungestTwo.m in Sources */ = {isa = PBXBuildFile; fileRef = 1890CCEA0BC9A995E001FD0F /* NSError+YoungestTwo.m */; };
		7D4001CF1F211D0D2F562C0D /* HavePlusWhoManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 8F813465D747B0DBC1E95DFD /* HavePlusWhoManager.h */; };
		7EE276798E6A4CDA4A2DC657 /* ManAllMildCaseCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 6C5850D3CE95995E6A60248A /* ManAllMildCaseCell.m */; };
		80B4AE7363944132F517E6B5 /* UIColor+JobColor.m in Sources */ = {isa = PBXBuildFile; fileRef = BAA72DE67CE53E709627831A /* UIColor+JobColor.m */; };
		8104B283BE240A3937A3547A /* SDImageCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = BD4C585F9C5B2ED58A192339 /* SDImageCoder.m */; };
		81E5C33CC2CEEA05EBC89BFB /* SchemesCapView.m in Sources */ = {isa = PBXBuildFile; fileRef = E83AF3B99842C9553798BC43 /* SchemesCapView.m */; };
		829D34F86D4B42B06915DC2C /* SDImageCache.h in Headers */ = {isa = PBXBuildFile; fileRef = DC0AE235B834CECABDE27EA0 /* SDImageCache.h */; };
		82F17F0516E677C57D14D174 /* TurnGenericManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 1E79979138BA816F1FE511EB /* TurnGenericManager.m */; };
		837C4DA0EB7B476C5F3B6E25 /* SDWebImagePrefetcher.h in Headers */ = {isa = PBXBuildFile; fileRef = B5584A8C09A481F7CD8B2F4A /* SDWebImagePrefetcher.h */; };
		8407C2475C202F42BCB3287D /* NSBezierPath+SDRoundedCorners.h in Headers */ = {isa = PBXBuildFile; fileRef = 6076A78D7212EC65375F807B /* NSBezierPath+SDRoundedCorners.h */; };
		8566105C5F1567D67DC4B04D /* AgeHardSentinelFaxAgeDiacritic.m in Sources */ = {isa = PBXBuildFile; fileRef = F735F520BA59A88D03C8B4A9 /* AgeHardSentinelFaxAgeDiacritic.m */; };
		86064C21BDAE40FCC6CB0B0D /* SDWebImageDownloaderOperation.h in Headers */ = {isa = PBXBuildFile; fileRef = 2CE539E3628D113325CAB863 /* SDWebImageDownloaderOperation.h */; };
		86C9089E2019938573ABC75B /* OwnCacheView.m in Sources */ = {isa = PBXBuildFile; fileRef = 4C22D2534E5464F3134F5372 /* OwnCacheView.m */; };
		86D7D2ADD41CDB12D2D74BB9 /* ShortHoverWide.m in Sources */ = {isa = PBXBuildFile; fileRef = 99AF8ADF4F9ECD5C97754594 /* ShortHoverWide.m */; };
		872073B2CD7A7D03BC10379C /* SDImageCodersManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 2AC5A65F6A32216AA89BC44C /* SDImageCodersManager.m */; };
		881883C5F8F14CE5FEB08B7B /* SDDiskCache.m in Sources */ = {isa = PBXBuildFile; fileRef = BE33DA6A7BE4A3F40A9BD931 /* SDDiskCache.m */; };
		888A41C11F8C0C4C5FC1C88D /* JobJapanese.h in Headers */ = {isa = PBXBuildFile; fileRef = F0C8D04590AA1923EA29B34B /* JobJapanese.h */; };
		889AD1E178A36B804A56A4BA /* BetterEyeIncrementLoopsSummaryTool.m in Sources */ = {isa = PBXBuildFile; fileRef = E8DF6F211689C6D51E6A5D48 /* BetterEyeIncrementLoopsSummaryTool.m */; };
		894298337E0CAABDDAECAEFC /* KilobitsMenGallonWorkoutsDueSpherical.m in Sources */ = {isa = PBXBuildFile; fileRef = C711260E44B4354ABC36FF4D /* KilobitsMenGallonWorkoutsDueSpherical.m */; };
		89F493491F87E70C45B187DD /* SDWebImageOperation.h in Headers */ = {isa = PBXBuildFile; fileRef = C3A461F5584FF92273C0679D /* SDWebImageOperation.h */; };
		8A44965CD9136F081D4C1322 /* UIButton+WebCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 46D6DC94C1C447E77D6FBBBD /* UIButton+WebCache.h */; };
		8B98FA117149B04991252D68 /* SDAnimatedImagePlayer.m in Sources */ = {isa = PBXBuildFile; fileRef = 3C959DF3D6D670D1F83CA529 /* SDAnimatedImagePlayer.m */; };
		8CF5FD759D632A771264692A /* GCDTimer.m in Sources */ = {isa = PBXBuildFile; fileRef = 27155D8DAAE120F9F17B664D /* GCDTimer.m */; };
		8D3F30ABF59AFFD0C693C781 /* View+MASAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = EDF85B71A42365F87EFD5133 /* View+MASAdditions.m */; };
		8DBE02F97A0EE6F5B3C021B7 /* NSString+DirtyFoot.m in Sources */ = {isa = PBXBuildFile; fileRef = D393779C17E505BA5599C6F9 /* NSString+DirtyFoot.m */; };
		8E72E248754B01C48AF987D4 /* NSArray+MASAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = 274FB6EA10E2ED244D1E91FF /* NSArray+MASAdditions.h */; };
		8F574C1145436CF6D32C76F3 /* BurmeseZone.h in Headers */ = {isa = PBXBuildFile; fileRef = 3F50383FA0D0A86FF62AADA1 /* BurmeseZone.h */; };
		8FF86EE68EC1667CB4CC5A6D /* BrokenDid.m in Sources */ = {isa = PBXBuildFile; fileRef = 336D9CEB7F9D047368C4225E /* BrokenDid.m */; };
		90AA7B47DEC47AD341688690 /* UIImage+Metadata.h in Headers */ = {isa = PBXBuildFile; fileRef = 392513E943BF5672CCB28573 /* UIImage+Metadata.h */; };
		90E1F427689683ECD4212B26 /* SDAnimatedImageView+WebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 36D71A4DD7F82151C06FDA0D /* SDAnimatedImageView+WebCache.m */; };
		92290CAA01D8BE2DDDC08501 /* SDImageTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = 81049B7C4C2ABF1CB69E8D02 /* SDImageTransformer.m */; };
		926CF881B8BBD465BA07B157 /* OldestLink.h in Headers */ = {isa = PBXBuildFile; fileRef = 016A69C980537C998C19F6BC /* OldestLink.h */; settings = {ATTRIBUTES = (Public, ); }; };
		92D6F803F2B4FE1A8A9A4528 /* SDAnimatedImageRep.h in Headers */ = {isa = PBXBuildFile; fileRef = B10C9694BD7A5079EACF0AC9 /* SDAnimatedImageRep.h */; };
		93FFFE3355B2607F7F9A96F2 /* HoldBigBoxNapController.h in Headers */ = {isa = PBXBuildFile; fileRef = 71A77F9FE801D14D215AA88C /* HoldBigBoxNapController.h */; };
		94E525040F1B2F98F23870DB /* TheDarkArmViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 134F2515410B392ADD6F342A /* TheDarkArmViewController.m */; };
		950554C5861BAED404106E74 /* UIImageView+HighlightedWebCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 4D88D026ED54FFBBED7B8467 /* UIImageView+HighlightedWebCache.h */; };
		954252D291BC4483636C390D /* SDWebImageTransitionInternal.h in Headers */ = {isa = PBXBuildFile; fileRef = 8366574A0BA13B5C47A3750A /* SDWebImageTransitionInternal.h */; };
		95EB6FF1DCC5B3184F166955 /* UniformFlash.m in Sources */ = {isa = PBXBuildFile; fileRef = 406878E935BFE0F86423F4D7 /* UniformFlash.m */; };
		95F874678802A3BD1924E8F0 /* SDmetamacros.h in Headers */ = {isa = PBXBuildFile; fileRef = 4BA8066D6988B29D03C357D2 /* SDmetamacros.h */; };
		9754DDC8D8A653C6EE2B388B /* TapForHasRenew+Total.m in Sources */ = {isa = PBXBuildFile; fileRef = FCE1505512FC47584A94FDDB /* TapForHasRenew+Total.m */; };
		975AD35DE09DBBDF0E553D8A /* TorchGainManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 0B3D87BBC7FB574B52AFF2EB /* TorchGainManager.h */; };
		980804CEFFB36C10D1DA5FE5 /* ModelHexModel.h in Headers */ = {isa = PBXBuildFile; fileRef = F5870C4C21489E4B3D8F7046 /* ModelHexModel.h */; };
		9840A321EEC795321684DB2B /* PasswordsViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = BD11F818CE698B2FDD21FA07 /* PasswordsViewController.m */; };
		986B3175216273371B37FCF3 /* LinkOnlyHowNearbyAvailable.m in Sources */ = {isa = PBXBuildFile; fileRef = 1E4CF7D3603D59381F5FFBC3 /* LinkOnlyHowNearbyAvailable.m */; };
		98A51EBD3DB1A3A13CE9B0CF /* SexNetwork.h in Headers */ = {isa = PBXBuildFile; fileRef = 08B44FE7C02B2D22A211BE69 /* SexNetwork.h */; };
		994817080CFD95CF32B97626 /* UIView+WebCacheState.h in Headers */ = {isa = PBXBuildFile; fileRef = 395C6211A3E356211127D17F /* UIView+WebCacheState.h */; };
		995B8D02B7E966E962FD07F8 /* SDWebImageDownloaderDecryptor.m in Sources */ = {isa = PBXBuildFile; fileRef = 01743D9FFCB02DE46080FF8C /* SDWebImageDownloaderDecryptor.m */; };
		99DDF12720BBF6BD6654EA2F /* SDImageLoader.h in Headers */ = {isa = PBXBuildFile; fileRef = 600A27E77E18A4C3D9C7108B /* SDImageLoader.h */; };
		9A58C3738780773F439EBD03 /* SDImageGraphics.m in Sources */ = {isa = PBXBuildFile; fileRef = B84B651663E8083C40F98554 /* SDImageGraphics.m */; };
		9A92834EBC96F713A1199148 /* SDWebImageDefine.h in Headers */ = {isa = PBXBuildFile; fileRef = B564459FAB6D12CE588DE24C /* SDWebImageDefine.h */; };
		9B337A569F98CCB3181FA99A /* ForegroundReconnection.m in Sources */ = {isa = PBXBuildFile; fileRef = 990BF4DCD6BDD350BA4C2451 /* ForegroundReconnection.m */; };
		9B96F742266716846234AD2C /* AbsoluteTriggerMonitoredSensitiveLocalizes.h in Headers */ = {isa = PBXBuildFile; fileRef = A98455467B2D0AACBB37F385 /* AbsoluteTriggerMonitoredSensitiveLocalizes.h */; };
		9CB4DD285F363EAE1ADBFA4F /* NSString+DirtyFoot.h in Headers */ = {isa = PBXBuildFile; fileRef = 22A8FADB8B39C9A32423869D /* NSString+DirtyFoot.h */; };
		9CC9B9AAF0DD3E8A2E76F1A0 /* SDAnimatedImageRep.m in Sources */ = {isa = PBXBuildFile; fileRef = 32D9B230616BC896AE5DE56B /* SDAnimatedImageRep.m */; };
		9CF1A902D3F19D831E459391 /* TapForHasRenew+Total.h in Headers */ = {isa = PBXBuildFile; fileRef = 7BA3A0832EC4E4ECD75A830B /* TapForHasRenew+Total.h */; };
		9D4A870133660B32934130EE /* MQTTLog.m in Sources */ = {isa = PBXBuildFile; fileRef = A8E58EED7D7BBF755A54F002 /* MQTTLog.m */; };
		9D681B2A9669E65B0869CB44 /* ReadyHeadViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 147BD20BFECC94D41D7F0256 /* ReadyHeadViewController.h */; };
		9D69BB5C02442F873B538D3C /* SDImageCachesManagerOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = 31A66A530DD3F35FBEFF3C27 /* SDImageCachesManagerOperation.m */; };
		9DE797B86BCA833C8B947951 /* FadeEarManager.m in Sources */ = {isa = PBXBuildFile; fileRef = D49167CE0580E7701F6B4BD8 /* FadeEarManager.m */; };
		9E60F35E7A508DD12B707E69 /* SurgeBreakStay.h in Headers */ = {isa = PBXBuildFile; fileRef = CFE42970522065E9097F27CA /* SurgeBreakStay.h */; };
		9F0AC3B3C033C6F88D13966E /* IdentifyToneAcrossRegistryOccur.h in Headers */ = {isa = PBXBuildFile; fileRef = B1225B741C9B0BA9A48CF30E /* IdentifyToneAcrossRegistryOccur.h */; };
		A0150F0943740AC8989B481F /* SDImageCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 59699892DA8AF57D64019350 /* SDImageCoder.h */; };
		A13222EF12B4EA9B5F963F08 /* MQTTSession.m in Sources */ = {isa = PBXBuildFile; fileRef = 53E9FE8BCAF14467A0C54C2D /* MQTTSession.m */; };
		A148D6D6A69581A00C98BAC4 /* DashSawMenSkinCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 80B865C7B7DD3F19B3F3566F /* DashSawMenSkinCell.m */; };
		A22C69D5C4951395A71158AE /* SDImageAPNGCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = CC952C75D256C0C783C3CF4F /* SDImageAPNGCoder.m */; };
		A28D514B228468C808827E72 /* SDImageTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = 7864B500CE178DBDC117F26D /* SDImageTransformer.h */; };
		A2A791A3C5A4130CC2647796 /* NSImage+Compatibility.m in Sources */ = {isa = PBXBuildFile; fileRef = 4C16AFF3F87357A800EE5AE8 /* NSImage+Compatibility.m */; };
		A2E5BD3C97B340B737CC15C4 /* LinkOnlyHowNearbyAvailable.h in Headers */ = {isa = PBXBuildFile; fileRef = 5E39C852BCF30328111B029D /* LinkOnlyHowNearbyAvailable.h */; };
		A3156E1646E33F8308E3BF90 /* TorchGainManager.m in Sources */ = {isa = PBXBuildFile; fileRef = AACEE6ADCE2BDE4CF78249C0 /* TorchGainManager.m */; };
		A3EE8DE4FD313F306957A169 /* MoreVisualView.h in Headers */ = {isa = PBXBuildFile; fileRef = 8A3AFFA97A8D35385FF2099F /* MoreVisualView.h */; };
		A462E3AEAC055D279766E314 /* EncodeCupReplyLoadAdverbModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D62382B1D39A1D14C60ACA6 /* EncodeCupReplyLoadAdverbModel.m */; };
		A4983DA92AFE603450036F5F /* ViewController+MASAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = 552FED5ABD2B35D8399833A5 /* ViewController+MASAdditions.h */; };
		A526953EB8E7815826989CEE /* MQTTMessage.m in Sources */ = {isa = PBXBuildFile; fileRef = 26367074D32D566AB6533174 /* MQTTMessage.m */; };
		A52AFA42435E4D2FB577BE34 /* TapForHasRenew+QuitAcute.h in Headers */ = {isa = PBXBuildFile; fileRef = 61FA071BDE0AD115F370B62F /* TapForHasRenew+QuitAcute.h */; };
		A5AF2B021C4AF6F7576E717F /* DateInvertInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = DB5FA9CC30E284120E6A9257 /* DateInvertInfo.h */; };
		A64C97CD05E34D86F5D1F671 /* SDWebImageCompat.h in Headers */ = {isa = PBXBuildFile; fileRef = 0D6AC0851C1CD096814031EB /* SDWebImageCompat.h */; };
		A65AE20687E2FB573B2BF9AB /* SignalLowProtocol.h in Headers */ = {isa = PBXBuildFile; fileRef = 2F250CFAA6DC5935DB352DFB /* SignalLowProtocol.h */; };
		A69C16E7EA75670DE2CFAEF1 /* SDWeakProxy.m in Sources */ = {isa = PBXBuildFile; fileRef = 068874D3D2185C47352329BD /* SDWeakProxy.m */; };
		A80DBEBC38135188DD1A7612 /* NSData+CropSum.m in Sources */ = {isa = PBXBuildFile; fileRef = 212616CEF8D60226BF63AFFA /* NSData+CropSum.m */; };
		A87F2139840057AC611F80EC /* UIViewController+DueViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = EAEE822732B22A93B6D60F71 /* UIViewController+DueViewController.h */; };
		A8B559FB7235EDBD290214A9 /* HeadAskFootDay.h in Headers */ = {isa = PBXBuildFile; fileRef = 097F92B9A13B30F58B1B832D /* HeadAskFootDay.h */; };
		A8E118EE635A51FF0237806B /* SDGraphicsImageRenderer.h in Headers */ = {isa = PBXBuildFile; fileRef = CFC376449E0F9DCB227284D3 /* SDGraphicsImageRenderer.h */; };
		A9136E2FD4433176E42341CB /* UIImage+Transform.h in Headers */ = {isa = PBXBuildFile; fileRef = 5FCC6D7450B496DFB8008E81 /* UIImage+Transform.h */; };
		AA0F0A880EE1750CE337BEBB /* MQTTInMemoryPersistence.m in Sources */ = {isa = PBXBuildFile; fileRef = CE7C443D7E51F49506E7BF85 /* MQTTInMemoryPersistence.m */; };
		AA18E435062068DDC850CEE5 /* NSData+ImageContentType.m in Sources */ = {isa = PBXBuildFile; fileRef = 7A9F1821FC35982A623A712D /* NSData+ImageContentType.m */; };
		AA44DFA96456CDA28098DDD0 /* UIView+WebCacheOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = CB1DC3CF6991177FBD67D501 /* UIView+WebCacheOperation.m */; };
		AABA24CAF30C8C99E52211FA /* OptPaceSuchAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 3F84E16CEA425D4CE35E2F32 /* OptPaceSuchAction.m */; };
		AB76B4CB53E13E2E765FCB95 /* SDWebImageOptionsProcessor.m in Sources */ = {isa = PBXBuildFile; fileRef = 7DA64A2E2C310776D852CBE7 /* SDWebImageOptionsProcessor.m */; };
		AB9762F5E2FB1819BF4A5F77 /* SDAnimatedImageView.h in Headers */ = {isa = PBXBuildFile; fileRef = B57936FCA6466C171E1128B4 /* SDAnimatedImageView.h */; };
		ABAE13D64C3F9408557D8D44 /* SDWebImageDownloaderConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = B79A424F120C5E282D19A13B /* SDWebImageDownloaderConfig.m */; };
		AC6E528DCE43AA64108B5B4D /* OptShowDryViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 07992C3DCB152FA6DBFC4F3A /* OptShowDryViewController.m */; };
		AD208B4B141BEA907B9ADA2E /* SDCallbackQueue.h in Headers */ = {isa = PBXBuildFile; fileRef = A92B489047F82A66200AC6C5 /* SDCallbackQueue.h */; };
		ADBEA6D3A3B29C4A2DAA268E /* StorageNumberSmallestProjectLaw.h in Headers */ = {isa = PBXBuildFile; fileRef = D860BF9B40C6F7AF0A7FF76C /* StorageNumberSmallestProjectLaw.h */; };
		ADC3271AAD878AC709468FCF /* MQTTSSLSecurityPolicyTransport.h in Headers */ = {isa = PBXBuildFile; fileRef = D6ECB86D21A459FB23D76612 /* MQTTSSLSecurityPolicyTransport.h */; };
		AEC88503B652020326A94D54 /* XXGProtocolLabel.m in Sources */ = {isa = PBXBuildFile; fileRef = 861309CBCC40A4A5E652B2EB /* XXGProtocolLabel.m */; };
		B0360D9177F129D24E3111C7 /* MASConstraint+Private.h in Headers */ = {isa = PBXBuildFile; fileRef = 9853777DC3DDEC57B998772A /* MASConstraint+Private.h */; };
		B1E25B10045F037F341E889E /* AllocateHeavyViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 30C89C89BAB22E6341A65554 /* AllocateHeavyViewController.m */; };
		B1EDBB0457AE8ECCFAA4FAE2 /* SDWebImageCompat.m in Sources */ = {isa = PBXBuildFile; fileRef = EB61C1C9A5433E887D3AB713 /* SDWebImageCompat.m */; };
		B2D8B6E4057DC0BE198978CC /* DayUpsidePutManager.h in Headers */ = {isa = PBXBuildFile; fileRef = CAA3D37C8160CA517438575E /* DayUpsidePutManager.h */; };
		B30A40F2E6FAE8451D38ED9D /* NSString+CutTen.m in Sources */ = {isa = PBXBuildFile; fileRef = E3F5C8B6A36B0A109CD60792 /* NSString+CutTen.m */; };
		B3E1F0A1E46B6E52DE5716F0 /* SDImageHEICCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 37F07D218BCECD95BAF4C614 /* SDImageHEICCoder.h */; };
		B4457112AF011B9DE930785C /* MQTTSession.h in Headers */ = {isa = PBXBuildFile; fileRef = 2FB9AA68D226ED4B4328441A /* MQTTSession.h */; };
		B4C5117DB496217BC94E071C /* GCDTimer.h in Headers */ = {isa = PBXBuildFile; fileRef = 2EFF84FD243DB2AFD61E3601 /* GCDTimer.h */; };
		B4D41CD1EE42F1941004FEC9 /* ForwardWetList.m in Sources */ = {isa = PBXBuildFile; fileRef = 47B1CC86E88CD9959D89421F /* ForwardWetList.m */; };
		B4FCD656A0B56D961A19F85A /* SDImageGIFCoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 1CBFA8CC4BAD5863F5E7533F /* SDImageGIFCoder.h */; };
		B590249F16459846A7BBC862 /* TimeQueueManager.h in Headers */ = {isa = PBXBuildFile; fileRef = C92157ABC4751543832E414D /* TimeQueueManager.h */; };
		B691874D8206EF2036BC2EFF /* UIImage+Metadata.m in Sources */ = {isa = PBXBuildFile; fileRef = 05CB581F4DAB24A26D8426CD /* UIImage+Metadata.m */; };
		B740EB759A7471EF02B63E96 /* MQTTTransportProtocol.m in Sources */ = {isa = PBXBuildFile; fileRef = 1F3FC073F02A3EC01A078132 /* MQTTTransportProtocol.m */; };
		B74FAC80DE003A5654334CD1 /* ForwardWetList.h in Headers */ = {isa = PBXBuildFile; fileRef = 7D3424BB6E1EA47FA8A44952 /* ForwardWetList.h */; };
		B78D60A88711DF299F737519 /* SDImageCacheConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = 8BE4130592E6B643550E2C23 /* SDImageCacheConfig.m */; };
		B7EDCCBBD87539373A10CBB6 /* SDImageAWebPCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = C8469496DD5CEB580E8FA3FF /* SDImageAWebPCoder.m */; };
		B9C0A21642714F67C07A3077 /* MASCompositeConstraint.h in Headers */ = {isa = PBXBuildFile; fileRef = 130233525FDB2FDFEB867795 /* MASCompositeConstraint.h */; };
		B9D50C78E017F4397BB56926 /* BinLawParseThickMixCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 3ABF26898702C46C9172EFFD /* BinLawParseThickMixCell.m */; };
		BC1018912A84C2B94EB20201 /* GainPassGetDogWindow.m in Sources */ = {isa = PBXBuildFile; fileRef = C63AAB8834C0161F78761E1C /* GainPassGetDogWindow.m */; };
		BC318E08206CA3009472A422 /* RightManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 444744D7B6A301996EE69EEC /* RightManager.h */; };
		BCC2AC11AE2CEB43417CB3E2 /* SDFileAttributeHelper.h in Headers */ = {isa = PBXBuildFile; fileRef = 0A1048A70CB191CD1389478E /* SDFileAttributeHelper.h */; };
		BCD36A619A5CC38B89D74FB9 /* MASViewConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = 2D98BE329BB6CCFC7679F89E /* MASViewConstraint.m */; };
		BCE42FF05F2CE652563126C9 /* NSButton+WebCache.h in Headers */ = {isa = PBXBuildFile; fileRef = FCB5DEA39FBEC3CDF6659529 /* NSButton+WebCache.h */; };
		BDC406D604C493C805D5B2AA /* UplinkDeep.h in Headers */ = {isa = PBXBuildFile; fileRef = 50B9E8E4E9D5A3F6FE2BBCCD /* UplinkDeep.h */; };
		BDCBE38BC940EA97FACD04C5 /* RetryAwakeSentencesBarsAll.h in Headers */ = {isa = PBXBuildFile; fileRef = 6C5EF0657F553B9EC0C46358 /* RetryAwakeSentencesBarsAll.h */; };
		BDE8C94F65AF12EBF0BD58AB /* PinPartModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 711D5380DE079318A253E2D4 /* PinPartModel.h */; };
		BE6FFAD40A86C8AF7C7D0F98 /* SDDisplayLink.h in Headers */ = {isa = PBXBuildFile; fileRef = 01806656D598C40508DAE16D /* SDDisplayLink.h */; };
		BED9C38F6B6F62E8FBCEA0C6 /* LogoHexBoxManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 5E922E29381D5F76940D9276 /* LogoHexBoxManager.m */; };
		BF31D324B7336FEF1F15AF36 /* TurnItemOldDog.m in Sources */ = {isa = PBXBuildFile; fileRef = F8A1311E6F3F85EB46AE22D2 /* TurnItemOldDog.m */; };
		C07D69AD6421F5884AA2D845 /* TurnItemOldDog.h in Headers */ = {isa = PBXBuildFile; fileRef = ADB886296A6C372EA9249768 /* TurnItemOldDog.h */; };
		C0D0F57A3350CC85CAF3FD2C /* WayTipManager.m in Sources */ = {isa = PBXBuildFile; fileRef = FB404F4D23485486BED1DB41 /* WayTipManager.m */; };
		C160CCAECEEE144416C06900 /* DepthRootViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 602D719ED39D1ACA56C1E863 /* DepthRootViewController.m */; };
		C16AA9C1C6EA3570AFA30DDA /* MQTTProperties.m in Sources */ = {isa = PBXBuildFile; fileRef = C9B221D07953DB37AD8BD7A8 /* MQTTProperties.m */; };
		C1D55274483C6F7ECB7FDEC0 /* SDImageAssetManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 65B5EF40928913D2AF78EEE9 /* SDImageAssetManager.m */; };
		C1E5CD6540D2858FB1AAFADE /* MergeNameViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 86DC34F49F08A98926C00BA3 /* MergeNameViewController.m */; };
		C5927818ADF675596C939389 /* MASConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = 3F24CF5E9DA21431B348FF61 /* MASConstraint.m */; };
		C59BCD070FF9F9335CEF0A00 /* SDImageCodersManager.h in Headers */ = {isa = PBXBuildFile; fileRef = C2AA533DEA67A21AA2748EB1 /* SDImageCodersManager.h */; };
		C60EB0C623A0F1B93F09FFC7 /* NSObject+PinModel.h in Headers */ = {isa = PBXBuildFile; fileRef = E56DEDF9A2525667D5A033E5 /* NSObject+PinModel.h */; };
		C742F534811ADF6DDC8B0E66 /* DrainageCompetence.h in Headers */ = {isa = PBXBuildFile; fileRef = E70804B06044FB66B0087595 /* DrainageCompetence.h */; settings = {ATTRIBUTES = (Public, ); }; };
		C8364E04EABBF935C58E2ECE /* View+MASShorthandAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = E55EF1548B5FE8614B561B8D /* View+MASShorthandAdditions.h */; };
		C89939513BF6C2E329A07302 /* View+MASAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = DB9DAA15652491B39C4F1713 /* View+MASAdditions.h */; };
		C8D4B58D72BDCB8F3BF19770 /* SDImageGraphics.h in Headers */ = {isa = PBXBuildFile; fileRef = 0A1C8E99CFB2C7D23E996FCC /* SDImageGraphics.h */; };
		C99FA88009B3B6741525971D /* DateInvertInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 5DAF9779A3A044B7A89157E3 /* DateInvertInfo.m */; };
		CA2F9F8D531E251800B9AA80 /* GainPassGetDogWindow.h in Headers */ = {isa = PBXBuildFile; fileRef = 7571DC539A39E547A59B2A8A /* GainPassGetDogWindow.h */; };
		CA538E90A753D4B689CC5A3C /* PolishFaxTextField.m in Sources */ = {isa = PBXBuildFile; fileRef = CBB61A77AA19B32692D9DECD /* PolishFaxTextField.m */; };
		CAD99436D7F528BD7C1FC77C /* SDImageLoadersManager.h in Headers */ = {isa = PBXBuildFile; fileRef = D705681A54AF9923B98266AB /* SDImageLoadersManager.h */; };
		CD244AF0ECBCEA6B2FCDC83B /* SDImageFramePool.h in Headers */ = {isa = PBXBuildFile; fileRef = D5B796730577AFA814F904CF /* SDImageFramePool.h */; };
		CDC0BBA3CA889CB02A9887B4 /* SDImageLoadersManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 9CE50F1CFDF5F9A8AEA84D4A /* SDImageLoadersManager.m */; };
		CDE0D12A6641BD5968114D61 /* SDWebImageDownloader.m in Sources */ = {isa = PBXBuildFile; fileRef = 122A67C2BDEC54D845556956 /* SDWebImageDownloader.m */; };
		CE2FF85631A3C97AAE2F2047 /* AndToast.h in Headers */ = {isa = PBXBuildFile; fileRef = 671FEF740ED11C313579071A /* AndToast.h */; };
		CF16464F509E874563E63840 /* PickReason.h in Headers */ = {isa = PBXBuildFile; fileRef = A646A3D4F37C8070281414DC /* PickReason.h */; };
		CF895C2A6BA6DE62C72EEEFC /* SDWebImageCacheSerializer.m in Sources */ = {isa = PBXBuildFile; fileRef = D2D2835575B4775DE7C9D114 /* SDWebImageCacheSerializer.m */; };
		D02DE54F689F63D5A307F0E3 /* MQTTClient.h in Headers */ = {isa = PBXBuildFile; fileRef = CD0D8155B9C907236B96792A /* MQTTClient.h */; };
		D03EDC57DB1584084D881647 /* MQTTLog.h in Headers */ = {isa = PBXBuildFile; fileRef = A41A79A8ED3F2F815F7C1148 /* MQTTLog.h */; };
		D0BF67C85B8E42D930ADD753 /* MQTTSessionLegacy.h in Headers */ = {isa = PBXBuildFile; fileRef = 5C58C2A2F29F4DBDDD244A65 /* MQTTSessionLegacy.h */; };
		D1213B5E3C5A9538D0035EC2 /* SDInternalMacros.h in Headers */ = {isa = PBXBuildFile; fileRef = 1B568C272BB5025D3735CF20 /* SDInternalMacros.h */; };
		D12176884997B70254690C7F /* UIImageView+WebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = 0B8329775555BBCE91561D38 /* UIImageView+WebCache.m */; };
		D16F9F4C3452BC47B97043AE /* EggReuseRawEra.h in Headers */ = {isa = PBXBuildFile; fileRef = 24903FD878E993065407D828 /* EggReuseRawEra.h */; };
		D1A40FC996ABFF2EA6A2D567 /* TapForHasRenew+QuitAcute.m in Sources */ = {isa = PBXBuildFile; fileRef = 94D10A7C4A4CF4CD5E3EE720 /* TapForHasRenew+QuitAcute.m */; };
		D1CFFCCCCBFE31D3E0D70B34 /* MASCompositeConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = 56935EF63F0690C98756D15A /* MASCompositeConstraint.m */; };
		D269585C595C747CCF13B80A /* UIImage+MemoryCacheCost.h in Headers */ = {isa = PBXBuildFile; fileRef = F90E4C006A80A58F8DFF383E /* UIImage+MemoryCacheCost.h */; };
		D2723D4A7BE43D4797EE00AB /* SDImageFramePool.m in Sources */ = {isa = PBXBuildFile; fileRef = 477991C0F34086DE614FF59D /* SDImageFramePool.m */; };
		D27928BF03238DCEBE8FDBD1 /* EncodeCupReplyLoadAdverbModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 3BD14C54B37A257233F8993C /* EncodeCupReplyLoadAdverbModel.h */; };
		D2E5C8EAA01861788011C81A /* SDImageGIFCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 797E4CC6DC8D4BFEFB3E5935 /* SDImageGIFCoder.m */; };
		D345E2D32D74C7EA497B7B38 /* SDWebImageOptionsProcessor.h in Headers */ = {isa = PBXBuildFile; fileRef = 2163A9B0B07800A693FB3932 /* SDWebImageOptionsProcessor.h */; };
		D374EAC77A3A399B88866D26 /* UIView+WebCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 4FA9F8ACB87C036195633CE4 /* UIView+WebCache.h */; };
		D477A6BCF4F5E25C96754524 /* HailSheCanadianLooperBefore.m in Sources */ = {isa = PBXBuildFile; fileRef = 331421D88F3587D3496C4583 /* HailSheCanadianLooperBefore.m */; };
		D503989C91841CDA9B269427 /* MQTTDecoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 80063A3BE279129E77B68CB7 /* MQTTDecoder.h */; };
		D50FB127688C16CE9233D308 /* DayUpsidePutManager.m in Sources */ = {isa = PBXBuildFile; fileRef = B8E78C2BABF6D0764E58CD8C /* DayUpsidePutManager.m */; };
		D5C71BAA22AC1D5DB8DE0BB9 /* GuideStand.h in Headers */ = {isa = PBXBuildFile; fileRef = D018545C4347F1148133F021 /* GuideStand.h */; };
		D621A72674081071BDFF3FEA /* UIColor+SDHexString.m in Sources */ = {isa = PBXBuildFile; fileRef = F0D4904A04DFFB3A323BC7CC /* UIColor+SDHexString.m */; };
		D6BB9D6D9B031E310438669B /* RestInferiors.m in Sources */ = {isa = PBXBuildFile; fileRef = 92FF51981B550C214B78463B /* RestInferiors.m */; };
		D6DE0C5DA09B817EDAFFCD1B /* MQTTInMemoryPersistence.h in Headers */ = {isa = PBXBuildFile; fileRef = 7E4EFEF422510DE341EEDE09 /* MQTTInMemoryPersistence.h */; };
		D73C6E24CF32FD744E886DC7 /* LogoHexBoxManager.h in Headers */ = {isa = PBXBuildFile; fileRef = B7D590299D8F40FC329C1903 /* LogoHexBoxManager.h */; };
		D7F849AF5B2644D51F7314E4 /* MASViewAttribute.h in Headers */ = {isa = PBXBuildFile; fileRef = 3CB29854AD284365E9032598 /* MASViewAttribute.h */; };
		D836BC90AC361EF16106483D /* UplinkDeep.m in Sources */ = {isa = PBXBuildFile; fileRef = 705A64CB056676C178A88DE7 /* UplinkDeep.m */; };
		D871BB053B2946392BB7F9DB /* IllDayBodyRule.m in Sources */ = {isa = PBXBuildFile; fileRef = B556A707109777810003BC88 /* IllDayBodyRule.m */; };
		D87D1CB47841A05F5490D81A /* MessagingInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 695822313419CB64229FF8DE /* MessagingInfo.m */; };
		D946EAA3A919CDA9A295E05C /* AbsoluteTriggerMonitoredSensitiveLocalizes.m in Sources */ = {isa = PBXBuildFile; fileRef = C7E4EF0CD487B89B6C84BA97 /* AbsoluteTriggerMonitoredSensitiveLocalizes.m */; };
		D9C91BD81E9AC77670574F20 /* UniformFlash.h in Headers */ = {isa = PBXBuildFile; fileRef = 4B939C3B1D27A5582002EBAA /* UniformFlash.h */; };
		DA6A6319E781624C02FF5306 /* SDWebImageDownloaderRequestModifier.h in Headers */ = {isa = PBXBuildFile; fileRef = 3EC678791EFFEEEEA9587C4D /* SDWebImageDownloaderRequestModifier.h */; };
		DAC762087794AF29C483F17D /* BuddyIndigoButton.h in Headers */ = {isa = PBXBuildFile; fileRef = 81CE51318C9F978FC9A0D054 /* BuddyIndigoButton.h */; };
		DB2F04C0065552982326F855 /* UIImage+MemoryCacheCost.m in Sources */ = {isa = PBXBuildFile; fileRef = B91FB9ECE3911618FD468543 /* UIImage+MemoryCacheCost.m */; };
		DCBECDC3C1CC9681C606B778 /* SDWebImageManager.m in Sources */ = {isa = PBXBuildFile; fileRef = D1540B45FEF89149AE4F1B2F /* SDWebImageManager.m */; };
		DCD7ABA46BDBFA7E129F8652 /* AndToast.m in Sources */ = {isa = PBXBuildFile; fileRef = BA425AE293E3BF6D1939E830 /* AndToast.m */; };
		DCEBC8455A348DFAEB62CB4D /* MQTTSSLSecurityPolicyDecoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 0004C502A2093E3EA9DECA5E /* MQTTSSLSecurityPolicyDecoder.h */; };
		DD6ECDC2E58D751380460C3B /* SDWebImageIndicator.h in Headers */ = {isa = PBXBuildFile; fileRef = 507432B3255C38FAC0BF76FF /* SDWebImageIndicator.h */; };
		DDB214FDEEF471D57E4E926E /* UIImage+ForceDecode.m in Sources */ = {isa = PBXBuildFile; fileRef = 90AB7847C37100FB54F5CC69 /* UIImage+ForceDecode.m */; };
		E0C0A240D9F618D44D32949C /* RegionExposureInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = 2A0B0C073A070EE6AFC61065 /* RegionExposureInfo.h */; };
		E0D268D55912C38116131650 /* LossWayDiskViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = B6F596BCA482257AEA47B356 /* LossWayDiskViewController.m */; };
		E10DA418F1D88F3B6550D1F5 /* ButColorViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 22A55080FE15AB372A9E04C1 /* ButColorViewController.m */; };
		E17F92FFA8FBB5D286619018 /* SDAnimatedImageView+WebCache.h in Headers */ = {isa = PBXBuildFile; fileRef = DCF147F50882ED0093013EA8 /* SDAnimatedImageView+WebCache.h */; };
		E23E131955D7C68F5F581FC5 /* UIImageView+WebCache.h in Headers */ = {isa = PBXBuildFile; fileRef = 227BE6E008B57E34AB5FC3B8 /* UIImageView+WebCache.h */; };
		E2AEFCAB54E3000BB762B98E /* UIDevice+TapDevice.h in Headers */ = {isa = PBXBuildFile; fileRef = FCA3611062DE86D8C4D97356 /* UIDevice+TapDevice.h */; };
		E2D1B86E18119F663DE684F7 /* FourMinFoundModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 433E89B7A13D6172C76567AD /* FourMinFoundModel.h */; };
		E493E5B24204198DA6088120 /* BuddyIndigoButton.m in Sources */ = {isa = PBXBuildFile; fileRef = 6CBEC61C36EC7E2818303777 /* BuddyIndigoButton.m */; };
		E500CD643BC7ED60A22B5D14 /* SDWebImageTransition.h in Headers */ = {isa = PBXBuildFile; fileRef = 3E861DE63A2BDA482F40C411 /* SDWebImageTransition.h */; };
		E5EFDB5521D201E4EC9BDFF8 /* MQTTCFSocketEncoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 033F67A0F052F8FD5680763A /* MQTTCFSocketEncoder.m */; };
		E61FC178461A957E9CA1B61B /* UIImage+MultiFormat.h in Headers */ = {isa = PBXBuildFile; fileRef = 0A08C08DBB2A0144153B5E46 /* UIImage+MultiFormat.h */; };
		E67968504BA23D9456704AF6 /* NSString+CutTen.h in Headers */ = {isa = PBXBuildFile; fileRef = 55BEB991BEDB441263A2CE05 /* NSString+CutTen.h */; };
		E6CBC399D3C9144867DA56BD /* HeadAskFootDay.m in Sources */ = {isa = PBXBuildFile; fileRef = 73E3F6EAB5B84236BF3E59E0 /* HeadAskFootDay.m */; };
		E6F2DC8FC9B95BA0328A72DF /* ButAlertView.h in Headers */ = {isa = PBXBuildFile; fileRef = EBB51D381389A2A641BB2B31 /* ButAlertView.h */; };
		E751BC830765F74AD230F4CB /* DepthRootViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 274A65173F2F86385E769D5A /* DepthRootViewController.h */; };
		E851B25ECE5FE1911E1C5522 /* SDWebImageIndicator.m in Sources */ = {isa = PBXBuildFile; fileRef = 19062D4FB001C0234B6B02F7 /* SDWebImageIndicator.m */; };
		E906DFF505EA79F32081E06F /* MQTTCFSocketDecoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 997C5CF9977F3C3C161FF161 /* MQTTCFSocketDecoder.m */; };
		E9273C29CCAD991A36ABE467 /* SDImageHEICCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 43ECD26F92E5955DF3FB989A /* SDImageHEICCoder.m */; };
		E94E01837202601E3FB7B426 /* SlowDetails.m in Sources */ = {isa = PBXBuildFile; fileRef = EFE183D9111DFD5AB4D7C395 /* SlowDetails.m */; };
		E9B10E338547CA6F6B1F1674 /* SDDeviceHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = E22E0288486A9D780F35F40B /* SDDeviceHelper.m */; };
		EAAE0DF7979E709A1F94E101 /* UIDevice+TapDevice.m in Sources */ = {isa = PBXBuildFile; fileRef = 127B9E35D5DB185E9B356DB4 /* UIDevice+TapDevice.m */; };
		EB2B6500F97891863271B899 /* EggReuseRawEra.m in Sources */ = {isa = PBXBuildFile; fileRef = 8A2DF678F0CC0E51D215121F /* EggReuseRawEra.m */; };
		EBCF1FE80374332D26167353 /* SDImageCoderHelper.h in Headers */ = {isa = PBXBuildFile; fileRef = 107BA7FE3943AB752CACE83C /* SDImageCoderHelper.h */; };
		EC9F957CC940E6CF6C2D2802 /* TapCardMenArtsViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 3C1A688265D8A6E98A1346BB /* TapCardMenArtsViewController.h */; };
		ECDF3EF9F1C718128272BD65 /* DrainageCompetence.m in Sources */ = {isa = PBXBuildFile; fileRef = F0D5BA300A457906E68E2892 /* DrainageCompetence.m */; };
		ECE21501E6026E53B705C2D0 /* NSURL+PaletteBad.h in Headers */ = {isa = PBXBuildFile; fileRef = 579D1DB8DAF20C7DDA17DC1B /* NSURL+PaletteBad.h */; };
		ECEA4985CC7F3853BEF43AFE /* MQTTStrict.m in Sources */ = {isa = PBXBuildFile; fileRef = 0F354942B76530367F8F6DD4 /* MQTTStrict.m */; };
		ECEEC1C5A5DA76DA8DE6F280 /* Recovery.h in Headers */ = {isa = PBXBuildFile; fileRef = C1197A6733A9B186979120BB /* Recovery.h */; };
		EDA588CFC9C41A54F3528DC8 /* MQTTCFSocketDecoder.h in Headers */ = {isa = PBXBuildFile; fileRef = E52570C6D7DAF7BDABF90CAF /* MQTTCFSocketDecoder.h */; };
		EE31A5A9EEBB749EBA82C2DE /* SDImageIOAnimatedCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 4A43DAB7F738B58EB82095A3 /* SDImageIOAnimatedCoder.m */; };
		EE320A075E53E4062C35D454 /* StoneDustViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = C0F6B3C9B9D02E70A69C2205 /* StoneDustViewController.m */; };
		EE59F3B0223F989B4795F68C /* ButColorViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 98A4786CBF00AF1808284F8C /* ButColorViewController.h */; };
		EFC3BB23D63FDD45F234CFDA /* MQTTCFSocketTransport.h in Headers */ = {isa = PBXBuildFile; fileRef = A5D25ADB48A9BE1FB1AEE96C /* MQTTCFSocketTransport.h */; };
		F013FA49FF86BA94CCEE19C0 /* ExcludeSlideExtendsSpaLongest.h in Headers */ = {isa = PBXBuildFile; fileRef = 6261FC5F68A06830FE8B294D /* ExcludeSlideExtendsSpaLongest.h */; };
		F0BBCE85E1B214D2EF3F2E98 /* SubBarrierViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = A2E92A83044143E9B9814E2D /* SubBarrierViewController.h */; };
		F0C15B3F16243521699E9EEA /* BinLawParseThickMixCell.h in Headers */ = {isa = PBXBuildFile; fileRef = 17688073E7AE82A23F5D9815 /* BinLawParseThickMixCell.h */; };
		F10A4F783B163B235A797641 /* OldestLink.m in Sources */ = {isa = PBXBuildFile; fileRef = CD0780FF36FF53437C734C06 /* OldestLink.m */; };
		F117C8E001DE95EAD3499093 /* SDWebImageDownloaderDecryptor.h in Headers */ = {isa = PBXBuildFile; fileRef = 4DBFD99311FF3E5515262D5E /* SDWebImageDownloaderDecryptor.h */; };
		F123730482C03519A37865BE /* UIImage+GIF.h in Headers */ = {isa = PBXBuildFile; fileRef = F919006AC1E0B559ACA104F6 /* UIImage+GIF.h */; };
		F151355F31DB05B22C6E48F7 /* TimeQueueManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 968DF7CB5C7BECBA4558E1FC /* TimeQueueManager.m */; };
		F249ABC18F9C4F55698F635D /* MessagingInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = 51EC80BB45CD1E9E2865E2ED /* MessagingInfo.h */; };
		F3AD15552A36DA9672AEBFA4 /* NSURL+PaletteBad.m in Sources */ = {isa = PBXBuildFile; fileRef = FDE6A5CB59D029DA0E715B3D /* NSURL+PaletteBad.m */; };
		F3E8B2904F6C0C1156415225 /* ReconnectTimer.m in Sources */ = {isa = PBXBuildFile; fileRef = 0452D1175FF7F539A2AC3883 /* ReconnectTimer.m */; };
		F4EAB4A66B6998F33319622C /* PathPeakZipAllViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 3A88F677F2E78FADE43EF5FA /* PathPeakZipAllViewController.m */; };
		F622A8ECD857DC36C0560B8D /* RestInferiors.h in Headers */ = {isa = PBXBuildFile; fileRef = D26D3739F9159184CC67A72E /* RestInferiors.h */; };
		F66A9383CD2158654BDCF737 /* PasswordsViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = 56000E352DFC7DF568B786BF /* PasswordsViewController.h */; };
		F674340994D646CFEAF91F7B /* ArraySawSequencerReceiveCutPacket.h in Headers */ = {isa = PBXBuildFile; fileRef = 8FDD6BC0FA7AB14ABF8B3BDA /* ArraySawSequencerReceiveCutPacket.h */; };
		F6C1D4A9518AF5B345B3A38D /* SDWebImageCacheKeyFilter.h in Headers */ = {isa = PBXBuildFile; fileRef = 93DAFF3667A5FD57CB164F7F /* SDWebImageCacheKeyFilter.h */; };
		F7427DD9C96C15053C1FCEB1 /* ThirdCivilCell.h in Headers */ = {isa = PBXBuildFile; fileRef = E137011340E6224B0444D1F8 /* ThirdCivilCell.h */; };
		F74C7AD62CF6B4722B2EE75B /* BurmeseZone.m in Sources */ = {isa = PBXBuildFile; fileRef = B280F071075333FF1E09DB93 /* BurmeseZone.m */; };
		F76712AD34D6DAF4F8E6CDBB /* MASLayoutConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = A357BCA07831E74C4078FCD6 /* MASLayoutConstraint.m */; };
		F76CABBCCE3B7DCC4A2DDF8F /* MASUtilities.h in Headers */ = {isa = PBXBuildFile; fileRef = 0B0910A2A9A85CC1C85BFDEF /* MASUtilities.h */; };
		F7826C20664FF54332354943 /* SDImageFrame.m in Sources */ = {isa = PBXBuildFile; fileRef = 79053BB02A0B74B839186E7B /* SDImageFrame.m */; };
		F8241DBF45EDC77DBEB8B051 /* BezelItsManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 52B6848FA04BA880B028E74A /* BezelItsManager.h */; };
		FA9222F97654F3E27ECA2B2C /* ThirdCivilCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 8A445C7C5D3F63F98CC35A17 /* ThirdCivilCell.m */; };
		FBEEE1CB33396627149CEAFE /* FadeEarManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 6C8BA57CD1138D0F7E2218C6 /* FadeEarManager.h */; };
		FC0752B6DB003B4424101837 /* TapDenseManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 93D7D8DCB5DE4E3726D20BFC /* TapDenseManager.m */; };
		FCA54DAB2611EF2633E3BF24 /* SDImageCacheDefine.m in Sources */ = {isa = PBXBuildFile; fileRef = FF52485AF11A7AB5B18D95BB /* SDImageCacheDefine.m */; };
		FCDFBC4531CDEFCE06E49E98 /* MQTTSSLSecurityPolicyEncoder.h in Headers */ = {isa = PBXBuildFile; fileRef = C59A11227CF2E475F839D228 /* MQTTSSLSecurityPolicyEncoder.h */; };
		FD525ED3BCD3A9EB32113534 /* SDImageCachesManager.m in Sources */ = {isa = PBXBuildFile; fileRef = EDA7FEDC61F457B3B0FD6162 /* SDImageCachesManager.m */; };
		FE1A6F11CDA2196660841E30 /* UIImage+MultiFormat.m in Sources */ = {isa = PBXBuildFile; fileRef = 661A351485773CF6AE57F07A /* UIImage+MultiFormat.m */; };
		FE3E5A24F872165D75022FCA /* NSData+CropSum.h in Headers */ = {isa = PBXBuildFile; fileRef = A121C4125213A403343D91AC /* NSData+CropSum.h */; };
		FFC0BD3B823E225611ECF9FD /* BagCheckoutNarrativeArmGain.h in Headers */ = {isa = PBXBuildFile; fileRef = A91B264C4FAB4E77145059A8 /* BagCheckoutNarrativeArmGain.h */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		0004C502A2093E3EA9DECA5E /* MQTTSSLSecurityPolicyDecoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTSSLSecurityPolicyDecoder.h; sourceTree = "<group>"; };
		015CA462C5ABE9B6F9E515A3 /* NSArray+MASAdditions.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSArray+MASAdditions.m"; sourceTree = "<group>"; };
		016A69C980537C998C19F6BC /* OldestLink.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = OldestLink.h; sourceTree = "<group>"; };
		01743D9FFCB02DE46080FF8C /* SDWebImageDownloaderDecryptor.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageDownloaderDecryptor.m; sourceTree = "<group>"; };
		01806656D598C40508DAE16D /* SDDisplayLink.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDDisplayLink.h; sourceTree = "<group>"; };
		025D4EA91DD0D6615AC9AC66 /* NSObject+MindStickySpatialHelloBox.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSObject+MindStickySpatialHelloBox.h"; sourceTree = "<group>"; };
		02D6CB81DE39FF902CA72F77 /* InsetTowerConfig.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = InsetTowerConfig.h; sourceTree = "<group>"; };
		033F67A0F052F8FD5680763A /* MQTTCFSocketEncoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTCFSocketEncoder.m; sourceTree = "<group>"; };
		0452D1175FF7F539A2AC3883 /* ReconnectTimer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ReconnectTimer.m; sourceTree = "<group>"; };
		05CB581F4DAB24A26D8426CD /* UIImage+Metadata.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImage+Metadata.m"; sourceTree = "<group>"; };
		068874D3D2185C47352329BD /* SDWeakProxy.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWeakProxy.m; sourceTree = "<group>"; };
		07992C3DCB152FA6DBFC4F3A /* OptShowDryViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = OptShowDryViewController.m; sourceTree = "<group>"; };
		08B44FE7C02B2D22A211BE69 /* SexNetwork.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SexNetwork.h; sourceTree = "<group>"; };
		08CDE16A6483EB1478F73093 /* SDMemoryCache.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDMemoryCache.m; sourceTree = "<group>"; };
		097F92B9A13B30F58B1B832D /* HeadAskFootDay.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HeadAskFootDay.h; sourceTree = "<group>"; };
		0A08C08DBB2A0144153B5E46 /* UIImage+MultiFormat.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImage+MultiFormat.h"; sourceTree = "<group>"; };
		0A1048A70CB191CD1389478E /* SDFileAttributeHelper.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDFileAttributeHelper.h; sourceTree = "<group>"; };
		0A1C8E99CFB2C7D23E996FCC /* SDImageGraphics.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageGraphics.h; sourceTree = "<group>"; };
		0A972F5AE62B93436B077CD2 /* AllocateHeavyViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AllocateHeavyViewController.h; sourceTree = "<group>"; };
		0ADB12C79A21A6C2F5694F6F /* StoneDustViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = StoneDustViewController.h; sourceTree = "<group>"; };
		0B0910A2A9A85CC1C85BFDEF /* MASUtilities.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MASUtilities.h; sourceTree = "<group>"; };
		0B3D87BBC7FB574B52AFF2EB /* TorchGainManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TorchGainManager.h; sourceTree = "<group>"; };
		0B8329775555BBCE91561D38 /* UIImageView+WebCache.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImageView+WebCache.m"; sourceTree = "<group>"; };
		0B8EB28663F6E7429D894D75 /* TapCardMenArtsViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TapCardMenArtsViewController.m; sourceTree = "<group>"; };
		0D6AC0851C1CD096814031EB /* SDWebImageCompat.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageCompat.h; sourceTree = "<group>"; };
		0F354942B76530367F8F6DD4 /* MQTTStrict.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTStrict.m; sourceTree = "<group>"; };
		107BA7FE3943AB752CACE83C /* SDImageCoderHelper.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageCoderHelper.h; sourceTree = "<group>"; };
		10F8CE1926F0C25AB8F81165 /* ViewDayCloudInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ViewDayCloudInfo.h; sourceTree = "<group>"; };
		11331CA51275FA728B67D9B5 /* MQTTStrict.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTStrict.h; sourceTree = "<group>"; };
		1179D1421A8477D0A4BD88B6 /* ArbiterLocal.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ArbiterLocal.m; sourceTree = "<group>"; };
		122A67C2BDEC54D845556956 /* SDWebImageDownloader.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageDownloader.m; sourceTree = "<group>"; };
		127B9E35D5DB185E9B356DB4 /* UIDevice+TapDevice.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIDevice+TapDevice.m"; sourceTree = "<group>"; };
		12941105536073ED4DA9CF4C /* HertzViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HertzViewController.h; sourceTree = "<group>"; };
		12BB866ADF7B159434867E4B /* UIImage+GIF.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImage+GIF.m"; sourceTree = "<group>"; };
		12F1A6F272FA259940309903 /* ManAllMildCaseCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ManAllMildCaseCell.h; sourceTree = "<group>"; };
		130233525FDB2FDFEB867795 /* MASCompositeConstraint.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MASCompositeConstraint.h; sourceTree = "<group>"; };
		134F2515410B392ADD6F342A /* TheDarkArmViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TheDarkArmViewController.m; sourceTree = "<group>"; };
		13D2D8E4AF12E9A22AE4A50A /* DiacriticVital.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DiacriticVital.h; sourceTree = "<group>"; };
		141BB055207E906290B82B44 /* SDAnimatedImageView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDAnimatedImageView.m; sourceTree = "<group>"; };
		147BD20BFECC94D41D7F0256 /* ReadyHeadViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ReadyHeadViewController.h; sourceTree = "<group>"; };
		15C01B225EDACCED1BBAABEA /* SDImageCachesManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageCachesManager.h; sourceTree = "<group>"; };
		1615062F148B71D24026EAF8 /* SDImageCache.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageCache.m; sourceTree = "<group>"; };
		17688073E7AE82A23F5D9815 /* BinLawParseThickMixCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BinLawParseThickMixCell.h; sourceTree = "<group>"; };
		1890CCEA0BC9A995E001FD0F /* NSError+YoungestTwo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSError+YoungestTwo.m"; sourceTree = "<group>"; };
		1897AD37369E10C5F04F10D3 /* SDAsyncBlockOperation.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDAsyncBlockOperation.m; sourceTree = "<group>"; };
		19062D4FB001C0234B6B02F7 /* SDWebImageIndicator.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageIndicator.m; sourceTree = "<group>"; };
		1ABBCE8B5A63BEB6F2A7EA58 /* UIButton+WebCache.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIButton+WebCache.m"; sourceTree = "<group>"; };
		1AE178CA2C98B55CC2D4774A /* SDDisplayLink.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDDisplayLink.m; sourceTree = "<group>"; };
		1B568C272BB5025D3735CF20 /* SDInternalMacros.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDInternalMacros.h; sourceTree = "<group>"; };
		1C83FFA711772A628FE88312 /* TerabytesMapManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TerabytesMapManager.h; sourceTree = "<group>"; };
		1CBFA8CC4BAD5863F5E7533F /* SDImageGIFCoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageGIFCoder.h; sourceTree = "<group>"; };
		1CE215362510A2169523CD75 /* BinRebusBody.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BinRebusBody.h; sourceTree = "<group>"; };
		1D08F2B44A5F2ABFD38D2EAB /* TapDenseManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TapDenseManager.h; sourceTree = "<group>"; };
		1E4CF7D3603D59381F5FFBC3 /* LinkOnlyHowNearbyAvailable.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = LinkOnlyHowNearbyAvailable.m; sourceTree = "<group>"; };
		1E79979138BA816F1FE511EB /* TurnGenericManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TurnGenericManager.m; sourceTree = "<group>"; };
		1EC4B504B74161DCD91901D5 /* SDImageIOAnimatedCoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageIOAnimatedCoder.h; sourceTree = "<group>"; };
		1F3FC073F02A3EC01A078132 /* MQTTTransportProtocol.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTTransportProtocol.m; sourceTree = "<group>"; };
		1FEF375A81D83837C0510496 /* BinRebusBody.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BinRebusBody.m; sourceTree = "<group>"; };
		212616CEF8D60226BF63AFFA /* NSData+CropSum.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSData+CropSum.m"; sourceTree = "<group>"; };
		214E31A52D5FCEB4A06D72D6 /* UIViewController+DueViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIViewController+DueViewController.m"; sourceTree = "<group>"; };
		2163A9B0B07800A693FB3932 /* SDWebImageOptionsProcessor.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageOptionsProcessor.h; sourceTree = "<group>"; };
		221B6924E8C79E1C3840FBD2 /* StorageNumberSmallestProjectLaw.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = StorageNumberSmallestProjectLaw.m; sourceTree = "<group>"; };
		227BE6E008B57E34AB5FC3B8 /* UIImageView+WebCache.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImageView+WebCache.h"; sourceTree = "<group>"; };
		22A55080FE15AB372A9E04C1 /* ButColorViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ButColorViewController.m; sourceTree = "<group>"; };
		22A8FADB8B39C9A32423869D /* NSString+DirtyFoot.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSString+DirtyFoot.h"; sourceTree = "<group>"; };
		23E1AC2347E7F860D03624D7 /* ViewDayCloudInfo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ViewDayCloudInfo.m; sourceTree = "<group>"; };
		24903FD878E993065407D828 /* EggReuseRawEra.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = EggReuseRawEra.h; sourceTree = "<group>"; };
		249B76B85084427F31494526 /* DryTapParseViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DryTapParseViewController.m; sourceTree = "<group>"; };
		26367074D32D566AB6533174 /* MQTTMessage.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTMessage.m; sourceTree = "<group>"; };
		27155D8DAAE120F9F17B664D /* GCDTimer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = GCDTimer.m; sourceTree = "<group>"; };
		274A65173F2F86385E769D5A /* DepthRootViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DepthRootViewController.h; sourceTree = "<group>"; };
		274FB6EA10E2ED244D1E91FF /* NSArray+MASAdditions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSArray+MASAdditions.h"; sourceTree = "<group>"; };
		285CA7B4F2071C82F53BA420 /* NSArray+MASShorthandAdditions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSArray+MASShorthandAdditions.h"; sourceTree = "<group>"; };
		28C13AA26ADCAF466F6E9390 /* SDAssociatedObject.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDAssociatedObject.m; sourceTree = "<group>"; };
		29E260077ABAD4F5965CEF7A /* MQTTProperties.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTProperties.h; sourceTree = "<group>"; };
		2A0B0C073A070EE6AFC61065 /* RegionExposureInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RegionExposureInfo.h; sourceTree = "<group>"; };
		2AC5A65F6A32216AA89BC44C /* SDImageCodersManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageCodersManager.m; sourceTree = "<group>"; };
		2BF27640E3B9EE27FC013493 /* TextRetOldEggViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TextRetOldEggViewController.m; sourceTree = "<group>"; };
		2CE539E3628D113325CAB863 /* SDWebImageDownloaderOperation.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageDownloaderOperation.h; sourceTree = "<group>"; };
		2D98BE329BB6CCFC7679F89E /* MASViewConstraint.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MASViewConstraint.m; sourceTree = "<group>"; };
		2E25D0CE0EEA78F54AFE1663 /* SDWebImageDownloaderRequestModifier.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageDownloaderRequestModifier.m; sourceTree = "<group>"; };
		2EFF84FD243DB2AFD61E3601 /* GCDTimer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = GCDTimer.h; sourceTree = "<group>"; };
		2F250CFAA6DC5935DB352DFB /* SignalLowProtocol.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SignalLowProtocol.h; sourceTree = "<group>"; };
		2FB9AA68D226ED4B4328441A /* MQTTSession.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTSession.h; sourceTree = "<group>"; };
		301DBB4D473811DD79AAC0D1 /* TabNumberRevisionMegahertzPasteViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TabNumberRevisionMegahertzPasteViewController.h; sourceTree = "<group>"; };
		30C89C89BAB22E6341A65554 /* AllocateHeavyViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AllocateHeavyViewController.m; sourceTree = "<group>"; };
		31A66A530DD3F35FBEFF3C27 /* SDImageCachesManagerOperation.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageCachesManagerOperation.m; sourceTree = "<group>"; };
		32D9B230616BC896AE5DE56B /* SDAnimatedImageRep.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDAnimatedImageRep.m; sourceTree = "<group>"; };
		330DC7E92F0EE4BF881CFD31 /* LinearManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = LinearManager.m; sourceTree = "<group>"; };
		331421D88F3587D3496C4583 /* HailSheCanadianLooperBefore.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HailSheCanadianLooperBefore.m; sourceTree = "<group>"; };
		336D9CEB7F9D047368C4225E /* BrokenDid.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BrokenDid.m; sourceTree = "<group>"; };
		3494C66CB3AFF6B3B043E1FC /* SDWebImageDefine.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageDefine.m; sourceTree = "<group>"; };
		35F81EEAD28CF085ED745CA9 /* UIImage+ExtendedCacheData.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImage+ExtendedCacheData.h"; sourceTree = "<group>"; };
		36360B33FB1DE372DC1412F0 /* IllDayBodyRule.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = IllDayBodyRule.h; sourceTree = "<group>"; };
		36B7F1C4E6EA67ABDCB2C051 /* InsetTowerConfig.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = InsetTowerConfig.m; sourceTree = "<group>"; };
		36D71A4DD7F82151C06FDA0D /* SDAnimatedImageView+WebCache.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "SDAnimatedImageView+WebCache.m"; sourceTree = "<group>"; };
		37F07D218BCECD95BAF4C614 /* SDImageHEICCoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageHEICCoder.h; sourceTree = "<group>"; };
		384391EB0CD3C9AE3CFFFFF8 /* SDImageLoader.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageLoader.m; sourceTree = "<group>"; };
		392513E943BF5672CCB28573 /* UIImage+Metadata.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImage+Metadata.h"; sourceTree = "<group>"; };
		395C6211A3E356211127D17F /* UIView+WebCacheState.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIView+WebCacheState.h"; sourceTree = "<group>"; };
		3A88F677F2E78FADE43EF5FA /* PathPeakZipAllViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PathPeakZipAllViewController.m; sourceTree = "<group>"; };
		3ABF26898702C46C9172EFFD /* BinLawParseThickMixCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BinLawParseThickMixCell.m; sourceTree = "<group>"; };
		3BD14C54B37A257233F8993C /* EncodeCupReplyLoadAdverbModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = EncodeCupReplyLoadAdverbModel.h; sourceTree = "<group>"; };
		3C1A688265D8A6E98A1346BB /* TapCardMenArtsViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TapCardMenArtsViewController.h; sourceTree = "<group>"; };
		3C959DF3D6D670D1F83CA529 /* SDAnimatedImagePlayer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDAnimatedImagePlayer.m; sourceTree = "<group>"; };
		3CB29854AD284365E9032598 /* MASViewAttribute.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MASViewAttribute.h; sourceTree = "<group>"; };
		3E861DE63A2BDA482F40C411 /* SDWebImageTransition.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageTransition.h; sourceTree = "<group>"; };
		3EC678791EFFEEEEA9587C4D /* SDWebImageDownloaderRequestModifier.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageDownloaderRequestModifier.h; sourceTree = "<group>"; };
		3F24CF5E9DA21431B348FF61 /* MASConstraint.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MASConstraint.m; sourceTree = "<group>"; };
		3F335B5A4FC2E0FC90F2F0F1 /* BrokenDid.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BrokenDid.h; sourceTree = "<group>"; };
		3F50383FA0D0A86FF62AADA1 /* BurmeseZone.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BurmeseZone.h; sourceTree = "<group>"; };
		3F50C3FDDDA683A6949E470B /* UIView+WebCache.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIView+WebCache.m"; sourceTree = "<group>"; };
		3F84E16CEA425D4CE35E2F32 /* OptPaceSuchAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = OptPaceSuchAction.m; sourceTree = "<group>"; };
		3F96F9AB4AE4A0A8F1516551 /* ViewController+MASAdditions.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "ViewController+MASAdditions.m"; sourceTree = "<group>"; };
		406878E935BFE0F86423F4D7 /* UniformFlash.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UniformFlash.m; sourceTree = "<group>"; };
		428A2AC8E86D38E09AA9799A /* SDImageIOCoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageIOCoder.m; sourceTree = "<group>"; };
		433E89B7A13D6172C76567AD /* FourMinFoundModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FourMinFoundModel.h; sourceTree = "<group>"; };
		43ECD26F92E5955DF3FB989A /* SDImageHEICCoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageHEICCoder.m; sourceTree = "<group>"; };
		444744D7B6A301996EE69EEC /* RightManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RightManager.h; sourceTree = "<group>"; };
		44EA56130C211DB8938398B5 /* LogoSent.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = LogoSent.h; sourceTree = "<group>"; };
		4537D4140214C7B34392810F /* DashSawMenSkinCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DashSawMenSkinCell.h; sourceTree = "<group>"; };
		457B3033135C517A729D3535 /* HertzViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HertzViewController.m; sourceTree = "<group>"; };
		461D5508A8C868DF16263E99 /* SDWebImageManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageManager.h; sourceTree = "<group>"; };
		46D6DC94C1C447E77D6FBBBD /* UIButton+WebCache.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIButton+WebCache.h"; sourceTree = "<group>"; };
		477991C0F34086DE614FF59D /* SDImageFramePool.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageFramePool.m; sourceTree = "<group>"; };
		477A16D57919C65EF81D07B8 /* SDInternalMacros.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDInternalMacros.m; sourceTree = "<group>"; };
		47B1CC86E88CD9959D89421F /* ForwardWetList.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ForwardWetList.m; sourceTree = "<group>"; };
		4A43DAB7F738B58EB82095A3 /* SDImageIOAnimatedCoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageIOAnimatedCoder.m; sourceTree = "<group>"; };
		4AE24056D587AABB1648A602 /* WayTipManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WayTipManager.h; sourceTree = "<group>"; };
		4B939C3B1D27A5582002EBAA /* UniformFlash.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UniformFlash.h; sourceTree = "<group>"; };
		4BA8066D6988B29D03C357D2 /* SDmetamacros.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDmetamacros.h; sourceTree = "<group>"; };
		4C16AFF3F87357A800EE5AE8 /* NSImage+Compatibility.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSImage+Compatibility.m"; sourceTree = "<group>"; };
		4C22D2534E5464F3134F5372 /* OwnCacheView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = OwnCacheView.m; sourceTree = "<group>"; };
		4C35DA85AA4B46ADB67714C9 /* MQTTSSLSecurityPolicyDecoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTSSLSecurityPolicyDecoder.m; sourceTree = "<group>"; };
		4C59431ED98C4E0FB36F1F2B /* UIImage+ForceDecode.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImage+ForceDecode.h"; sourceTree = "<group>"; };
		4D14F18C9AB27109CE7F0920 /* MQTTSessionManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTSessionManager.m; sourceTree = "<group>"; };
		4D23922F4F59E3B4A4DF4D6C /* PenGramLossZipButton.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PenGramLossZipButton.m; sourceTree = "<group>"; };
		4D88D026ED54FFBBED7B8467 /* UIImageView+HighlightedWebCache.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImageView+HighlightedWebCache.h"; sourceTree = "<group>"; };
		4DBFD99311FF3E5515262D5E /* SDWebImageDownloaderDecryptor.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageDownloaderDecryptor.h; sourceTree = "<group>"; };
		4E370C834396373B2EA33F90 /* NSObject+MindStickySpatialHelloBox.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSObject+MindStickySpatialHelloBox.m"; sourceTree = "<group>"; };
		4E40A90867F47FB556496429 /* UIImage+YetImage.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImage+YetImage.m"; sourceTree = "<group>"; };
		4ED616A355CAEE027B60E185 /* MASLayoutConstraint.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MASLayoutConstraint.h; sourceTree = "<group>"; };
		4F16B6C98C67CF40BFAB0CD6 /* ClickTokenViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ClickTokenViewController.h; sourceTree = "<group>"; };
		4F1C65776340C2F0646BCE2D /* SDAssociatedObject.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDAssociatedObject.h; sourceTree = "<group>"; };
		4FA9F8ACB87C036195633CE4 /* UIView+WebCache.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIView+WebCache.h"; sourceTree = "<group>"; };
		507432B3255C38FAC0BF76FF /* SDWebImageIndicator.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageIndicator.h; sourceTree = "<group>"; };
		50A435C06DC74595FDE21BFD /* LogoSent.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = LogoSent.m; sourceTree = "<group>"; };
		50B9E8E4E9D5A3F6FE2BBCCD /* UplinkDeep.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UplinkDeep.h; sourceTree = "<group>"; };
		50BF3455CE6E3F90858F857E /* SDImageCacheConfig.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageCacheConfig.h; sourceTree = "<group>"; };
		5109F642D16ED1F7C56C5741 /* TerabytesMapManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TerabytesMapManager.m; sourceTree = "<group>"; };
		51595B2BCF7B15480E544D73 /* MQTTTransportProtocol.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTTransportProtocol.h; sourceTree = "<group>"; };
		51EC80BB45CD1E9E2865E2ED /* MessagingInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MessagingInfo.h; sourceTree = "<group>"; };
		52B6848FA04BA880B028E74A /* BezelItsManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BezelItsManager.h; sourceTree = "<group>"; };
		53E9FE8BCAF14467A0C54C2D /* MQTTSession.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTSession.m; sourceTree = "<group>"; };
		5492263F6C441C489C5AAB83 /* UseEditorInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UseEditorInfo.h; sourceTree = "<group>"; };
		552FED5ABD2B35D8399833A5 /* ViewController+MASAdditions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "ViewController+MASAdditions.h"; sourceTree = "<group>"; };
		55BEB991BEDB441263A2CE05 /* NSString+CutTen.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSString+CutTen.h"; sourceTree = "<group>"; };
		55F2DCDB1FD433FA366CC0C7 /* OptPaceSuchAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = OptPaceSuchAction.h; sourceTree = "<group>"; };
		56000E352DFC7DF568B786BF /* PasswordsViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PasswordsViewController.h; sourceTree = "<group>"; };
		561A55C2F2A1BF3F41349143 /* SubBarrierViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SubBarrierViewController.m; sourceTree = "<group>"; };
		563D6A4F6886020425E107C1 /* SchemesCapView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SchemesCapView.h; sourceTree = "<group>"; };
		56935EF63F0690C98756D15A /* MASCompositeConstraint.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MASCompositeConstraint.m; sourceTree = "<group>"; };
		579D1DB8DAF20C7DDA17DC1B /* NSURL+PaletteBad.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSURL+PaletteBad.h"; sourceTree = "<group>"; };
		579D62EB17AD9CF4C5297F26 /* DanceMidHisManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DanceMidHisManager.m; sourceTree = "<group>"; };
		58DAFC70F7CC443A9185E72C /* OptShowDryViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = OptShowDryViewController.h; sourceTree = "<group>"; };
		59699892DA8AF57D64019350 /* SDImageCoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageCoder.h; sourceTree = "<group>"; };
		5A2A32E140658F6194CEFFDB /* NSLayoutConstraint+MASDebugAdditions.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSLayoutConstraint+MASDebugAdditions.m"; sourceTree = "<group>"; };
		5B2D01B7123CC70085061273 /* ItalianLog.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ItalianLog.h; sourceTree = "<group>"; };
		5BD709CC82DEE6BBCCDB8AE1 /* DanceMidHisManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DanceMidHisManager.h; sourceTree = "<group>"; };
		5C0870BFCF52BB2921ED157B /* DarkerButPlusReportStrategy.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DarkerButPlusReportStrategy.m; sourceTree = "<group>"; };
		5C58C2A2F29F4DBDDD244A65 /* MQTTSessionLegacy.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTSessionLegacy.h; sourceTree = "<group>"; };
		5C84898857E25713DDC55B13 /* SDFileAttributeHelper.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDFileAttributeHelper.m; sourceTree = "<group>"; };
		5C90151EF14C456DA0C7FDFC /* MASConstraintMaker.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MASConstraintMaker.m; sourceTree = "<group>"; };
		5DAF9779A3A044B7A89157E3 /* DateInvertInfo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DateInvertInfo.m; sourceTree = "<group>"; };
		5E39C852BCF30328111B029D /* LinkOnlyHowNearbyAvailable.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = LinkOnlyHowNearbyAvailable.h; sourceTree = "<group>"; };
		5E922E29381D5F76940D9276 /* LogoHexBoxManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = LogoHexBoxManager.m; sourceTree = "<group>"; };
		5EB8AFFE87F67BA2683C8A6E /* NSData+ImageContentType.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSData+ImageContentType.h"; sourceTree = "<group>"; };
		5EF79911837971D4166F0BB5 /* TabNumberRevisionMegahertzPasteViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TabNumberRevisionMegahertzPasteViewController.m; sourceTree = "<group>"; };
		5F86C344EE6D82073E5987B8 /* RegisterColor.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RegisterColor.m; sourceTree = "<group>"; };
		5F94E754532B117A09000DD3 /* SDWeakProxy.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWeakProxy.h; sourceTree = "<group>"; };
		5FCC6D7450B496DFB8008E81 /* UIImage+Transform.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImage+Transform.h"; sourceTree = "<group>"; };
		60013077CCB32C161D404D18 /* RegisterColor.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RegisterColor.h; sourceTree = "<group>"; };
		600A27E77E18A4C3D9C7108B /* SDImageLoader.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageLoader.h; sourceTree = "<group>"; };
		602D719ED39D1ACA56C1E863 /* DepthRootViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DepthRootViewController.m; sourceTree = "<group>"; };
		6076A78D7212EC65375F807B /* NSBezierPath+SDRoundedCorners.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSBezierPath+SDRoundedCorners.h"; sourceTree = "<group>"; };
		6190538599F552A6EBFFF634 /* FilterDigestPreviewsMomentLose.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FilterDigestPreviewsMomentLose.h; sourceTree = "<group>"; };
		619E970B0C355455C4769687 /* UIView+WebCacheOperation.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIView+WebCacheOperation.h"; sourceTree = "<group>"; };
		61FA071BDE0AD115F370B62F /* TapForHasRenew+QuitAcute.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "TapForHasRenew+QuitAcute.h"; sourceTree = "<group>"; };
		6261FC5F68A06830FE8B294D /* ExcludeSlideExtendsSpaLongest.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ExcludeSlideExtendsSpaLongest.h; sourceTree = "<group>"; };
		629EB1FEE21834C1BB2583D4 /* ChatMidProtocol.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ChatMidProtocol.h; sourceTree = "<group>"; };
		63047D7E585286AAD12E9B44 /* UseEditorInfo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UseEditorInfo.m; sourceTree = "<group>"; };
		635E90D7F86630F66D3F5992 /* SDWebImageTransition.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageTransition.m; sourceTree = "<group>"; };
		6473D2789049187B7C759F0E /* PartMayJobManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PartMayJobManager.h; sourceTree = "<group>"; };
		64BF842715FBF1E2431B77DC /* ButAlertView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ButAlertView.m; sourceTree = "<group>"; };
		64D0438572649BF01494ECA6 /* MQTTSessionSynchron.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTSessionSynchron.m; sourceTree = "<group>"; };
		65457C6C6C6C1C847438ECE9 /* ItalianLog.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ItalianLog.m; sourceTree = "<group>"; };
		65B5EF40928913D2AF78EEE9 /* SDImageAssetManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageAssetManager.m; sourceTree = "<group>"; };
		661A351485773CF6AE57F07A /* UIImage+MultiFormat.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImage+MultiFormat.m"; sourceTree = "<group>"; };
		671FEF740ED11C313579071A /* AndToast.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AndToast.h; sourceTree = "<group>"; };
		674F8BEC1F9724A3F9F53F8C /* AgeHardSentinelFaxAgeDiacritic.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AgeHardSentinelFaxAgeDiacritic.h; sourceTree = "<group>"; };
		67939FA9819D474BB544EC20 /* MiddleHue.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MiddleHue.m; sourceTree = "<group>"; };
		67D2F42472DB75D33504FE9A /* SDWebImageDownloaderConfig.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageDownloaderConfig.h; sourceTree = "<group>"; };
		68F99AB8880BE0D4B84970A9 /* OwnCacheView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = OwnCacheView.h; sourceTree = "<group>"; };
		695822313419CB64229FF8DE /* MessagingInfo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MessagingInfo.m; sourceTree = "<group>"; };
		6962C188A317A22A2210DAF2 /* Masonry.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = Masonry.h; sourceTree = "<group>"; };
		6A6EE765D0CAE0631BC80AE4 /* HyphenPhone.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HyphenPhone.m; sourceTree = "<group>"; };
		6BC70D59E929673660CA2312 /* RightManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RightManager.m; sourceTree = "<group>"; };
		6C5169F9C162417452724FEA /* ModelHexModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ModelHexModel.m; sourceTree = "<group>"; };
		6C5850D3CE95995E6A60248A /* ManAllMildCaseCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ManAllMildCaseCell.m; sourceTree = "<group>"; };
		6C5EF0657F553B9EC0C46358 /* RetryAwakeSentencesBarsAll.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RetryAwakeSentencesBarsAll.h; sourceTree = "<group>"; };
		6C8BA57CD1138D0F7E2218C6 /* FadeEarManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FadeEarManager.h; sourceTree = "<group>"; };
		6CBEC61C36EC7E2818303777 /* BuddyIndigoButton.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BuddyIndigoButton.m; sourceTree = "<group>"; };
		6E5A95B489F4EDF6FC75A31A /* SDWebImageDownloader.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageDownloader.h; sourceTree = "<group>"; };
		6E83DCEDDBC6B3AA04820720 /* SDWebImageError.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageError.m; sourceTree = "<group>"; };
		705A64CB056676C178A88DE7 /* UplinkDeep.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UplinkDeep.m; sourceTree = "<group>"; };
		707D8F31DAD892B1A16330AF /* SDDiskCache.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDDiskCache.h; sourceTree = "<group>"; };
		711D5380DE079318A253E2D4 /* PinPartModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PinPartModel.h; sourceTree = "<group>"; };
		7170C7C6D65FDDC9CAAAD5AB /* SDImageFrame.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageFrame.h; sourceTree = "<group>"; };
		719CF62CFCE6532DE861AF5B /* ReadyHeadViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ReadyHeadViewController.m; sourceTree = "<group>"; };
		71A77F9FE801D14D215AA88C /* HoldBigBoxNapController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HoldBigBoxNapController.h; sourceTree = "<group>"; };
		71A835FC472D9461C6D15E07 /* RealTryViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RealTryViewController.m; sourceTree = "<group>"; };
		73E3F6EAB5B84236BF3E59E0 /* HeadAskFootDay.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HeadAskFootDay.m; sourceTree = "<group>"; };
		7571DC539A39E547A59B2A8A /* GainPassGetDogWindow.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = GainPassGetDogWindow.h; sourceTree = "<group>"; };
		76ACC0A56E5BC606D9211E80 /* MiddleHue.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MiddleHue.h; sourceTree = "<group>"; };
		770EA2FCD5B8C7B604FAE7EA /* SDAnimatedImage.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDAnimatedImage.m; sourceTree = "<group>"; };
		7854180F821EDEB33E03A7E3 /* MergeNameViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MergeNameViewController.h; sourceTree = "<group>"; };
		7864B500CE178DBDC117F26D /* SDImageTransformer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageTransformer.h; sourceTree = "<group>"; };
		789ABCAAE7DBC3F2A5C8780D /* MQTTSSLSecurityPolicyEncoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTSSLSecurityPolicyEncoder.m; sourceTree = "<group>"; };
		78CD62031381714D0BB7365B /* DryTapParseViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DryTapParseViewController.h; sourceTree = "<group>"; };
		79053BB02A0B74B839186E7B /* SDImageFrame.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageFrame.m; sourceTree = "<group>"; };
		797E4CC6DC8D4BFEFB3E5935 /* SDImageGIFCoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageGIFCoder.m; sourceTree = "<group>"; };
		7A4DAE610336C2D92B286E13 /* UIColor+SDHexString.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIColor+SDHexString.h"; sourceTree = "<group>"; };
		7A6993F5C2DCD8840EB21953 /* PartMayJobManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PartMayJobManager.m; sourceTree = "<group>"; };
		7A943BA18A5259AA0CCCBA05 /* DiacriticVital.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DiacriticVital.m; sourceTree = "<group>"; };
		7A9F1821FC35982A623A712D /* NSData+ImageContentType.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSData+ImageContentType.m"; sourceTree = "<group>"; };
		7BA3A0832EC4E4ECD75A830B /* TapForHasRenew+Total.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "TapForHasRenew+Total.h"; sourceTree = "<group>"; };
		7D3424BB6E1EA47FA8A44952 /* ForwardWetList.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ForwardWetList.h; sourceTree = "<group>"; };
		7D635D802F6F5D2A3AA0CB53 /* OwnPasswordsManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = OwnPasswordsManager.m; sourceTree = "<group>"; };
		7DA64A2E2C310776D852CBE7 /* SDWebImageOptionsProcessor.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageOptionsProcessor.m; sourceTree = "<group>"; };
		7E00617224339495E59B0951 /* MQTTPersistence.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTPersistence.h; sourceTree = "<group>"; };
		7E4EFEF422510DE341EEDE09 /* MQTTInMemoryPersistence.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTInMemoryPersistence.h; sourceTree = "<group>"; };
		80063A3BE279129E77B68CB7 /* MQTTDecoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTDecoder.h; sourceTree = "<group>"; };
		8053B4F96E759F64786592B8 /* RegionExposureInfo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RegionExposureInfo.m; sourceTree = "<group>"; };
		80B865C7B7DD3F19B3F3566F /* DashSawMenSkinCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DashSawMenSkinCell.m; sourceTree = "<group>"; };
		81049B7C4C2ABF1CB69E8D02 /* SDImageTransformer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageTransformer.m; sourceTree = "<group>"; };
		81CE51318C9F978FC9A0D054 /* BuddyIndigoButton.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BuddyIndigoButton.h; sourceTree = "<group>"; };
		8334EB457B033DBA7209659E /* TapForHasRenew+PopRed.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "TapForHasRenew+PopRed.h"; sourceTree = "<group>"; };
		8366574A0BA13B5C47A3750A /* SDWebImageTransitionInternal.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageTransitionInternal.h; sourceTree = "<group>"; };
		861309CBCC40A4A5E652B2EB /* XXGProtocolLabel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XXGProtocolLabel.m; sourceTree = "<group>"; };
		86CD3D05951C3A4E7D908BF9 /* SDImageAPNGCoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageAPNGCoder.h; sourceTree = "<group>"; };
		86DC34F49F08A98926C00BA3 /* MergeNameViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MergeNameViewController.m; sourceTree = "<group>"; };
		888EFAE2562BCB3212DF42D0 /* MASViewAttribute.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MASViewAttribute.m; sourceTree = "<group>"; };
		88D1429EB67A73102B4CDEEC /* ClickTokenViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ClickTokenViewController.m; sourceTree = "<group>"; };
		89D7EA6EB29A0EF7C22C936A /* BezelItsManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BezelItsManager.m; sourceTree = "<group>"; };
		8A2DF678F0CC0E51D215121F /* EggReuseRawEra.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = EggReuseRawEra.m; sourceTree = "<group>"; };
		8A3AFFA97A8D35385FF2099F /* MoreVisualView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MoreVisualView.h; sourceTree = "<group>"; };
		8A445C7C5D3F63F98CC35A17 /* ThirdCivilCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ThirdCivilCell.m; sourceTree = "<group>"; };
		8A93DDB3A534F8FF14734D97 /* NSObject+PinModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSObject+PinModel.m"; sourceTree = "<group>"; };
		8A96A847DCEA35250F3E0226 /* ForegroundReconnection.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ForegroundReconnection.h; sourceTree = "<group>"; };
		8B1298CB6CAA81B5C3773D19 /* NSString+ReversesAsk.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSString+ReversesAsk.h"; sourceTree = "<group>"; };
		8BE4130592E6B643550E2C23 /* SDImageCacheConfig.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageCacheConfig.m; sourceTree = "<group>"; };
		8D62382B1D39A1D14C60ACA6 /* EncodeCupReplyLoadAdverbModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = EncodeCupReplyLoadAdverbModel.m; sourceTree = "<group>"; };
		8DF20259B4832036D2C8B8B6 /* LinearManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = LinearManager.h; sourceTree = "<group>"; };
		8F813465D747B0DBC1E95DFD /* HavePlusWhoManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HavePlusWhoManager.h; sourceTree = "<group>"; };
		8FDD6BC0FA7AB14ABF8B3BDA /* ArraySawSequencerReceiveCutPacket.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ArraySawSequencerReceiveCutPacket.h; sourceTree = "<group>"; };
		90AB7847C37100FB54F5CC69 /* UIImage+ForceDecode.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImage+ForceDecode.m"; sourceTree = "<group>"; };
		91A6B4D732FB66EE5AE2FD94 /* SDCallbackQueue.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDCallbackQueue.m; sourceTree = "<group>"; };
		92FF51981B550C214B78463B /* RestInferiors.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RestInferiors.m; sourceTree = "<group>"; };
		93D7D8DCB5DE4E3726D20BFC /* TapDenseManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TapDenseManager.m; sourceTree = "<group>"; };
		93DAFF3667A5FD57CB164F7F /* SDWebImageCacheKeyFilter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageCacheKeyFilter.h; sourceTree = "<group>"; };
		9403E740B0E24B8CA0E11F19 /* MQTTSessionSynchron.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTSessionSynchron.h; sourceTree = "<group>"; };
		9475DFABB47529406BB6A139 /* FourMinFoundModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FourMinFoundModel.m; sourceTree = "<group>"; };
		94D10A7C4A4CF4CD5E3EE720 /* TapForHasRenew+QuitAcute.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "TapForHasRenew+QuitAcute.m"; sourceTree = "<group>"; };
		9578E6C06480B2755F8694A0 /* NSButton+WebCache.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSButton+WebCache.m"; sourceTree = "<group>"; };
		968DF7CB5C7BECBA4558E1FC /* TimeQueueManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TimeQueueManager.m; sourceTree = "<group>"; };
		9853777DC3DDEC57B998772A /* MASConstraint+Private.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "MASConstraint+Private.h"; sourceTree = "<group>"; };
		98A4786CBF00AF1808284F8C /* ButColorViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ButColorViewController.h; sourceTree = "<group>"; };
		990BF4DCD6BDD350BA4C2451 /* ForegroundReconnection.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ForegroundReconnection.m; sourceTree = "<group>"; };
		99442D6AB4024F590B88BD49 /* SDAnimatedImage.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDAnimatedImage.h; sourceTree = "<group>"; };
		997C5CF9977F3C3C161FF161 /* MQTTCFSocketDecoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTCFSocketDecoder.m; sourceTree = "<group>"; };
		99AF8ADF4F9ECD5C97754594 /* ShortHoverWide.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ShortHoverWide.m; sourceTree = "<group>"; };
		9B3B10493135E58622EADAC6 /* PinPartModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PinPartModel.m; sourceTree = "<group>"; };
		9B971DA05B7315364EE79A30 /* SDWebImageCacheKeyFilter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageCacheKeyFilter.m; sourceTree = "<group>"; };
		9CB1C1A76C4274A79EA46856 /* InheritedViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = InheritedViewController.h; sourceTree = "<group>"; };
		9CE50F1CFDF5F9A8AEA84D4A /* SDImageLoadersManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageLoadersManager.m; sourceTree = "<group>"; };
		9E6A3EE76DDAF3ACECF5251C /* CharShiftDark.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CharShiftDark.h; sourceTree = "<group>"; };
		9F102264BDA22857C9253CA6 /* UIImage+Transform.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImage+Transform.m"; sourceTree = "<group>"; };
		A0F68E19E078298D207D9DEA /* TenStartupModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TenStartupModel.h; sourceTree = "<group>"; };
		A121C4125213A403343D91AC /* NSData+CropSum.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSData+CropSum.h"; sourceTree = "<group>"; };
		A167DA7DF6402395CDBAA033 /* MQTTSessionLegacy.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTSessionLegacy.m; sourceTree = "<group>"; };
		A19DCB32C93BB93869146621 /* MQTTSessionManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTSessionManager.h; sourceTree = "<group>"; };
		A1A6A01995B67A2FD246044C /* MQTTSSLSecurityPolicy.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTSSLSecurityPolicy.m; sourceTree = "<group>"; };
		A2E92A83044143E9B9814E2D /* SubBarrierViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SubBarrierViewController.h; sourceTree = "<group>"; };
		A304BB7F1BA5FE9E4DEA5799 /* HoldBigBoxNapController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HoldBigBoxNapController.m; sourceTree = "<group>"; };
		A357BCA07831E74C4078FCD6 /* MASLayoutConstraint.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MASLayoutConstraint.m; sourceTree = "<group>"; };
		A41A79A8ED3F2F815F7C1148 /* MQTTLog.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTLog.h; sourceTree = "<group>"; };
		A462FA611F18A8120FBA57F6 /* UIImage+ExtendedCacheData.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImage+ExtendedCacheData.m"; sourceTree = "<group>"; };
		A47D2222AEC3C8C1D637985A /* MQTTCoreDataPersistence.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTCoreDataPersistence.m; sourceTree = "<group>"; };
		A5D25ADB48A9BE1FB1AEE96C /* MQTTCFSocketTransport.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTCFSocketTransport.h; sourceTree = "<group>"; };
		A646A3D4F37C8070281414DC /* PickReason.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PickReason.h; sourceTree = "<group>"; };
		A8E25CF84D2DA0B13AEE7E78 /* Recovery.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = Recovery.m; sourceTree = "<group>"; };
		A8E58EED7D7BBF755A54F002 /* MQTTLog.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTLog.m; sourceTree = "<group>"; };
		A91B264C4FAB4E77145059A8 /* BagCheckoutNarrativeArmGain.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BagCheckoutNarrativeArmGain.h; sourceTree = "<group>"; };
		A92B489047F82A66200AC6C5 /* SDCallbackQueue.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDCallbackQueue.h; sourceTree = "<group>"; };
		A98455467B2D0AACBB37F385 /* AbsoluteTriggerMonitoredSensitiveLocalizes.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AbsoluteTriggerMonitoredSensitiveLocalizes.h; sourceTree = "<group>"; };
		A9A8C76EEBD22D9546EDF17C /* RetryAwakeSentencesBarsAll.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RetryAwakeSentencesBarsAll.m; sourceTree = "<group>"; };
		AA003FEBDC1C982818B996D8 /* PickReason.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PickReason.m; sourceTree = "<group>"; };
		AA177D0A45478CDA5E870059 /* ShortHoverWide.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ShortHoverWide.h; sourceTree = "<group>"; };
		AACEE6ADCE2BDE4CF78249C0 /* TorchGainManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TorchGainManager.m; sourceTree = "<group>"; };
		AC2EEA5F709B8A87836ED27E /* OwnPasswordsManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = OwnPasswordsManager.h; sourceTree = "<group>"; };
		ADB886296A6C372EA9249768 /* TurnItemOldDog.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TurnItemOldDog.h; sourceTree = "<group>"; };
		AE03CE9BCBFE592F547AD01C /* RealTryViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RealTryViewController.h; sourceTree = "<group>"; };
		AF8A49318FDC6F175C8BA081 /* HavePlusWhoManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HavePlusWhoManager.m; sourceTree = "<group>"; };
		B038B18BE450ABD3CAC5BDC3 /* MQTTDecoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTDecoder.m; sourceTree = "<group>"; };
		B061FF0400777705C4335609 /* MASViewConstraint.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MASViewConstraint.h; sourceTree = "<group>"; };
		B10C9694BD7A5079EACF0AC9 /* SDAnimatedImageRep.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDAnimatedImageRep.h; sourceTree = "<group>"; };
		B1225B741C9B0BA9A48CF30E /* IdentifyToneAcrossRegistryOccur.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = IdentifyToneAcrossRegistryOccur.h; sourceTree = "<group>"; };
		B19FEED26F1FF046AF323554 /* ProjectsCapturedArtCommitRedTraveledViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ProjectsCapturedArtCommitRedTraveledViewController.h; sourceTree = "<group>"; };
		B1EB7EFC95DEB8B9B14C8EFC /* UnwindInfo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UnwindInfo.m; sourceTree = "<group>"; };
		B280F071075333FF1E09DB93 /* BurmeseZone.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BurmeseZone.m; sourceTree = "<group>"; };
		B2FE53FBC85EBA73B8B18E55 /* TextRetOldEggViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TextRetOldEggViewController.h; sourceTree = "<group>"; };
		B3EBA07E9160BBCF2E37D426 /* SDWebImageOperation.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageOperation.m; sourceTree = "<group>"; };
		B456BA7BDC7AA9AD448685A4 /* UIColor+JobColor.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIColor+JobColor.h"; sourceTree = "<group>"; };
		B473112AE44CE908095B2087 /* UIView+WebCacheState.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIView+WebCacheState.m"; sourceTree = "<group>"; };
		B556A707109777810003BC88 /* IllDayBodyRule.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = IllDayBodyRule.m; sourceTree = "<group>"; };
		B5584A8C09A481F7CD8B2F4A /* SDWebImagePrefetcher.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImagePrefetcher.h; sourceTree = "<group>"; };
		B564459FAB6D12CE588DE24C /* SDWebImageDefine.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageDefine.h; sourceTree = "<group>"; };
		B57936FCA6466C171E1128B4 /* SDAnimatedImageView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDAnimatedImageView.h; sourceTree = "<group>"; };
		B5A8DA6599FDD3354A6844C8 /* CharShiftDark.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CharShiftDark.m; sourceTree = "<group>"; };
		B5E9F2C4B7820624331E260A /* MQTTCoreDataPersistence.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTCoreDataPersistence.h; sourceTree = "<group>"; };
		B61192AE53DF957EE7E5D4E7 /* MQTTSSLSecurityPolicy.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTSSLSecurityPolicy.h; sourceTree = "<group>"; };
		B688CC3387810DE0D6154B2B /* BagCheckoutNarrativeArmGain.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BagCheckoutNarrativeArmGain.m; sourceTree = "<group>"; };
		B6F596BCA482257AEA47B356 /* LossWayDiskViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = LossWayDiskViewController.m; sourceTree = "<group>"; };
		B79A424F120C5E282D19A13B /* SDWebImageDownloaderConfig.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageDownloaderConfig.m; sourceTree = "<group>"; };
		B7D590299D8F40FC329C1903 /* LogoHexBoxManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = LogoHexBoxManager.h; sourceTree = "<group>"; };
		B7E9C6DCBE31069AF0FCEA75 /* TheDarkArmViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TheDarkArmViewController.h; sourceTree = "<group>"; };
		B84B651663E8083C40F98554 /* SDImageGraphics.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageGraphics.m; sourceTree = "<group>"; };
		B8E78C2BABF6D0764E58CD8C /* DayUpsidePutManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DayUpsidePutManager.m; sourceTree = "<group>"; };
		B91FB9ECE3911618FD468543 /* UIImage+MemoryCacheCost.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImage+MemoryCacheCost.m"; sourceTree = "<group>"; };
		B9BA07E83ABC4553BBEBF4DB /* SDImageCachesManagerOperation.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageCachesManagerOperation.h; sourceTree = "<group>"; };
		B9CDF03919FFAA5626F16554 /* SDWebImageDownloaderOperation.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageDownloaderOperation.m; sourceTree = "<group>"; };
		BA425AE293E3BF6D1939E830 /* AndToast.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AndToast.m; sourceTree = "<group>"; };
		BA89D2A090F394B409558A23 /* InheritedViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = InheritedViewController.m; sourceTree = "<group>"; };
		BAA72DE67CE53E709627831A /* UIColor+JobColor.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIColor+JobColor.m"; sourceTree = "<group>"; };
		BD11F818CE698B2FDD21FA07 /* PasswordsViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PasswordsViewController.m; sourceTree = "<group>"; };
		BD4C585F9C5B2ED58A192339 /* SDImageCoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageCoder.m; sourceTree = "<group>"; };
		BE33DA6A7BE4A3F40A9BD931 /* SDDiskCache.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDDiskCache.m; sourceTree = "<group>"; };
		C0D05E2E15455D4F0C4BBC3E /* MoreVisualView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MoreVisualView.m; sourceTree = "<group>"; };
		C0F6B3C9B9D02E70A69C2205 /* StoneDustViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = StoneDustViewController.m; sourceTree = "<group>"; };
		C1197A6733A9B186979120BB /* Recovery.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = Recovery.h; sourceTree = "<group>"; };
		C14558FFFCADF1D00588FE90 /* MASConstraint.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MASConstraint.h; sourceTree = "<group>"; };
		C2578D8E772EE6FEFDDD005F /* SurgeBreakStay.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SurgeBreakStay.m; sourceTree = "<group>"; };
		C2AA533DEA67A21AA2748EB1 /* SDImageCodersManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageCodersManager.h; sourceTree = "<group>"; };
		C3051CC331618DAB930D6A21 /* UIImage+YetImage.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImage+YetImage.h"; sourceTree = "<group>"; };
		C36E019F800F08CE1B488A1B /* TapForHasRenew.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TapForHasRenew.h; sourceTree = "<group>"; };
		C3A461F5584FF92273C0679D /* SDWebImageOperation.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageOperation.h; sourceTree = "<group>"; };
		C4254BAF95A9B5C140E3E1AE /* SDWebImagePrefetcher.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImagePrefetcher.m; sourceTree = "<group>"; };
		C462B961D67182D3EAECBE8F /* NSError+YoungestTwo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSError+YoungestTwo.h"; sourceTree = "<group>"; };
		C4B69F4CA964495D2C9F82BB /* SDDeviceHelper.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDDeviceHelper.h; sourceTree = "<group>"; };
		C59A11227CF2E475F839D228 /* MQTTSSLSecurityPolicyEncoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTSSLSecurityPolicyEncoder.h; sourceTree = "<group>"; };
		C63AAB8834C0161F78761E1C /* GainPassGetDogWindow.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = GainPassGetDogWindow.m; sourceTree = "<group>"; };
		C711260E44B4354ABC36FF4D /* KilobitsMenGallonWorkoutsDueSpherical.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = KilobitsMenGallonWorkoutsDueSpherical.m; sourceTree = "<group>"; };
		C7E4EF0CD487B89B6C84BA97 /* AbsoluteTriggerMonitoredSensitiveLocalizes.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AbsoluteTriggerMonitoredSensitiveLocalizes.m; sourceTree = "<group>"; };
		C8469496DD5CEB580E8FA3FF /* SDImageAWebPCoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageAWebPCoder.m; sourceTree = "<group>"; };
		C9176FE819F488CEC97A1E38 /* LossWayDiskViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = LossWayDiskViewController.h; sourceTree = "<group>"; };
		C92157ABC4751543832E414D /* TimeQueueManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TimeQueueManager.h; sourceTree = "<group>"; };
		C9B221D07953DB37AD8BD7A8 /* MQTTProperties.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTProperties.m; sourceTree = "<group>"; };
		CAA3D37C8160CA517438575E /* DayUpsidePutManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DayUpsidePutManager.h; sourceTree = "<group>"; };
		CB1DC3CF6991177FBD67D501 /* UIView+WebCacheOperation.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIView+WebCacheOperation.m"; sourceTree = "<group>"; };
		CBB61A77AA19B32692D9DECD /* PolishFaxTextField.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PolishFaxTextField.m; sourceTree = "<group>"; };
		CC461ED6776ED50BBA883B5D /* MQTTMessage.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTMessage.h; sourceTree = "<group>"; };
		CC952C75D256C0C783C3CF4F /* SDImageAPNGCoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageAPNGCoder.m; sourceTree = "<group>"; };
		CC9E25F523DD9B4ECD0ECD42 /* TapForHasRenew.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TapForHasRenew.m; sourceTree = "<group>"; };
		CD0780FF36FF53437C734C06 /* OldestLink.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = OldestLink.m; sourceTree = "<group>"; };
		CD0D8155B9C907236B96792A /* MQTTClient.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTClient.h; sourceTree = "<group>"; };
		CE7C443D7E51F49506E7BF85 /* MQTTInMemoryPersistence.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTInMemoryPersistence.m; sourceTree = "<group>"; };
		CEA0ADC3A77AC7590FFBECA2 /* AwayFullySpa.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AwayFullySpa.h; sourceTree = "<group>"; };
		CFC376449E0F9DCB227284D3 /* SDGraphicsImageRenderer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDGraphicsImageRenderer.h; sourceTree = "<group>"; };
		CFC99165FA77CB55E4951B41 /* NSImage+Compatibility.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSImage+Compatibility.h"; sourceTree = "<group>"; };
		CFE42970522065E9097F27CA /* SurgeBreakStay.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SurgeBreakStay.h; sourceTree = "<group>"; };
		D018545C4347F1148133F021 /* GuideStand.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = GuideStand.h; sourceTree = "<group>"; };
		D0F596A92578CF0DD8E19C46 /* XXGProtocolLabel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XXGProtocolLabel.h; sourceTree = "<group>"; };
		D1540B45FEF89149AE4F1B2F /* SDWebImageManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageManager.m; sourceTree = "<group>"; };
		D1A538F3EEA1BD72C5FDE2B6 /* SDImageAWebPCoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageAWebPCoder.h; sourceTree = "<group>"; };
		D26D3739F9159184CC67A72E /* RestInferiors.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RestInferiors.h; sourceTree = "<group>"; };
		D2B51A3B6F499C51FB0831F0 /* SDAsyncBlockOperation.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDAsyncBlockOperation.h; sourceTree = "<group>"; };
		D2D2835575B4775DE7C9D114 /* SDWebImageCacheSerializer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageCacheSerializer.m; sourceTree = "<group>"; };
		D309C946B33979761A085CF1 /* DrainageCompetence.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = DrainageCompetence.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		D37DBC6F6601E02448363C39 /* ArbiterLocal.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ArbiterLocal.h; sourceTree = "<group>"; };
		D393779C17E505BA5599C6F9 /* NSString+DirtyFoot.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSString+DirtyFoot.m"; sourceTree = "<group>"; };
		D39400BF47E742D3A1A25765 /* Color.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = Color.h; sourceTree = "<group>"; };
		D3B2EC10DE17B83C3B30B81C /* NSString+ReversesAsk.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSString+ReversesAsk.m"; sourceTree = "<group>"; };
		D49167CE0580E7701F6B4BD8 /* FadeEarManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FadeEarManager.m; sourceTree = "<group>"; };
		D49BE0E05EBD2856C0520098 /* PathPeakZipAllViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PathPeakZipAllViewController.h; sourceTree = "<group>"; };
		D5686184C9322B36826099F2 /* HailSheCanadianLooperBefore.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HailSheCanadianLooperBefore.h; sourceTree = "<group>"; };
		D58DE27181558E81D87B10A5 /* SDWebImageDownloaderResponseModifier.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageDownloaderResponseModifier.h; sourceTree = "<group>"; };
		D5B796730577AFA814F904CF /* SDImageFramePool.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageFramePool.h; sourceTree = "<group>"; };
		D5E8309DB0D0912DDBB24D79 /* SexNetwork.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SexNetwork.m; sourceTree = "<group>"; };
		D6ECB86D21A459FB23D76612 /* MQTTSSLSecurityPolicyTransport.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTSSLSecurityPolicyTransport.h; sourceTree = "<group>"; };
		D705681A54AF9923B98266AB /* SDImageLoadersManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageLoadersManager.h; sourceTree = "<group>"; };
		D787C0D5D22457B253F60AAD /* PolishFaxTextField.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PolishFaxTextField.h; sourceTree = "<group>"; };
		D8298597DCE164D09A88C5EF /* SDImageCacheDefine.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageCacheDefine.h; sourceTree = "<group>"; };
		D83F01DFFB3186A3D2F34386 /* FilterDigestPreviewsMomentLose.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FilterDigestPreviewsMomentLose.m; sourceTree = "<group>"; };
		D860BF9B40C6F7AF0A7FF76C /* StorageNumberSmallestProjectLaw.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = StorageNumberSmallestProjectLaw.h; sourceTree = "<group>"; };
		D9072A9CE4C558909BC257B2 /* KilobitsMenGallonWorkoutsDueSpherical.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = KilobitsMenGallonWorkoutsDueSpherical.h; sourceTree = "<group>"; };
		D99B852B6FC88CD752CC27D9 /* AwayFullySpa.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AwayFullySpa.m; sourceTree = "<group>"; };
		D9DA5B376458049AD09186DA /* DarkerButPlusReportStrategy.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DarkerButPlusReportStrategy.h; sourceTree = "<group>"; };
		DB5FA9CC30E284120E6A9257 /* DateInvertInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DateInvertInfo.h; sourceTree = "<group>"; };
		DB9DAA15652491B39C4F1713 /* View+MASAdditions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "View+MASAdditions.h"; sourceTree = "<group>"; };
		DBD8DCAEBEDD5875983DF880 /* AnswerConfig.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AnswerConfig.m; sourceTree = "<group>"; };
		DC0AE235B834CECABDE27EA0 /* SDImageCache.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageCache.h; sourceTree = "<group>"; };
		DCF147F50882ED0093013EA8 /* SDAnimatedImageView+WebCache.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "SDAnimatedImageView+WebCache.h"; sourceTree = "<group>"; };
		E137011340E6224B0444D1F8 /* ThirdCivilCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ThirdCivilCell.h; sourceTree = "<group>"; };
		E1D03C1903AC3AB1A8B27626 /* SlowDetails.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SlowDetails.h; sourceTree = "<group>"; };
		E22E0288486A9D780F35F40B /* SDDeviceHelper.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDDeviceHelper.m; sourceTree = "<group>"; };
		E34CAA9B03D2A0E183EEFCAD /* TenStartupModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TenStartupModel.m; sourceTree = "<group>"; };
		E39D3C020942D666B6351FE1 /* SDWebImageDownloaderResponseModifier.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageDownloaderResponseModifier.m; sourceTree = "<group>"; };
		E3F5C8B6A36B0A109CD60792 /* NSString+CutTen.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSString+CutTen.m"; sourceTree = "<group>"; };
		E52570C6D7DAF7BDABF90CAF /* MQTTCFSocketDecoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTCFSocketDecoder.h; sourceTree = "<group>"; };
		E55EF1548B5FE8614B561B8D /* View+MASShorthandAdditions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "View+MASShorthandAdditions.h"; sourceTree = "<group>"; };
		E56DEDF9A2525667D5A033E5 /* NSObject+PinModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSObject+PinModel.h"; sourceTree = "<group>"; };
		E5D31FF1D3555DE38C2CAF70 /* AnswerConfig.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AnswerConfig.h; sourceTree = "<group>"; };
		E5E17E9C167143CE89EF377C /* PenGramLossZipButton.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PenGramLossZipButton.h; sourceTree = "<group>"; };
		E70804B06044FB66B0087595 /* DrainageCompetence.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DrainageCompetence.h; sourceTree = "<group>"; };
		E83AF3B99842C9553798BC43 /* SchemesCapView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SchemesCapView.m; sourceTree = "<group>"; };
		E8DF6F211689C6D51E6A5D48 /* BetterEyeIncrementLoopsSummaryTool.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BetterEyeIncrementLoopsSummaryTool.m; sourceTree = "<group>"; };
		E94FB146C5FE700D7E2AC820 /* ReconnectTimer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ReconnectTimer.h; sourceTree = "<group>"; };
		E9DF1FDA5205150BBBF9EE94 /* TurnGenericManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TurnGenericManager.h; sourceTree = "<group>"; };
		EAEE822732B22A93B6D60F71 /* UIViewController+DueViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIViewController+DueViewController.h"; sourceTree = "<group>"; };
		EB61C1C9A5433E887D3AB713 /* SDWebImageCompat.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDWebImageCompat.m; sourceTree = "<group>"; };
		EBB51D381389A2A641BB2B31 /* ButAlertView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ButAlertView.h; sourceTree = "<group>"; };
		ED41E2FB31B281B8349E222E /* LessRaceProtocol.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = LessRaceProtocol.h; sourceTree = "<group>"; };
		EDA7FEDC61F457B3B0FD6162 /* SDImageCachesManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageCachesManager.m; sourceTree = "<group>"; };
		EDF85B71A42365F87EFD5133 /* View+MASAdditions.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "View+MASAdditions.m"; sourceTree = "<group>"; };
		EE034196822ED15D8E97655D /* SDImageIOAnimatedCoderInternal.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageIOAnimatedCoderInternal.h; sourceTree = "<group>"; };
		EE5EDFE175C4517CA55F2904 /* SDImageCoderHelper.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageCoderHelper.m; sourceTree = "<group>"; };
		EF3B8804964E3252F001DA75 /* ArraySawSequencerReceiveCutPacket.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ArraySawSequencerReceiveCutPacket.m; sourceTree = "<group>"; };
		EFE183D9111DFD5AB4D7C395 /* SlowDetails.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SlowDetails.m; sourceTree = "<group>"; };
		F0C8D04590AA1923EA29B34B /* JobJapanese.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = JobJapanese.h; sourceTree = "<group>"; };
		F0D4904A04DFFB3A323BC7CC /* UIColor+SDHexString.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIColor+SDHexString.m"; sourceTree = "<group>"; };
		F0D5BA300A457906E68E2892 /* DrainageCompetence.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DrainageCompetence.m; sourceTree = "<group>"; };
		F1377CE9BCD5E119A62EBCBC /* UIImageView+HighlightedWebCache.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImageView+HighlightedWebCache.m"; sourceTree = "<group>"; };
		F20593849D706C33221490B1 /* MQTTCFSocketTransport.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTCFSocketTransport.m; sourceTree = "<group>"; };
		F34D19F61C6B498859370CCD /* IdentifyToneAcrossRegistryOccur.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = IdentifyToneAcrossRegistryOccur.m; sourceTree = "<group>"; };
		F357E3CB7A3362D73871A15E /* SDAnimatedImagePlayer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDAnimatedImagePlayer.h; sourceTree = "<group>"; };
		F498C1D1D26B88EB35C7161D /* TapForHasRenew+PopRed.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "TapForHasRenew+PopRed.m"; sourceTree = "<group>"; };
		F5870C4C21489E4B3D8F7046 /* ModelHexModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ModelHexModel.h; sourceTree = "<group>"; };
		F5BB5BD4EEB0BF69DBAE7BB5 /* MQTTSSLSecurityPolicyTransport.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MQTTSSLSecurityPolicyTransport.m; sourceTree = "<group>"; };
		F5BFCAD14F8CD43F9A74BC77 /* SDWebImageCacheSerializer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageCacheSerializer.h; sourceTree = "<group>"; };
		F5C2783E04C75E202B32D6AD /* HyphenPhone.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HyphenPhone.h; sourceTree = "<group>"; };
		F5E0E3218F91B623C041274E /* SDWebImageError.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImageError.h; sourceTree = "<group>"; };
		F6859DB3D21E61EBB4A54248 /* Color.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = Color.m; sourceTree = "<group>"; };
		F735F520BA59A88D03C8B4A9 /* AgeHardSentinelFaxAgeDiacritic.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AgeHardSentinelFaxAgeDiacritic.m; sourceTree = "<group>"; };
		F85D7C276EEBB7432213A19D /* SDMemoryCache.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDMemoryCache.h; sourceTree = "<group>"; };
		F8A1311E6F3F85EB46AE22D2 /* TurnItemOldDog.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TurnItemOldDog.m; sourceTree = "<group>"; };
		F8EED95EB1B6ABF06B4FD3B6 /* SDImageAssetManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageAssetManager.h; sourceTree = "<group>"; };
		F90E4C006A80A58F8DFF383E /* UIImage+MemoryCacheCost.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImage+MemoryCacheCost.h"; sourceTree = "<group>"; };
		F919006AC1E0B559ACA104F6 /* UIImage+GIF.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImage+GIF.h"; sourceTree = "<group>"; };
		FA9D86B65EF76E6378A194D2 /* SDGraphicsImageRenderer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDGraphicsImageRenderer.m; sourceTree = "<group>"; };
		FAB61A4666E33303B97B1251 /* SDImageIOCoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDImageIOCoder.h; sourceTree = "<group>"; };
		FB404F4D23485486BED1DB41 /* WayTipManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WayTipManager.m; sourceTree = "<group>"; };
		FB6193C8671A84748D419B8D /* MQTTCFSocketEncoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MQTTCFSocketEncoder.h; sourceTree = "<group>"; };
		FBE1B7EA846AC338DF199043 /* NSBezierPath+SDRoundedCorners.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSBezierPath+SDRoundedCorners.m"; sourceTree = "<group>"; };
		FCA3611062DE86D8C4D97356 /* UIDevice+TapDevice.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIDevice+TapDevice.h"; sourceTree = "<group>"; };
		FCB5DEA39FBEC3CDF6659529 /* NSButton+WebCache.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSButton+WebCache.h"; sourceTree = "<group>"; };
		FCE1505512FC47584A94FDDB /* TapForHasRenew+Total.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "TapForHasRenew+Total.m"; sourceTree = "<group>"; };
		FD1B865DA0BE4D1CDE671EE0 /* MASConstraintMaker.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MASConstraintMaker.h; sourceTree = "<group>"; };
		FD5A648198380875F8C946CE /* BetterEyeIncrementLoopsSummaryTool.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BetterEyeIncrementLoopsSummaryTool.h; sourceTree = "<group>"; };
		FDB00E0424953F1A2D040295 /* ProjectsCapturedArtCommitRedTraveledViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ProjectsCapturedArtCommitRedTraveledViewController.m; sourceTree = "<group>"; };
		FDE6A5CB59D029DA0E715B3D /* NSURL+PaletteBad.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSURL+PaletteBad.m"; sourceTree = "<group>"; };
		FE4FC49FE5C2249593BF5397 /* NSLayoutConstraint+MASDebugAdditions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSLayoutConstraint+MASDebugAdditions.h"; sourceTree = "<group>"; };
		FF52485AF11A7AB5B18D95BB /* SDImageCacheDefine.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDImageCacheDefine.m; sourceTree = "<group>"; };
		FF88A68EBD984FE98C96DFBC /* UnwindInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UnwindInfo.h; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		0488CEBAE9A9421779838D84 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		151D473E2F50CE7AE3A19589 /* UsesOld */ = {
			isa = PBXGroup;
			children = (
				9B48B785A77EDA580B77BF69 /* Lossy */,
				BC4CA71230767DE57D75D3FD /* EndsOpt */,
				A725824B81EB7769EE590256 /* Lemma */,
				E5D31FF1D3555DE38C2CAF70 /* AnswerConfig.h */,
				DBD8DCAEBEDD5875983DF880 /* AnswerConfig.m */,
				D018545C4347F1148133F021 /* GuideStand.h */,
				2F250CFAA6DC5935DB352DFB /* SignalLowProtocol.h */,
			);
			path = UsesOld;
			sourceTree = "<group>";
		};
		303FBB785F8945D7E29B5DAB = {
			isa = PBXGroup;
			children = (
				BA6F233659B22CA7604B8181 /* DrainageCompetence */,
				76158ACAD3DC139DE8BF6479 /* Products */,
			);
			sourceTree = "<group>";
		};
		344DF5546264A1BDAF4FA253 /* MindLog */ = {
			isa = PBXGroup;
			children = (
				8B33C2C29297A0B08C61A867 /* Was */,
				47A2AFDEB3932BD65C57FFEC /* Cat */,
				1D08F2B44A5F2ABFD38D2EAB /* TapDenseManager.h */,
				93D7D8DCB5DE4E3726D20BFC /* TapDenseManager.m */,
				8DF20259B4832036D2C8B8B6 /* LinearManager.h */,
				330DC7E92F0EE4BF881CFD31 /* LinearManager.m */,
				4AE24056D587AABB1648A602 /* WayTipManager.h */,
				FB404F4D23485486BED1DB41 /* WayTipManager.m */,
				6C8BA57CD1138D0F7E2218C6 /* FadeEarManager.h */,
				D49167CE0580E7701F6B4BD8 /* FadeEarManager.m */,
			);
			path = MindLog;
			sourceTree = "<group>";
		};
		462934F2CB939E7185FA28A1 /* TimeCiphersDueChamberKilometer */ = {
			isa = PBXGroup;
			children = (
				274A65173F2F86385E769D5A /* DepthRootViewController.h */,
				602D719ED39D1ACA56C1E863 /* DepthRootViewController.m */,
				98A4786CBF00AF1808284F8C /* ButColorViewController.h */,
				22A55080FE15AB372A9E04C1 /* ButColorViewController.m */,
				A2E92A83044143E9B9814E2D /* SubBarrierViewController.h */,
				561A55C2F2A1BF3F41349143 /* SubBarrierViewController.m */,
			);
			path = TimeCiphersDueChamberKilometer;
			sourceTree = "<group>";
		};
		47A2AFDEB3932BD65C57FFEC /* Cat */ = {
			isa = PBXGroup;
			children = (
				5BD709CC82DEE6BBCCDB8AE1 /* DanceMidHisManager.h */,
				579D62EB17AD9CF4C5297F26 /* DanceMidHisManager.m */,
				1C83FFA711772A628FE88312 /* TerabytesMapManager.h */,
				5109F642D16ED1F7C56C5741 /* TerabytesMapManager.m */,
				E9DF1FDA5205150BBBF9EE94 /* TurnGenericManager.h */,
				1E79979138BA816F1FE511EB /* TurnGenericManager.m */,
				444744D7B6A301996EE69EEC /* RightManager.h */,
				6BC70D59E929673660CA2312 /* RightManager.m */,
				C92157ABC4751543832E414D /* TimeQueueManager.h */,
				968DF7CB5C7BECBA4558E1FC /* TimeQueueManager.m */,
				52B6848FA04BA880B028E74A /* BezelItsManager.h */,
				89D7EA6EB29A0EF7C22C936A /* BezelItsManager.m */,
				8F813465D747B0DBC1E95DFD /* HavePlusWhoManager.h */,
				AF8A49318FDC6F175C8BA081 /* HavePlusWhoManager.m */,
			);
			path = Cat;
			sourceTree = "<group>";
		};
		4F883BAEE6E33527749BBD56 /* FootballEnteredTextualPriceOperand */ = {
			isa = PBXGroup;
			children = (
				A98455467B2D0AACBB37F385 /* AbsoluteTriggerMonitoredSensitiveLocalizes.h */,
				C7E4EF0CD487B89B6C84BA97 /* AbsoluteTriggerMonitoredSensitiveLocalizes.m */,
				674F8BEC1F9724A3F9F53F8C /* AgeHardSentinelFaxAgeDiacritic.h */,
				F735F520BA59A88D03C8B4A9 /* AgeHardSentinelFaxAgeDiacritic.m */,
				B1225B741C9B0BA9A48CF30E /* IdentifyToneAcrossRegistryOccur.h */,
				F34D19F61C6B498859370CCD /* IdentifyToneAcrossRegistryOccur.m */,
				D39400BF47E742D3A1A25765 /* Color.h */,
				F6859DB3D21E61EBB4A54248 /* Color.m */,
				13D2D8E4AF12E9A22AE4A50A /* DiacriticVital.h */,
				7A943BA18A5259AA0CCCBA05 /* DiacriticVital.m */,
				F0C8D04590AA1923EA29B34B /* JobJapanese.h */,
				12941105536073ED4DA9CF4C /* HertzViewController.h */,
				457B3033135C517A729D3535 /* HertzViewController.m */,
				6261FC5F68A06830FE8B294D /* ExcludeSlideExtendsSpaLongest.h */,
			);
			path = FootballEnteredTextualPriceOperand;
			sourceTree = "<group>";
		};
		5179DF0CB3ECCB37929FA847 /* DogHour */ = {
			isa = PBXGroup;
			children = (
				02D6CB81DE39FF902CA72F77 /* InsetTowerConfig.h */,
				36B7F1C4E6EA67ABDCB2C051 /* InsetTowerConfig.m */,
				C36E019F800F08CE1B488A1B /* TapForHasRenew.h */,
				CC9E25F523DD9B4ECD0ECD42 /* TapForHasRenew.m */,
				7BA3A0832EC4E4ECD75A830B /* TapForHasRenew+Total.h */,
				FCE1505512FC47584A94FDDB /* TapForHasRenew+Total.m */,
				61FA071BDE0AD115F370B62F /* TapForHasRenew+QuitAcute.h */,
				94D10A7C4A4CF4CD5E3EE720 /* TapForHasRenew+QuitAcute.m */,
				8334EB457B033DBA7209659E /* TapForHasRenew+PopRed.h */,
				F498C1D1D26B88EB35C7161D /* TapForHasRenew+PopRed.m */,
				6C5EF0657F553B9EC0C46358 /* RetryAwakeSentencesBarsAll.h */,
				A9A8C76EEBD22D9546EDF17C /* RetryAwakeSentencesBarsAll.m */,
				55F2DCDB1FD433FA366CC0C7 /* OptPaceSuchAction.h */,
				3F84E16CEA425D4CE35E2F32 /* OptPaceSuchAction.m */,
			);
			path = DogHour;
			sourceTree = "<group>";
		};
		5D5B66E6E7575748266A21A3 /* Due */ = {
			isa = PBXGroup;
			children = (
				9E6A3EE76DDAF3ACECF5251C /* CharShiftDark.h */,
				B5A8DA6599FDD3354A6844C8 /* CharShiftDark.m */,
				D26D3739F9159184CC67A72E /* RestInferiors.h */,
				92FF51981B550C214B78463B /* RestInferiors.m */,
				711D5380DE079318A253E2D4 /* PinPartModel.h */,
				9B3B10493135E58622EADAC6 /* PinPartModel.m */,
				60013077CCB32C161D404D18 /* RegisterColor.h */,
				5F86C344EE6D82073E5987B8 /* RegisterColor.m */,
				4B939C3B1D27A5582002EBAA /* UniformFlash.h */,
				406878E935BFE0F86423F4D7 /* UniformFlash.m */,
				DB5FA9CC30E284120E6A9257 /* DateInvertInfo.h */,
				5DAF9779A3A044B7A89157E3 /* DateInvertInfo.m */,
				5E39C852BCF30328111B029D /* LinkOnlyHowNearbyAvailable.h */,
				1E4CF7D3603D59381F5FFBC3 /* LinkOnlyHowNearbyAvailable.m */,
				36360B33FB1DE372DC1412F0 /* IllDayBodyRule.h */,
				B556A707109777810003BC88 /* IllDayBodyRule.m */,
				8FDD6BC0FA7AB14ABF8B3BDA /* ArraySawSequencerReceiveCutPacket.h */,
				EF3B8804964E3252F001DA75 /* ArraySawSequencerReceiveCutPacket.m */,
				A91B264C4FAB4E77145059A8 /* BagCheckoutNarrativeArmGain.h */,
				B688CC3387810DE0D6154B2B /* BagCheckoutNarrativeArmGain.m */,
				2A0B0C073A070EE6AFC61065 /* RegionExposureInfo.h */,
				8053B4F96E759F64786592B8 /* RegionExposureInfo.m */,
				10F8CE1926F0C25AB8F81165 /* ViewDayCloudInfo.h */,
				23E1AC2347E7F860D03624D7 /* ViewDayCloudInfo.m */,
				097F92B9A13B30F58B1B832D /* HeadAskFootDay.h */,
				73E3F6EAB5B84236BF3E59E0 /* HeadAskFootDay.m */,
				5492263F6C441C489C5AAB83 /* UseEditorInfo.h */,
				63047D7E585286AAD12E9B44 /* UseEditorInfo.m */,
			);
			path = Due;
			sourceTree = "<group>";
		};
		6840779963900F0AA80D4AC0 /* SDWebImage */ = {
			isa = PBXGroup;
			children = (
				B3B5A2269D1009806CDC2B80 /* Core */,
				B60641CC11A635AB0084A560 /* Private */,
			);
			path = SDWebImage;
			sourceTree = "<group>";
		};
		70B829580080868406274398 /* DoubleExist */ = {
			isa = PBXGroup;
			children = (
				E1D03C1903AC3AB1A8B27626 /* SlowDetails.h */,
				EFE183D9111DFD5AB4D7C395 /* SlowDetails.m */,
			);
			path = DoubleExist;
			sourceTree = "<group>";
		};
		76158ACAD3DC139DE8BF6479 /* Products */ = {
			isa = PBXGroup;
			children = (
				D309C946B33979761A085CF1 /* DrainageCompetence.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		7FF50560C199B6A0A55CA054 /* YellowBiotin */ = {
			isa = PBXGroup;
			children = (
				D9DA5B376458049AD09186DA /* DarkerButPlusReportStrategy.h */,
				5C0870BFCF52BB2921ED157B /* DarkerButPlusReportStrategy.m */,
				433E89B7A13D6172C76567AD /* FourMinFoundModel.h */,
				9475DFABB47529406BB6A139 /* FourMinFoundModel.m */,
				F5870C4C21489E4B3D8F7046 /* ModelHexModel.h */,
				6C5169F9C162417452724FEA /* ModelHexModel.m */,
			);
			path = YellowBiotin;
			sourceTree = "<group>";
		};
		84F741287C216951EC67DE71 /* BondUse */ = {
			isa = PBXGroup;
			children = (
				24903FD878E993065407D828 /* EggReuseRawEra.h */,
				8A2DF678F0CC0E51D215121F /* EggReuseRawEra.m */,
				6190538599F552A6EBFFF634 /* FilterDigestPreviewsMomentLose.h */,
				D83F01DFFB3186A3D2F34386 /* FilterDigestPreviewsMomentLose.m */,
			);
			path = BondUse;
			sourceTree = "<group>";
		};
		8AC3FE6B13D01C34584ED2BE /* LowerPen */ = {
			isa = PBXGroup;
			children = (
				C1197A6733A9B186979120BB /* Recovery.h */,
				A8E25CF84D2DA0B13AEE7E78 /* Recovery.m */,
				D1CD7F67BA6F0E12DBA086CE /* Wrap */,
				DB8CC40D428F66CBC496F755 /* Swap */,
				DBD1B642AA0024576AD07D90 /* TeamLessCopper */,
				462934F2CB939E7185FA28A1 /* TimeCiphersDueChamberKilometer */,
			);
			path = LowerPen;
			sourceTree = "<group>";
		};
		8B33C2C29297A0B08C61A867 /* Was */ = {
			isa = PBXGroup;
			children = (
				6473D2789049187B7C759F0E /* PartMayJobManager.h */,
				7A6993F5C2DCD8840EB21953 /* PartMayJobManager.m */,
				AC2EEA5F709B8A87836ED27E /* OwnPasswordsManager.h */,
				7D635D802F6F5D2A3AA0CB53 /* OwnPasswordsManager.m */,
			);
			path = Was;
			sourceTree = "<group>";
		};
		949B700047A08B6A671F32D0 /* Masonry */ = {
			isa = PBXGroup;
			children = (
				130233525FDB2FDFEB867795 /* MASCompositeConstraint.h */,
				56935EF63F0690C98756D15A /* MASCompositeConstraint.m */,
				C14558FFFCADF1D00588FE90 /* MASConstraint.h */,
				3F24CF5E9DA21431B348FF61 /* MASConstraint.m */,
				9853777DC3DDEC57B998772A /* MASConstraint+Private.h */,
				FD1B865DA0BE4D1CDE671EE0 /* MASConstraintMaker.h */,
				5C90151EF14C456DA0C7FDFC /* MASConstraintMaker.m */,
				4ED616A355CAEE027B60E185 /* MASLayoutConstraint.h */,
				A357BCA07831E74C4078FCD6 /* MASLayoutConstraint.m */,
				6962C188A317A22A2210DAF2 /* Masonry.h */,
				0B0910A2A9A85CC1C85BFDEF /* MASUtilities.h */,
				3CB29854AD284365E9032598 /* MASViewAttribute.h */,
				888EFAE2562BCB3212DF42D0 /* MASViewAttribute.m */,
				B061FF0400777705C4335609 /* MASViewConstraint.h */,
				2D98BE329BB6CCFC7679F89E /* MASViewConstraint.m */,
				274FB6EA10E2ED244D1E91FF /* NSArray+MASAdditions.h */,
				015CA462C5ABE9B6F9E515A3 /* NSArray+MASAdditions.m */,
				285CA7B4F2071C82F53BA420 /* NSArray+MASShorthandAdditions.h */,
				FE4FC49FE5C2249593BF5397 /* NSLayoutConstraint+MASDebugAdditions.h */,
				5A2A32E140658F6194CEFFDB /* NSLayoutConstraint+MASDebugAdditions.m */,
				DB9DAA15652491B39C4F1713 /* View+MASAdditions.h */,
				EDF85B71A42365F87EFD5133 /* View+MASAdditions.m */,
				E55EF1548B5FE8614B561B8D /* View+MASShorthandAdditions.h */,
				552FED5ABD2B35D8399833A5 /* ViewController+MASAdditions.h */,
				3F96F9AB4AE4A0A8F1516551 /* ViewController+MASAdditions.m */,
			);
			path = Masonry;
			sourceTree = "<group>";
		};
		98FC709B8134404FB43FA0AA /* NapNoticeOpticalSpaRetain */ = {
			isa = PBXGroup;
			children = (
				D860BF9B40C6F7AF0A7FF76C /* StorageNumberSmallestProjectLaw.h */,
				221B6924E8C79E1C3840FBD2 /* StorageNumberSmallestProjectLaw.m */,
			);
			path = NapNoticeOpticalSpaRetain;
			sourceTree = "<group>";
		};
		9B48B785A77EDA580B77BF69 /* Lossy */ = {
			isa = PBXGroup;
			children = (
				C462B961D67182D3EAECBE8F /* NSError+YoungestTwo.h */,
				1890CCEA0BC9A995E001FD0F /* NSError+YoungestTwo.m */,
			);
			path = Lossy;
			sourceTree = "<group>";
		};
		A1264E5B62429134224640A0 /* SaveFootnote */ = {
			isa = PBXGroup;
			children = (
				68F99AB8880BE0D4B84970A9 /* OwnCacheView.h */,
				4C22D2534E5464F3134F5372 /* OwnCacheView.m */,
				7571DC539A39E547A59B2A8A /* GainPassGetDogWindow.h */,
				C63AAB8834C0161F78761E1C /* GainPassGetDogWindow.m */,
			);
			path = SaveFootnote;
			sourceTree = "<group>";
		};
		A66460C7C1E4ACC39D22FB01 /* FactFlag */ = {
			isa = PBXGroup;
			children = (
				EE1675740439DB67E12DE0A5 /* BedMix */,
				5179DF0CB3ECCB37929FA847 /* DogHour */,
				D520C43BB795ED2162771AF2 /* PubOne */,
				DDC87059C251F9B4AFD7B320 /* BlobNap */,
				344DF5546264A1BDAF4FA253 /* MindLog */,
			);
			path = FactFlag;
			sourceTree = "<group>";
		};
		A725824B81EB7769EE590256 /* Lemma */ = {
			isa = PBXGroup;
			children = (
				3BD14C54B37A257233F8993C /* EncodeCupReplyLoadAdverbModel.h */,
				8D62382B1D39A1D14C60ACA6 /* EncodeCupReplyLoadAdverbModel.m */,
			);
			path = Lemma;
			sourceTree = "<group>";
		};
		A7BC7D37404F8C2BED3F87A9 /* SodiumBothDrum */ = {
			isa = PBXGroup;
			children = (
				B19FEED26F1FF046AF323554 /* ProjectsCapturedArtCommitRedTraveledViewController.h */,
				FDB00E0424953F1A2D040295 /* ProjectsCapturedArtCommitRedTraveledViewController.m */,
				A646A3D4F37C8070281414DC /* PickReason.h */,
				AA003FEBDC1C982818B996D8 /* PickReason.m */,
				E5E17E9C167143CE89EF377C /* PenGramLossZipButton.h */,
				4D23922F4F59E3B4A4DF4D6C /* PenGramLossZipButton.m */,
			);
			path = SodiumBothDrum;
			sourceTree = "<group>";
		};
		B3B5A2269D1009806CDC2B80 /* Core */ = {
			isa = PBXGroup;
			children = (
				FCB5DEA39FBEC3CDF6659529 /* NSButton+WebCache.h */,
				9578E6C06480B2755F8694A0 /* NSButton+WebCache.m */,
				5EB8AFFE87F67BA2683C8A6E /* NSData+ImageContentType.h */,
				7A9F1821FC35982A623A712D /* NSData+ImageContentType.m */,
				CFC99165FA77CB55E4951B41 /* NSImage+Compatibility.h */,
				4C16AFF3F87357A800EE5AE8 /* NSImage+Compatibility.m */,
				99442D6AB4024F590B88BD49 /* SDAnimatedImage.h */,
				770EA2FCD5B8C7B604FAE7EA /* SDAnimatedImage.m */,
				F357E3CB7A3362D73871A15E /* SDAnimatedImagePlayer.h */,
				3C959DF3D6D670D1F83CA529 /* SDAnimatedImagePlayer.m */,
				B10C9694BD7A5079EACF0AC9 /* SDAnimatedImageRep.h */,
				32D9B230616BC896AE5DE56B /* SDAnimatedImageRep.m */,
				B57936FCA6466C171E1128B4 /* SDAnimatedImageView.h */,
				141BB055207E906290B82B44 /* SDAnimatedImageView.m */,
				DCF147F50882ED0093013EA8 /* SDAnimatedImageView+WebCache.h */,
				36D71A4DD7F82151C06FDA0D /* SDAnimatedImageView+WebCache.m */,
				A92B489047F82A66200AC6C5 /* SDCallbackQueue.h */,
				91A6B4D732FB66EE5AE2FD94 /* SDCallbackQueue.m */,
				707D8F31DAD892B1A16330AF /* SDDiskCache.h */,
				BE33DA6A7BE4A3F40A9BD931 /* SDDiskCache.m */,
				CFC376449E0F9DCB227284D3 /* SDGraphicsImageRenderer.h */,
				FA9D86B65EF76E6378A194D2 /* SDGraphicsImageRenderer.m */,
				86CD3D05951C3A4E7D908BF9 /* SDImageAPNGCoder.h */,
				CC952C75D256C0C783C3CF4F /* SDImageAPNGCoder.m */,
				D1A538F3EEA1BD72C5FDE2B6 /* SDImageAWebPCoder.h */,
				C8469496DD5CEB580E8FA3FF /* SDImageAWebPCoder.m */,
				DC0AE235B834CECABDE27EA0 /* SDImageCache.h */,
				1615062F148B71D24026EAF8 /* SDImageCache.m */,
				50BF3455CE6E3F90858F857E /* SDImageCacheConfig.h */,
				8BE4130592E6B643550E2C23 /* SDImageCacheConfig.m */,
				D8298597DCE164D09A88C5EF /* SDImageCacheDefine.h */,
				FF52485AF11A7AB5B18D95BB /* SDImageCacheDefine.m */,
				15C01B225EDACCED1BBAABEA /* SDImageCachesManager.h */,
				EDA7FEDC61F457B3B0FD6162 /* SDImageCachesManager.m */,
				59699892DA8AF57D64019350 /* SDImageCoder.h */,
				BD4C585F9C5B2ED58A192339 /* SDImageCoder.m */,
				107BA7FE3943AB752CACE83C /* SDImageCoderHelper.h */,
				EE5EDFE175C4517CA55F2904 /* SDImageCoderHelper.m */,
				C2AA533DEA67A21AA2748EB1 /* SDImageCodersManager.h */,
				2AC5A65F6A32216AA89BC44C /* SDImageCodersManager.m */,
				7170C7C6D65FDDC9CAAAD5AB /* SDImageFrame.h */,
				79053BB02A0B74B839186E7B /* SDImageFrame.m */,
				1CBFA8CC4BAD5863F5E7533F /* SDImageGIFCoder.h */,
				797E4CC6DC8D4BFEFB3E5935 /* SDImageGIFCoder.m */,
				0A1C8E99CFB2C7D23E996FCC /* SDImageGraphics.h */,
				B84B651663E8083C40F98554 /* SDImageGraphics.m */,
				37F07D218BCECD95BAF4C614 /* SDImageHEICCoder.h */,
				43ECD26F92E5955DF3FB989A /* SDImageHEICCoder.m */,
				1EC4B504B74161DCD91901D5 /* SDImageIOAnimatedCoder.h */,
				4A43DAB7F738B58EB82095A3 /* SDImageIOAnimatedCoder.m */,
				FAB61A4666E33303B97B1251 /* SDImageIOCoder.h */,
				428A2AC8E86D38E09AA9799A /* SDImageIOCoder.m */,
				600A27E77E18A4C3D9C7108B /* SDImageLoader.h */,
				384391EB0CD3C9AE3CFFFFF8 /* SDImageLoader.m */,
				D705681A54AF9923B98266AB /* SDImageLoadersManager.h */,
				9CE50F1CFDF5F9A8AEA84D4A /* SDImageLoadersManager.m */,
				7864B500CE178DBDC117F26D /* SDImageTransformer.h */,
				81049B7C4C2ABF1CB69E8D02 /* SDImageTransformer.m */,
				F85D7C276EEBB7432213A19D /* SDMemoryCache.h */,
				08CDE16A6483EB1478F73093 /* SDMemoryCache.m */,
				93DAFF3667A5FD57CB164F7F /* SDWebImageCacheKeyFilter.h */,
				9B971DA05B7315364EE79A30 /* SDWebImageCacheKeyFilter.m */,
				F5BFCAD14F8CD43F9A74BC77 /* SDWebImageCacheSerializer.h */,
				D2D2835575B4775DE7C9D114 /* SDWebImageCacheSerializer.m */,
				0D6AC0851C1CD096814031EB /* SDWebImageCompat.h */,
				EB61C1C9A5433E887D3AB713 /* SDWebImageCompat.m */,
				B564459FAB6D12CE588DE24C /* SDWebImageDefine.h */,
				3494C66CB3AFF6B3B043E1FC /* SDWebImageDefine.m */,
				6E5A95B489F4EDF6FC75A31A /* SDWebImageDownloader.h */,
				122A67C2BDEC54D845556956 /* SDWebImageDownloader.m */,
				67D2F42472DB75D33504FE9A /* SDWebImageDownloaderConfig.h */,
				B79A424F120C5E282D19A13B /* SDWebImageDownloaderConfig.m */,
				4DBFD99311FF3E5515262D5E /* SDWebImageDownloaderDecryptor.h */,
				01743D9FFCB02DE46080FF8C /* SDWebImageDownloaderDecryptor.m */,
				2CE539E3628D113325CAB863 /* SDWebImageDownloaderOperation.h */,
				B9CDF03919FFAA5626F16554 /* SDWebImageDownloaderOperation.m */,
				3EC678791EFFEEEEA9587C4D /* SDWebImageDownloaderRequestModifier.h */,
				2E25D0CE0EEA78F54AFE1663 /* SDWebImageDownloaderRequestModifier.m */,
				D58DE27181558E81D87B10A5 /* SDWebImageDownloaderResponseModifier.h */,
				E39D3C020942D666B6351FE1 /* SDWebImageDownloaderResponseModifier.m */,
				F5E0E3218F91B623C041274E /* SDWebImageError.h */,
				6E83DCEDDBC6B3AA04820720 /* SDWebImageError.m */,
				507432B3255C38FAC0BF76FF /* SDWebImageIndicator.h */,
				19062D4FB001C0234B6B02F7 /* SDWebImageIndicator.m */,
				461D5508A8C868DF16263E99 /* SDWebImageManager.h */,
				D1540B45FEF89149AE4F1B2F /* SDWebImageManager.m */,
				C3A461F5584FF92273C0679D /* SDWebImageOperation.h */,
				B3EBA07E9160BBCF2E37D426 /* SDWebImageOperation.m */,
				2163A9B0B07800A693FB3932 /* SDWebImageOptionsProcessor.h */,
				7DA64A2E2C310776D852CBE7 /* SDWebImageOptionsProcessor.m */,
				B5584A8C09A481F7CD8B2F4A /* SDWebImagePrefetcher.h */,
				C4254BAF95A9B5C140E3E1AE /* SDWebImagePrefetcher.m */,
				3E861DE63A2BDA482F40C411 /* SDWebImageTransition.h */,
				635E90D7F86630F66D3F5992 /* SDWebImageTransition.m */,
				46D6DC94C1C447E77D6FBBBD /* UIButton+WebCache.h */,
				1ABBCE8B5A63BEB6F2A7EA58 /* UIButton+WebCache.m */,
				35F81EEAD28CF085ED745CA9 /* UIImage+ExtendedCacheData.h */,
				A462FA611F18A8120FBA57F6 /* UIImage+ExtendedCacheData.m */,
				4C59431ED98C4E0FB36F1F2B /* UIImage+ForceDecode.h */,
				90AB7847C37100FB54F5CC69 /* UIImage+ForceDecode.m */,
				F919006AC1E0B559ACA104F6 /* UIImage+GIF.h */,
				12BB866ADF7B159434867E4B /* UIImage+GIF.m */,
				F90E4C006A80A58F8DFF383E /* UIImage+MemoryCacheCost.h */,
				B91FB9ECE3911618FD468543 /* UIImage+MemoryCacheCost.m */,
				392513E943BF5672CCB28573 /* UIImage+Metadata.h */,
				05CB581F4DAB24A26D8426CD /* UIImage+Metadata.m */,
				0A08C08DBB2A0144153B5E46 /* UIImage+MultiFormat.h */,
				661A351485773CF6AE57F07A /* UIImage+MultiFormat.m */,
				5FCC6D7450B496DFB8008E81 /* UIImage+Transform.h */,
				9F102264BDA22857C9253CA6 /* UIImage+Transform.m */,
				4D88D026ED54FFBBED7B8467 /* UIImageView+HighlightedWebCache.h */,
				F1377CE9BCD5E119A62EBCBC /* UIImageView+HighlightedWebCache.m */,
				227BE6E008B57E34AB5FC3B8 /* UIImageView+WebCache.h */,
				0B8329775555BBCE91561D38 /* UIImageView+WebCache.m */,
				4FA9F8ACB87C036195633CE4 /* UIView+WebCache.h */,
				3F50C3FDDDA683A6949E470B /* UIView+WebCache.m */,
				619E970B0C355455C4769687 /* UIView+WebCacheOperation.h */,
				CB1DC3CF6991177FBD67D501 /* UIView+WebCacheOperation.m */,
				395C6211A3E356211127D17F /* UIView+WebCacheState.h */,
				B473112AE44CE908095B2087 /* UIView+WebCacheState.m */,
			);
			path = Core;
			sourceTree = "<group>";
		};
		B60641CC11A635AB0084A560 /* Private */ = {
			isa = PBXGroup;
			children = (
				6076A78D7212EC65375F807B /* NSBezierPath+SDRoundedCorners.h */,
				FBE1B7EA846AC338DF199043 /* NSBezierPath+SDRoundedCorners.m */,
				4F1C65776340C2F0646BCE2D /* SDAssociatedObject.h */,
				28C13AA26ADCAF466F6E9390 /* SDAssociatedObject.m */,
				D2B51A3B6F499C51FB0831F0 /* SDAsyncBlockOperation.h */,
				1897AD37369E10C5F04F10D3 /* SDAsyncBlockOperation.m */,
				C4B69F4CA964495D2C9F82BB /* SDDeviceHelper.h */,
				E22E0288486A9D780F35F40B /* SDDeviceHelper.m */,
				01806656D598C40508DAE16D /* SDDisplayLink.h */,
				1AE178CA2C98B55CC2D4774A /* SDDisplayLink.m */,
				0A1048A70CB191CD1389478E /* SDFileAttributeHelper.h */,
				5C84898857E25713DDC55B13 /* SDFileAttributeHelper.m */,
				F8EED95EB1B6ABF06B4FD3B6 /* SDImageAssetManager.h */,
				65B5EF40928913D2AF78EEE9 /* SDImageAssetManager.m */,
				B9BA07E83ABC4553BBEBF4DB /* SDImageCachesManagerOperation.h */,
				31A66A530DD3F35FBEFF3C27 /* SDImageCachesManagerOperation.m */,
				D5B796730577AFA814F904CF /* SDImageFramePool.h */,
				477991C0F34086DE614FF59D /* SDImageFramePool.m */,
				EE034196822ED15D8E97655D /* SDImageIOAnimatedCoderInternal.h */,
				1B568C272BB5025D3735CF20 /* SDInternalMacros.h */,
				477A16D57919C65EF81D07B8 /* SDInternalMacros.m */,
				4BA8066D6988B29D03C357D2 /* SDmetamacros.h */,
				5F94E754532B117A09000DD3 /* SDWeakProxy.h */,
				068874D3D2185C47352329BD /* SDWeakProxy.m */,
				8366574A0BA13B5C47A3750A /* SDWebImageTransitionInternal.h */,
				7A4DAE610336C2D92B286E13 /* UIColor+SDHexString.h */,
				F0D4904A04DFFB3A323BC7CC /* UIColor+SDHexString.m */,
			);
			path = Private;
			sourceTree = "<group>";
		};
		B62C7A4AF3BCC65D5918F020 /* OccurCar */ = {
			isa = PBXGroup;
			children = (
				44EA56130C211DB8938398B5 /* LogoSent.h */,
				50A435C06DC74595FDE21BFD /* LogoSent.m */,
				4F883BAEE6E33527749BBD56 /* FootballEnteredTextualPriceOperand */,
				70B829580080868406274398 /* DoubleExist */,
				C95891743D29883C59965846 /* MQTTClient */,
				151D473E2F50CE7AE3A19589 /* UsesOld */,
				7FF50560C199B6A0A55CA054 /* YellowBiotin */,
				6840779963900F0AA80D4AC0 /* SDWebImage */,
				949B700047A08B6A671F32D0 /* Masonry */,
				FA7A39E6E483AE3E5A2CDC26 /* Armenian */,
				84F741287C216951EC67DE71 /* BondUse */,
				98FC709B8134404FB43FA0AA /* NapNoticeOpticalSpaRetain */,
				FF88A68EBD984FE98C96DFBC /* UnwindInfo.h */,
				B1EB7EFC95DEB8B9B14C8EFC /* UnwindInfo.m */,
				FD5A648198380875F8C946CE /* BetterEyeIncrementLoopsSummaryTool.h */,
				E8DF6F211689C6D51E6A5D48 /* BetterEyeIncrementLoopsSummaryTool.m */,
			);
			path = OccurCar;
			sourceTree = "<group>";
		};
		BA6F233659B22CA7604B8181 /* DrainageCompetence */ = {
			isa = PBXGroup;
			children = (
				A66460C7C1E4ACC39D22FB01 /* FactFlag */,
				B62C7A4AF3BCC65D5918F020 /* OccurCar */,
				8AC3FE6B13D01C34584ED2BE /* LowerPen */,
			);
			path = DrainageCompetence;
			sourceTree = "<group>";
		};
		BC4CA71230767DE57D75D3FD /* EndsOpt */ = {
			isa = PBXGroup;
			children = (
				B7D590299D8F40FC329C1903 /* LogoHexBoxManager.h */,
				5E922E29381D5F76940D9276 /* LogoHexBoxManager.m */,
				CAA3D37C8160CA517438575E /* DayUpsidePutManager.h */,
				B8E78C2BABF6D0764E58CD8C /* DayUpsidePutManager.m */,
			);
			path = EndsOpt;
			sourceTree = "<group>";
		};
		C30879936D4BE54FD3FB1217 /* Handler */ = {
			isa = PBXGroup;
			children = (
				E70804B06044FB66B0087595 /* DrainageCompetence.h */,
				F0D5BA300A457906E68E2892 /* DrainageCompetence.m */,
				3F335B5A4FC2E0FC90F2F0F1 /* BrokenDid.h */,
				336D9CEB7F9D047368C4225E /* BrokenDid.m */,
			);
			path = Handler;
			sourceTree = "<group>";
		};
		C95891743D29883C59965846 /* MQTTClient */ = {
			isa = PBXGroup;
			children = (
				8A96A847DCEA35250F3E0226 /* ForegroundReconnection.h */,
				990BF4DCD6BDD350BA4C2451 /* ForegroundReconnection.m */,
				2EFF84FD243DB2AFD61E3601 /* GCDTimer.h */,
				27155D8DAAE120F9F17B664D /* GCDTimer.m */,
				E52570C6D7DAF7BDABF90CAF /* MQTTCFSocketDecoder.h */,
				997C5CF9977F3C3C161FF161 /* MQTTCFSocketDecoder.m */,
				FB6193C8671A84748D419B8D /* MQTTCFSocketEncoder.h */,
				033F67A0F052F8FD5680763A /* MQTTCFSocketEncoder.m */,
				A5D25ADB48A9BE1FB1AEE96C /* MQTTCFSocketTransport.h */,
				F20593849D706C33221490B1 /* MQTTCFSocketTransport.m */,
				CD0D8155B9C907236B96792A /* MQTTClient.h */,
				B5E9F2C4B7820624331E260A /* MQTTCoreDataPersistence.h */,
				A47D2222AEC3C8C1D637985A /* MQTTCoreDataPersistence.m */,
				80063A3BE279129E77B68CB7 /* MQTTDecoder.h */,
				B038B18BE450ABD3CAC5BDC3 /* MQTTDecoder.m */,
				7E4EFEF422510DE341EEDE09 /* MQTTInMemoryPersistence.h */,
				CE7C443D7E51F49506E7BF85 /* MQTTInMemoryPersistence.m */,
				A41A79A8ED3F2F815F7C1148 /* MQTTLog.h */,
				A8E58EED7D7BBF755A54F002 /* MQTTLog.m */,
				CC461ED6776ED50BBA883B5D /* MQTTMessage.h */,
				26367074D32D566AB6533174 /* MQTTMessage.m */,
				7E00617224339495E59B0951 /* MQTTPersistence.h */,
				29E260077ABAD4F5965CEF7A /* MQTTProperties.h */,
				C9B221D07953DB37AD8BD7A8 /* MQTTProperties.m */,
				2FB9AA68D226ED4B4328441A /* MQTTSession.h */,
				53E9FE8BCAF14467A0C54C2D /* MQTTSession.m */,
				5C58C2A2F29F4DBDDD244A65 /* MQTTSessionLegacy.h */,
				A167DA7DF6402395CDBAA033 /* MQTTSessionLegacy.m */,
				A19DCB32C93BB93869146621 /* MQTTSessionManager.h */,
				4D14F18C9AB27109CE7F0920 /* MQTTSessionManager.m */,
				9403E740B0E24B8CA0E11F19 /* MQTTSessionSynchron.h */,
				64D0438572649BF01494ECA6 /* MQTTSessionSynchron.m */,
				B61192AE53DF957EE7E5D4E7 /* MQTTSSLSecurityPolicy.h */,
				A1A6A01995B67A2FD246044C /* MQTTSSLSecurityPolicy.m */,
				0004C502A2093E3EA9DECA5E /* MQTTSSLSecurityPolicyDecoder.h */,
				4C35DA85AA4B46ADB67714C9 /* MQTTSSLSecurityPolicyDecoder.m */,
				C59A11227CF2E475F839D228 /* MQTTSSLSecurityPolicyEncoder.h */,
				789ABCAAE7DBC3F2A5C8780D /* MQTTSSLSecurityPolicyEncoder.m */,
				D6ECB86D21A459FB23D76612 /* MQTTSSLSecurityPolicyTransport.h */,
				F5BB5BD4EEB0BF69DBAE7BB5 /* MQTTSSLSecurityPolicyTransport.m */,
				11331CA51275FA728B67D9B5 /* MQTTStrict.h */,
				0F354942B76530367F8F6DD4 /* MQTTStrict.m */,
				51595B2BCF7B15480E544D73 /* MQTTTransportProtocol.h */,
				1F3FC073F02A3EC01A078132 /* MQTTTransportProtocol.m */,
				E94FB146C5FE700D7E2AC820 /* ReconnectTimer.h */,
				0452D1175FF7F539A2AC3883 /* ReconnectTimer.m */,
			);
			path = MQTTClient;
			sourceTree = "<group>";
		};
		D1CD7F67BA6F0E12DBA086CE /* Wrap */ = {
			isa = PBXGroup;
			children = (
				3C1A688265D8A6E98A1346BB /* TapCardMenArtsViewController.h */,
				0B8EB28663F6E7429D894D75 /* TapCardMenArtsViewController.m */,
				AE03CE9BCBFE592F547AD01C /* RealTryViewController.h */,
				71A835FC472D9461C6D15E07 /* RealTryViewController.m */,
				71A77F9FE801D14D215AA88C /* HoldBigBoxNapController.h */,
				A304BB7F1BA5FE9E4DEA5799 /* HoldBigBoxNapController.m */,
				0B3D87BBC7FB574B52AFF2EB /* TorchGainManager.h */,
				AACEE6ADCE2BDE4CF78249C0 /* TorchGainManager.m */,
				3F50383FA0D0A86FF62AADA1 /* BurmeseZone.h */,
				B280F071075333FF1E09DB93 /* BurmeseZone.m */,
				ED41E2FB31B281B8349E222E /* LessRaceProtocol.h */,
				ADB886296A6C372EA9249768 /* TurnItemOldDog.h */,
				F8A1311E6F3F85EB46AE22D2 /* TurnItemOldDog.m */,
				5B2D01B7123CC70085061273 /* ItalianLog.h */,
				65457C6C6C6C1C847438ECE9 /* ItalianLog.m */,
			);
			path = Wrap;
			sourceTree = "<group>";
		};
		D520C43BB795ED2162771AF2 /* PubOne */ = {
			isa = PBXGroup;
			children = (
				D67931A14591FB66450945A2 /* Add */,
				5D5B66E6E7575748266A21A3 /* Due */,
			);
			path = PubOne;
			sourceTree = "<group>";
		};
		D67931A14591FB66450945A2 /* Add */ = {
			isa = PBXGroup;
			children = (
				D5686184C9322B36826099F2 /* HailSheCanadianLooperBefore.h */,
				331421D88F3587D3496C4583 /* HailSheCanadianLooperBefore.m */,
				CEA0ADC3A77AC7590FFBECA2 /* AwayFullySpa.h */,
				D99B852B6FC88CD752CC27D9 /* AwayFullySpa.m */,
				D37DBC6F6601E02448363C39 /* ArbiterLocal.h */,
				1179D1421A8477D0A4BD88B6 /* ArbiterLocal.m */,
				51EC80BB45CD1E9E2865E2ED /* MessagingInfo.h */,
				695822313419CB64229FF8DE /* MessagingInfo.m */,
				CFE42970522065E9097F27CA /* SurgeBreakStay.h */,
				C2578D8E772EE6FEFDDD005F /* SurgeBreakStay.m */,
				D9072A9CE4C558909BC257B2 /* KilobitsMenGallonWorkoutsDueSpherical.h */,
				C711260E44B4354ABC36FF4D /* KilobitsMenGallonWorkoutsDueSpherical.m */,
				F5C2783E04C75E202B32D6AD /* HyphenPhone.h */,
				6A6EE765D0CAE0631BC80AE4 /* HyphenPhone.m */,
			);
			path = Add;
			sourceTree = "<group>";
		};
		DB8CC40D428F66CBC496F755 /* Swap */ = {
			isa = PBXGroup;
			children = (
				A7BC7D37404F8C2BED3F87A9 /* SodiumBothDrum */,
				A1264E5B62429134224640A0 /* SaveFootnote */,
				F85D4F668C609F2866DC5B2B /* IllProxyTry */,
				EBB51D381389A2A641BB2B31 /* ButAlertView.h */,
				64BF842715FBF1E2431B77DC /* ButAlertView.m */,
				D0F596A92578CF0DD8E19C46 /* XXGProtocolLabel.h */,
				861309CBCC40A4A5E652B2EB /* XXGProtocolLabel.m */,
				8A3AFFA97A8D35385FF2099F /* MoreVisualView.h */,
				C0D05E2E15455D4F0C4BBC3E /* MoreVisualView.m */,
				17688073E7AE82A23F5D9815 /* BinLawParseThickMixCell.h */,
				3ABF26898702C46C9172EFFD /* BinLawParseThickMixCell.m */,
				81CE51318C9F978FC9A0D054 /* BuddyIndigoButton.h */,
				6CBEC61C36EC7E2818303777 /* BuddyIndigoButton.m */,
				671FEF740ED11C313579071A /* AndToast.h */,
				BA425AE293E3BF6D1939E830 /* AndToast.m */,
				E137011340E6224B0444D1F8 /* ThirdCivilCell.h */,
				8A445C7C5D3F63F98CC35A17 /* ThirdCivilCell.m */,
				D787C0D5D22457B253F60AAD /* PolishFaxTextField.h */,
				CBB61A77AA19B32692D9DECD /* PolishFaxTextField.m */,
			);
			path = Swap;
			sourceTree = "<group>";
		};
		DBD1B642AA0024576AD07D90 /* TeamLessCopper */ = {
			isa = PBXGroup;
			children = (
				56000E352DFC7DF568B786BF /* PasswordsViewController.h */,
				BD11F818CE698B2FDD21FA07 /* PasswordsViewController.m */,
				58DAFC70F7CC443A9185E72C /* OptShowDryViewController.h */,
				07992C3DCB152FA6DBFC4F3A /* OptShowDryViewController.m */,
				9CB1C1A76C4274A79EA46856 /* InheritedViewController.h */,
				BA89D2A090F394B409558A23 /* InheritedViewController.m */,
				7854180F821EDEB33E03A7E3 /* MergeNameViewController.h */,
				86DC34F49F08A98926C00BA3 /* MergeNameViewController.m */,
				301DBB4D473811DD79AAC0D1 /* TabNumberRevisionMegahertzPasteViewController.h */,
				5EF79911837971D4166F0BB5 /* TabNumberRevisionMegahertzPasteViewController.m */,
				0ADB12C79A21A6C2F5694F6F /* StoneDustViewController.h */,
				C0F6B3C9B9D02E70A69C2205 /* StoneDustViewController.m */,
				147BD20BFECC94D41D7F0256 /* ReadyHeadViewController.h */,
				719CF62CFCE6532DE861AF5B /* ReadyHeadViewController.m */,
				0A972F5AE62B93436B077CD2 /* AllocateHeavyViewController.h */,
				30C89C89BAB22E6341A65554 /* AllocateHeavyViewController.m */,
				C9176FE819F488CEC97A1E38 /* LossWayDiskViewController.h */,
				B6F596BCA482257AEA47B356 /* LossWayDiskViewController.m */,
				78CD62031381714D0BB7365B /* DryTapParseViewController.h */,
				249B76B85084427F31494526 /* DryTapParseViewController.m */,
				4F16B6C98C67CF40BFAB0CD6 /* ClickTokenViewController.h */,
				88D1429EB67A73102B4CDEEC /* ClickTokenViewController.m */,
				D49BE0E05EBD2856C0520098 /* PathPeakZipAllViewController.h */,
				3A88F677F2E78FADE43EF5FA /* PathPeakZipAllViewController.m */,
				B2FE53FBC85EBA73B8B18E55 /* TextRetOldEggViewController.h */,
				2BF27640E3B9EE27FC013493 /* TextRetOldEggViewController.m */,
				B7E9C6DCBE31069AF0FCEA75 /* TheDarkArmViewController.h */,
				134F2515410B392ADD6F342A /* TheDarkArmViewController.m */,
			);
			path = TeamLessCopper;
			sourceTree = "<group>";
		};
		DDC87059C251F9B4AFD7B320 /* BlobNap */ = {
			isa = PBXGroup;
			children = (
				50B9E8E4E9D5A3F6FE2BBCCD /* UplinkDeep.h */,
				705A64CB056676C178A88DE7 /* UplinkDeep.m */,
				A0F68E19E078298D207D9DEA /* TenStartupModel.h */,
				E34CAA9B03D2A0E183EEFCAD /* TenStartupModel.m */,
				08B44FE7C02B2D22A211BE69 /* SexNetwork.h */,
				D5E8309DB0D0912DDBB24D79 /* SexNetwork.m */,
				7D3424BB6E1EA47FA8A44952 /* ForwardWetList.h */,
				47B1CC86E88CD9959D89421F /* ForwardWetList.m */,
			);
			path = BlobNap;
			sourceTree = "<group>";
		};
		E70B7E517CF1BEC8E9BAEB57 /* Russian */ = {
			isa = PBXGroup;
			children = (
				1CE215362510A2169523CD75 /* BinRebusBody.h */,
				1FEF375A81D83837C0510496 /* BinRebusBody.m */,
				76ACC0A56E5BC606D9211E80 /* MiddleHue.h */,
				67939FA9819D474BB544EC20 /* MiddleHue.m */,
			);
			path = Russian;
			sourceTree = "<group>";
		};
		EE1675740439DB67E12DE0A5 /* BedMix */ = {
			isa = PBXGroup;
			children = (
				E70B7E517CF1BEC8E9BAEB57 /* Russian */,
				C30879936D4BE54FD3FB1217 /* Handler */,
				016A69C980537C998C19F6BC /* OldestLink.h */,
				CD0780FF36FF53437C734C06 /* OldestLink.m */,
				629EB1FEE21834C1BB2583D4 /* ChatMidProtocol.h */,
			);
			path = BedMix;
			sourceTree = "<group>";
		};
		F85D4F668C609F2866DC5B2B /* IllProxyTry */ = {
			isa = PBXGroup;
			children = (
				563D6A4F6886020425E107C1 /* SchemesCapView.h */,
				E83AF3B99842C9553798BC43 /* SchemesCapView.m */,
				12F1A6F272FA259940309903 /* ManAllMildCaseCell.h */,
				6C5850D3CE95995E6A60248A /* ManAllMildCaseCell.m */,
				AA177D0A45478CDA5E870059 /* ShortHoverWide.h */,
				99AF8ADF4F9ECD5C97754594 /* ShortHoverWide.m */,
				4537D4140214C7B34392810F /* DashSawMenSkinCell.h */,
				80B865C7B7DD3F19B3F3566F /* DashSawMenSkinCell.m */,
			);
			path = IllProxyTry;
			sourceTree = "<group>";
		};
		FA7A39E6E483AE3E5A2CDC26 /* Armenian */ = {
			isa = PBXGroup;
			children = (
				E56DEDF9A2525667D5A033E5 /* NSObject+PinModel.h */,
				8A93DDB3A534F8FF14734D97 /* NSObject+PinModel.m */,
				22A8FADB8B39C9A32423869D /* NSString+DirtyFoot.h */,
				D393779C17E505BA5599C6F9 /* NSString+DirtyFoot.m */,
				B456BA7BDC7AA9AD448685A4 /* UIColor+JobColor.h */,
				BAA72DE67CE53E709627831A /* UIColor+JobColor.m */,
				EAEE822732B22A93B6D60F71 /* UIViewController+DueViewController.h */,
				214E31A52D5FCEB4A06D72D6 /* UIViewController+DueViewController.m */,
				C3051CC331618DAB930D6A21 /* UIImage+YetImage.h */,
				4E40A90867F47FB556496429 /* UIImage+YetImage.m */,
				8B1298CB6CAA81B5C3773D19 /* NSString+ReversesAsk.h */,
				D3B2EC10DE17B83C3B30B81C /* NSString+ReversesAsk.m */,
				025D4EA91DD0D6615AC9AC66 /* NSObject+MindStickySpatialHelloBox.h */,
				4E370C834396373B2EA33F90 /* NSObject+MindStickySpatialHelloBox.m */,
				579D1DB8DAF20C7DDA17DC1B /* NSURL+PaletteBad.h */,
				FDE6A5CB59D029DA0E715B3D /* NSURL+PaletteBad.m */,
				55BEB991BEDB441263A2CE05 /* NSString+CutTen.h */,
				E3F5C8B6A36B0A109CD60792 /* NSString+CutTen.m */,
				A121C4125213A403343D91AC /* NSData+CropSum.h */,
				212616CEF8D60226BF63AFFA /* NSData+CropSum.m */,
				FCA3611062DE86D8C4D97356 /* UIDevice+TapDevice.h */,
				127B9E35D5DB185E9B356DB4 /* UIDevice+TapDevice.m */,
			);
			path = Armenian;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		D3FCFC824E502950E75EE5EC /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				004992F8C46044A6E0218E49 /* TenStartupModel.h in Headers */,
				6C979E44214FD2D2168EF54F /* IllDayBodyRule.h in Headers */,
				F8241DBF45EDC77DBEB8B051 /* BezelItsManager.h in Headers */,
				6C69CAADA794547ADB9CF85D /* ProjectsCapturedArtCommitRedTraveledViewController.h in Headers */,
				C07D69AD6421F5884AA2D845 /* TurnItemOldDog.h in Headers */,
				0FF47C0DDF52328BB16B41CD /* TabNumberRevisionMegahertzPasteViewController.h in Headers */,
				75D4D7712D86D60A6F871BB4 /* PathPeakZipAllViewController.h in Headers */,
				E67968504BA23D9456704AF6 /* NSString+CutTen.h in Headers */,
				F0C15B3F16243521699E9EEA /* BinLawParseThickMixCell.h in Headers */,
				20F10E5E6675608EA406DDCB /* ArbiterLocal.h in Headers */,
				D9C91BD81E9AC77670574F20 /* UniformFlash.h in Headers */,
				A5AF2B021C4AF6F7576E717F /* DateInvertInfo.h in Headers */,
				ECEEC1C5A5DA76DA8DE6F280 /* Recovery.h in Headers */,
				F7427DD9C96C15053C1FCEB1 /* ThirdCivilCell.h in Headers */,
				E6F2DC8FC9B95BA0328A72DF /* ButAlertView.h in Headers */,
				767CB79A118BDCFB3847E0D1 /* FilterDigestPreviewsMomentLose.h in Headers */,
				A87F2139840057AC611F80EC /* UIViewController+DueViewController.h in Headers */,
				620D63201039A0F59A4BBFCA /* LinearManager.h in Headers */,
				79A67F799BBD296323E2BDB8 /* LossWayDiskViewController.h in Headers */,
				CE2FF85631A3C97AAE2F2047 /* AndToast.h in Headers */,
				9CB4DD285F363EAE1ADBFA4F /* NSString+DirtyFoot.h in Headers */,
				C60EB0C623A0F1B93F09FFC7 /* NSObject+PinModel.h in Headers */,
				BDE8C94F65AF12EBF0BD58AB /* PinPartModel.h in Headers */,
				F249ABC18F9C4F55698F635D /* MessagingInfo.h in Headers */,
				B74FAC80DE003A5654334CD1 /* ForwardWetList.h in Headers */,
				3595EFFBAE0B96E8127CF122 /* UseEditorInfo.h in Headers */,
				ADBEA6D3A3B29C4A2DAA268E /* StorageNumberSmallestProjectLaw.h in Headers */,
				3494358BEC07AC17CBED6304 /* NSObject+MindStickySpatialHelloBox.h in Headers */,
				C742F534811ADF6DDC8B0E66 /* DrainageCompetence.h in Headers */,
				3AD31B5A4FDEA85632371472 /* RegisterColor.h in Headers */,
				93FFFE3355B2607F7F9A96F2 /* HoldBigBoxNapController.h in Headers */,
				1EB0517B87FF755283A0F26F /* UnwindInfo.h in Headers */,
				0CD3DD885486906F76D4DA4A /* WayTipManager.h in Headers */,
				4993CEE69EC746952931CADF /* TapDenseManager.h in Headers */,
				14B20922F1E79D33F0522765 /* BetterEyeIncrementLoopsSummaryTool.h in Headers */,
				D16F9F4C3452BC47B97043AE /* EggReuseRawEra.h in Headers */,
				68A78BACCD1D2E905BC80F23 /* ItalianLog.h in Headers */,
				7389E044544E949F3EF51676 /* DryTapParseViewController.h in Headers */,
				A2E5BD3C97B340B737CC15C4 /* LinkOnlyHowNearbyAvailable.h in Headers */,
				926CF881B8BBD465BA07B157 /* OldestLink.h in Headers */,
				16FB8B6E0AE1B7B79BA3A5B6 /* AnswerConfig.h in Headers */,
				BC318E08206CA3009472A422 /* RightManager.h in Headers */,
				D73C6E24CF32FD744E886DC7 /* LogoHexBoxManager.h in Headers */,
				D5C71BAA22AC1D5DB8DE0BB9 /* GuideStand.h in Headers */,
				B2D8B6E4057DC0BE198978CC /* DayUpsidePutManager.h in Headers */,
				D27928BF03238DCEBE8FDBD1 /* EncodeCupReplyLoadAdverbModel.h in Headers */,
				A65AE20687E2FB573B2BF9AB /* SignalLowProtocol.h in Headers */,
				19D25C663DE3BC38B9FCFE20 /* NSError+YoungestTwo.h in Headers */,
				0194AFB3CFC6DE4595CC0400 /* TapForHasRenew.h in Headers */,
				F66A9383CD2158654BDCF737 /* PasswordsViewController.h in Headers */,
				975AD35DE09DBBDF0E553D8A /* TorchGainManager.h in Headers */,
				7AD78C5A55B5745CBC2CDBD4 /* UIColor+JobColor.h in Headers */,
				BDCBE38BC940EA97FACD04C5 /* RetryAwakeSentencesBarsAll.h in Headers */,
				9D681B2A9669E65B0869CB44 /* ReadyHeadViewController.h in Headers */,
				A52AFA42435E4D2FB577BE34 /* TapForHasRenew+QuitAcute.h in Headers */,
				F622A8ECD857DC36C0560B8D /* RestInferiors.h in Headers */,
				7A5E57749931C1352047753C /* CharShiftDark.h in Headers */,
				552D582FC0A85F8D0BE5C7F5 /* OptPaceSuchAction.h in Headers */,
				6CACD27CA00FAA7690C9AFD0 /* InsetTowerConfig.h in Headers */,
				A3EE8DE4FD313F306957A169 /* MoreVisualView.h in Headers */,
				41D84487FD7919A99F66E261 /* UIColor+SDHexString.h in Headers */,
				634447714772958696BB2AAE /* LogoSent.h in Headers */,
				5E5EC9419627988BC677F964 /* SDImageIOCoder.h in Headers */,
				6F454CA909E992FC7BC27147 /* HailSheCanadianLooperBefore.h in Headers */,
				BCC2AC11AE2CEB43417CB3E2 /* SDFileAttributeHelper.h in Headers */,
				837C4DA0EB7B476C5F3B6E25 /* SDWebImagePrefetcher.h in Headers */,
				AB9762F5E2FB1819BF4A5F77 /* SDAnimatedImageView.h in Headers */,
				BCE42FF05F2CE652563126C9 /* NSButton+WebCache.h in Headers */,
				E751BC830765F74AD230F4CB /* DepthRootViewController.h in Headers */,
				B3E1F0A1E46B6E52DE5716F0 /* SDImageHEICCoder.h in Headers */,
				DAC762087794AF29C483F17D /* BuddyIndigoButton.h in Headers */,
				EE59F3B0223F989B4795F68C /* ButColorViewController.h in Headers */,
				51E004BFB8117638A792FF37 /* SDImageIOAnimatedCoder.h in Headers */,
				EBCF1FE80374332D26167353 /* SDImageCoderHelper.h in Headers */,
				024F008A387BE8E59E99360E /* SDAnimatedImagePlayer.h in Headers */,
				5D7AFF8C1331ACC9650C87BD /* ViewDayCloudInfo.h in Headers */,
				D374EAC77A3A399B88866D26 /* UIView+WebCache.h in Headers */,
				29DBAC86F330C8FE91AFA917 /* LessRaceProtocol.h in Headers */,
				1F7D575CB11DE963E1991F68 /* XXGProtocolLabel.h in Headers */,
				427D1F638F09434C22ADF85A /* SlowDetails.h in Headers */,
				D345E2D32D74C7EA497B7B38 /* SDWebImageOptionsProcessor.h in Headers */,
				954252D291BC4483636C390D /* SDWebImageTransitionInternal.h in Headers */,
				3DC68219FF03D1143858E18F /* SDWeakProxy.h in Headers */,
				FBEEE1CB33396627149CEAFE /* FadeEarManager.h in Headers */,
				A64C97CD05E34D86F5D1F671 /* SDWebImageCompat.h in Headers */,
				AD208B4B141BEA907B9ADA2E /* SDCallbackQueue.h in Headers */,
				95F874678802A3BD1924E8F0 /* SDmetamacros.h in Headers */,
				A9136E2FD4433176E42341CB /* UIImage+Transform.h in Headers */,
				E61FC178461A957E9CA1B61B /* UIImage+MultiFormat.h in Headers */,
				5F29BD6B62651075C399220B /* SDImageAssetManager.h in Headers */,
				537B99887E92975407DD9572 /* SDImageCachesManagerOperation.h in Headers */,
				E0C0A240D9F618D44D32949C /* RegionExposureInfo.h in Headers */,
				950554C5861BAED404106E74 /* UIImageView+HighlightedWebCache.h in Headers */,
				86064C21BDAE40FCC6CB0B0D /* SDWebImageDownloaderOperation.h in Headers */,
				F117C8E001DE95EAD3499093 /* SDWebImageDownloaderDecryptor.h in Headers */,
				2169F9AE990D6A39EB30749F /* InheritedViewController.h in Headers */,
				1D4E669C85A1EB5628FF26F2 /* SDImageAWebPCoder.h in Headers */,
				D1213B5E3C5A9538D0035EC2 /* SDInternalMacros.h in Headers */,
				E17F92FFA8FBB5D286619018 /* SDAnimatedImageView+WebCache.h in Headers */,
				A0150F0943740AC8989B481F /* SDImageCoder.h in Headers */,
				618FDE21D706075482C8BB19 /* SDWebImageCacheSerializer.h in Headers */,
				CAD99436D7F528BD7C1FC77C /* SDImageLoadersManager.h in Headers */,
				F0BBCE85E1B214D2EF3F2E98 /* SubBarrierViewController.h in Headers */,
				B590249F16459846A7BBC862 /* TimeQueueManager.h in Headers */,
				20AAFEC59FA67C474554A4B3 /* DashSawMenSkinCell.h in Headers */,
				BE6FFAD40A86C8AF7C7D0F98 /* SDDisplayLink.h in Headers */,
				7D4001CF1F211D0D2F562C0D /* HavePlusWhoManager.h in Headers */,
				56C72AE88C8C8671153FBE2D /* TheDarkArmViewController.h in Headers */,
				DD6ECDC2E58D751380460C3B /* SDWebImageIndicator.h in Headers */,
				338ED543808EDDC6F64CF209 /* SDWebImageDownloader.h in Headers */,
				27198752C999BD4CE988505F /* ClickTokenViewController.h in Headers */,
				92D6F803F2B4FE1A8A9A4528 /* SDAnimatedImageRep.h in Headers */,
				374F71824E57DC716C2FE452 /* SDImageCacheConfig.h in Headers */,
				CD244AF0ECBCEA6B2FCDC83B /* SDImageFramePool.h in Headers */,
				099B6F1A9D4853518BDF2F02 /* AgeHardSentinelFaxAgeDiacritic.h in Headers */,
				335DE796147EBAF35CF6E2AA /* DiacriticVital.h in Headers */,
				64B322F7CC12DF6AF0ECC90B /* HertzViewController.h in Headers */,
				888A41C11F8C0C4C5FC1C88D /* JobJapanese.h in Headers */,
				9B96F742266716846234AD2C /* AbsoluteTriggerMonitoredSensitiveLocalizes.h in Headers */,
				5B4633C69BBEE214EB8379E7 /* Color.h in Headers */,
				9F0AC3B3C033C6F88D13966E /* IdentifyToneAcrossRegistryOccur.h in Headers */,
				F013FA49FF86BA94CCEE19C0 /* ExcludeSlideExtendsSpaLongest.h in Headers */,
				C8D4B58D72BDCB8F3BF19770 /* SDImageGraphics.h in Headers */,
				FE3E5A24F872165D75022FCA /* NSData+CropSum.h in Headers */,
				09CCF99EC59546ADE48E3EAF /* DarkerButPlusReportStrategy.h in Headers */,
				F123730482C03519A37865BE /* UIImage+GIF.h in Headers */,
				3D446B0B9296637FC5A5EE41 /* AwayFullySpa.h in Headers */,
				45CB7677B6AD33BD9EA61AC5 /* SDAsyncBlockOperation.h in Headers */,
				5FF24A54E7D290436399D6DA /* SDImageCacheDefine.h in Headers */,
				64AE7FC24CBED5B5761D5E5D /* SDWebImageDownloaderResponseModifier.h in Headers */,
				1B657304D7DBC9F2A54532BA /* UIView+WebCacheOperation.h in Headers */,
				89F493491F87E70C45B187DD /* SDWebImageOperation.h in Headers */,
				980804CEFFB36C10D1DA5FE5 /* ModelHexModel.h in Headers */,
				010DE514579BD0515A1E7141 /* OptShowDryViewController.h in Headers */,
				A8E118EE635A51FF0237806B /* SDGraphicsImageRenderer.h in Headers */,
				9CF1A902D3F19D831E459391 /* TapForHasRenew+Total.h in Headers */,
				FFC0BD3B823E225611ECF9FD /* BagCheckoutNarrativeArmGain.h in Headers */,
				28ED03D440BF7ED2EBBC7222 /* TurnGenericManager.h in Headers */,
				30F2B1E45F5F652C8EA384F6 /* PenGramLossZipButton.h in Headers */,
				99DDF12720BBF6BD6654EA2F /* SDImageLoader.h in Headers */,
				90AA7B47DEC47AD341688690 /* UIImage+Metadata.h in Headers */,
				74918C378CE04F9052136E70 /* TextRetOldEggViewController.h in Headers */,
				E2D1B86E18119F663DE684F7 /* FourMinFoundModel.h in Headers */,
				B4FCD656A0B56D961A19F85A /* SDImageGIFCoder.h in Headers */,
				27ECDFCEEF79B3BBE4E55200 /* UIImage+ForceDecode.h in Headers */,
				576EA78FFDDC9BFC29126B51 /* ManAllMildCaseCell.h in Headers */,
				0958F4AC58B7295BBC4A54F0 /* ShortHoverWide.h in Headers */,
				346716248BA129E598129BB5 /* SDImageFrame.h in Headers */,
				5E469FD31C6B7A0621B6462B /* UIImage+YetImage.h in Headers */,
				518950E76A20566994CCA0D6 /* MQTTCoreDataPersistence.h in Headers */,
				1AB863F62AC08D6BDFDBEAED /* MQTTSessionSynchron.h in Headers */,
				15F6E0A67C95E4F0982B08DA /* MQTTSSLSecurityPolicy.h in Headers */,
				540A10DCB48AA5A4AA91A2A3 /* MQTTPersistence.h in Headers */,
				7A1FA3F2769C9ED69A22EF4E /* ReconnectTimer.h in Headers */,
				FCDFBC4531CDEFCE06E49E98 /* MQTTSSLSecurityPolicyEncoder.h in Headers */,
				ADC3271AAD878AC709468FCF /* MQTTSSLSecurityPolicyTransport.h in Headers */,
				0E37E95CB61BB40CD718632F /* MQTTProperties.h in Headers */,
				ECE21501E6026E53B705C2D0 /* NSURL+PaletteBad.h in Headers */,
				63985111A732546BF8FEDE81 /* MQTTStrict.h in Headers */,
				D03EDC57DB1584084D881647 /* MQTTLog.h in Headers */,
				111888313EA32D75BB0D90C5 /* ForegroundReconnection.h in Headers */,
				3E78C0D7DD8DD09ACC250E91 /* MQTTMessage.h in Headers */,
				D6DE0C5DA09B817EDAFFCD1B /* MQTTInMemoryPersistence.h in Headers */,
				D503989C91841CDA9B269427 /* MQTTDecoder.h in Headers */,
				1FE2B4B6EB667560A1A97C10 /* TerabytesMapManager.h in Headers */,
				CA2F9F8D531E251800B9AA80 /* GainPassGetDogWindow.h in Headers */,
				B4C5117DB496217BC94E071C /* GCDTimer.h in Headers */,
				1B5B134ACE3BF77F3C8EFB30 /* MQTTCFSocketEncoder.h in Headers */,
				EFC3BB23D63FDD45F234CFDA /* MQTTCFSocketTransport.h in Headers */,
				E2AEFCAB54E3000BB762B98E /* UIDevice+TapDevice.h in Headers */,
				68385A102FF10764E6F31E17 /* MQTTTransportProtocol.h in Headers */,
				D02DE54F689F63D5A307F0E3 /* MQTTClient.h in Headers */,
				D0BF67C85B8E42D930ADD753 /* MQTTSessionLegacy.h in Headers */,
				B4457112AF011B9DE930785C /* MQTTSession.h in Headers */,
				DCEBC8455A348DFAEB62CB4D /* MQTTSSLSecurityPolicyDecoder.h in Headers */,
				EDA588CFC9C41A54F3528DC8 /* MQTTCFSocketDecoder.h in Headers */,
				0DC61F93D52046DE02F32032 /* MQTTSessionManager.h in Headers */,
				8F574C1145436CF6D32C76F3 /* BurmeseZone.h in Headers */,
				1F8AA79007210BF92B5C86C8 /* NSImage+Compatibility.h in Headers */,
				2C2F59FE0B13B6E219F4EE18 /* SDAnimatedImage.h in Headers */,
				08DB5AAC1E9FF0E9A463A5CF /* SDWebImageManager.h in Headers */,
				71E145D21C04466DCE08F3DC /* PolishFaxTextField.h in Headers */,
				4B904F9F5455C1C5DADBEB50 /* TapForHasRenew+PopRed.h in Headers */,
				0F3EB36B380A509387326709 /* NSData+ImageContentType.h in Headers */,
				9A92834EBC96F713A1199148 /* SDWebImageDefine.h in Headers */,
				994817080CFD95CF32B97626 /* UIView+WebCacheState.h in Headers */,
				A28D514B228468C808827E72 /* SDImageTransformer.h in Headers */,
				0CA44CE636D333123E615697 /* SDDiskCache.h in Headers */,
				3C5AC37EE64412FB6FA4ECC5 /* StoneDustViewController.h in Headers */,
				3C7A15BCDAAC548FC744DDF5 /* SDAssociatedObject.h in Headers */,
				4D6FF90E7CBD29FA3B0ABE1B /* SDImageIOAnimatedCoderInternal.h in Headers */,
				4988A84B37A59B0F3F0A3CEF /* ChatMidProtocol.h in Headers */,
				40546FD8EA8DD0F9655385D8 /* NSString+ReversesAsk.h in Headers */,
				2E40D3843E73B6C038808CC2 /* SDDeviceHelper.h in Headers */,
				C59BCD070FF9F9335CEF0A00 /* SDImageCodersManager.h in Headers */,
				4AEB228E8D8B73427EA1B65B /* SDWebImageDownloaderConfig.h in Headers */,
				26718807568F09EF9978C68A /* DanceMidHisManager.h in Headers */,
				45ADED2116875FC725D5295F /* HyphenPhone.h in Headers */,
				6FF6BABE0E7A8709AB4A68C7 /* UIImage+ExtendedCacheData.h in Headers */,
				E23E131955D7C68F5F581FC5 /* UIImageView+WebCache.h in Headers */,
				2F169AFD22A63A983A64F1CE /* SDWebImageError.h in Headers */,
				638235AD6D8A8E4293470022 /* SDImageAPNGCoder.h in Headers */,
				F6C1D4A9518AF5B345B3A38D /* SDWebImageCacheKeyFilter.h in Headers */,
				58E06C41D673C5B8A1FDE442 /* MergeNameViewController.h in Headers */,
				47F622E2803F962F53F3E037 /* SchemesCapView.h in Headers */,
				D269585C595C747CCF13B80A /* UIImage+MemoryCacheCost.h in Headers */,
				DA6A6319E781624C02FF5306 /* SDWebImageDownloaderRequestModifier.h in Headers */,
				829D34F86D4B42B06915DC2C /* SDImageCache.h in Headers */,
				18248CD5A29C137486E5C696 /* SDMemoryCache.h in Headers */,
				8407C2475C202F42BCB3287D /* NSBezierPath+SDRoundedCorners.h in Headers */,
				8A44965CD9136F081D4C1322 /* UIButton+WebCache.h in Headers */,
				F674340994D646CFEAF91F7B /* ArraySawSequencerReceiveCutPacket.h in Headers */,
				E500CD643BC7ED60A22B5D14 /* SDWebImageTransition.h in Headers */,
				5A5D5FD26EF6866A6C09DA4D /* SDImageCachesManager.h in Headers */,
				BDC406D604C493C805D5B2AA /* UplinkDeep.h in Headers */,
				98A51EBD3DB1A3A13CE9B0CF /* SexNetwork.h in Headers */,
				EC9F957CC940E6CF6C2D2802 /* TapCardMenArtsViewController.h in Headers */,
				A4983DA92AFE603450036F5F /* ViewController+MASAdditions.h in Headers */,
				B9C0A21642714F67C07A3077 /* MASCompositeConstraint.h in Headers */,
				C8364E04EABBF935C58E2ECE /* View+MASShorthandAdditions.h in Headers */,
				1F41C8D1D352BE190BA9A7BE /* NSArray+MASShorthandAdditions.h in Headers */,
				8E72E248754B01C48AF987D4 /* NSArray+MASAdditions.h in Headers */,
				C89939513BF6C2E329A07302 /* View+MASAdditions.h in Headers */,
				2A5D9C86CD7DD1AECAC0E0DB /* MASConstraint.h in Headers */,
				1B218D7DFB9FA638DD821DFB /* MASViewConstraint.h in Headers */,
				306330C90A447C68C8D70031 /* MASConstraintMaker.h in Headers */,
				CF16464F509E874563E63840 /* PickReason.h in Headers */,
				F76CABBCCE3B7DCC4A2DDF8F /* MASUtilities.h in Headers */,
				4C6011F16A6DAFC682103CCC /* MASLayoutConstraint.h in Headers */,
				D7F849AF5B2644D51F7314E4 /* MASViewAttribute.h in Headers */,
				3137309C93AA6BFC14CBAA62 /* NSLayoutConstraint+MASDebugAdditions.h in Headers */,
				B0360D9177F129D24E3111C7 /* MASConstraint+Private.h in Headers */,
				23F5C7BE536A99DB9B6DD123 /* KilobitsMenGallonWorkoutsDueSpherical.h in Headers */,
				06B351EFCB02FC813907FDDE /* OwnCacheView.h in Headers */,
				056E206AB2CF344D27E736CE /* Masonry.h in Headers */,
				4380BBC5C614AF7076C171F1 /* BrokenDid.h in Headers */,
				142C9919F33B16F16F811D21 /* AllocateHeavyViewController.h in Headers */,
				A8B559FB7235EDBD290214A9 /* HeadAskFootDay.h in Headers */,
				3C32F44AC2A9D4CE86AD3BF7 /* RealTryViewController.h in Headers */,
				9E60F35E7A508DD12B707E69 /* SurgeBreakStay.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		5D32140ECB212184C6AE84ED /* DrainageCompetence */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = ADD663CAEEC5AF1729F89B29 /* Build configuration list for PBXNativeTarget "DrainageCompetence" */;
			buildPhases = (
				D3FCFC824E502950E75EE5EC /* Headers */,
				3B2A2A7ABEC765EBE0036C0B /* Sources */,
				0488CEBAE9A9421779838D84 /* Frameworks */,
				295A733647D74CACE6F95BF5 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = DrainageCompetence;
			packageProductDependencies = (
			);
			productName = DrainageCompetence;
			productReference = D309C946B33979761A085CF1 /* DrainageCompetence.framework */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		E6936F01267A309F538D292D /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					808C954C9F5D0E49FF6B3BEB = {
						CreatedOnToolsVersion = 16.2;
					};
				};
			};
			buildConfigurationList = 8CF7C9A31B453FADBC7EF53E /* Build configuration list for PBXProject "DrainageCompetence" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 303FBB785F8945D7E29B5DAB;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 76158ACAD3DC139DE8BF6479 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				5D32140ECB212184C6AE84ED /* DrainageCompetence */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		295A733647D74CACE6F95BF5 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		3B2A2A7ABEC765EBE0036C0B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				D6BB9D6D9B031E310438669B /* RestInferiors.m in Sources */,
				F10A4F783B163B235A797641 /* OldestLink.m in Sources */,
				572B546C90C4B620ECE99D1E /* InheritedViewController.m in Sources */,
				529CBBF1835C6C52A0AB0AF6 /* ViewController+MASAdditions.m in Sources */,
				2B03D9BDC54B69CA19AD8787 /* SurgeBreakStay.m in Sources */,
				BCD36A619A5CC38B89D74FB9 /* MASViewConstraint.m in Sources */,
				9754DDC8D8A653C6EE2B388B /* TapForHasRenew+Total.m in Sources */,
				2F20E58A20E7737FDA9F5125 /* NSLayoutConstraint+MASDebugAdditions.m in Sources */,
				FA9222F97654F3E27ECA2B2C /* ThirdCivilCell.m in Sources */,
				C5927818ADF675596C939389 /* MASConstraint.m in Sources */,
				86C9089E2019938573ABC75B /* OwnCacheView.m in Sources */,
				F76712AD34D6DAF4F8E6CDBB /* MASLayoutConstraint.m in Sources */,
				79467E37DB8D58D5404A42F8 /* SDMemoryCache.m in Sources */,
				5442CB6C9ABDF6B72EC1B810 /* SDAnimatedImageView.m in Sources */,
				5BE6D23C8E7D84EB9621D705 /* AwayFullySpa.m in Sources */,
				894298337E0CAABDDAECAEFC /* KilobitsMenGallonWorkoutsDueSpherical.m in Sources */,
				7982D8A5AC4089F421944102 /* SDInternalMacros.m in Sources */,
				FC0752B6DB003B4424101837 /* TapDenseManager.m in Sources */,
				23FA74E4DF619A23B41EBE81 /* SDAnimatedImage.m in Sources */,
				AA18E435062068DDC850CEE5 /* NSData+ImageContentType.m in Sources */,
				75640C7FAACBB35C2B89BC79 /* NSButton+WebCache.m in Sources */,
				92290CAA01D8BE2DDDC08501 /* SDImageTransformer.m in Sources */,
				347C2E6BE120407F7E516BF4 /* TextRetOldEggViewController.m in Sources */,
				B7EDCCBBD87539373A10CBB6 /* SDImageAWebPCoder.m in Sources */,
				4FA51D85E021D11180D98E05 /* SDAsyncBlockOperation.m in Sources */,
				404B16462538FF1079FEEA88 /* UIButton+WebCache.m in Sources */,
				90E1F427689683ECD4212B26 /* SDAnimatedImageView+WebCache.m in Sources */,
				E10DA418F1D88F3B6550D1F5 /* ButColorViewController.m in Sources */,
				5E1C8DE570C6C6ACEE73FD06 /* UIImage+ExtendedCacheData.m in Sources */,
				59C6C653E9F35ADEAD00BB7D /* SDImageLoader.m in Sources */,
				995B8D02B7E966E962FD07F8 /* SDWebImageDownloaderDecryptor.m in Sources */,
				4198878735CCDEB687D3E5DB /* UIImage+YetImage.m in Sources */,
				0B1CEC1F7800F835A1846653 /* SubBarrierViewController.m in Sources */,
				E851B25ECE5FE1911E1C5522 /* SDWebImageIndicator.m in Sources */,
				F7826C20664FF54332354943 /* SDImageFrame.m in Sources */,
				6C5D0EBD9F22BD41F3798BFC /* SDFileAttributeHelper.m in Sources */,
				7713A06BC38749EDC04700B5 /* SDWebImageError.m in Sources */,
				ABAE13D64C3F9408557D8D44 /* SDWebImageDownloaderConfig.m in Sources */,
				CDC0BBA3CA889CB02A9887B4 /* SDImageLoadersManager.m in Sources */,
				DB2F04C0065552982326F855 /* UIImage+MemoryCacheCost.m in Sources */,
				45DDFFB2B9C0C186A0D049FB /* UIImageView+HighlightedWebCache.m in Sources */,
				9A58C3738780773F439EBD03 /* SDImageGraphics.m in Sources */,
				9D69BB5C02442F873B538D3C /* SDImageCachesManagerOperation.m in Sources */,
				DCBECDC3C1CC9681C606B778 /* SDWebImageManager.m in Sources */,
				8104B283BE240A3937A3547A /* SDImageCoder.m in Sources */,
				E493E5B24204198DA6088120 /* BuddyIndigoButton.m in Sources */,
				00A2B3DA7D509FE3F6170928 /* UIImage+GIF.m in Sources */,
				E0D268D55912C38116131650 /* LossWayDiskViewController.m in Sources */,
				056EA36B1E03CDB2F5562EEF /* SDImageCoderHelper.m in Sources */,
				EAAE0DF7979E709A1F94E101 /* UIDevice+TapDevice.m in Sources */,
				75B4431184EAEB54EABD93DE /* TapCardMenArtsViewController.m in Sources */,
				BF31D324B7336FEF1F15AF36 /* TurnItemOldDog.m in Sources */,
				7B62B11507717C5BB655F5FF /* SDWebImageDownloaderRequestModifier.m in Sources */,
				C160CCAECEEE144416C06900 /* DepthRootViewController.m in Sources */,
				82F17F0516E677C57D14D174 /* TurnGenericManager.m in Sources */,
				1D576136C534FA7F0A045F13 /* SDWebImageCacheKeyFilter.m in Sources */,
				02A12221F802EE3E91B5A31B /* ClickTokenViewController.m in Sources */,
				7AD73911C67B893F84BF1235 /* FourMinFoundModel.m in Sources */,
				EE320A075E53E4062C35D454 /* StoneDustViewController.m in Sources */,
				C1E5CD6540D2858FB1AAFADE /* MergeNameViewController.m in Sources */,
				B78D60A88711DF299F737519 /* SDImageCacheConfig.m in Sources */,
				872073B2CD7A7D03BC10379C /* SDImageCodersManager.m in Sources */,
				8B98FA117149B04991252D68 /* SDAnimatedImagePlayer.m in Sources */,
				A80DBEBC38135188DD1A7612 /* NSData+CropSum.m in Sources */,
				C0D0F57A3350CC85CAF3FD2C /* WayTipManager.m in Sources */,
				2D9FAC5006E9EDD7DD134C00 /* SDImageIOCoder.m in Sources */,
				00C95D2D1B1DDEBBF744CAEB /* PickReason.m in Sources */,
				6B8AC851D829A54F4B29A1B9 /* PenGramLossZipButton.m in Sources */,
				A2A791A3C5A4130CC2647796 /* NSImage+Compatibility.m in Sources */,
				241C24F2F8D23DDCB9BABA13 /* SDWebImageDefine.m in Sources */,
				D12176884997B70254690C7F /* UIImageView+WebCache.m in Sources */,
				4505EC90A88A21335BB37C9C /* ReadyHeadViewController.m in Sources */,
				73E93012EF9D2844E4197D62 /* BagCheckoutNarrativeArmGain.m in Sources */,
				6B971071CA0E66A173475F99 /* ItalianLog.m in Sources */,
				AB76B4CB53E13E2E765FCB95 /* SDWebImageOptionsProcessor.m in Sources */,
				635E7AE0C254D7C84B196806 /* TerabytesMapManager.m in Sources */,
				0CD71E3ABC40F192936C1AEB /* SDCallbackQueue.m in Sources */,
				DDB214FDEEF471D57E4E926E /* UIImage+ForceDecode.m in Sources */,
				F3AD15552A36DA9672AEBFA4 /* NSURL+PaletteBad.m in Sources */,
				E9B10E338547CA6F6B1F1674 /* SDDeviceHelper.m in Sources */,
				D2E5C8EAA01861788011C81A /* SDImageGIFCoder.m in Sources */,
				36BCDDF653383C01B678C44A /* UIView+WebCacheState.m in Sources */,
				B691874D8206EF2036BC2EFF /* UIImage+Metadata.m in Sources */,
				AA44DFA96456CDA28098DDD0 /* UIView+WebCacheOperation.m in Sources */,
				A148D6D6A69581A00C98BAC4 /* DashSawMenSkinCell.m in Sources */,
				881883C5F8F14CE5FEB08B7B /* SDDiskCache.m in Sources */,
				FCA54DAB2611EF2633E3BF24 /* SDImageCacheDefine.m in Sources */,
				F4EAB4A66B6998F33319622C /* PathPeakZipAllViewController.m in Sources */,
				26D14C0DF534C010FBCDEDE7 /* NSObject+MindStickySpatialHelloBox.m in Sources */,
				AABA24CAF30C8C99E52211FA /* OptPaceSuchAction.m in Sources */,
				A69C16E7EA75670DE2CFAEF1 /* SDWeakProxy.m in Sources */,
				4074F142F051C1D89C899D45 /* RightManager.m in Sources */,
				2058C6AF3A82E71A34FE8311 /* SDWebImageDownloaderOperation.m in Sources */,
				D621A72674081071BDFF3FEA /* UIColor+SDHexString.m in Sources */,
				CA538E90A753D4B689CC5A3C /* PolishFaxTextField.m in Sources */,
				01B2ABAFD7B0C4AF8637EC1C /* SDWebImageDownloaderResponseModifier.m in Sources */,
				277ABF6F652A8C36E4488737 /* SDWebImageOperation.m in Sources */,
				6DE79D7E062FB4F95831F800 /* NSString+ReversesAsk.m in Sources */,
				21CDCD8BEDE806DAC53D9FAF /* NSBezierPath+SDRoundedCorners.m in Sources */,
				74236E8798FD752C07CAD312 /* ProjectsCapturedArtCommitRedTraveledViewController.m in Sources */,
				A22C69D5C4951395A71158AE /* SDImageAPNGCoder.m in Sources */,
				F151355F31DB05B22C6E48F7 /* TimeQueueManager.m in Sources */,
				175794F698A8F5A7CB120718 /* UIImage+Transform.m in Sources */,
				7933AEC5D926D1F3431BFCB2 /* ViewDayCloudInfo.m in Sources */,
				C1D55274483C6F7ECB7FDEC0 /* SDImageAssetManager.m in Sources */,
				430ABDFCC4F26AA0350C7790 /* SDWebImageTransition.m in Sources */,
				E94E01837202601E3FB7B426 /* SlowDetails.m in Sources */,
				54CED9B79CD271C3C7CA56BF /* SDImageCache.m in Sources */,
				E9273C29CCAD991A36ABE467 /* SDImageHEICCoder.m in Sources */,
				FD525ED3BCD3A9EB32113534 /* SDImageCachesManager.m in Sources */,
				AC6E528DCE43AA64108B5B4D /* OptShowDryViewController.m in Sources */,
				330FB4BDAED0C97E3D008209 /* MQTTSessionSynchron.m in Sources */,
				B30A40F2E6FAE8451D38ED9D /* NSString+CutTen.m in Sources */,
				75E490305D44A8ADDC1BEBF4 /* MQTTDecoder.m in Sources */,
				3B79C35550DDD01323A9BF6E /* MQTTSessionLegacy.m in Sources */,
				666776D76919EA96E4D468D8 /* MQTTSSLSecurityPolicy.m in Sources */,
				A526953EB8E7815826989CEE /* MQTTMessage.m in Sources */,
				0A7833B477D00DA691C72DAA /* MQTTSessionManager.m in Sources */,
				F3E8B2904F6C0C1156415225 /* ReconnectTimer.m in Sources */,
				08725C6258842C5E3AD505D5 /* MQTTSSLSecurityPolicyTransport.m in Sources */,
				A13222EF12B4EA9B5F963F08 /* MQTTSession.m in Sources */,
				51133415D4D0D5E270401530 /* MQTTCoreDataPersistence.m in Sources */,
				3CA441359D169956E197ED45 /* MQTTCFSocketTransport.m in Sources */,
				9D4A870133660B32934130EE /* MQTTLog.m in Sources */,
				0B99476F4ED1FF77CFDF20B6 /* MQTTSSLSecurityPolicyDecoder.m in Sources */,
				2A92A25637B6FB3935431E62 /* MQTTSSLSecurityPolicyEncoder.m in Sources */,
				B740EB759A7471EF02B63E96 /* MQTTTransportProtocol.m in Sources */,
				AA0F0A880EE1750CE337BEBB /* MQTTInMemoryPersistence.m in Sources */,
				ECEA4985CC7F3853BEF43AFE /* MQTTStrict.m in Sources */,
				E906DFF505EA79F32081E06F /* MQTTCFSocketDecoder.m in Sources */,
				72FA66D465A938D1BCBE0EDE /* TapForHasRenew+PopRed.m in Sources */,
				8CF5FD759D632A771264692A /* GCDTimer.m in Sources */,
				E5EFDB5521D201E4EC9BDFF8 /* MQTTCFSocketEncoder.m in Sources */,
				C16AA9C1C6EA3570AFA30DDA /* MQTTProperties.m in Sources */,
				9B337A569F98CCB3181FA99A /* ForegroundReconnection.m in Sources */,
				48DE3B6148A14CD4BC5A737F /* SDGraphicsImageRenderer.m in Sources */,
				65ABD2DCAD74EECA30263F1D /* UIView+WebCache.m in Sources */,
				4C0DF51797385C7BE63910BB /* SDWebImagePrefetcher.m in Sources */,
				F74C7AD62CF6B4722B2EE75B /* BurmeseZone.m in Sources */,
				CF895C2A6BA6DE62C72EEEFC /* SDWebImageCacheSerializer.m in Sources */,
				4923166191F9088023F9226A /* SDDisplayLink.m in Sources */,
				CDE0D12A6641BD5968114D61 /* SDWebImageDownloader.m in Sources */,
				D2723D4A7BE43D4797EE00AB /* SDImageFramePool.m in Sources */,
				EE31A5A9EEBB749EBA82C2DE /* SDImageIOAnimatedCoder.m in Sources */,
				FE1A6F11CDA2196660841E30 /* UIImage+MultiFormat.m in Sources */,
				0F3B5952477440229E9C941B /* SDAssociatedObject.m in Sources */,
				9CC9B9AAF0DD3E8A2E76F1A0 /* SDAnimatedImageRep.m in Sources */,
				566319D6F64DF42AC73B6461 /* HavePlusWhoManager.m in Sources */,
				B1EDBB0457AE8ECCFAA4FAE2 /* SDWebImageCompat.m in Sources */,
				8566105C5F1567D67DC4B04D /* AgeHardSentinelFaxAgeDiacritic.m in Sources */,
				4716EFB6045CA31FE1070DF6 /* DiacriticVital.m in Sources */,
				54397A075691CF20589B824C /* IdentifyToneAcrossRegistryOccur.m in Sources */,
				5C67CF5AB524F2AAA903F073 /* Color.m in Sources */,
				57B0AFAF04E15CCF02BED583 /* HertzViewController.m in Sources */,
				D946EAA3A919CDA9A295E05C /* AbsoluteTriggerMonitoredSensitiveLocalizes.m in Sources */,
				6EE0434775314FB460D7A4AF /* NSArray+MASAdditions.m in Sources */,
				4B2A0D1AD9F8E51BBEC73542 /* MASConstraintMaker.m in Sources */,
				257C6E0C9F0B9224F1673034 /* UseEditorInfo.m in Sources */,
				3F8683A9908ABAE90B703188 /* MASViewAttribute.m in Sources */,
				D1CFFCCCCBFE31D3E0D70B34 /* MASCompositeConstraint.m in Sources */,
				8D3F30ABF59AFFD0C693C781 /* View+MASAdditions.m in Sources */,
				9840A321EEC795321684DB2B /* PasswordsViewController.m in Sources */,
				B1E25B10045F037F341E889E /* AllocateHeavyViewController.m in Sources */,
				AEC88503B652020326A94D54 /* XXGProtocolLabel.m in Sources */,
				1AC0F8379BC58A679E84EE94 /* RealTryViewController.m in Sources */,
				A462E3AEAC055D279766E314 /* EncodeCupReplyLoadAdverbModel.m in Sources */,
				7BB9CFEC8C52C2714CF2265D /* NSError+YoungestTwo.m in Sources */,
				259B24EE17B678F7F306E950 /* AnswerConfig.m in Sources */,
				81E5C33CC2CEEA05EBC89BFB /* SchemesCapView.m in Sources */,
				9DE797B86BCA833C8B947951 /* FadeEarManager.m in Sources */,
				D50FB127688C16CE9233D308 /* DayUpsidePutManager.m in Sources */,
				77F7105E2234B918EED1477E /* RegionExposureInfo.m in Sources */,
				ECDF3EF9F1C718128272BD65 /* DrainageCompetence.m in Sources */,
				BED9C38F6B6F62E8FBCEA0C6 /* LogoHexBoxManager.m in Sources */,
				B9D50C78E017F4397BB56926 /* BinLawParseThickMixCell.m in Sources */,
				727F940EBBBEB915F3EF7374 /* TapForHasRenew.m in Sources */,
				53EBF25E79E55BB646057BD7 /* LinearManager.m in Sources */,
				6ABC128C77714CA9A0A21180 /* CharShiftDark.m in Sources */,
				0553AF32D6EE9BA48B425853 /* MoreVisualView.m in Sources */,
				16ADBD1D625E5B4AD94DB96C /* StorageNumberSmallestProjectLaw.m in Sources */,
				3AD309169261C55DF7735C38 /* Recovery.m in Sources */,
				554340FB46402C64BC115649 /* LogoSent.m in Sources */,
				6B6D0177EC5A1CD45EE8B479 /* ArbiterLocal.m in Sources */,
				415ABD1F2667FCA4C4BF15DD /* DanceMidHisManager.m in Sources */,
				0D9377216C87DCFB89DAD456 /* InsetTowerConfig.m in Sources */,
				94E525040F1B2F98F23870DB /* TheDarkArmViewController.m in Sources */,
				D871BB053B2946392BB7F9DB /* IllDayBodyRule.m in Sources */,
				3BBF52D74B1D03B04EABE819 /* NSObject+PinModel.m in Sources */,
				0F03555E8BE60DAA625E7464 /* TabNumberRevisionMegahertzPasteViewController.m in Sources */,
				4137B3057BEEFD9CCE9972B7 /* HoldBigBoxNapController.m in Sources */,
				1C2DCEC5998E2544054524BD /* UIViewController+DueViewController.m in Sources */,
				7EE276798E6A4CDA4A2DC657 /* ManAllMildCaseCell.m in Sources */,
				E6CBC399D3C9144867DA56BD /* HeadAskFootDay.m in Sources */,
				86D7D2ADD41CDB12D2D74BB9 /* ShortHoverWide.m in Sources */,
				986B3175216273371B37FCF3 /* LinkOnlyHowNearbyAvailable.m in Sources */,
				3D00230108C61A0CE7CD3B87 /* FilterDigestPreviewsMomentLose.m in Sources */,
				D836BC90AC361EF16106483D /* UplinkDeep.m in Sources */,
				D477A6BCF4F5E25C96754524 /* HailSheCanadianLooperBefore.m in Sources */,
				80B4AE7363944132F517E6B5 /* UIColor+JobColor.m in Sources */,
				3D45789DAB5DB4D517089BD7 /* ButAlertView.m in Sources */,
				6D975F2B45791F29D259A827 /* TenStartupModel.m in Sources */,
				17F7DAB92A1C6CB8F4C36A26 /* ArraySawSequencerReceiveCutPacket.m in Sources */,
				DCD7ABA46BDBFA7E129F8652 /* AndToast.m in Sources */,
				EB2B6500F97891863271B899 /* EggReuseRawEra.m in Sources */,
				4EDF56A392DA205731E6CE1B /* RegisterColor.m in Sources */,
				6D83D69635E3C175A921DFE7 /* BezelItsManager.m in Sources */,
				6328D31F37C58FC63976925B /* PinPartModel.m in Sources */,
				889AD1E178A36B804A56A4BA /* BetterEyeIncrementLoopsSummaryTool.m in Sources */,
				B4D41CD1EE42F1941004FEC9 /* ForwardWetList.m in Sources */,
				D1A40FC996ABFF2EA6A2D567 /* TapForHasRenew+QuitAcute.m in Sources */,
				5063F8B4D5F51C95B8445A3B /* UnwindInfo.m in Sources */,
				4E12B00CE833D393C2F6E5DE /* SexNetwork.m in Sources */,
				8DBE02F97A0EE6F5B3C021B7 /* NSString+DirtyFoot.m in Sources */,
				A3156E1646E33F8308E3BF90 /* TorchGainManager.m in Sources */,
				0545A1BE5E9EEC69813C04FB /* DarkerButPlusReportStrategy.m in Sources */,
				6DEEB88C4AD4F01E59E87F73 /* RetryAwakeSentencesBarsAll.m in Sources */,
				2A7A0925E169377FADF2BF8C /* ModelHexModel.m in Sources */,
				BC1018912A84C2B94EB20201 /* GainPassGetDogWindow.m in Sources */,
				95EB6FF1DCC5B3184F166955 /* UniformFlash.m in Sources */,
				8FF86EE68EC1667CB4CC5A6D /* BrokenDid.m in Sources */,
				D87D1CB47841A05F5490D81A /* MessagingInfo.m in Sources */,
				C99FA88009B3B6741525971D /* DateInvertInfo.m in Sources */,
				6CA26C88F422F813394AE4FE /* HyphenPhone.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		CCBFACC942314972A3577EDA /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		CF61AF291DCC8E40D21A244A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		D87F09E369242A73422CDBD1 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = NO;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 13;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MARKETING_VERSION = 1.0;
				MODULE_VERIFIER_SUPPORTED_LANGUAGES = "objective-c objective-c++";
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu17 gnu++20";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_BUNDLE_IDENTIFIER = com.DrainageCompetence;
				PRODUCT_NAME = DrainageCompetence;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		DD9E01C5BA5D5C51777869DF /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = NO;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 13;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MARKETING_VERSION = 1.0;
				MODULE_VERIFIER_SUPPORTED_LANGUAGES = "objective-c objective-c++";
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu17 gnu++20";
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_BUNDLE_IDENTIFIER = com.DrainageCompetence;
				PRODUCT_NAME = DrainageCompetence;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		8CF7C9A31B453FADBC7EF53E /* Build configuration list for PBXProject "DrainageCompetence" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				CCBFACC942314972A3577EDA /* Debug */,
				CF61AF291DCC8E40D21A244A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		ADD663CAEEC5AF1729F89B29 /* Build configuration list for PBXNativeTarget "DrainageCompetence" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DD9E01C5BA5D5C51777869DF /* Debug */,
				D87F09E369242A73422CDBD1 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = E6936F01267A309F538D292D /* Project object */;
}
