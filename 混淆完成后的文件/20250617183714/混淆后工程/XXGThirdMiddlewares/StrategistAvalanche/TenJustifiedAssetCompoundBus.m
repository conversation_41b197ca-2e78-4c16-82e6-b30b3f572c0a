






#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import <VKID/VKID-Swift.h>

@interface TenJustifiedAssetCompoundBus : NSObject

@end

@implementation TenJustifiedAssetCompoundBus

+ (void)normalHitLawViewController:(UIViewController *)vc handler:(void(^)(BOOL isCancell,NSString *userID, NSString*token, NSString*error))handler {
    [[VKIDExtension shared] oauthWithPresentingController:vc completion:^(BOOL isCancell, NSString * userId, NSString * token, NSString * error) {
        if (isCancell) {
            handler(YES,@"",@"",@"");
        }else {
            handler(NO,userId,token,error);
        }
    }];
}

+ (BOOL)authorsHowTremorGoalPressure:(UIApplication *)application
                splatHas:(NSURL *)url
                lazyFont:(nullable NSDictionary<UIApplicationOpenURLOptionsKey, id> *)options {
    [[VKIDExtension shared] handleOpenURL:url];
    return YES;
}

+ (void)afterMixerShiftSubfamilyBinInfinite:(NSString *)clientId returnedRest:(NSString *)returnedRest{
    [[VKIDExtension shared] initvkWithClientId:clientId returnedRest:returnedRest];
}

@end
