





#import <CL_ShanYanSDK/CL_ShanYanSDK.h>

@interface CardioidForceRemoteJobEmergencyLexicon : NSObject<CLShanYanSDKManagerDelegate>

@property (nonatomic, copy) void(^sayChestSpaTabAction)(NSInteger);

@end

@implementation CardioidForceRemoteJobEmergencyLexicon

- (void)dealloc {
    
}

+ (instancetype)shared {
    static id shared = nil;
    static dispatch_once_t onlyToken;
    dispatch_once(&onlyToken, ^{
        shared = [[super allocWithZone:NULL] init];
    });
    return shared;
}

- (void)hierarchyMetalChunkStrictlySuffixTransportSinhalese:(NSString *)appId complete:(void (^_Nullable)(BOOL evictLogin))complete {

    [CLShanYanSDKManager setCLShanYanSDKManagerDelegate:self];
    
    [CLShanYanSDKManager setPreGetPhonenumberUseCacheIfNoCellularNetwork:NO];
    
    [C<PERSON>hanYanSDKManager initWithAppId:appId complete:^(CLCompleteResult * _Nonnull completeResult) {
        __block BOOL moreSplitUpperLogin = !completeResult.error;
        
        if (moreSplitUpperLogin) {
            
            [CLShanYanSDKManager preGetPhonenumber:^(CLCompleteResult * _Nonnull completeResult) {
                dispatch_sync(dispatch_get_main_queue(), ^{
                    complete(completeResult.error == nil);
                });
            }];
            
        } else {
            dispatch_sync(dispatch_get_main_queue(), ^{
                complete(NO);
            });
        }
    }];
}


- (void)preventsSafetyFormattedEntitiesBasqueController:(UIViewController *_Nonnull)controller array:(NSArray *)array success:(void (^_Nullable)(NSDictionary * _Nonnull signScene))success specialBox:(void (^_Nullable)(NSString * _Nonnull error))error overTabAction:(void(^)(NSInteger))action {
    self.sayChestSpaTabAction = action;
    

    [CLShanYanSDKManager quickAuthLoginWithConfigure:[self mayEraEraFire:controller nextArray:array] openLoginAuthListener:^(CLCompleteResult * _Nonnull completeResult) {
        if (completeResult.error) {
            error(completeResult.message);
        }
    } oneKeyLoginListener:^(CLCompleteResult * _Nonnull completeResult) {
        
        if (completeResult.error == nil) {
            dispatch_sync(dispatch_get_main_queue(), ^{
                success(completeResult.data);
            });
        }else {
            error(completeResult.message);
        }
    }];
}





- (CLUIConfigure *)mayEraEraFire:(UIViewController *)viewController nextArray:(NSArray *)array {
    CLUIConfigure *eachEnhance = [[CLUIConfigure alloc] init];
    
    eachEnhance.viewController = viewController;
    
    
    eachEnhance.clNavigationBarHidden = @(YES);
    
    //logo
    eachEnhance.clLogoHiden = @(YES);
    
    
    eachEnhance.clPhoneNumberFont = [UIFont boldSystemFontOfSize:26];
    eachEnhance.clPhoneNumberColor = UIColor.darkGrayColor;
    eachEnhance.clPhoneNumberTextAlignment = @(NSTextAlignmentCenter);
    
    
    eachEnhance.clSloganTextFont = [UIFont systemFontOfSize:14];
    eachEnhance.clSloganTextColor = UIColor.darkGrayColor;
    eachEnhance.clSlogaTextAlignment = @(NSTextAlignmentCenter);
    
    
    eachEnhance.clLoginBtnText = array[4];
    eachEnhance.clLoginBtnTextFont = [UIFont systemFontOfSize:18];
    eachEnhance.clLoginBtnTextColor = UIColor.whiteColor;
    eachEnhance.clLoginBtnBgColor = array[0];
    eachEnhance.clLoginBtnCornerRadius = @(2);
    
    
    eachEnhance.clCheckBoxSize = [NSValue valueWithCGSize:CGSizeMake(0, 0)];
    eachEnhance.clCheckBoxValue = @(YES);
    
    
    eachEnhance.clAppPrivacyNormalDesTextFirst = array[5];
    eachEnhance.clAppPrivacyFirst = @[array[6], array[1]];
    eachEnhance.clAppPrivacyNormalDesTextSecond = array[7];
    eachEnhance.clAppPrivacyNormalDesTextLast = @"";
    eachEnhance.clAppPrivacyTextFont = [UIFont systemFontOfSize:12];
    eachEnhance.clAppPrivacyTextAlignment = @(NSTextAlignmentLeft);
    eachEnhance.clAppPrivacyWebNavigationBarTintColor = UIColor.whiteColor;
    eachEnhance.clAppPrivacyPunctuationMarks = @(YES);
    eachEnhance.clAppPrivacyColor = @[UIColor.darkGrayColor,array[0]];;
    eachEnhance.clAuthTypeUseWindow = @(YES);
    eachEnhance.clPrivacyShowUnderline = @(YES);
    eachEnhance.clAppPrivacyLineSpacing = @(2.5);
    eachEnhance.clAuthWindowModalTransitionStyle = @(UIModalTransitionStyleCrossDissolve);
    
    eachEnhance.clAppPrivacyWebBackBtnImage = array[2];
    
    
    eachEnhance.clLoadingSize = [NSValue valueWithCGSize:CGSizeMake(90, 90)];
    eachEnhance.clLoadingCornerRadius = @(2);
    eachEnhance.clLoadingIndicatorStyle = @(UIActivityIndicatorViewStyleLarge);
    eachEnhance.clLoadingTintColor = UIColor.blackColor;
    eachEnhance.clLoadingBackgroundColor = UIColor.clearColor;
    
    
    CLOrientationLayOut *unifiedRetBrowseWonOwner = [[CLOrientationLayOut alloc] init];
    eachEnhance.clOrientationLayOutPortrait = unifiedRetBrowseWonOwner;
    
    
    CGFloat y = (([UIScreen mainScreen].bounds.size.height - [array[3] CGSizeValue].height) * 0.5) + 35;
    CGFloat height = 30;
    unifiedRetBrowseWonOwner.clLayoutPhoneTop = @(y);
    unifiedRetBrowseWonOwner.clLayoutPhoneHeight = @(height);
    unifiedRetBrowseWonOwner.clLayoutPhoneCenterX = @(0);
    
    
    y += (height + 20);
    height = 17;
    unifiedRetBrowseWonOwner.clLayoutSloganTop = @(y);
    unifiedRetBrowseWonOwner.clLayoutSloganCenterX = @(0);
    unifiedRetBrowseWonOwner.clLayoutSloganHeight = @(height);

    
    y += (height + 20);
    height = 50;
    unifiedRetBrowseWonOwner.clLayoutLoginBtnTop = @(y);
    unifiedRetBrowseWonOwner.clLayoutLoginBtnWidth = @([array[3] CGSizeValue].width - 40);
    unifiedRetBrowseWonOwner.clLayoutLoginBtnCenterX = @(0);
    unifiedRetBrowseWonOwner.clLayoutLoginBtnHeight = @(height);

    
    y += (height + 15);
    unifiedRetBrowseWonOwner.clLayoutAppPrivacyTop = @(y);
    unifiedRetBrowseWonOwner.clLayoutAppPrivacyCenterX = @(0);
    unifiedRetBrowseWonOwner.clLayoutAppPrivacyWidth = @([array[3] CGSizeValue].width - 40);
    
    
    eachEnhance.customAreaView = ^(UIView * _Nonnull customAreaView) {
        
        customAreaView.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0];
        
        UIView *slideView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, [array[3] CGSizeValue].width, [array[3] CGSizeValue].height)];
        slideView.backgroundColor = UIColor.whiteColor;
        slideView.layer.cornerRadius = 2.0;
        [customAreaView addSubview:slideView];
        slideView.center = customAreaView.center;
        
        
        UIButton *close = [UIButton buttonWithType:UIButtonTypeCustom];
        [close addTarget:self action:@selector(lingerAdditionResumeOutputsRollbackScoreHandler:) forControlEvents:(UIControlEventTouchUpInside)];
        [close setBackgroundImage:array[2] forState:UIControlStateNormal];
        [slideView addSubview:close];
        close.frame = CGRectMake(8, 8, 20, 20);
    };
    
    return eachEnhance;
}

- (void)lingerAdditionResumeOutputsRollbackScoreHandler:(id)sender {
    [CLShanYanSDKManager finishAuthControllerCompletion:nil];
    self.sayChestSpaTabAction(0);
}

@end
