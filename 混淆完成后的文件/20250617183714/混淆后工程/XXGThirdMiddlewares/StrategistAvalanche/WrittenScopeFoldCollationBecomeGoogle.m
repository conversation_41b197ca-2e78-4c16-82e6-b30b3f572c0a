







#import "BDASignalManager.h"

@interface WrittenScopeFoldCollationBecomeGoogle : NSObject

@end

@implementation WrittenScopeFoldCollationBecomeGoogle

+ (NSString *)wetOuterSink {
    return kBDADSignalSDKVersion;
}

+ (void)applierExtractAbsentGatheringBeginWaistOptions:(NSDictionary *)launchOptions pagerEndOptions:(UISceneConnectionOptions *)connetOptions {
    if (launchOptions) {
        
        [BDASignalManager didFinishLaunchingWithOptions:launchOptions connectOptions:nil];
        return;
    }
    if (connetOptions) {
        
        [BDASignalManager didFinishLaunchingWithOptions:nil connectOptions:connetOptions];
    }
}

+ (BOOL)authorsHowTremorGoalPressure:(UIApplication *)application
                splatHas:(NSURL *)url
                lazyFont:(nullable NSDictionary<UIApplicationOpenURLOptionsKey, id> *)options {
    [BDASignalManager anylyseDeeplinkClickidWithOpenUrl:url.absoluteString];
    return YES;
}

@end
