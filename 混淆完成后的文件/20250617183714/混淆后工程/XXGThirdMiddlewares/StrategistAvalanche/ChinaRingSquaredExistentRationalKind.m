








#import <AppLovinSDK/AppLovinSDK.h>
#import <AppTrackingTransparency/AppTrackingTransparency.h>

@interface ChinaRingSquaredExistentRationalKind : NSObject<MARewardedAdDelegate,MAAdViewAdDelegate>

@property (nonatomic, strong) MARewardedAd *funContentPopRadioFrench;
@property (nonatomic, assign) NSInteger tintMainRadioTransientNotified;

@property (nonatomic, copy) NSString *dueNapAlbumData;

@property (nonatomic, copy) void (^tipCutCaseWho)(BOOL result);

@end

@implementation ChinaRingSquaredExistentRationalKind

+ (instancetype)shared {
    static id shared = nil;
    static dispatch_once_t onlyToken;
    dispatch_once(&onlyToken, ^{
        shared = [[super allocWithZone:NULL] init];
    });
    return shared;
}

+ (NSString *)wetOuterSink {
    return ALSdk.version;
}

- (void)evaluatedAlcoholOwnKazakhAllocatedComment {
    [[ALSdk shared] evaluatedAlcoholOwnKazakhAllocatedComment];
}


- (void)skippedGolfArrivalBigRetrieveEnterKey:(NSString *)xxpk_maxkey criteriaStartingAffectedBitmapText:(NSString *)criteriaStartingAffectedBitmapText illEasyHiddenAlgorithmPatterns:(NSArray *)illEasyHiddenAlgorithmPatterns {
    
    
    
    Class cls = NSClassFromString(@"FBAdSettings");
    if (cls) {
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wundeclared-selector"
        [cls performSelector:@selector(setDataProcessingOptions:) withObject:@[]];
        [cls performSelector:@selector(setAdvertiserTrackingEnabled:) withObject:@(YES)];
#pragma clang diagnostic pop
    }
    
    

    
    ALSdkInitializationConfiguration *xxpk_configuration = [ALSdkInitializationConfiguration configurationWithSdkKey:xxpk_maxkey builderBlock:^(ALSdkInitializationConfigurationBuilder * _Nonnull builder) {
        builder.mediationProvider = ALMediationProviderMAX;
        if (illEasyHiddenAlgorithmPatterns) {
            builder.testDeviceAdvertisingIdentifiers = illEasyHiddenAlgorithmPatterns;
        }
        if (@available(iOS 14, *)) {
            if ([ATTrackingManager trackingAuthorizationStatus] == ATTrackingManagerAuthorizationStatusAuthorized ) {
                [ALPrivacySettings setHasUserConsent: YES];
            }
        } else {
            [ALPrivacySettings setHasUserConsent: YES];
        }
    }];
    
    [[ALSdk shared] initializeWithConfiguration:xxpk_configuration completionHandler:^(ALSdkConfiguration * _Nonnull configuration) {
        [self weeklySpanHistoryImproperLessLevelSuspended:criteriaStartingAffectedBitmapText];
    }];
}

- (void)weeklySpanHistoryImproperLessLevelSuspended:(NSString *)criteriaStartingAffectedBitmapText
{
    self.funContentPopRadioFrench = [MARewardedAd sharedWithAdUnitIdentifier:criteriaStartingAffectedBitmapText];
    self.funContentPopRadioFrench.delegate = self;

    
    [self.funContentPopRadioFrench loadAd];
}

- (void)winAzimuthSinBondHeadTokenData:(nullable NSString *)dueNapAlbumData mainUses:(void(^)(BOOL result))mainUses {
    self.dueNapAlbumData = dueNapAlbumData;
    self.tipCutCaseWho = mainUses;
    if ( [self.funContentPopRadioFrench isReady]) {
        [self.funContentPopRadioFrench showAdForPlacement:nil customData:dueNapAlbumData];
    }else {
        mainUses(NO);
    }
}

- (void)handlingPaperYiddishTapWelsh:(const char *)name error:(NSString *)error {
    
}


- (void)bayerWalk:(nonnull MAAd *)ad {
    
    [self handlingPaperYiddishTapWelsh: __PRETTY_FUNCTION__ error:nil];
    
    
    self.tintMainRadioTransientNotified = 0;
}

- (void)biotinBirthdayChloridePetabytesSockConverterIdentifier:(nonnull NSString *)adUnitIdentifier withError:(nonnull MAError *)error {
    
    [self handlingPaperYiddishTapWelsh: __PRETTY_FUNCTION__ error:error.message];
    
    
    
    self.tintMainRadioTransientNotified++;
    NSInteger printAll = pow(2, MIN(6, self.tintMainRadioTransientNotified));
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, printAll * NSEC_PER_SEC), dispatch_get_main_queue(), ^{
        [self.funContentPopRadioFrench loadAd];
    });
}

- (void)civilNone:(MAAd *)ad {
    [self handlingPaperYiddishTapWelsh: __PRETTY_FUNCTION__ error:nil];
    
    
    [self.funContentPopRadioFrench loadAd];
}

- (void)fillerMeanFathomsTrademarkEight:(MAAd *)ad withError:(MAError *)error
{
    [self handlingPaperYiddishTapWelsh: __PRETTY_FUNCTION__ error:error.message];
    
    
    [self.funContentPopRadioFrench loadAd];
    
    if (self.tipCutCaseWho) {
        self.tipCutCaseWho(NO);
    }
}

- (void)useMixWarp:(nonnull MAAd *)ad {
    [self handlingPaperYiddishTapWelsh: __PRETTY_FUNCTION__ error:nil];
}

- (void)patternBezel:(nonnull MAAd *)ad {
    [self handlingPaperYiddishTapWelsh: __PRETTY_FUNCTION__ error:nil];
}

- (void)phoneIntegerArcheryProductForever:(nonnull MAAd *)ad bedAllPort:(nonnull MAReward *)reward {
    [self handlingPaperYiddishTapWelsh: __PRETTY_FUNCTION__ error:nil];
    
    if (self.tipCutCaseWho) {
        self.tipCutCaseWho(YES);
    }
}


- (void)crossDogLayer:(nonnull MAAd *)ad {
    [self handlingPaperYiddishTapWelsh: __PRETTY_FUNCTION__ error:nil];
}

- (void)drawRopeWas:(nonnull MAAd *)ad {
    [self handlingPaperYiddishTapWelsh: __PRETTY_FUNCTION__ error:nil];
}

@end
