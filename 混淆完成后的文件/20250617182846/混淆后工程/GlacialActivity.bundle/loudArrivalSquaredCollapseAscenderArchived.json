{"maleInputCut": "352e8711bcca025e07230a8402f03d09", "endsSegueNot": "3.2.2", "pubGigahertzBlobHomeYou": "%@ code:%ld", "newtonsDarkerBrandMealSidebar": "base_url_value", "principalUsageEjectMuteSliding": "api.xxgameos.com", "uniformRemoveCompactRouterFlipped": "api.xxgame.cn,api.xxbox.cn,api.xxtop.cn,api.xxyx.cn", "ageWorldColor": "https://%@/kds/v1/init", "mouthKazakh": "action", "waxSumDegree": "actions", "tabularRest": "device", "seePersonal": "extend", "noiseKnowBars": "api_list", "bloodTen": "app", "outGainFlip": "secret", "songBus": "id", "saltSeeDark": "config", "arbitraryInfo": "adaption", "browseTry": "skin", "bodyBecome": "login", "slavicTwoUsedDroppedFoodAny": "extra_params", "levelFunShe": "server", "bridgedHandler": "timestamp", "penTrait": "user", "eggDecline": "token", "mixSimple": "?sign=%@", "rhythmAll": "gzip", "singularTriangleSpellHandleLayeringWrite": "Content-Encoding", "wasGeneratesThermalBarsAddCurl": "application/json; charset=utf-8", "loveCarDitherApplyingShelf": "Content-Type", "storylineAdjustingTenSpeechLandscape": "POST", "buffersFill": "status", "tabWasPressed": "redirect", "squaredHit": "error", "manPhrasePan": "ZipNetwork", "eyeMagnetic": "errmsg", "highRow": "ok", "whoLower": "tip", "prefixesAdobe": "relogin", "barAngle": "url", "pipeResting": "sinNap", "wonSplatName": "username", "ourCustom": "name", "plateCutKey": "password", "floatingBit": "mobile", "parentalPlan": "purpose", "runLinerFitRet": "dial_code", "holdThemeSite": "10002", "rowDenyNapTry": "0", "badSlide": "iOS", "brandWord": "AppleLanguages", "finalIntegers": "uniqueId", "beginBracketEnhancedFlatBookmarks": "open_close", "reorderMan": "close", "askRoleTaps": "update", "sheSpokenArtsNeutralCursors": "trampoline", "findTopSlow": "splash", "wonCleanSunHis": "real_name", "prettyGenerate": "subscribe", "volumesCenter": "sms_code", "mildPopTorch": "new_password", "depthCapFeet": "old_password", "tabRateOpt": "order", "countRankRope": "<PERSON><PERSON><PERSON>", "jumpMap": "pay_method", "visitSob": "iap", "flipHis": "h5", "winTooNordic": "poopopay", "dutchSafeFlat": "pay_status", "bottomEncodingsMergeMessagingReal": "%@coupon_id=%@", "buffersRetry": "payload", "headerLook": "state", "timeBinBendExcludedManagers": "user_info_url", "routerHisFollowAllowArtery": "1", "exceedsCapturedResizingFinalDegree": "2", "sugarDynamicProcessorBypassedSin": "-30003", "hiddenSix": "open", "busEject": "uid", "assertStriationNumbersDryMovie": "boxm_boxs_value", "jobTopSlopeSupportOwnTab": "boxm_comeinedbox_value", "solidJabberBoundaryStoodTeacher": "&open_page=%@", "teamGuide": "type", "sevenPermanentArmpitTipBlue": "<PERSON><PERSON>yan", "onlyWasMount": "created", "exportingNone": "cp_extra", "assumeBit": "data", "yetAffiliate": "content", "retLogDownFeet": "wx", "batteryHeader": "://oauth", "fitThickPut": "weixin", "swahiliSignalChloridePriorSwahiliMargin": "weixin://app/%@/auth/?scope=snsapi_userinfo&state=wxAuth", "catFilenameCutoffHasScopeBirth": "日志配置完成", "visualOwnLigaturesFarInfiniteComposite": "开始加载资源", "paragraphChatterCreditBeforeHeadSquaredOperating": "资源加载完成", "textureWonFourSparseSave": "开始初始化", "denseRowTransientAssertionLineFourth": "已经/正在初始化-(%ld)", "partialProductCinematicKitReversedAssign": "初始化完成", "poloAreOcclusionGopherShakeTrack": "初始化失败,%@", "hundredSecondaryNumeralStorylineShelf": "初始化完成，检测调用过登录接口，现在去登录", "remembersRenewalChangeWaxHyphen": "正在登录中...", "logAirborneMasteringBendSymmetricDecision": "已登录，返回用户信息", "styleHueTapBiotinPlacementCan": "准备登录", "diagnoseDirectlyFarSlashedSpaFormatted": "未完成初始化", "uniquePaceLowThousandsNegativeBlend": "开始登录", "chromaWrittenEyeWithinUrgentMegahertz": "登录成功", "availBridgeLocalizedClampLexical": "退出登录", "verticalUnitCutChlorideBrushBand": "上报角色信息", "slashesAdditiveLookIrishThreadWrapped": "上报角色信息成功", "alienFireBlobManParameterCatalog": "上报角色信息失败", "interAvailableWhoKannadaCadence": "拉起支付", "discardedProducingLiterConsumerEngravedContents": "支付成功", "vitalityDelayedChatterEighteenAdjust": "支付失败", "exposureElderPopoverReplaceIndian": "取消支付", "ascentRainHasBedReflectProcedure": "冷启动 URL %@", "megawattsSymbolButTransformReuseClock": "热启动 URL %@", "fatalPromotionStreamsAppearingCopper": "当前网络状态 - %@", "uptimeInterlaceSparseEnableAlignmentCup": "Action事件-上报触点", "devicesCousinBeenMillOccurredCricket": "Action事件-跳转界面: %@", "vitalDidBarCornerLandmarkPerformed": "Action事件-开场动画: %@", "withStoppedCleanupHoverChunkConverter": "Action事件-绑定手机: %d", "tableTitleTelephonyAnimatorBarsPan": "Action事件-实名认证: %d", "hyphenSentSexEqualAwakeCervical": "Action事件-MQTT", "deferredNotifiedExtendSegmentsStampForkCandidate": "[ATT] 已有进程在处理IDFA请求，忽略重复调用", "identifyOceanEdgeLegalRestOther": "[ATT] 开始IDFA权限检查流程", "peopleShowersBasalPowerIntegratePointer": "[ATT] 当前授权状态: %@ (%ld)", "dirtyWhoSmallAbortMenstrualThatReduce": "[ATT] 已获得追踪权限，直接执行回调", "sayStrictDarkVerifyDog": "[ATT] 用户已拒绝追踪权限，执行回调", "adjustingDownloadTeaspoonsStrongestWeekBridging": "[ATT] 设备限制追踪权限，执行回调", "manualSkipDarkAudiogramFunctionsBordered": "[ATT] 权限未确定，准备在合适时机请求权限", "hyphenEggComposerDetectorVariablesMusic": "[ATT] iOS版本 < 14.0，无需ATT权限，直接执行回调", "processMuteCoulombsIdenticalSymmetricUpdating": "[ATT] 注册App激活通知，等待合适时机弹出权限请求", "makeBuilderTertiaryTooRowAdvanced": "[ATT] App已激活，准备延迟%d秒后请求权限", "figureShowingRatingsDecaySeasonShear": "[ATT] 延迟后App状态: %@", "headSourcesBlendPageChildRenameSensitive": "[ATT] App处于前台活跃状态，开始请求ATT权限", "sentenceSuffixPatchClampKilometerFit": "[ATT] App非活跃状态(%@)，原因: 存在系统弹窗｜用户可能已切换到其他应用或锁屏", "cubicChunkyParagraphAgreementLessEmergency": "[ATT] 添加新的观察者2号，等待下次活跃再请求ATT权限", "likeBoxPetiteSockSilenceResonant": "[ATT] 已移除App激活通知观察者2号", "minuteMealUniversalRelevanceCanadianLoveSpecify": "[ATT] App已激活，直接请求权限", "stopTabExtrasEvictProjectSubmit": "[ATT] 已移除App激活通知观察者", "smallestNearestAwayYiddishUpscaleUnplugged": "[ATT] 正在显示ATT权限弹窗...", "indigoLetterRawChromaMixLater": "[ATT] 权限请求完成", "exerciseOxygenExchangesOcclusionPinMind": "[ATT] - 回调状态: %@ (%ld)", "leadUnlockFailCommonExtentSpringAddition": "[ATT] - 当前实际状态: %@ (%ld)", "promptArrangedArrangedDolbyLeaveSpanAccessed": "[ATT] ✅ 用户授权成功，可以获取IDFA", "snowPlusStoreOptAutoHandledBookmark": "[ATT] ⏳ 权限状态仍未确定，启动等待机制", "pairUsabilityLigaturesEvaluateDevicesCarSingular": "[ATT] ❌ 用户拒绝或限制了追踪权限", "resourceIndicatorSerializeHasRespondSpatial": "[ATT] 等待用户授权操作 - 尝试次数: %ld/%ld, 当前状态: %@", "tooSpokenPublicGrowQueryUse": "[ATT] 权限状态仍未确定，1秒后进行第%ld次检查", "resizeUndoShakeSquaredMartialKilogram": "[ATT] ⏰ 等待超时，已达到最大重试次数(%ld)，结束等待", "remainingOutputLocationsPurpleStepchildGraceful": "[ATT] 最终状态: %@，可能用户未做选择或系统延迟", "unwrapPressesAwakeFadeOffsetSetup": "[ATT] 🎯 用户已做出选择，最终状态: %@", "detectedPencilHandshakeLookupIntroBengali": "[ATT] ✅ 用户最终选择了授权", "fallbackRotatingAlgorithmMapTabularPasswords": "[ATT] ❌ 用户最终选择了拒绝", "undoneDepartureTiedEnableTeaspoonsRender": "[ATT] 🚫 系统限制了追踪权限", "connectedMostBurstJobExceededVirtual": "[ATT] 等待流程结束，执行最终回调", "priorityDiagnoseUplinkSizeFocalSegmentPostal": "[ATT] iOS版本 < 14.0，无需等待授权，直接执行回调", "snapIconCervicalLongitudeUpdateOddSun": "未确定", "looseBlurCutTintCommittedMust": "受限制", "boldHisServicesPartCarrierEgg": "已拒绝", "slidingBarFullyNoneWhoOverflow": "已授权", "earlierRevisionsDietaryNineAssignPing": "未知状态(%ld)", "updatingSinSaveHasFoldReceiptStroke": "iOS版本不支持", "protocolsNearestDescendBinWorkingTree": "前台活跃", "snowReadTryGreenJapaneseVery": "前台非活跃", "trackingSegmentThatReliablePrimariesExpecting": "后台", "artIssueSerializeFiveNetTemporal": "未知状态(%ld)", "userArbitraryKilogramsMacintoshNow": "[VK] %@", "outdoorRecipientRelationAirHumanMillibars": "[AppLovin] %@", "deepHoverReflectInterlaceNoticeDisparity": "[Poopo] %@", "arrowNetHebrewMaskRectangleWarp": "[<PERSON><PERSON><PERSON><PERSON><PERSON>] %@", "needSubGroupedNapPrefixFace": "[Facebook] %@", "enhanceRedirectCupAnchoringInstantRevision": "[Firebase] %@", "signKeyboardEstonianHungarianNineCapturing": "[Adjust] %@", "disappearTwentyReadyDescenderSpaHash": "✅存在", "originsAreDetermineAllCurlView": "❌不存在", "optFullBothBasalRelationMidTab": "✅存在 版本:%@", "appleSymbolsTriggeredYahooScrollingNonce": "[BDASignal] %@", "fifteenDraftIndexRealmDayApplier": "[<PERSON><PERSON><PERSON>] %@", "subscribeRotorMildCapHigherPronoun": "[%@ 调用 %@]", "helperCoercionLateNorthMayDanceExist": "--- 实例方法签名未找到，尝试类方法 ---", "utilityFactCharacterTenUnderlineLocalesBlue": "--- 类方法签名也未找到，返回nil ---", "manualLingerEchoKeyGroupLayeringRegistry": "--- 参数数量不匹配: 期望 %lu，实际 %lu ---", "floaterPreventedMinQualifiedTheLoad": "[IAP] 恢复订单 %lu/%lu 状态:%ld 详情:%@", "indentFifteenCurrencyCallingManProposal": "[IAP] -----------收到产品反馈信息--------------", "magnesiumFootersOurHockeyNanogramsPresented": "[IAP] 产品付费数量: %d", "nothingPatientDigitTaskTypeBehavior": "[IAP] 产品标题 %@", "arrowQueryPlacementPashtoBookmarkObserved": "[IAP] 产品描述信息: %@", "bevelSoccerDeltaDropLoseSin": "[IAP] 价格: %@", "siteBlueHoverPascalConstantHost": "[IAP] 产品ID: %@", "maxPacketsExpiresHailMethodAuthority": "[IAP] 货币代码:%@  符号:%@", "flatnessOpaqueManualDeviationHectaresAssertion": "[IAP] 开始进行购买: %@,%@", "sixShipmentPicturesGrandsonDarkenAssistantMutation": "[IAP] 交易延迟", "preparingPanSummaryEpsilonGeneratorBeenIncoming": "[IAP] 丢失交易标识符!!!", "volumesJapaneseAlignEmergencyFlippedTildeOxygen": "[IAP] 购买完成,向自己的服务器验证 ---- %@,%@,paying:%lu", "magnitudeSuggestLooperBedQuarterBed": "[IAP] 添加产品列表,%@,username:%@", "goldenUnboundedSlopeSlideCloseCroatian": "[IAP] 订单丢失!?", "selectRelatedFormattedBlockCellVectorSex": "[IAP] 交易失败,%@,order:%@,error:%@", "nextSubscribeSeeGenreFitnessWait": "[IAP] 收到恢复交易: %lu", "catReceiverFifteenDuctilityChangedSynthesisMid": "[IAP] 恢复产品ID %@", "yardCurrencyWeekendForeverIllMantissa": "[IAP] 恢复错误%@", "allocatorHueMaxPaperAdaptorWateryMonth": "[IAP] 订单在后台验证成功, 但是从 IAP 的未完成订单里取不到这比交易的错误 transactionIdentifier: %@", "sensitiveLogGradeNoneSpeakersHomepageKit": "[IAP] 准备删除储存订单:%@", "notifiesMoodPurchasedPhaseIncludingSequence": "[IAP] 验证回调:product:%@", "safetyStrengthDustActionsStormMidFrame": "[IAP] 票据刷新成功", "iodineIdiomExtrasMinDesiredTropicalRole": "[IAP] 票据刷新错误:%@", "nepaliAcrossYardSugarSelectorsContact": "[IAP] 正在验证中....%@", "funnelContainsObtainSequencerNodeUsage": "[IAP] 开始验证....%@", "icyFoggyUseVolumesUnwind": "[IAP] %@", "hairGetEarRetLibrariesBaselinesMagnesium": "[IAP] 完成删除订单:%@", "refreshRecentlyOldestPasteKashmiriGetContainer": "[IAP] 删除订单失败:%@", "oddRebusEditorShutterPushPeriod": "[MQTT]收到消息主题: %@ \nTYPE: %@ \nJSON: %@", "taskJobLawSize": "o<PERSON>h", "sundaneseTen": "src", "singleGoldenArrivalAccountsBarriers": "auth_token", "mayCarSuchRear": "nonce", "checkingTipCornersPhaseFinishingIndex": "facebook", "limitSixFindDryMixer": "vk", "boxFoodPolarExpandedNow": "poopo", "localeClustersDomainsHerTerabytes": "real_id", "advancesStableArtEndColor": "real_name", "webpageOcclusionIslamicSpeechAxes": "adjid", "tintPartially": "code", "armManSonLoops": "appid", "priceBevelCharWarnManText": "ReportPassMathRemainderDidEra", "magentaStrategySeedBankWonPrototype": "TryStayWriteJumpTransposeBand", "sharpnessAuditedLocationsLaterDetailedPreset": "FathomsRemotelyPairKernelSodiumThumbnail", "endsMutationsRetainSingularDegree": "LongDistanceTrainerFilmCoached", "timeRingHighHockeyFinalConsumer": "PastAgeFrameDidRectum", "canceledDeleteEffectBadDownhillMongolian": "ServicePrivacyPanelPubKernels", "welshSixUpdateSonEightAxes": "BringSoftStyleNotationCombiningChoose", "loudLeaveMeasureConvertProducesDispatch": "VolatilePaceSquaresRepublicNordicFiltered", "containedZoomingAgeMediumIronStylistic": "HitMattingRequestLeadRequestAllergy", "nameZoneEnable": "exit", "flowDayCompoundEraserAltitudeFeatured": "unsubscribe", "capExecutingPieceTwitterFile": "topic", "sheOffHoldPut": "qos", "oneEyeLeapLogo": "type", "numeralForLenientTapNextImpact": "red_dot", "subscriptSilentLawEstimatedAloneEnglish": "marquee", "tiedUnionGregorianMildSobLog": "music", "protocolDublinHasGreatPrologVortex": "popup", "armFeatSoundQuotesResizeReceiver": "ucenter", "malePopoverTruncatedReferentBeganLeaky": "offline", "eventualGesturesCyclingCaptureSumAirline": "apple_review", "allPendingOverhangNepaliAnd": "label", "privacyObscuredSkipSynthesisIcy": "click", "paperHighlight": "jump", "icyEyeHowHair": "url", "extrasTrialMealPinPolicies": "action", "capsEggSortSaw": "open", "senseFixTorqueTypeSaveMagic": "openURL", "evaluatedPoloWhoExpansionResizeRomanian": "changePassword", "senderShortBoxRowStylize": "bindMobile", "millGigabytesAccessoryFriendsToggleMenstrual": "switchAccount", "sexSonBagRareDocumentPlus": "ucenter", "quarterGenderDingbatsCubeIssueRebus": "iapRepair", "neverProvinceReturningMovementFemaleProvider": "getInfomation", "minderDegreesSheVersionKernelsLongerSpectral": "uid", "findSheetPublishOffsetProvinceWayUnbounded": "name", "pickerResizingProcedureBigPurchasedSumFat": "token", "framePronounCoverEncodeArtShePanel": "fbuid", "unboundedProcessedReviewPieceShadowRequireYet": "fbtoken", "awakeFormatsLightPluralKeyWrapperColor": "fbauthtoken", "outputCalciumMainSindhiOrnamentsThresholdLocally": "fbnonce", "moduleCollationMarqueePintEvaluateScopeUnder": "user", "capAlbumNotBloodOnceFact": "userInfoSub", "transientBounceFastCameraWetHandoff": "closeSplash", "monitoredReadNumeratorProductOpacityNineSupports": "openUserCenterSidebar", "armDesktopRedEnclosingCinematicAgent": "accountRemove", "contentsLockingBlobWeekDenyHigh": "getApiUrl", "milesAdoptCreateStyleNowToken": "getToken", "birthCenteredOriginalEightImpliedReactor": "facebookShare", "windowsCreditsQueryingSectionPositionPub": "facebookSub", "responseFlexibleEulerDarkActivityOuter": "facebookBind", "mediumSwedishCropIncludingThatBeginning": "facebookInvite", "fixingReasonResolvedAdjustingNorwegian": "popup", "changingSyntheticRouteAllocatedCursive": "coinPay", "dynamicPathParseOrderedDeepLenient": "continueOrder", "appearingSecurelyReadoutRateRussian": "wxL<PERSON>in", "coastCoulombsVowelFemaleGloballyRepeatHectares": "returnInfomation(`%@`)", "barriersZoomUnpluggedDogAlgorithmCentersToken": "returnToken(`%@`)", "ellipsisSlopeCallAllSixteenRoute": "returnApiUrl(`%@`)", "indoorFourthReduceHailEscape": "%@%@landscape=%@&language=%@&token=%@", "bracketExecuteFunMixCross": "&token=", "draftCollationReferentArmourMeasured": "%@%@landscape=%@&language=%@%@", "whoClaimPut": "&", "lowEvictMapSun": "?", "shoulderWayNodeMountEpisodeTight": "priceString", "iterativeFairSmileUtteranceSharpnessPast": "seriverOrder", "offsetsLearnEligiblePreventsRejectLexical": "userId", "globalWetLevelExpandMarginCase": "codeString", "streamStaticTrustItsKeyModalIdentifier": "productIdentifier", "securityCroppingReplaceCacheFemaleBatteryTail": "applicationUsername", "favoritesMaskAudiogramFourKinCloudStatus": "transactionStatus", "whoDiagnoseKeyInfoDeepCellDate": "transactionDate", "priceTomorrowRowsArteryUnableTransmit": "domain.iap.error", "digitizedPetabytesMovementGramDog": "laiguo", "cocoaCarButOutcomeNextEdgeEpsilon": "NSPhotoLibraryAddUsageDescription", "ampereCapturedAffineDiscoverHasVirtual": "en,zh-<PERSON>,zh-<PERSON><PERSON>,th,vi,ru", "learnEffortSpeakEventualMinSocial": "singleBarNoteTwelveActive", "spatialItemBurnWalkHandoverAchieved": "boxSmoothingLowNetDeny", "menuPerformerHalfMicroCollapsedMongolian": "logger-queue-%@", "contentsFarForwardsHallPrototypeArabicInverted": "VERBOSE", "schoolBatchDriveEditMusicSaw": "DEBUG", "weekArmpitBackwardsDuplexMalteseCustom": "INFO", "encodingsAmbienceCadenceIntrinsicBitFilteredSemantics": "WARNING", "anyManganeseFreezingNotifyLazySemaphore": "ERROR", "binaryPermittedPathCheckingLongPartner": "ALL", "majorDescribeCoverOutletPencilDisorderWarning": "🟣", "foldResponseEraCallbackPutRepeats": "🟢", "noneCanRecordingPrecisionMaxDetection": "🔵", "fallbackExistentDetermineInternetOutsideTertiaryBin": "🟡", "claimLabeledStrokeEggResignToday": "🔴", "gatheringDiskButterflyAdoptEnumerateKilogram": "⚫️", "whoAbsoluteSpecialRecordAdvertiseReceipt": "HH:mm:ss.SSS", "disabledVisibleUploadingTheYoungestContents": "%@ [闲闲SDK-%@] %@", "didInvertSumRenewInferiorsLocalizes": "%@[闲闲SDK-%@%@:%@] %@", "enumerateGoogleRestoreSinkStartupPreserved": "(null)", "volumesWithinSunNotationDistortedOutput": "(无参数)", "headphoneInputAlphabetCenterMovieDeparture": "(文本数据)", "allDescentLimitedTertiaryOceanMovie": "(文本数据: %lu chars)", "airSaturatedWalkSongEggPersist": "(二进制数据: %lu bytes)", "receiptCustomCatIterativeIronAudible": "(无错误)", "displayedRankedEldestInferPortalCycling": "错误码:", "specifiedSentinelSegueEncodingsTryLaw": "描述:", "covariantSockReportingNegatePrintIgnoring": "详细信息:", "petabytesSubscribeCustomPieceShoulderObsolete": "...%ld items...", "symbolStablePointExponentCupInterrupt": "<Data: %lu bytes>", "softballPostSenseGenreGoldenPerfusion": "yyyy-MM-dd HH:mm:ss", "browsingMaintainKilovoltsUrgentCommandsExactFreestyle": "yyyy-MM-dd", "catMartialAssignDigitalWhileCousinEpisode": "%0.2d:%0.2d:%0.2d.%03d", "separatorExceededRunningProvidedOrdinalEndsInland": "=== %@ ===\n", "reductionCarSystemSelectEmptyDisplaysLight": "🌐 请求: %@\n参数: %@", "runningCaptureElasticTrainerRealBuddyThin": "📥 响应: %@\n数据: %@", "contactSlightAbortDefinesEraserDashAbsent": "❌ 网络错误: %@\n错误: %@", "deepDifferentCounterCloudyHighestRejection": "...", "alienDiskPostRestorePanoramaMetric": "日志查看器", "lazyPassiveTraverseScopeCoalescedVolatile": "日志系统未初始化\n请先调用 [BurstViewController heapDropYou]", "yellowHitMembersNotLowerGigabits": "暂无日志记录", "sameCellNegotiateHeightSocketElastic": "提示", "lemmaFitVowelKitAddressMirrored": "暂无日志文件", "italicSurgeAppliesIllegalProgramSpeak": "确定", "doneVerySemanticsNegativeLatitudeOrdinalsHockey": "分享日志", "internalRepeatSequencerNowSexualHuePint": "分享所有日志", "semicolonSpectralPhaseStripFactorHasSay": "分享 %@", "busHeartOptimizeSnapLossEgg": "取消", "halftoneSixEyeDarkenMoleAddressExtras": "选择日期", "operandMismatchPersianWakePresetIncrement": "所有日志", "orderedActionJobTightNarrativePrevious": "今天", "cancelledEarClampingRefusedAlcoholDeviationArea": "昨天", "yahooRegisterSolveNegateSobMidFeatures": "yyyy年MM月dd日", "strictlyWeekendSockAdditionMusicianOptimized": {"ourCustom": "name", "weeklySindhiCode": "code", "encodeWayCode": "dial_code"}, "spacingPrologEventButSignWho": "←", "penHeapMidAttachedFormattedWrong": "✕", "thermalTemplateBehaviorsDictationRealPaste": "_UIAlertControllerShimPresenterWindow", "borderCurrentlyStreamDurationCricketButterfly": "FLOAT_CENTER", "beatStayAnyBin": "action", "friendUnable": "show", "overInnerWho": "hide", "retLookupLongSuggestedCat": "show_once", "loopsWatery": "url", "rearWalkYetName": "username", "ageFloatWonKey": "password", "telephonyIndoorRepairProvinceHoldCursive": "已复制到剪贴板", "artPrimariesIntensitySampleFullyTransport": "工程配置", "kilobytesProvideOldThumbAdobeCutoff": "产品信息", "pongPubProfileEligibleOwnerOther": "SDK信息", "flowLinerFinalizePatchRowGasp": "设备信息", "skipHashModifyPastCollectedOpt": "SDK配置", "desiredFrenchAlarmAgeHandledTabular": "三方参数", "rowDocumentsSoundCompactTemporaryRecoveredBound": "person.fill.questionmark", "intersectContentDensityMongolianGermanTibetan": "message.fill", "paletteRespondBodyPartialSuccessLate": "phone.fill", "defaultsOffsetOwnershipArtGermanBrand": "bubble.right.fill", "polishUnorderedMustElevationLaterPrefers": "mqq://im/chat?chat_type=wpa&uin=%@&version=1&src_type=web", "formatPatientAmbiguousBadFixSlashed": "tel://%@", "lyricistAttachedCorruptKeysTintDescended": "SDK VERSION：%@", "wireFolder": "guest", "touchBadLease": "register", "featWin": "weixin", "speakModifiers": "one_click", "olympus": "vk", "brushAppleEra": "facebook", "highestPen": "poopo", "subjectQueryingBookmarksDisplayedWin": "login", "maxBoldWakeSon": "bind", "drainRequestSnowNativeAdobe": "password", "loadingUnsafe": "+", "closureLiveArabicIterateVisual": "var meta = document.createElement('meta'); meta.setAttribute('name', 'viewport'); meta.setAttribute('content', 'width=device-width'); document.getElementsByTagName('head')[0].appendChild(meta);", "trackingAngleZoomAddPhotoArm": "var script = document.createElement('meta'); script.name = 'viewport'; script.content=\"width=device-width, user-scalable=no\"; document.getElementsByTagName('head')[0].appendChild(script);", "feedbackCapturingBalticNaturalUkrainianActivate": "document.documentElement.style.webkitTouchCallout='none';", "capturePreservedBoldfaceExtentsStroke": "document.documentElement.style.webkitUserSelect='none';", "seekHueQueue": "http", "artsSummariesNewsstandBaselineSuch": "kds_token", "minArtsDogExportOuterCompile": "<b>", "featuredPairObscuresRenewedPaceAge": "</b>", "incrementRevisionMixEvictionPlanarPager": "__OrderExtraBlock", "popPercentFemaleCenteringArmpit": "txt", "varianceShakeTipFactoryLeft": "style", "negateBeenCombinedChecksumNotified": "width", "glyphLostEchoQuotationSession": "height", "ageAirIllIssue": "url", "renewalInsertedAreThirdTipConsole": "close_button", "ascendingDigestBrownAnyMarqueeMin": "shade_close", "atomLessLayoutBlockEar": "is_alpha", "writePassivelyChunkyExponentStrideMetric": "optionCallGrowExistentFiberAsleep:", "hoursAbsentSonDrainStepchildRecipient": "rollBriefBridgeCupTwoWidget:", "refreshedCapturedTwelveDanishCivilCharging": "fadeFocusVersionsMaleShapeRouter:", "chooseSemicolonInnerTintWetAnchor": "xxpk_vxBtnDidClick:", "chromaSymbolListenStrokingOffPink": "xxpk_oneClickBtnDidClick:", "mismatchEraDisappearUnsavedAutomaticExpanded": "countryPostVitaminDetectorRadio:", "appendingNibblesComparedAddSpineDecompose": "hueBoxTrackingNowSonHit:", "molarAppleTapsIdentityDeveloperLow": "mandarinSpacingNewsstandAreVerifyFailure:", "parentalEmailMindfulMetabolicCommonPen": "localesWinExecutorCauseBatch", "privacyChunkSurfaceWakeSlovenianYou": "tempBigSpaSindhiCropping", "dietaryMiterBreakingDiscountsLocationsLog": "yardHowUrgentFillTransformPost", "bringRunUnitReachedKeyboard": "hasMealEggChat", "createdParseReliableGuaraniStarRelease": "handoffSegmentedBankGeneratesSlovakCatalyst", "chunkIntentsAlphaMinuteHandover": "areaRemoveIron", "widgetOverwriteDependentVeryReadoutTen": "appendedProminentHertzSuffixFeedSmart", "oldestWidgetOperatingClimbedReturning": "tripleBadYou", "fetchedBracketCountedSugarNibbles": "binBadgeSunMax", "periodPatchBikeKannadaDominantYears": "lemmaClaimMeteringDescenderEarly", "rawIllLargerSemanticLatitudeExtending": "rowAndBadIconLift", "trashDiscoverManagedIterateSegments": "bandPastEmptyPatchSix", "coastProfileUnableSchoolFive": "memberUpperOptPowerAgent", "apertureOwnSinkSnowOperation": "descenderAuditColleagueSeeIncrement", "sumMusicThumbTooFormattedAllow": "fullPlanarContactsRunBed", "libraryDependingPenSeeTipScheme": "equalRectangleBatchStereoPub", "tensionBundleStoneCanNominalPipe": "callingTwoLogicalOverSlant", "staticKeyColor": "424242", "telephonyColor": "1E9FFF", "bodyIllAlphaFaxSeekColor": "FAFAFA", "sixGetLoops": "F5F5F5", "axesCleared": "E1F5FE", "describeFit": "333333", "blinkOnline": "666666", "netOneLiftSoftwareTicketsWidth": 350, "filenamePetiteSeventeenMacintoshSymbolsHue": 238, "rankedStepFadeWidth": 400, "fitWaitItsMen": 420, "portraitsLeap": 0.45, "wasLoopLabel": 0.9, "fadeHisTail": 1, "indexSpaEar": 2, "planeRepeat": 3, "rawLogoGrow": 4, "pintBigFace": 5, "gradeWinHit": 6, "shareSumNet": 7, "rowExercise": 8, "rightSubFar": 9, "mustFatMiles": 10, "pushRendered": 12, "tapMastering": 13, "oddLemmaSend": 14, "humanPinPart": 15, "maxChatShake": 16, "moveIdiomOld": 17, "flipTipPress": 18, "areaLawEuler": 20, "filmDigitCup": 22, "onceReceives": 24, "bitThickWire": 25, "videoFileHas": 26, "tabDecayPlay": 28, "rankedInland": 30, "specifiedDry": 35, "speakerIssue": 36, "youUndoneBar": 38, "bitmapOffRet": 40, "useAddScalar": 43, "liveFoundMix": 45, "previewBrand": 48, "violationKin": 52, "mixDuplexTry": 55, "indirectHall": 57, "molarAddBond": 60, "rollbackReal": 70, "selectorsBin": 75, "wireDancePolo": 120, "pipeMailWatch": 180, "indexedBoundNapDogVitality": {"clockPointBar": "device.id", "vitaminWon": "app.id", "finishedPeerCubeDiscountsBuilt": "app.bundle_id", "dingbatsSoftwareIllTriangleWas": "app.version", "canRoundName": "app.name", "dispatchName": "sdk.name", "endsSegueNot": "sdk.version", "rowDenyNapTry": "sdk.campaign", "holdThemeSite": "sdk.platform", "teamGuide": "sdk.type"}, "heartPacketsSystolicUnionSafe": {"ourCustom": "name", "boxPhotos": "idfa", "discarded": "idfv", "outcomeSee": "model", "cardNap": "os", "agentOwnerDust": "osv", "arterySettings": "jailbreak", "nineMovePath": "doc_uuid", "manPhrasePan": "network", "xxpk_operator": "operator", "brandWord": "lang", "fastCarTip": "scale", "xxpk_screen": "screen", "manFinalTopAir": "landscape", "largerMix": "afid", "partCroatianScannerFeetWeek": "app_instance_id", "finalIntegers": "uuid"}, "axesMaintainGreekBrowsingFilename": {"busyOptTaskSum": "order.cp_order_id", "keepDriveTabCode": "order.item_id", "bagMayRemoteName": "order.item", "pitchBasque": "order.amount", "highSoloWatch": "order.server", "changeBedName": "order.role_name", "beenOddRank": "order.role_id", "launchingLevel": "order.role_level", "maleCelticInfo": "order.cp_extra_info", "elementChest": "order.id", "countRankRope": "order.elder<PERSON><PERSON>"}, "anonymousSchemeArtInterlaceArt": {"mixEraOutMenu": "order.id", "panelRemembersBothFunctionsStay": "apple_iap.receipt_data", "rectangleHighlightFreezingWelshBut": "apple_iap.item_id", "responderCarrierSpeakerMeanOunces": "apple_iap.transaction_id", "countRankRope": "apple_iap.elderWas", "circleMail": "apple_iap.price"}, "outputOrdering": {"oddWhitePubName": "role.server_name", "highSoloWatch": "role.server", "beenOddRank": "role.id", "launchingLevel": "role.level", "changeBedName": "role.name", "seePersonal": "role.extend"}, "usedJobAttempterSentinelShoulder": {"teamGuide": "type", "engineSerif": "target", "ownWarpDense": "message", "theOffFull": "force", "playSunRow": "bangs", "overallChargeComponentWindowsPretty": "orientation"}, "speakHerWasExecutingFraction": {"appearNone": "id", "wonSplatName": "name", "plateCutKey": "password", "panBoostToken": "token", "plugHighEncode": "mobile", "reuseBusTerahertzOutlinePronounTime": "time", "workFilmType": "type", "onlyWasMount": "created", "restMathIll": "fb_bind", "ownershipPortraitsArchiveTempChina": "mobile_bind", "gatheringEndDetailArchivedOverlap": "fb_uid", "spineTipLeaveToken": "fb_token", "hitSquareContentSliceBigToken": "fb_auth_token", "childrenSegmentsDefinesSwitchAgent": "fb_nonce", "redoWonHash": "vk_bind", "easyPurple": "vk_uid", "controlToken": "vk_token", "beatPanelSame": "poopo_uid", "lossyEmailToken": "poopo_token"}, "tamilIrregularOverduePrepareDays": {"buffersFill": "status", "ourCustom": "name", "cupKnowHas": "image", "jobUnknownColor": "label.color", "civilLeakyText": "label.text"}, "cleanKernelNodeEventBeat": {"bodyIllAlphaFaxSeekColor": "background.color", "telephonyColor": "color", "staticKeyColor": "font.color"}, "alphabetOutHeapEnsureCritical": {"playSunRow": "bangs", "cupKnowHas": "image", "eyeSonNodeKeep": "red_dot.image", "checkTenBold": "red_dot.offset.x", "rowAskRedone": "red_dot.offset.y", "buffersFill": "status"}, "creditsMomentaryControlRelationAgent": {"encryptEpsilon": "agreement", "sortOddFrame": "fb_home", "addressesKindMapRunEveryCathedral": "one_click_agreement", "reactorSplat": "privacy", "cellFax": "qq", "tipPress": "tel", "barAngle": "url"}, "slashedCanWristOurContainer": {"hashTransitSheBoundaryCharging": "bangs_color", "dueBuffer": "size", "barAngle": "url", "selfSortCommon": "add_token"}, "spanishHourChooseGreenCustom": {"changeDeferringRearrangeDetectorPut": "adaption.type", "hierarchyCallbackMongolianBloodClosest": "auto_login", "preserveStatus": "log_status", "xxpk_realname_bg": "realname_bg", "compressExecutorInitiallyIdleOcclusionMany": "adaption.report.adjust", "fixMolarPrivilegeRevealedRepublicReportingDry": "adaption.report.apps_flyer", "keyVisionSelectorRomanPrepIrregular": "adaption.report.facebook", "spaPerformerPascalDiscoveryFreeDocuments": "adaption.report.firebase", "likeFusionMutationFaceWalkCorners": "adaption.skin.login_btns", "pressIcyLowPoolMagicSolve": "adaption.skin.login", "millibarsSwitchEsperantoDayBigAlpineWrapper": "adaption.skin.login.register.only_login", "sliderPosterMonotonicPutCookieRead": "adaption.skin.logo", "belowDensityDefinesTextualControlsSolo": "adaption.skin.theme", "rotateExtra": "docker", "putRecording": "service", "failingBackwardFilenamesGaelicPostal": "user_center"}, "oldEscapedImpliedPrintCollapsed": {"dueAwayGoalRegister": "adjustRegister", "blackCupKinLogin": "adjustLogin", "applyPriorChromaticSendOptimized": "adjustClickPay", "hasDogLiterRed": "adjustPay", "awakeMegahertzToken": "adjustAppToken", "motionGestureTildeLegalLaw": "adjustActivate", "badEndRowsKey": "afDevKey", "regularStorm": "appid", "zeroBrushEraEntitySinhalese": "afClickPay", "iconModalSystemGigabitsSorting": "afActivate", "panSuchOur": "afPay", "dispenseUnorderedGoogleVisitFoggy": "fireClickPay", "permanentSortShearLogChecked": "fireActivate", "reclaimAgent": "firePay", "duplexDeliveryOwnHybridAlert": "fbClickPay", "tagWonMany": "fbPay", "finnishBeenRotorGradeStepson": "vk_id", "subtitlesLayoutFoodResonantBiometryElevation": "vk_secret", "handlerOffer": "adv.max_key", "cellSawRetPatchCommitted": "adv.max_reward_id", "swappedVisualTypeInsteadCatalog": "poopo_code"}, "midContainer": {"xxpk_shanya_secret": "shanyan.one_click_secret"}, "rawSucceedLargerPathLayout": {"ourCustom": "name", "teamGuide": "type", "eulerRole": "logo", "darkPeerStylus": "order_url", "badAcross": "note"}, "lawGigahertz": {"countRankRope": "<PERSON><PERSON><PERSON>", "elementChest": "id", "decodingGallonsCustomFileAttach": "pay_method", "circleMail": "price", "pitchBasque": "amount", "xxpk_amount_text": "amount_text", "xxpk_discount_text": "discount_text"}, "tibetanConnect": {"spatial": "ip", "redSpeech": "port", "longPrintable": "username", "bagEraAddMind": "password", "audioDogTooMan": "client_id", "clockwiseAvailSeeHiddenArmpit": "keep_alive", "hourFoodNap": "topics"}, "criteriaOperationSoftWasAdjustingComposer": {"teamGuide": "type", "ownWarpDense": "message", "sheBrother": "count", "stoodArtPulse": "position", "messageMax": "speed", "signerSpeakHeadphoneAtomDiscountsRepair": "style.background.alpha", "scaleRowsTertiarySheCupUpscale": "style.background.color", "unionExpiredArmDryForeverAuto": "style.text.color", "elevenTransientFileWristPrologWindows": "style.text.font.size", "visibleWrappingLeastCanceledPub": "click.action", "tapRedFixMeter": "click.url", "oldFixtureBirthdayCustodianUploaded": "buttons", "endConsole": "title", "mouthKazakh": "action", "barAngle": "url", "subjectMin": "retry"}, "crossBoldfaceLockingDetectorSin": {"criticalSourcesClipFixSymbolic": "product_id", "squareHerMay": "run_env", "bridgedHandler": "timestamp", "endsSegueNot": "version"}, "bagWonCostEgg": {"spokenPrivacyFunOffOldFactored": "account_remove", "signMissingKilometerAttributeBypassExecutor": "adjustid_report", "collapsePreservedKernelInterlaceFork": "adview", "stylusNumeralHasHerMastersLoad": "asa_report", "majorMutationsPutWarpManModel": "facebook_auth", "regionsFragmentsChangedScanningTruncates": "id_report", "eventualReviewCropEchoSon": "login", "putDismissedPrefixesIssueHasLenient": "login_guest", "napPhraseThreadEscapingCarParse": "login_mobile", "mapOddPackageJobDeriveAltitude": "login_token", "schemeLargeBlinkBeforeAttribute": "login_weixin", "sixPeakUnpluggedDrumChangeSent": "login_one_click", "creatorElevenBelowStreamedSearchingRecipient": "mobile", "bendFixRadioFaceIntensity": "order", "currencyReceivedPlacementFreeBlockerWindows": "coin_order", "scanTwelveAddUsesRedoWelsh": "order_check", "gracefulYoungestGenreCatalogEntityFriend": "coin_order_check", "radixEachRecognizeCrossSonCompress": "order_extra", "extraBoxFormatsPasteLeaveSigma": "order_receipt", "analysisSpecificRomanGregorianIrishReorder": "password_change", "guaraniPatternLookupExcludeInstallsFair": "password_reset", "resultsServerAssignPresetPoint": "real_name", "writeScannedCyrillicLatvianEuler": "register", "buddyMongolian": "role", "binaryRestingSafetyClosureNet": "sms_code", "truncatesHungarianAlbanianReportsReview": "subscribe", "downEncipherRebusCurrencyRaw": "vk_auth", "anySubProjectsTexturedCan": "weixin_auth", "finishReportPrimaryReleaseSettingsRespond": "test_report"}, "fillerReplyPotentialArabicDoneOther": ["/Applications/Cydia.app", "/usr/sbin/sshd", "/bin/bash", "/etc/apt", "/Library/MobileSubstrate", "/User/Applications/"], "buffersKilobitsEnhancedQueryRankSuffix": ["/usr/lib/CepheiUI.framework/CepheiUI", "/usr/lib/libsubstitute.dylib", "/usr/lib/substitute-inserter.dylib", "/usr/lib/substitute-loader.dylib", "/usr/lib/substrate/SubstrateLoader.dylib", "/usr/lib/substrate/SubstrateInserter.dylib", "/Library/MobileSubstrate/MobileSubstrate.dylib", "/Library/MobileSubstrate/DynamicLibraries/0Shadow.dylib"], "microVerifyOutPrintRaiseResources": ["/Application/Cydia.app", "/Library/MobileSubstrate/MobileSubstrate.dylib", "/bin/bash", "/usr/sbin/sshd", "/etc/apt", "/usr/bin/ssh", "/private/var/lib/apt", "/private/var/lib/cydia", "/private/var/tmp/cydia.log", "/Applications/WinterBoard.app", "/var/lib/cydia", "/private/etc/dpkg/origins/debian", "/bin.sh", "/private/etc/apt", "/etc/ssh/sshd_config", "/private/etc/ssh/sshd_config", "/Applications/SBSetttings.app", "/private/var/mobileLibrary/SBSettingsThemes/", "/private/var/stash", "/usr/libexec/sftp-server", "/usr/libexec/cydia/", "/usr/sbin/frida-server", "/usr/bin/cycript", "/usr/local/bin/cycript", "/usr/lib/libcycript.dylib", "/System/Library/LaunchDaemons/com.saurik.Cydia.Startup.plist", "/System/Library/LaunchDaemons/com.ikey.bbot.plist", "/Applications/FakeCarrier.app", "/Library/MobileSubstrate/DynamicLibraries/Veency.plist", "/Library/MobileSubstrate/DynamicLibraries/LiveClock.plist", "/usr/libexec/ssh-keysign", "/usr/libexec/sftp-server", "/Applications/blackra1n.app", "/Applications/IntelliScreen.app", "/Applications/Snoop-itConfig.app", "/var/lib/dpkg/info"], "dictationNetAscendedAcceptedKitVariable": ["HBPreferences"], "availDominantKilobytesAfterRawComplex": "cydia://package/com.avl.com", "resultingSemicolonFactoryDragNeutralPetite": "cydia://package/com.example.package", "spaceAssistiveNepaliCancelsFourSuggested": "/private/avl.txt", "flattenCondensedPrimaryBurnRedMusic": "AVL was here", "feetRemovableExplicitArcadeComponentComputer": "/usr/lib/system/libsystem_kernel.dylib", "solutionsRhythmJoiningExpandProvideTool": "DYLD_INSERT_LIBRARIES", "simulatesHandleBelowCopperExemplarDisk": ["/Applications", "/var/stash/Library/Ringtones", "/var/stash/Library/Wallpaper", "/var/stash/usr/include", "/var/stash/usr/libexec", "/var/stash/usr/share", "/var/stash/usr/arm-apple-darwin9"]}