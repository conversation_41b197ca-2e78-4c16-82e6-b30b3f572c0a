// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		186AE8F82D9424B3000F1A11 /* libTryStayWriteJumpTransposeBand.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 186AE8F72D9424B3000F1A11 /* libTryStayWriteJumpTransposeBand.a */; };
		186AE8FA2D9424B3000F1A11 /* libReportPassMathRemainderDidEra.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 186AE8F92D9424B3000F1A11 /* libReportPassMathRemainderDidEra.a */; };
		186AE8FC2D9424B3000F1A11 /* libFathomsRemotelyPairKernelSodiumThumbnail.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 186AE8FB2D9424B3000F1A11 /* libFathomsRemotelyPairKernelSodiumThumbnail.a */; };
		18989E552D71CA2C00E11C3B /* GlacialActivity.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 18989E542D71CA2C00E11C3B /* GlacialActivity.framework */; };
		18DD450F2D7EC0F500E809EC /* GlacialActivity.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 18DD450E2D7EC0F500E809EC /* GlacialActivity.bundle */; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		187C510C2DC2BFE9001A02F7 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		186AE8F72D9424B3000F1A11 /* libTryStayWriteJumpTransposeBand.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; path = libTryStayWriteJumpTransposeBand.a; sourceTree = BUILT_PRODUCTS_DIR; };
		186AE8F92D9424B3000F1A11 /* libReportPassMathRemainderDidEra.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; path = libReportPassMathRemainderDidEra.a; sourceTree = BUILT_PRODUCTS_DIR; };
		186AE8FB2D9424B3000F1A11 /* libFathomsRemotelyPairKernelSodiumThumbnail.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; path = libFathomsRemotelyPairKernelSodiumThumbnail.a; sourceTree = BUILT_PRODUCTS_DIR; };
		18989E1C2D71C70800E11C3B /* XXGPlayKitOSDemo.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = XXGPlayKitOSDemo.app; sourceTree = BUILT_PRODUCTS_DIR; };
		18989E542D71CA2C00E11C3B /* GlacialActivity.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = GlacialActivity.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		18DD450E2D7EC0F500E809EC /* GlacialActivity.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; name = GlacialActivity.bundle; path = ../GlacialActivity.bundle; sourceTree = SOURCE_ROOT; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		18E064252DC47DD000F15793 = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 18989E1B2D71C70800E11C3B /* XXGPlayKitOSDemo */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		1815F6922DBF3180008EBF46 /* OSFrameworks */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			name = OSFrameworks;
			path = ../OSFrameworks;
			sourceTree = SOURCE_ROOT;
		};
		188F7D792DA4F72200699DF4 /* OSResources */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			name = OSResources;
			path = ../OSResources;
			sourceTree = SOURCE_ROOT;
		};
		18E0641A2DC47DD000F15793 /* XXGPlayKitOSDemo */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				18E064252DC47DD000F15793,
			);
			path = XXGPlayKitOSDemo;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		18989E192D71C70800E11C3B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				186AE8F82D9424B3000F1A11 /* libTryStayWriteJumpTransposeBand.a in Frameworks */,
				186AE8FA2D9424B3000F1A11 /* libReportPassMathRemainderDidEra.a in Frameworks */,
				186AE8FC2D9424B3000F1A11 /* libFathomsRemotelyPairKernelSodiumThumbnail.a in Frameworks */,
				18989E552D71CA2C00E11C3B /* GlacialActivity.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		18989E132D71C70800E11C3B = {
			isa = PBXGroup;
			children = (
				1815F6922DBF3180008EBF46 /* OSFrameworks */,
				188F7D792DA4F72200699DF4 /* OSResources */,
				18DD450E2D7EC0F500E809EC /* GlacialActivity.bundle */,
				18E0641A2DC47DD000F15793 /* XXGPlayKitOSDemo */,
				18989E532D71CA2C00E11C3B /* Frameworks */,
				18989E1D2D71C70800E11C3B /* Products */,
			);
			sourceTree = "<group>";
		};
		18989E1D2D71C70800E11C3B /* Products */ = {
			isa = PBXGroup;
			children = (
				18989E1C2D71C70800E11C3B /* XXGPlayKitOSDemo.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		18989E532D71CA2C00E11C3B /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				186AE8F72D9424B3000F1A11 /* libTryStayWriteJumpTransposeBand.a */,
				186AE8F92D9424B3000F1A11 /* libReportPassMathRemainderDidEra.a */,
				186AE8FB2D9424B3000F1A11 /* libFathomsRemotelyPairKernelSodiumThumbnail.a */,
				18989E542D71CA2C00E11C3B /* GlacialActivity.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		18989E1B2D71C70800E11C3B /* XXGPlayKitOSDemo */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 18989E342D71C70900E11C3B /* Build configuration list for PBXNativeTarget "XXGPlayKitOSDemo" */;
			buildPhases = (
				18989E182D71C70800E11C3B /* Sources */,
				18989E192D71C70800E11C3B /* Frameworks */,
				18989E1A2D71C70800E11C3B /* Resources */,
				187C510C2DC2BFE9001A02F7 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				1815F6922DBF3180008EBF46 /* OSFrameworks */,
				188F7D792DA4F72200699DF4 /* OSResources */,
				18E0641A2DC47DD000F15793 /* XXGPlayKitOSDemo */,
			);
			name = XXGPlayKitOSDemo;
			packageProductDependencies = (
			);
			productName = XXGPlayKitOSDemo;
			productReference = 18989E1C2D71C70800E11C3B /* XXGPlayKitOSDemo.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		18989E142D71C70800E11C3B /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					18989E1B2D71C70800E11C3B = {
						CreatedOnToolsVersion = 16.2;
						LastSwiftMigration = 1620;
					};
				};
			};
			buildConfigurationList = 18989E172D71C70800E11C3B /* Build configuration list for PBXProject "XXGPlayKitOSDemo" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 18989E132D71C70800E11C3B;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 18989E1D2D71C70800E11C3B /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				18989E1B2D71C70800E11C3B /* XXGPlayKitOSDemo */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		18989E1A2D71C70800E11C3B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				18DD450F2D7EC0F500E809EC /* GlacialActivity.bundle in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		18989E182D71C70800E11C3B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		18989E352D71C70900E11C3B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = W5ELC54N2N;
				FRAMEWORK_SEARCH_PATHS = (
					"\"$(SRCROOT)/../OSFrameworks/AppsFlyer\"",
					"\"$(SRCROOT)/../OSFrameworks/Facebook\"",
					"\"$(SRCROOT)/../OSFrameworks/Firebase/FirebaseAnalytics\"",
					"\"$(SRCROOT)/../OSFrameworks/Firebase/FirebaseCrashlytics\"",
				);
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
					XXGPLAYKIT_DEBUG,
				);
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = XXGPlayKitOSDemo/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "闲闲游戏";
				INFOPLIST_KEY_NSCameraUsageDescription = "This app requires your permission to access the camera. If denied, photo features within the game will be unavailable.";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "The app requires your consent to access the photo album for saving screenshots of account passwords to your photo album. If you prohibit this access, the saving operation cannot be completed.";
				INFOPLIST_KEY_NSUserTrackingUsageDescription = "The App needs to access your device identifier (IDFA) to provide content that is more relevant to your interests and reduce irrelevant advertising recommendations.";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 13;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.0;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_BUNDLE_IDENTIFIER = com.xxgame.sdk.int;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 6.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		18989E362D71C70900E11C3B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = W5ELC54N2N;
				FRAMEWORK_SEARCH_PATHS = (
					"\"$(SRCROOT)/../OSFrameworks/AppsFlyer\"",
					"\"$(SRCROOT)/../OSFrameworks/Facebook\"",
					"\"$(SRCROOT)/../OSFrameworks/Firebase/FirebaseAnalytics\"",
					"\"$(SRCROOT)/../OSFrameworks/Firebase/FirebaseCrashlytics\"",
				);
				GCC_PREPROCESSOR_DEFINITIONS = XXGPLAYKIT_DEBUG;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = XXGPlayKitOSDemo/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "闲闲游戏";
				INFOPLIST_KEY_NSCameraUsageDescription = "This app requires your permission to access the camera. If denied, photo features within the game will be unavailable.";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "The app requires your consent to access the photo album for saving screenshots of account passwords to your photo album. If you prohibit this access, the saving operation cannot be completed.";
				INFOPLIST_KEY_NSUserTrackingUsageDescription = "The App needs to access your device identifier (IDFA) to provide content that is more relevant to your interests and reduce irrelevant advertising recommendations.";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 13;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.0;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_BUNDLE_IDENTIFIER = com.xxgame.sdk.int;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 6.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		18989E372D71C70900E11C3B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		18989E382D71C70900E11C3B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		18989E172D71C70800E11C3B /* Build configuration list for PBXProject "XXGPlayKitOSDemo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				18989E372D71C70900E11C3B /* Debug */,
				18989E382D71C70900E11C3B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		18989E342D71C70900E11C3B /* Build configuration list for PBXNativeTarget "XXGPlayKitOSDemo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				18989E352D71C70900E11C3B /* Debug */,
				18989E362D71C70900E11C3B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 18989E142D71C70800E11C3B /* Project object */;
}
