/*********************************
 *********************************
 ***当前SDK接口对接演示，请结合文档对接
 *********************************
 *********************************
 **/
#import "_.h"
#import "ViewController.h"
#import <GlacialActivity/GlacialActivity.h>

// MARK: - 遵循协议（必接）
@interface ViewController ()<MembersDelegate>

@end

@implementation ViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    // MARK: - 设置各个接口回调代理（必接）
    // 设置代理用于接收登录、退出、支付、上报角色结果
    [Mandatory mostBusTrackDelegate:self];
}

//MARK: - 登录（必接）
- (IBAction)demo_login:(id)sender {
    [self demo_log:@"点击登录"];
    
    [Mandatory albumUsePop];
}

//MARK: - 退出（必接）
- (IBAction)demo_logout:(id)sender {
    [self demo_log:@"点击退出"];
    
    [Mandatory texturedAir];
}

//MARK: - 支付（必接）
- (IBAction)demo_pay:(id)sender {
    [self demo_log:@"点击支付"];
    /**
     * 订单模型
     * 注意：确保传进的参数都为NSString类型，不接受NSNumber类型
     */
    NSString *cpOrderId = NSUUID.UUID.UUIDString;        // 游戏生成的订单号 (必传)
    NSString *productCode = @"com.xxgame.sdk.demo.test"; // 商品标识(苹果商品标识)(必传)
    NSString *amount = @"6";                             // 商品金额（单位：元） (必传)
    NSString *productName = @"6元套餐";                   // 商品名称、例：60元宝 (必传)
    NSString *serverId = @"20190927001";                 // 用户游戏角色所在的服务器id (必传)
    NSString *roleId = @"100001";                        // 用户游戏角色ID (选传)
    NSString *roleName = @"角色-XXGameSDK";               // 用户游戏角色名称 (选传)
    NSString *roleLevel = @"99";                          // 用户游戏角色等级 (选传)
    NSString *extraInfo = @"2019";                        // 订单额外信息，最终将回传给游戏服务器 (选传)
    
    /**
     * 支付,传入订单模型
     * 异步回调支付结果
     */
    [Mandatory jobEntriesStrongestInverseOrdinals:cpOrderId
             keepDriveTabCode:productCode
                  pitchBasque:amount
             bagMayRemoteName:productName
                highSoloWatch:serverId
               maleCelticInfo:extraInfo
                  beenOddRank:roleId
                changeBedName:roleName
               launchingLevel:roleLevel];
}

//MARK: - 上报角色（必接）
- (IBAction)demo_uploadRoleInfo:(id)sender {
    [self demo_log:@"点击上报角色"];
    /**
     * 必接！！！
     * 注意：确保传进的参数都为NSString类型，不接受NSNumber类型，extend字段类型为字典
     * 调用时机(重要!!只在以下两个场景需调用):
     * - 玩家在选择区服进入游戏时调用该接口。
     * - 角色升级或其他角色汇报信息发生变化时调用该接
     */
    // 区服id (必传)
    NSString *serverId = @"20190927001";
    // 区服名称 (必传)
    NSString *serverName = @"XXGame";
    // 用户游戏角色ID (必传)
    NSString *roleId = @"100001";
    // 角色名称 (必传)
    NSString *roleName = @"角色-XXGameSDK";
    // 角色等级 (必传)
    NSString *roleLevel = @"1";
    // 角色扩展信息（选填）类型：字典
    NSDictionary *extend = @{
        @"pet": @"60",          //宠物等级（5）
        @"horse": @"15",        //坐骑等级（1）
        @"power": @"10000",        //战力（100）
        @"promote": @"2",      //转职（2转）
        @"married": @"0",      //'0': 未婚, '1': 已婚
        @"liveness": @"2000",     //活跃度 (2000)
        @"hero_level": @"98",               //英雄等级(98级)
        @"trumps": @[ //已激活的法宝列表
             @"fabao1",   //法宝1
             @"fabao2"    //法宝2
        ],
        @"wings": @[  //已激活的翅膀列表
             @"wing1",    //翅膀1
             @"wing2"     //翅膀2
        ],
        @"artifacts": @[  //已激活的神器列表
             @"artifact1",    //神器1
             @"artifact2",    //神器2
        ],
        //@"xxx":@"xxx"                     //其他自定义信息
        //@"xxx":@"xxx"                     //其他自定义信息
        //@"xxx":@"xxx"                     //其他自定义信息
        //...
    };
    
    /**上报角色信息*/
    [Mandatory depthChromaticAppendBackwardRowInfo:serverId
                   oddWhitePubName:serverName
                       beenOddRank:roleId
                     changeBedName:roleName
                    launchingLevel:roleLevel
                       seePersonal:extend];
}

//MARK: - 打开个人中心
- (IBAction)demo_openUserCenter:(id)sender {
    [self demo_log:@"点击打开个人中心"];
    /*
     @param type  指定打开默认展示页面；NSString类型
        Red -  红包
        My - 我的
        Gift - 礼包
        Share - 分享
        Activity - 活动
     */
    [Mandatory pipePastDictationPoloDecigramsEar:@"My"];
}

//MARK: - 绑定Facebook
- (IBAction)demo_bindFacebook:(UIButton *)sender {
    [self demo_log:@"点击绑定Facebook"];
    [Mandatory resonantSourcesReversesMotionAscender:^(NSDictionary * _Nullable userInfo, NSString * _Nonnull errorMsg) {
        if (userInfo) {
            // 绑定成功
            [self demo_log:[NSString stringWithFormat:@"绑定 Facebook 成功！userInfo:%@",userInfo.description]];
        }else {
            // 绑定失败
            [self demo_log:[NSString stringWithFormat:@"绑定 Facebook 失败 : %@", errorMsg]];
        }
    }];
}

//MARK: - 绑定VK
- (IBAction)demo_bindFVK:(UIButton *)sender {
    [self demo_log:@"点击绑定VK"];
    [Mandatory rowFoodCurl:^(NSDictionary * _Nullable userInfo, NSString * _Nonnull errorMsg) {
        if (userInfo) {
            // 绑定成功
            [self demo_log:[NSString stringWithFormat:@"绑定 VK 成功！userInfo:%@",userInfo.description]];
        }else {
            // 绑定失败
            [self demo_log:[NSString stringWithFormat:@"绑定 VK 失败 : %@", errorMsg]];
        }
    }];
}

//MARK: - 事件打点
- (IBAction)demo_logEvent {
    [self demo_log:@"点击事件打点"];
    /*
     根据运营提供第三方打点平台事件，接入对应第三方平台接口，平台：AppFlyer、Facebook、Firebase、Adjust
     例如：如果运营只提供了AppFlyer和Firebase的打点参数 则：
     [Mandatory stickyToneMattingSurgeAnyOpaque:@"eventName" params:params];
     [Mandatory clustersSeasonStampUsesGopherTen:@"eventName" params:nil]; // params未要求提供可传nil
     
     @param - event：为事件标识，类型：NSString
     @param - params：透传额外参数，类型：NSDictionary 例如：@{@"param_key":@"param_value"} 未要求提供可传nil
     */
    NSDictionary *params = @{@"param_key":@"param_value"};
    // AppFlyer
    [Mandatory stickyToneMattingSurgeAnyOpaque:@"demo_eventName" params:params];
    // Facebook
    [Mandatory mealMemoryCombinePinchResourcesProviding:@"demo_eventName" params:params];
    // Firebase
    [Mandatory clustersSeasonStampUsesGopherTen:@"demo_eventName" params:params];
    // Adjust
    [Mandatory whoPressesWrappedDraftUnload:@"demo_eventName" params:params];
}

//MARK: - 激励广告
- (IBAction)demo_showRewaredAd:(id)sender {
    [self demo_log:@"点击激励广告"];
    /*
     * 为了防止激励广告音频干扰应用的背景音频，AppLovin 建议您在展示广告之前停止应用的背景音频。 关闭广告后，您可以恢复应用的背景音频。
     
     * @param customData 自定义参数，传入后可通过服务端接口获取，可为 nil
     */
    NSString *demo_customData = @"customData";
    [Mandatory squaredFoodInstantClockMeterWakeData:demo_customData clipBits:^(BOOL result) {
        if (result) {
            [self demo_log:[NSString stringWithFormat:@"完成激励广告任务回调-携带参数-%@", demo_customData]];
        }else {
            [self demo_log:@"未完成激励广告"];
        }
    }];
}

//MARK: - 上报日志
- (IBAction)demo_reportlog:(id)sender {
    NSString *type = @"login";// type:任意String类型
    NSString *content = @"success";// content:任意String类型
    [Mandatory textInvertTrapCommentsModifierType:type yetAffiliate:content];
}

//MARK: - 苹果内购修复
- (IBAction)demo_iapRepair:(id)sender {
    [Mandatory darkDivideHalf];
}

// MARK: MembersDelegate
///登录回调（必接）
- (void)printedApplyingReadoutResourcesInitiated:(NSDictionary *)penTrait {
    [self demo_log:[NSString stringWithFormat:@"登录回调-登录成功 - %@", penTrait.description]];
}

///退出回调（必接）
// 必接 - 对接方需在此回调中调用游戏切换账号接口,退到游戏【登录界面】
// SDK个人中心有切换账号功能，所以用户在个人中心操作切换账号需游戏一并退出
// 另外游戏内退出同时调用SDK的logout:函数也会走此回调，
// 所以建议游戏的退出接口直接调用SDK的logout:在此回调中一并退出，不然游戏会重复退出2次
- (void)twistNoiseAre {
    [self demo_log:@"退出回调-SDK已登录出"];
}

///支付回调
- (void)commitAlertOrdinalsGuestUpper:(BOOL)isSuc {
    [self demo_log:[NSString stringWithFormat:@"支付回调-支付%@", isSuc?@"成功":@"失败"]];
}

///上报角色回调
- (void)fusionYardAlignSubmitTapsIntent:(BOOL)isSuc {
    [self demo_log:[NSString stringWithFormat:@"上报角色回调-上报角色%@", isSuc?@"成功":@"失败"]];
}

@end
