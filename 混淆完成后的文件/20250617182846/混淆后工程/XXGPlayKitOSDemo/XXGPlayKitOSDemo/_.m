/*********************************
 *********************************
 ***当前文件为Demo界面设置，请忽略
 *********************************
 *********************************
 **/





























































































































































































































































































































#import "_.h"
#import <GlacialActivity/GlacialActivity.h>

@implementation ViewController (Demo)

- (UIInterfaceOrientationMask)supportedInterfaceOrientations {
    return UIInterfaceOrientationMaskPortrait;
}
- (BOOL)prefersHomeIndicatorAutoHidden {
    return NO;
}
- (UIRectEdge)preferredScreenEdgesDeferringSystemGestures {
    return UIRectEdgeAll;
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    self.demo_logs = [NSMutableArray new];
    [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(unableSupportedSeekingBetweenRenderYiddish) name:PageFatZip.SingleIdiomSuperiorsGrowSelfReusableScrollMode object:nil];
    self.demo_sdk_version.text = [NSString stringWithFormat:@"SDK VERSION：%@",PageFatZip.endsSegueNot];
    [self yellowDurationFoggyPostalDeliverVertical];
    [self specifierRedoneFormattedReversesEstonianExpectRecording:self.view.subviews];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    [self pressureDescenderMiterArcadeFadeAnimation];
}

- (void)yellowDurationFoggyPostalDeliverVertical {
    UIStoryboard *panelRearrangeLegalBagCert = [UIStoryboard storyboardWithName:@"LaunchScreen" bundle:nil];
    UIViewController *alienIts = [panelRearrangeLegalBagCert instantiateInitialViewController];
    UIView *rawPanView = alienIts.view;
    rawPanView.frame = self.view.bounds;
    rawPanView.tag = 99;
    [self.view addSubview:rawPanView];
    self.demo_logo.hidden = YES;
}

- (void)pressureDescenderMiterArcadeFadeAnimation {
    UIView *rawPanView = [self.view viewWithTag:99];
    UIView *logoView = [rawPanView viewWithTag:100];
    CGPoint jobArtCenter = [self.view convertPoint:self.demo_logo.center
                                         fromView:self.demo_logo.superview];
    UIView *movingLogo = [logoView snapshotViewAfterScreenUpdates:YES];
    movingLogo.frame = [self.view convertRect:logoView.frame fromView:logoView.superview];
    [self.view addSubview:movingLogo];
    logoView.hidden = YES;
    self.demo_login_status.hidden = YES;
    // 用dispatch_after实现真正的延迟
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [UIView animateWithDuration:1.1
                              delay:0
                            options:UIViewAnimationOptionCurveEaseInOut
                         animations:^{
            rawPanView.alpha = 0;
            movingLogo.center = jobArtCenter;
            movingLogo.transform = CGAffineTransformMakeScale(self.demo_logo.bounds.size.width/movingLogo.bounds.size.width,
                                                             self.demo_logo.bounds.size.height/movingLogo.bounds.size.height);
        } completion:^(BOOL finished) {
            [UIView animateWithDuration:0.5 animations:^{
                self.demo_logo.hidden = NO;
                self.demo_login_status.hidden = NO;
            }];
            [rawPanView removeFromSuperview];
            [movingLogo removeFromSuperview];
            [self.demo_logo.layer addAnimation:[self midEyeAnimation] forKey:nil];
            // 动画结束后依次显示按钮
            [self managerPrefixedMaskIncludingAboutFifteenAcross:self.view.subviews];
        }];
    });
}

- (CABasicAnimation *)midEyeAnimation {
    CABasicAnimation *sink = [CABasicAnimation animationWithKeyPath:@"opacity"];
    sink.fromValue = @0;
    sink.toValue = @1;
    sink.duration = 0.3;
    sink.removedOnCompletion = NO;
    sink.fillMode = kCAFillModeForwards;
    return sink;
}

- (void)unableSupportedSeekingBetweenRenderYiddish {
    switch (PageFatZip.highArrivalStatus) {
        case OperatorCalciumFractionIntegrateBusResults:
            self.demo_login_status.text = @"○ 未登录";
            self.demo_login_status.textColor = UIColor.grayColor;
            break;
        case ExtentRespondDistantSquareValidatesFoldExclusion:
            self.demo_login_status.text = @"○ 准备登录";
            self.demo_login_status.textColor = UIColor.yellowColor;
            break;
        case MinorFaceOnlyAdvisoryRevisionsMicroLoading:
            self.demo_login_status.text = @"○ 登录中...";
            self.demo_login_status.textColor = UIColor.blueColor;
            break;
        case BodyLearnedLazyIcyWasFlushed:
            self.demo_login_status.text = @"● 已登录";
            self.demo_login_status.textColor = UIColor.greenColor;
            break;
    }
}

- (void)specifierRedoneFormattedReversesEstonianExpectRecording:(NSArray *)subviews {
    for (UIView *subview in subviews) {
        [self specifierRedoneFormattedReversesEstonianExpectRecording:subview.subviews];
        if ([subview isKindOfClass:[UIButton class]]) {
            subview.layer.cornerRadius = 5.0;
            subview.layer.shadowColor = [UIColor lightGrayColor].CGColor;
            subview.layer.shadowOffset = CGSizeMake(0, 3);
            subview.layer.shadowOpacity = 0.5;
            subview.layer.shadowRadius = 4.0;
            subview.clipsToBounds = NO;
            subview.alpha = 0;
        }
   }
}

- (void)managerPrefixedMaskIncludingAboutFifteenAcross:(NSArray *)subviews {
    static NSTimeInterval calculate = 0.08;
    static NSTimeInterval celticPlanar = 0.25;
    __block NSInteger btnIndex = 0;
    for (UIView *subview in subviews) {
        [self managerPrefixedMaskIncludingAboutFifteenAcross:subview.subviews];
        if ([subview isKindOfClass:[UIButton class]]) {
            subview.alpha = 0;
            [UIView animateWithDuration:celticPlanar
                                  delay:btnIndex * calculate
                                options:UIViewAnimationOptionCurveEaseInOut
                             animations:^{
                subview.alpha = 1;
            } completion:nil];
            btnIndex++;
        }
    }
}

- (void)demo_log:(NSString *)logMessage {
    NSLog(@"%@",logMessage);
    dispatch_async(dispatch_get_main_queue(), ^{
        [self.demo_logs addObject:logMessage];
        NSIndexPath *newIndexPath = [NSIndexPath indexPathForRow:self.demo_logs.count - 1 inSection:0];
        [self.demo_tableView insertRowsAtIndexPaths:@[newIndexPath] withRowAnimation:UITableViewRowAnimationAutomatic];
        [self.demo_tableView scrollToRowAtIndexPath:newIndexPath
                             atScrollPosition:UITableViewScrollPositionBottom
                                     animated:YES];
    });
}

#pragma mark - UITableViewDataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.demo_logs.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"LogCell" forIndexPath:indexPath];
    cell.textLabel.text = self.demo_logs[indexPath.row];
    cell.textLabel.numberOfLines = 0;
    return cell;
}
@end
