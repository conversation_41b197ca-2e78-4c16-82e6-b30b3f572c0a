






#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import <VKID/VKID-Swift.h>

@interface LongDistanceTrainerFilmCoached : NSObject

@end

@implementation LongDistanceTrainerFilmCoached

+ (void)forkYouMountViewController:(UIViewController *)vc handler:(void(^)(BOOL isCancell,NSString *userID, NSString*token, NSString*error))handler {
    [[VKIDExtension shared] oauthWithPresentingController:vc completion:^(BOOL isCancell, NSString * userId, NSString * token, NSString * error) {
        if (isCancell) {
            handler(YES,@"",@"",@"");
        }else {
            handler(NO,userId,token,error);
        }
    }];
}

+ (BOOL)crossDiscardsSpeedBitsThree:(UIApplication *)application
                capsUndo:(NSURL *)url
                triangle:(nullable NSDictionary<UIApplicationOpenURLOptionsKey, id> *)options {
    [[VKIDExtension shared] handleOpenURL:url];
    return YES;
}

+ (void)badAspectParallelHasModalRealm:(NSString *)clientId notTailNonce:(NSString *)notTailNonce{
    [[VKIDExtension shared] initvkWithClientId:clientId notTailNonce:notTailNonce];
}

@end
