







#import "BDASignalManager.h"

@interface HitMattingRequestLeadRequestAllergy : NSObject

@end

@implementation HitMattingRequestLeadRequestAllergy

+ (NSString *)endsSegueNot {
    return kBDADSignalSDKVersion;
}

+ (void)sliceMilesHostAreRowLigatureOptions:(NSDictionary *)launchOptions wayInuitOptions:(UISceneConnectionOptions *)connetOptions {
    if (launchOptions) {
        
        [BDASignalManager didFinishLaunchingWithOptions:launchOptions connectOptions:nil];
        return;
    }
    if (connetOptions) {
        
        [BDASignalManager didFinishLaunchingWithOptions:nil connectOptions:connetOptions];
    }
}

+ (BOOL)crossDiscardsSpeedBitsThree:(UIApplication *)application
                capsUndo:(NSURL *)url
                triangle:(nullable NSDictionary<UIApplicationOpenURLOptionsKey, id> *)options {
    [BDASignalManager anylyseDeeplinkClickidWithOpenUrl:url.absoluteString];
    return YES;
}

@end
