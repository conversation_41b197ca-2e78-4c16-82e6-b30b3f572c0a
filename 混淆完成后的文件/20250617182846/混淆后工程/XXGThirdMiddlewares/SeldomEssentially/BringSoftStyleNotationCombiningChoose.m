








#import <AppLovinSDK/AppLovinSDK.h>
#import <AppTrackingTransparency/AppTrackingTransparency.h>

@interface BringSoftStyleNotationCombiningChoose : NSObject<MARewardedAdDelegate,MAAdViewAdDelegate>

@property (nonatomic, strong) MARewardedAd *locationsRedMixerEntityFlag;
@property (nonatomic, assign) NSInteger redirectsConstantExpensivePhaseCache;

@property (nonatomic, copy) NSString *helloFarGetData;

@property (nonatomic, copy) void (^artsDenyAbove)(BOOL result);

@end

@implementation BringSoftStyleNotationCombiningChoose

+ (instancetype)shared {
    static id shared = nil;
    static dispatch_once_t mathToken;
    dispatch_once(&mathToken, ^{
        shared = [[super allocWithZone:NULL] init];
    });
    return shared;
}

+ (NSString *)endsSegueNot {
    return ALSdk.version;
}

- (void)alwaysDecigramsStrengthBoundaryTopDown {
    [[ALSdk shared] alwaysDecigramsStrengthBoundaryTopDown];
}


- (void)longerTwoCreationIndexGuideCoverageKey:(NSString *)xxpk_maxkey requestInterTotalPublisherVariables:(NSString *)requestInterTotalPublisherVariables megawattsIntentsLoudCleanupMaximum:(NSArray *)megawattsIntentsLoudCleanupMaximum {
    
    
    
    Class cls = NSClassFromString(@"FBAdSettings");
    if (cls) {
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wundeclared-selector"
        [cls performSelector:@selector(setDataProcessingOptions:) withObject:@[]];
        [cls performSelector:@selector(setAdvertiserTrackingEnabled:) withObject:@(YES)];
#pragma clang diagnostic pop
    }
    
    

    
    ALSdkInitializationConfiguration *xxpk_configuration = [ALSdkInitializationConfiguration configurationWithSdkKey:xxpk_maxkey builderBlock:^(ALSdkInitializationConfigurationBuilder * _Nonnull builder) {
        builder.mediationProvider = ALMediationProviderMAX;
        if (megawattsIntentsLoudCleanupMaximum) {
            builder.testDeviceAdvertisingIdentifiers = megawattsIntentsLoudCleanupMaximum;
        }
        if (@available(iOS 14, *)) {
            if ([ATTrackingManager trackingAuthorizationStatus] == ATTrackingManagerAuthorizationStatusAuthorized ) {
                [ALPrivacySettings setHasUserConsent: YES];
            }
        } else {
            [ALPrivacySettings setHasUserConsent: YES];
        }
    }];
    
    [[ALSdk shared] initializeWithConfiguration:xxpk_configuration completionHandler:^(ALSdkConfiguration * _Nonnull configuration) {
        [self matchLinkCanonBitsSixIssuerRetried:requestInterTotalPublisherVariables];
    }];
}

- (void)matchLinkCanonBitsSixIssuerRetried:(NSString *)requestInterTotalPublisherVariables
{
    self.locationsRedMixerEntityFlag = [MARewardedAd sharedWithAdUnitIdentifier:requestInterTotalPublisherVariables];
    self.locationsRedMixerEntityFlag.delegate = self;

    
    [self.locationsRedMixerEntityFlag loadAd];
}

- (void)squaredFoodInstantClockMeterWakeData:(nullable NSString *)helloFarGetData clipBits:(void(^)(BOOL result))clipBits {
    self.helloFarGetData = helloFarGetData;
    self.artsDenyAbove = clipBits;
    if ( [self.locationsRedMixerEntityFlag isReady]) {
        [self.locationsRedMixerEntityFlag showAdForPlacement:nil customData:helloFarGetData];
    }else {
        clipBits(NO);
    }
}

- (void)mealNapParserAskMessage:(const char *)name error:(NSString *)error {
    
}


- (void)unloadCap:(nonnull MAAd *)ad {
    
    [self mealNapParserAskMessage: __PRETTY_FUNCTION__ error:nil];
    
    
    self.redirectsConstantExpensivePhaseCache = 0;
}

- (void)durationProducingExcludeDanceVoiceScatteredIdentifier:(nonnull NSString *)adUnitIdentifier withError:(nonnull MAError *)error {
    
    [self mealNapParserAskMessage: __PRETTY_FUNCTION__ error:error.message];
    
    
    
    self.redirectsConstantExpensivePhaseCache++;
    NSInteger kitLease = pow(2, MIN(6, self.redirectsConstantExpensivePhaseCache));
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, kitLease * NSEC_PER_SEC), dispatch_get_main_queue(), ^{
        [self.locationsRedMixerEntityFlag loadAd];
    });
}

- (void)tatarBend:(MAAd *)ad {
    [self mealNapParserAskMessage: __PRETTY_FUNCTION__ error:nil];
    
    
    [self.locationsRedMixerEntityFlag loadAd];
}

- (void)switchCallCardYearFlag:(MAAd *)ad withError:(MAError *)error
{
    [self mealNapParserAskMessage: __PRETTY_FUNCTION__ error:error.message];
    
    
    [self.locationsRedMixerEntityFlag loadAd];
    
    if (self.artsDenyAbove) {
        self.artsDenyAbove(NO);
    }
}

- (void)optCaption:(nonnull MAAd *)ad {
    [self mealNapParserAskMessage: __PRETTY_FUNCTION__ error:nil];
}

- (void)syntaxChroma:(nonnull MAAd *)ad {
    [self mealNapParserAskMessage: __PRETTY_FUNCTION__ error:nil];
}

- (void)collectVolumeTotalNowYou:(nonnull MAAd *)ad dueMaleSum:(nonnull MAReward *)reward {
    [self mealNapParserAskMessage: __PRETTY_FUNCTION__ error:nil];
    
    if (self.artsDenyAbove) {
        self.artsDenyAbove(YES);
    }
}


- (void)stoodOceanTip:(nonnull MAAd *)ad {
    [self mealNapParserAskMessage: __PRETTY_FUNCTION__ error:nil];
}

- (void)rowStartMen:(nonnull MAAd *)ad {
    [self mealNapParserAskMessage: __PRETTY_FUNCTION__ error:nil];
}

@end
