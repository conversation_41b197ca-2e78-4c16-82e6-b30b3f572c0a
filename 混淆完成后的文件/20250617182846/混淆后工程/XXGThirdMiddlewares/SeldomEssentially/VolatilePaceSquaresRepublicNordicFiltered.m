





#import <CL_ShanYanSDK/CL_ShanYanSDK.h>

@interface VolatilePaceSquaresRepublicNordicFiltered : NSObject<CLShanYanSDKManagerDelegate>

@property (nonatomic, copy) void(^zipPlayTailRowAction)(NSInteger);

@end

@implementation VolatilePaceSquaresRepublicNordicFiltered

- (void)dealloc {
    
}

+ (instancetype)shared {
    static id shared = nil;
    static dispatch_once_t mathToken;
    dispatch_once(&mathToken, ^{
        shared = [[super allocWithZone:NULL] init];
    });
    return shared;
}

- (void)segmentOddGlyphRectangleOlympusSobDissolve:(NSString *)appId complete:(void (^_Nullable)(BOOL isCanLogin))complete {

    [CLShanYanSDKManager setCLShanYanSDKManagerDelegate:self];
    
    [CLShanYanSDKManager setPreGetPhonenumberUseCacheIfNoCellularNetwork:NO];
    
    [C<PERSON>hanYanSDKManager initWithAppId:appId complete:^(CLCompleteResult * _Nonnull completeResult) {
        __block BOOL isCanUseOneKeyLogin = !completeResult.error;
        
        if (isCanUseOneKeyLogin) {
            
            [CLShanYanSDKManager preGetPhonenumber:^(CLCompleteResult * _Nonnull completeResult) {
                dispatch_sync(dispatch_get_main_queue(), ^{
                    complete(completeResult.error == nil);
                });
            }];
            
        } else {
            dispatch_sync(dispatch_get_main_queue(), ^{
                complete(NO);
            });
        }
    }];
}


- (void)inlandSaltDeletionCalculateArgumentsController:(UIViewController *_Nonnull)controller array:(NSArray *)array success:(void (^_Nullable)(NSDictionary * _Nonnull insidePub))success squaredHit:(void (^_Nullable)(NSString * _Nonnull error))error drawCupAction:(void(^)(NSInteger))action {
    self.zipPlayTailRowAction = action;
    

    [CLShanYanSDKManager quickAuthLoginWithConfigure:[self didDrawBarAge:controller bondArray:array] openLoginAuthListener:^(CLCompleteResult * _Nonnull completeResult) {
        if (completeResult.error) {
            error(completeResult.message);
        }
    } oneKeyLoginListener:^(CLCompleteResult * _Nonnull completeResult) {
        
        if (completeResult.error == nil) {
            dispatch_sync(dispatch_get_main_queue(), ^{
                success(completeResult.data);
            });
        }else {
            error(completeResult.message);
        }
    }];
}





- (CLUIConfigure *)didDrawBarAge:(UIViewController *)viewController bondArray:(NSArray *)array {
    CLUIConfigure *boxStartDry = [[CLUIConfigure alloc] init];
    
    boxStartDry.viewController = viewController;
    
    
    boxStartDry.clNavigationBarHidden = @(YES);
    
    //logo
    boxStartDry.clLogoHiden = @(YES);
    
    
    boxStartDry.clPhoneNumberFont = [UIFont boldSystemFontOfSize:26];
    boxStartDry.clPhoneNumberColor = UIColor.darkGrayColor;
    boxStartDry.clPhoneNumberTextAlignment = @(NSTextAlignmentCenter);
    
    
    boxStartDry.clSloganTextFont = [UIFont systemFontOfSize:14];
    boxStartDry.clSloganTextColor = UIColor.darkGrayColor;
    boxStartDry.clSlogaTextAlignment = @(NSTextAlignmentCenter);
    
    
    boxStartDry.clLoginBtnText = array[4];
    boxStartDry.clLoginBtnTextFont = [UIFont systemFontOfSize:18];
    boxStartDry.clLoginBtnTextColor = UIColor.whiteColor;
    boxStartDry.clLoginBtnBgColor = array[0];
    boxStartDry.clLoginBtnCornerRadius = @(2);
    
    
    boxStartDry.clCheckBoxSize = [NSValue valueWithCGSize:CGSizeMake(0, 0)];
    boxStartDry.clCheckBoxValue = @(YES);
    
    
    boxStartDry.clAppPrivacyNormalDesTextFirst = array[5];
    boxStartDry.clAppPrivacyFirst = @[array[6], array[1]];
    boxStartDry.clAppPrivacyNormalDesTextSecond = array[7];
    boxStartDry.clAppPrivacyNormalDesTextLast = @"";
    boxStartDry.clAppPrivacyTextFont = [UIFont systemFontOfSize:12];
    boxStartDry.clAppPrivacyTextAlignment = @(NSTextAlignmentLeft);
    boxStartDry.clAppPrivacyWebNavigationBarTintColor = UIColor.whiteColor;
    boxStartDry.clAppPrivacyPunctuationMarks = @(YES);
    boxStartDry.clAppPrivacyColor = @[UIColor.darkGrayColor,array[0]];;
    boxStartDry.clAuthTypeUseWindow = @(YES);
    boxStartDry.clPrivacyShowUnderline = @(YES);
    boxStartDry.clAppPrivacyLineSpacing = @(2.5);
    boxStartDry.clAuthWindowModalTransitionStyle = @(UIModalTransitionStyleCrossDissolve);
    
    boxStartDry.clAppPrivacyWebBackBtnImage = array[2];
    
    
    boxStartDry.clLoadingSize = [NSValue valueWithCGSize:CGSizeMake(90, 90)];
    boxStartDry.clLoadingCornerRadius = @(2);
    boxStartDry.clLoadingIndicatorStyle = @(UIActivityIndicatorViewStyleLarge);
    boxStartDry.clLoadingTintColor = UIColor.blackColor;
    boxStartDry.clLoadingBackgroundColor = UIColor.clearColor;
    
    
    CLOrientationLayOut *intentAssertArgumentOverdueCar = [[CLOrientationLayOut alloc] init];
    boxStartDry.clOrientationLayOutPortrait = intentAssertArgumentOverdueCar;
    
    
    CGFloat y = (([UIScreen mainScreen].bounds.size.height - [array[3] CGSizeValue].height) * 0.5) + 35;
    CGFloat height = 30;
    intentAssertArgumentOverdueCar.clLayoutPhoneTop = @(y);
    intentAssertArgumentOverdueCar.clLayoutPhoneHeight = @(height);
    intentAssertArgumentOverdueCar.clLayoutPhoneCenterX = @(0);
    
    
    y += (height + 20);
    height = 17;
    intentAssertArgumentOverdueCar.clLayoutSloganTop = @(y);
    intentAssertArgumentOverdueCar.clLayoutSloganCenterX = @(0);
    intentAssertArgumentOverdueCar.clLayoutSloganHeight = @(height);

    
    y += (height + 20);
    height = 50;
    intentAssertArgumentOverdueCar.clLayoutLoginBtnTop = @(y);
    intentAssertArgumentOverdueCar.clLayoutLoginBtnWidth = @([array[3] CGSizeValue].width - 40);
    intentAssertArgumentOverdueCar.clLayoutLoginBtnCenterX = @(0);
    intentAssertArgumentOverdueCar.clLayoutLoginBtnHeight = @(height);

    
    y += (height + 15);
    intentAssertArgumentOverdueCar.clLayoutAppPrivacyTop = @(y);
    intentAssertArgumentOverdueCar.clLayoutAppPrivacyCenterX = @(0);
    intentAssertArgumentOverdueCar.clLayoutAppPrivacyWidth = @([array[3] CGSizeValue].width - 40);
    
    
    boxStartDry.customAreaView = ^(UIView * _Nonnull customAreaView) {
        
        customAreaView.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0];
        
        UIView *plateView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, [array[3] CGSizeValue].width, [array[3] CGSizeValue].height)];
        plateView.backgroundColor = UIColor.whiteColor;
        plateView.layer.cornerRadius = 2.0;
        [customAreaView addSubview:plateView];
        plateView.center = customAreaView.center;
        
        
        UIButton *close = [UIButton buttonWithType:UIButtonTypeCustom];
        [close addTarget:self action:@selector(manDispenseDropFrenchThiaminMalayHandler:) forControlEvents:(UIControlEventTouchUpInside)];
        [close setBackgroundImage:array[2] forState:UIControlStateNormal];
        [plateView addSubview:close];
        close.frame = CGRectMake(8, 8, 20, 20);
    };
    
    return boxStartDry;
}

- (void)manDispenseDropFrenchThiaminMalayHandler:(id)sender {
    [CLShanYanSDKManager finishAuthControllerCompletion:nil];
    self.zipPlayTailRowAction(0);
}

@end
