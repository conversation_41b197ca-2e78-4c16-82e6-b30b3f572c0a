// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		189899592DDB5653003A2C26 /* TryStayWriteJumpTransposeBand.m in Sources */ = {isa = PBXBuildFile; fileRef = 189899492DDB5653003A2C26 /* TryStayWriteJumpTransposeBand.m */; };
		1898995A2DDB5653003A2C26 /* ReportPassMathRemainderDidEra.m in Sources */ = {isa = PBXBuildFile; fileRef = 1898994F2DDB5653003A2C26 /* ReportPassMathRemainderDidEra.m */; };
		1898995B2DDB5653003A2C26 /* FathomsRemotelyPairKernelSodiumThumbnail.m in Sources */ = {isa = PBXBuildFile; fileRef = 189899512DDB5653003A2C26 /* FathomsRemotelyPairKernelSodiumThumbnail.m */; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		1812EB6C2D9246A300B7BB73 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1812EBA52D92475F00B7BB73 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1812EBB02D924E4F00B7BB73 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		15EC263A360F92CA8EC485AA /* Pods-BringSoftStyleNotationCombiningChoose.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-BringSoftStyleNotationCombiningChoose.debug.xcconfig"; path = "Target Support Files/Pods-BringSoftStyleNotationCombiningChoose/Pods-BringSoftStyleNotationCombiningChoose.debug.xcconfig"; sourceTree = "<group>"; };
		1812EB6E2D9246A300B7BB73 /* libTryStayWriteJumpTransposeBand.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libTryStayWriteJumpTransposeBand.a; sourceTree = BUILT_PRODUCTS_DIR; };
		1812EBA92D92475F00B7BB73 /* libReportPassMathRemainderDidEra.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libReportPassMathRemainderDidEra.a; sourceTree = BUILT_PRODUCTS_DIR; };
		1812EBB42D924E4F00B7BB73 /* libFathomsRemotelyPairKernelSodiumThumbnail.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libFathomsRemotelyPairKernelSodiumThumbnail.a; sourceTree = BUILT_PRODUCTS_DIR; };
		189899472DDB5653003A2C26 /* PastAgeFrameDidRectum.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PastAgeFrameDidRectum.m; sourceTree = "<group>"; };
		189899492DDB5653003A2C26 /* TryStayWriteJumpTransposeBand.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TryStayWriteJumpTransposeBand.m; sourceTree = "<group>"; };
		1898994B2DDB5653003A2C26 /* BringSoftStyleNotationCombiningChoose.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BringSoftStyleNotationCombiningChoose.m; sourceTree = "<group>"; };
		1898994D2DDB5653003A2C26 /* HitMattingRequestLeadRequestAllergy.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HitMattingRequestLeadRequestAllergy.m; sourceTree = "<group>"; };
		1898994F2DDB5653003A2C26 /* ReportPassMathRemainderDidEra.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ReportPassMathRemainderDidEra.m; sourceTree = "<group>"; };
		189899512DDB5653003A2C26 /* FathomsRemotelyPairKernelSodiumThumbnail.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FathomsRemotelyPairKernelSodiumThumbnail.m; sourceTree = "<group>"; };
		189899532DDB5653003A2C26 /* ServicePrivacyPanelPubKernels.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ServicePrivacyPanelPubKernels.m; sourceTree = "<group>"; };
		189899552DDB5653003A2C26 /* VolatilePaceSquaresRepublicNordicFiltered.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = VolatilePaceSquaresRepublicNordicFiltered.m; sourceTree = "<group>"; };
		189899572DDB5653003A2C26 /* LongDistanceTrainerFilmCoached.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = LongDistanceTrainerFilmCoached.m; sourceTree = "<group>"; };
		2E2760FC3A8E28AE8A590A04 /* Pods_XXGAppLovinMiddleware.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_XXGAppLovinMiddleware.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		5611E708CF359636AF2E1A12 /* Pods-BringSoftStyleNotationCombiningChoose.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-BringSoftStyleNotationCombiningChoose.release.xcconfig"; path = "Target Support Files/Pods-BringSoftStyleNotationCombiningChoose/Pods-BringSoftStyleNotationCombiningChoose.release.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		1812EB6B2D9246A300B7BB73 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1812EBA42D92475F00B7BB73 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1812EBAF2D924E4F00B7BB73 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		1812EB652D9246A300B7BB73 = {
			isa = PBXGroup;
			children = (
				189899582DDB5653003A2C26 /* SeldomEssentially */,
				1812EB6F2D9246A300B7BB73 /* Products */,
				2A878D9CD5149CEF1CBBA0BC /* Pods */,
				CCCFEE22B3DD2AF77567F153 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		1812EB6F2D9246A300B7BB73 /* Products */ = {
			isa = PBXGroup;
			children = (
				1812EB6E2D9246A300B7BB73 /* libTryStayWriteJumpTransposeBand.a */,
				1812EBA92D92475F00B7BB73 /* libReportPassMathRemainderDidEra.a */,
				1812EBB42D924E4F00B7BB73 /* libFathomsRemotelyPairKernelSodiumThumbnail.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		189899582DDB5653003A2C26 /* SeldomEssentially */ = {
			isa = PBXGroup;
			children = (
				189899472DDB5653003A2C26 /* PastAgeFrameDidRectum.m */,
				189899492DDB5653003A2C26 /* TryStayWriteJumpTransposeBand.m */,
				1898994B2DDB5653003A2C26 /* BringSoftStyleNotationCombiningChoose.m */,
				1898994D2DDB5653003A2C26 /* HitMattingRequestLeadRequestAllergy.m */,
				1898994F2DDB5653003A2C26 /* ReportPassMathRemainderDidEra.m */,
				189899512DDB5653003A2C26 /* FathomsRemotelyPairKernelSodiumThumbnail.m */,
				189899532DDB5653003A2C26 /* ServicePrivacyPanelPubKernels.m */,
				189899552DDB5653003A2C26 /* VolatilePaceSquaresRepublicNordicFiltered.m */,
				189899572DDB5653003A2C26 /* LongDistanceTrainerFilmCoached.m */,
			);
			path = SeldomEssentially;
			sourceTree = "<group>";
		};
		2A878D9CD5149CEF1CBBA0BC /* Pods */ = {
			isa = PBXGroup;
			children = (
				15EC263A360F92CA8EC485AA /* Pods-BringSoftStyleNotationCombiningChoose.debug.xcconfig */,
				5611E708CF359636AF2E1A12 /* Pods-BringSoftStyleNotationCombiningChoose.release.xcconfig */,
			);
			name = Pods;
			path = ../XXGPlayKitOSDemo/Pods;
			sourceTree = "<group>";
		};
		CCCFEE22B3DD2AF77567F153 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				2E2760FC3A8E28AE8A590A04 /* Pods_XXGAppLovinMiddleware.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		1812EB6D2D9246A300B7BB73 /* TryStayWriteJumpTransposeBand */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1812EB782D9246A300B7BB73 /* Build configuration list for PBXNativeTarget "TryStayWriteJumpTransposeBand" */;
			buildPhases = (
				1812EB6A2D9246A300B7BB73 /* Sources */,
				1812EB6B2D9246A300B7BB73 /* Frameworks */,
				1812EB6C2D9246A300B7BB73 /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = TryStayWriteJumpTransposeBand;
			productName = ThirdMiddlewares;
			productReference = 1812EB6E2D9246A300B7BB73 /* libTryStayWriteJumpTransposeBand.a */;
			productType = "com.apple.product-type.library.static";
		};
		1812EBA22D92475F00B7BB73 /* ReportPassMathRemainderDidEra */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1812EBA62D92475F00B7BB73 /* Build configuration list for PBXNativeTarget "ReportPassMathRemainderDidEra" */;
			buildPhases = (
				1812EBA32D92475F00B7BB73 /* Sources */,
				1812EBA42D92475F00B7BB73 /* Frameworks */,
				1812EBA52D92475F00B7BB73 /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = ReportPassMathRemainderDidEra;
			productName = ThirdMiddlewares;
			productReference = 1812EBA92D92475F00B7BB73 /* libReportPassMathRemainderDidEra.a */;
			productType = "com.apple.product-type.library.static";
		};
		1812EBAD2D924E4F00B7BB73 /* FathomsRemotelyPairKernelSodiumThumbnail */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1812EBB12D924E4F00B7BB73 /* Build configuration list for PBXNativeTarget "FathomsRemotelyPairKernelSodiumThumbnail" */;
			buildPhases = (
				1812EBAE2D924E4F00B7BB73 /* Sources */,
				1812EBAF2D924E4F00B7BB73 /* Frameworks */,
				1812EBB02D924E4F00B7BB73 /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = FathomsRemotelyPairKernelSodiumThumbnail;
			productName = ThirdMiddlewares;
			productReference = 1812EBB42D924E4F00B7BB73 /* libFathomsRemotelyPairKernelSodiumThumbnail.a */;
			productType = "com.apple.product-type.library.static";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		1812EB662D9246A300B7BB73 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					1812EB6D2D9246A300B7BB73 = {
						CreatedOnToolsVersion = 16.2;
					};
				};
			};
			buildConfigurationList = 1812EB692D9246A300B7BB73 /* Build configuration list for PBXProject "TryStayWriteJumpTransposeBand" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 1812EB652D9246A300B7BB73;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 1812EB6F2D9246A300B7BB73 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				1812EB6D2D9246A300B7BB73 /* TryStayWriteJumpTransposeBand */,
				1812EBA22D92475F00B7BB73 /* ReportPassMathRemainderDidEra */,
				1812EBAD2D924E4F00B7BB73 /* FathomsRemotelyPairKernelSodiumThumbnail */,
			);
		};
/* End PBXProject section */

/* Begin PBXSourcesBuildPhase section */
		1812EB6A2D9246A300B7BB73 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				189899592DDB5653003A2C26 /* TryStayWriteJumpTransposeBand.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1812EBA32D92475F00B7BB73 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1898995A2DDB5653003A2C26 /* ReportPassMathRemainderDidEra.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1812EBAE2D924E4F00B7BB73 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1898995B2DDB5653003A2C26 /* FathomsRemotelyPairKernelSodiumThumbnail.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		1812EB762D9246A300B7BB73 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		1812EB772D9246A300B7BB73 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		1812EB792D9246A300B7BB73 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				FRAMEWORK_SEARCH_PATHS = "\"$(SRCROOT)/../OSFrameworks/AppsFlyer\"";
				IPHONEOS_DEPLOYMENT_TARGET = 13;
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		1812EB7A2D9246A300B7BB73 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				FRAMEWORK_SEARCH_PATHS = "\"$(SRCROOT)/../OSFrameworks/AppsFlyer\"";
				IPHONEOS_DEPLOYMENT_TARGET = 13;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		1812EBA72D92475F00B7BB73 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				FRAMEWORK_SEARCH_PATHS = "\"$(SRCROOT)/../OSFrameworks/Facebook\"";
				IPHONEOS_DEPLOYMENT_TARGET = 13;
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		1812EBA82D92475F00B7BB73 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				FRAMEWORK_SEARCH_PATHS = "\"$(SRCROOT)/../OSFrameworks/Facebook\"";
				IPHONEOS_DEPLOYMENT_TARGET = 13;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		1812EBB22D924E4F00B7BB73 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				FRAMEWORK_SEARCH_PATHS = (
					"\"$(SRCROOT)/../OSFrameworks/Firebase/FirebaseAnalytics\"",
					"\"$(SRCROOT)/../OSFrameworks/Firebase/FirebaseCrashlytics\"",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 13;
				ONLY_ACTIVE_ARCH = NO;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		1812EBB32D924E4F00B7BB73 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				FRAMEWORK_SEARCH_PATHS = (
					"\"$(SRCROOT)/../OSFrameworks/Firebase/FirebaseAnalytics\"",
					"\"$(SRCROOT)/../OSFrameworks/Firebase/FirebaseCrashlytics\"",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 13;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1812EB692D9246A300B7BB73 /* Build configuration list for PBXProject "TryStayWriteJumpTransposeBand" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1812EB762D9246A300B7BB73 /* Debug */,
				1812EB772D9246A300B7BB73 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1812EB782D9246A300B7BB73 /* Build configuration list for PBXNativeTarget "TryStayWriteJumpTransposeBand" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1812EB792D9246A300B7BB73 /* Debug */,
				1812EB7A2D9246A300B7BB73 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1812EBA62D92475F00B7BB73 /* Build configuration list for PBXNativeTarget "ReportPassMathRemainderDidEra" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1812EBA72D92475F00B7BB73 /* Debug */,
				1812EBA82D92475F00B7BB73 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1812EBB12D924E4F00B7BB73 /* Build configuration list for PBXNativeTarget "FathomsRemotelyPairKernelSodiumThumbnail" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1812EBB22D924E4F00B7BB73 /* Debug */,
				1812EBB32D924E4F00B7BB73 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 1812EB662D9246A300B7BB73 /* Project object */;
}
