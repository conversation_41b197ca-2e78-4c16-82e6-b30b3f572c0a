






#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface AdoptManager : NSObject

+ (void)forkYouMountViewController:(UIViewController *)vc handler:(void(^)(<PERSON>OOL isCancell,NSString *userID, NSString*token, NSString*error))handler;

+ (BOOL)crossDiscardsSpeedBitsThree:(UIApplication *)application
                capsUndo:(NSURL *)url
                triangle:(nullable NSDictionary<UIApplicationOpenURLOptionsKey, id> *)options;

+ (void)badAspectParallelHasModalRealm:(NSString *)clientId notTailNonce:(NSString *)notTailNonce;

@end

NS_ASSUME_NONNULL_END
