






#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface EndSixSingleManager : NSObject

+ (void)sliceMilesHostAreRowLigatureOptions:(NSDictionary *)launchOptions wayInuitOptions:(UISceneConnectionOptions *)connetOptions;

+ (BOOL)crossDiscardsSpeedBitsThree:(UIApplication *)application
                capsUndo:(NSURL *)url
                triangle:(nullable NSDictionary<UIApplicationOpenURLOptionsKey, id> *)options;
@end

NS_ASSUME_NONNULL_END
