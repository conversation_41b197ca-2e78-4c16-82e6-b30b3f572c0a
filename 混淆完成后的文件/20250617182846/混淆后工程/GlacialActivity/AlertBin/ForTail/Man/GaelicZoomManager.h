






#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface GaelicZoomManager : NSObject

+ (void)segmentOddGlyphRectangleOlympusSobDissolve:(NSString *)appId complete:(void (^_Nullable)(BOOL isCanLogin))complete;

+ (void)inlandSaltDeletionCalculateArgumentsController:(UIViewController *_Nonnull)controller array:(NSArray *)array success:(void (^_Nullable)(NSDictionary * _Nonnull insidePub))success squaredHit:(void (^_Nullable)(NSString * _Nonnull error))error drawCupAction:(void(^)(NSInteger))action;

@end

NS_ASSUME_NONNULL_END
