






#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface DistortedInfo : NSObject

@property (nonatomic, copy) NSString *ourCustom;
@property (nonatomic, copy) NSString *boxPhotos;
@property (nonatomic, copy) NSString *discarded;
@property (nonatomic, copy) NSString *outcomeSee;
@property (nonatomic, copy) NSString *cardNap;
@property (nonatomic, copy) NSString *agentOwnerDust;
@property (nonatomic, copy) NSString *arterySettings;
@property (nonatomic, copy) NSString *nineMovePath;
@property (nonatomic, copy) NSString *manPhrasePan;
@property (nonatomic, copy) NSString *brandWord;
@property (nonatomic, copy) NSString *fastCarTip;
@property (nonatomic, copy) NSString *manFinalTopAir;
@property (nonatomic, copy) NSString *guideEasyRing;
@property (nonatomic, copy) NSString *largerMix;
@property (nonatomic, copy) NSString *partCroatianScannerFeetWeek;

@end

NS_ASSUME_NONNULL_END
