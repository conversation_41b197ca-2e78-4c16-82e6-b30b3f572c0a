






#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface HandNapExist : NSObject



@property (nonatomic, strong) NSString *vitaminWon;

@property (nonatomic, strong) NSString *canRoundName;


@property (nonatomic, strong) NSString *finishedPeerCubeDiscountsBuilt;

@property (nonatomic, strong) NSString *dingbatsSoftwareIllTriangleWas;


@property (nonatomic, copy) NSString *clockPointBar;


@property (nonatomic, copy) NSString *dispatchName;
@property (nonatomic, copy) NSString *endsSegueNot;
@property (nonatomic, copy) NSString *rowDenyNapTry;
@property (nonatomic, copy) NSString *holdThemeSite;
@property (nonatomic, copy) NSString *teamGuide;

@end

NS_ASSUME_NONNULL_END
