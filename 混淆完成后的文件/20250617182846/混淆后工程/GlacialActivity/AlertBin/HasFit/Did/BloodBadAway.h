






#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface BloodBadAway : NSObject

@property (nonatomic, assign) BOOL playSunRow;
@property (nonatomic, copy) NSString *cupKnowHas;
@property (nonatomic, copy) NSString *eyeSonNodeKeep;
@property (nonatomic, copy) NSString *checkTenBold;
@property (nonatomic, copy) NSString *rowAskRedone;
@property (nonatomic, assign) BOOL buffersFill;

@end

NS_ASSUME_NONNULL_END
