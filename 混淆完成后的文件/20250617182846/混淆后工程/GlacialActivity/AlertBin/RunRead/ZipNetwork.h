






#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface ZipNetwork : NSObject

@property (nonatomic, copy) NSString *barAngle;

+ (instancetype)cupBlendStarNetwork;

- (void)butCenterRequest:(NSString *)url
                  params:(NSDictionary * _Nullable)params
                 success:(void(^_Nullable)(NSDictionary *mayJobAutoRole))success
                 rawFeet:(void(^_Nullable)(NSError *error))rawFeet;

@end

NS_ASSUME_NONNULL_END
