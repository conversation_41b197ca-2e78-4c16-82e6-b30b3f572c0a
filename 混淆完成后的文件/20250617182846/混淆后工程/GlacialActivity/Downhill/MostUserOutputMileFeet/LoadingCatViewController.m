






#import "LoadingCatViewController.h"
#import "UpsideAskManager.h"
#import "HourBodyView.h"
#import "UIColor+MapColor.h"
#import "UIDevice+TabDevice.h"
#import "UIImage+ArtImage.h"
#import "SobDoneOldConfig.h"

@interface LoadingCatViewController ()

@property (nonatomic, strong) UIView * actualView;
@property (nonatomic, strong) UIImageView * pubView;

@end

@implementation LoadingCatViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = SuitableTab.previousParseDolbyMayAgentNewtons ? [UIColor sharpenAffectedSignBigChargePrep:SuitableTab.previousParseDolbyMayAgentNewtons]:UIColor.whiteColor;
    self.usesBlueRed = self.pinRadioUse;
    [HourBodyView tintHertz];
    
    self.actualView = [UIView new];
    self.actualView.backgroundColor = UIColor.whiteColor;
    [self.view addSubview:self.actualView];
    
    self.pubView = [[UIImageView alloc] initWithImage:[UIImage faeroeseSynthesisVariablesBeenTransformName:SuitableTab.dependingThe.tensionBundleStoneCanNominalPipe]];;
    [self.view addSubview:self.pubView];
    
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [self concludeRenewalLatencyCocoaExecute];
}

-(void)viewWillDisappear:(BOOL)animated {
    if ([SuitableTab finishIcyButtonRecordingSubmittedName] && SobDoneOldConfig.shared.behaviorTerabytesSensitiveClockMean.rotateExtra.buffersFill) {
        [HourBodyView batchLeap];
    }
    [super viewWillDisappear:animated];
    
}

- (CGSize)butStandPeerSize {
    if ([UIDevice mayHow]) {
        return CGSizeMake(SuitableTab.escapesFoundSlowMenOrdering, SuitableTab.escapesFoundSlowMenOrdering);
    }
    UIWindow *currentWindow = [[UpsideAskManager shared] eulerAnyBankWindow];
    UIEdgeInsets safe = currentWindow.safeAreaInsets;
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
    UIInterfaceOrientation orientation = [[UIApplication sharedApplication] statusBarOrientation];
#pragma clang diagnostic pop
    if (orientation == UIInterfaceOrientationPortrait || orientation == UIInterfaceOrientationPortraitUpsideDown) {
        if (![UIDevice thumbOpt]) {
            return CGSizeMake(UIScreen.mainScreen.bounds.size.width, SuitableTab.escapesFoundSlowMenOrdering);
        }
        return CGSizeMake(UIScreen.mainScreen.bounds.size.width, SuitableTab.escapesFoundSlowMenOrdering + safe.bottom);
    }else {
        if (![UIDevice thumbOpt]) {
            return CGSizeMake(SuitableTab.escapesFoundSlowMenOrdering,UIScreen.mainScreen.bounds.size.height);
        }
        if (orientation == UIInterfaceOrientationLandscapeRight) {
            return CGSizeMake(SuitableTab.escapesFoundSlowMenOrdering + safe.left,UIScreen.mainScreen.bounds.size.height);
        }else {
            return CGSizeMake(SuitableTab.escapesFoundSlowMenOrdering + 5,UIScreen.mainScreen.bounds.size.height);
        }
    }
}

- (void)concludeRenewalLatencyCocoaExecute {
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
    UIInterfaceOrientation orientation = [[UIApplication sharedApplication] statusBarOrientation];
#pragma clang diagnostic pop
    if (orientation == UIInterfaceOrientationPortrait || orientation == UIInterfaceOrientationPortraitUpsideDown) {
        [self.actualView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.view.mas_top);
            make.left.right.equalTo(self.view);
            make.height.mas_equalTo(SuitableTab.dependingThe.mustFatMiles);
        }];
        [self.pubView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.actualView.mas_top);
            make.centerX.equalTo(self.view);
            make.size.mas_equalTo(CGSizeMake(SuitableTab.dependingThe.selectorsBin, SuitableTab.dependingThe.filmDigitCup));
        }];
        [self.view mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.centerX.bottom.mas_equalTo(0);
            make.size.mas_equalTo(self.butStandPeerSize);
        }];
        [self.fadeDrawDry mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.left.right.mas_equalTo(0);
            make.height.mas_equalTo(SuitableTab.escapesFoundSlowMenOrdering);
        }];
        self.view.transform = CGAffineTransformMakeTranslation(0, self.butStandPeerSize.height);
    }else {
        [self.actualView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.view.mas_right);
            make.top.bottom.equalTo(self.view);
            make.width.mas_equalTo(SuitableTab.dependingThe.mustFatMiles);
        }];
        UIImage *originalImage = [UIImage faeroeseSynthesisVariablesBeenTransformName:SuitableTab.dependingThe.tensionBundleStoneCanNominalPipe];
        UIImage *rotatedImage = [UIImage imageWithCGImage:originalImage.CGImage
                                                    scale:originalImage.scale
                                              orientation:UIImageOrientationRight]; 
        self.pubView.image = rotatedImage;
        [self.pubView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.actualView.mas_right);
            make.centerY.equalTo(self.view);
            make.size.mas_equalTo(CGSizeMake(SuitableTab.dependingThe.filmDigitCup, SuitableTab.dependingThe.selectorsBin));
        }];
        [self.view mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.centerY.left.mas_equalTo(0);
            make.size.mas_equalTo(self.butStandPeerSize);
        }];
        [self.fadeDrawDry mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.bottom.right.mas_equalTo(0);
            make.width.mas_equalTo(SuitableTab.escapesFoundSlowMenOrdering);
        }];
        self.view.transform = CGAffineTransformMakeTranslation(-self.butStandPeerSize.width, 0);
    }
    [UIView animateWithDuration:0.3 animations:^{
        self.view.transform = CGAffineTransformIdentity;
    }];
}

- (void)uniqueBatchManEncodedPashto:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    [super uniqueBatchManEncodedPashto:touches withEvent:event];
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
    UIInterfaceOrientation orientation = [[UIApplication sharedApplication] statusBarOrientation];
#pragma clang diagnostic pop
    if (orientation == UIInterfaceOrientationPortrait || orientation == UIInterfaceOrientationPortraitUpsideDown) {
        [UIView animateWithDuration:0.3 animations:^{
            self.view.transform = CGAffineTransformMakeTranslation(0, self.butStandPeerSize.height);;
        } completion:^(BOOL finished) {
            [[UpsideAskManager shared] howGroupedStrokeSinSindhiConvertedViewController:self.navigationController];
        }];
    }else {
        [UIView animateWithDuration:0.3 animations:^{
            self.view.transform = CGAffineTransformMakeTranslation(-self.butStandPeerSize.width, 0);
        } completion:^(BOOL finished) {
            [[UpsideAskManager shared] howGroupedStrokeSinSindhiConvertedViewController:self.navigationController];
        }];
    }
}

@end
