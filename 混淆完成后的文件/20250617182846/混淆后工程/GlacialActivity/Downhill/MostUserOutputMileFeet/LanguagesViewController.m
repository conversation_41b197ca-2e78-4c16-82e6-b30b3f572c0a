






#import "LanguagesViewController.h"

@interface LanguagesViewController ()<WKNavigationDelegate,WKUIDelegate,WKScriptMessageHandler>

@end

@implementation LanguagesViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.airDefinesButton.hidden = YES;
    self.runAssignButton.hidden = YES;
    [self infoSelectingView];
}

- (void)infoSelectingView
{
    WKUserContentController *yetArtLegalTwo = [[WKUserContentController alloc] init];
    WKUserScript *userScript = [[WKUserScript alloc] initWithSource:SuitableTab.dependingThe.closureLiveArabicIterateVisual injectionTime:WKUserScriptInjectionTimeAtDocumentEnd forMainFrameOnly:YES];
    [yetArtLegalTwo addUserScript:userScript];
    
    WKWebViewConfiguration * config = [[WKWebViewConfiguration alloc] init];
    WKPreferences *preference = [[WKPreferences alloc]init];
    preference.javaScriptCanOpenWindowsAutomatically = YES;
    preference.minimumFontSize = 40.0;
    preference.javaScriptEnabled = YES;
    config.preferences = preference;
    config.selectionGranularity = WKSelectionGranularityDynamic;
    config.preferences.minimumFontSize = 18;
    config.preferences.javaScriptEnabled = YES;
    config.userContentController = yetArtLegalTwo;
    
    self.fadeDrawDry = [[WKWebView alloc] initWithFrame:CGRectZero];
    self.fadeDrawDry.backgroundColor = UIColor.clearColor;
    self.fadeDrawDry.scrollView.backgroundColor = UIColor.clearColor;
    self.fadeDrawDry.navigationDelegate = self;
    self.fadeDrawDry.opaque = YES;
    self.fadeDrawDry.scrollView.bounces = NO;
    self.fadeDrawDry.scrollView.showsVerticalScrollIndicator = NO;
    self.fadeDrawDry.scrollView.showsHorizontalScrollIndicator = NO;
    self.fadeDrawDry.UIDelegate = self;
    [self.view addSubview:self.fadeDrawDry];
    self.fadeDrawDry.scrollView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    
    [self.fadeDrawDry.configuration.userContentController addScriptMessageHandler:self name:SuitableTab.dependingThe.changingSyntheticRouteAllocatedCursive];
    [self.fadeDrawDry.configuration.userContentController addScriptMessageHandler:self name:SuitableTab.dependingThe.monitoredReadNumeratorProductOpacityNineSupports];
}

- (void)setUsesBlueRed:(NSString *)usesBlueRed {
    _usesBlueRed = usesBlueRed;
    NSURL *url = [NSURL URLWithString:usesBlueRed];
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:url cachePolicy:NSURLRequestReloadIgnoringCacheData timeoutInterval:15.0];
    [request addValue:[SuitableTab summariesFlashSentencesSettlingLegalToken] forHTTPHeaderField:SuitableTab.dependingThe.artsSummariesNewsstandBaselineSuch];
    [self.fadeDrawDry loadRequest:request];
}

- (void)hasStreamOperandChromaticPrototypePathClient:(NSURL *)URL {
    if ([self.eyeChatterFar respondsToSelector:@selector(cardBagView:faceScalarAction:arg:)]) {
        [self.eyeChatterFar cardBagView:self.fadeDrawDry faceScalarAction:URL.host arg:URL];
    }
}



- (void)webView:(WKWebView *)webView decidePolicyForNavigationResponse:(WKNavigationResponse *)navigationResponse decisionHandler:(void (^)(WKNavigationResponsePolicy))decisionHandler {
    decisionHandler(WKNavigationResponsePolicyAllow);
}


- (void)webView:(WKWebView *)webView decidePolicyForNavigationAction:(WKNavigationAction *)navigationAction decisionHandler:(void (^)(WKNavigationActionPolicy))decisionHandler {
    NSURL *URL = navigationAction.request.URL;
    
    if ([URL.scheme hasPrefix:SuitableTab.dependingThe.bloodTen]) {
        [self hasStreamOperandChromaticPrototypePathClient:URL];
        decisionHandler(WKNavigationActionPolicyCancel);
        return;
    }
    decisionHandler(WKNavigationActionPolicyAllow);
}


-(void)webView:(WKWebView *)webView didStartProvisionalNavigation:(WKNavigation *)navigation{
    [DeleteUnitView borderLexiconView:self.view];
}


- (void)webView:(WKWebView *)webView didFailProvisionalNavigation:(WKNavigation *)navigation{
    
    [DeleteUnitView blusteryWaySemicolonFriendsPreventsView:self.view];
}


-(void)webView:(WKWebView *)webView didFinishNavigation:(WKNavigation *)navigation {
    
    [webView evaluateJavaScript:SuitableTab.dependingThe.trackingAngleZoomAddPhotoArm completionHandler:nil];
    
    [webView evaluateJavaScript:SuitableTab.dependingThe.feedbackCapturingBalticNaturalUkrainianActivate completionHandler:nil];
    [webView evaluateJavaScript:SuitableTab.dependingThe.capturePreservedBoldfaceExtentsStroke completionHandler:nil];
    
    [DeleteUnitView blusteryWaySemicolonFriendsPreventsView:self.view];
    while (self.fadeDrawDry.isLoading) {
        return;
    }
}


- (void)userContentController:(WKUserContentController *)userContentController didReceiveScriptMessage:(WKScriptMessage *)message {
    
    
    if ([self.eyeChatterFar respondsToSelector:@selector(cardBagView:faceScalarAction:arg:)]) {
        [self.eyeChatterFar cardBagView:self.fadeDrawDry faceScalarAction:message.name arg:message.body];
    }
}

-(void)webView:(WKWebView *)webView runJavaScriptAlertPanelWithMessage:(NSString *)message initiatedByFrame:(WKFrameInfo *)frame completionHandler:(void (^)(void))completionHandler{
    
    [ManAlertView standMeanEvictionSheMenLeast:@"" message:message completion:^(NSInteger buttonIndex) {
        completionHandler();
    }];
}



- (WKWebView *)webView:(WKWebView *)webView createWebViewWithConfiguration:(WKWebViewConfiguration *)configuration forNavigationAction:(WKNavigationAction *)navigationAction windowFeatures:(WKWindowFeatures *)windowFeatures{
    WKFrameInfo *frameInfo = navigationAction.targetFrame;
    if (![frameInfo isMainFrame]) {
        [webView loadRequest:navigationAction.request];
    }
    return nil;
}


- (void)webView:(WKWebView *)webView runJavaScriptTextInputPanelWithPrompt:(NSString *)prompt defaultText:(nullable NSString *)defaultText initiatedByFrame:(WKFrameInfo *)frame completionHandler:(void (^)(NSString * __nullable result))completionHandler{
    completionHandler(SuitableTab.dependingThe.seekHueQueue);
}


- (void)webView:(WKWebView *)webView runJavaScriptConfirmPanelWithMessage:(NSString *)message initiatedByFrame:(WKFrameInfo *)frame completionHandler:(void (^)(BOOL result))completionHandler{
    completionHandler(YES);
}

- (void)dealloc {
    self.fadeDrawDry.UIDelegate = nil;
    self.view = nil;
    [self.fadeDrawDry.configuration.userContentController removeAllUserScripts];
}

@end
