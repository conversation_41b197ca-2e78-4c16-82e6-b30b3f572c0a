






#import "NumbersViewController.h"

@interface NumbersViewController ()

@end

@implementation NumbersViewController

- (UIButton *)runAssignButton
{
    if (!_runAssignButton) {
        _runAssignButton = [[UIButton alloc] init];
        [_runAssignButton setTitle:SuitableTab.dependingThe.spacingPrologEventButSignWho forState:UIControlStateNormal];
        [_runAssignButton setTitleColor:[SuitableTab staticKeyColor] forState:UIControlStateNormal];
        [_runAssignButton addTarget:self action:@selector(potentialOutputsSaveStampPanAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _runAssignButton;
}

- (UIButton *)airDefinesButton
{
    if (!_airDefinesButton) {
        _airDefinesButton = [[UIButton alloc] init];
        [_airDefinesButton setTitle:SuitableTab.dependingThe.penHeapMidAttachedFormattedWrong forState:UIControlStateNormal];
        [_airDefinesButton setTitleColor:[SuitableTab staticKeyColor] forState:UIControlStateNormal];
        [_airDefinesButton addTarget:self action:@selector(fadePlayBackBundlesYouAction:) forControlEvents:UIControlEventTouchUpInside];
        _airDefinesButton.hidden = [SuitableTab artBandIncomingDublinOwnerSession];
    }
    return _airDefinesButton;
}

- (void)potentialOutputsSaveStampPanAction:(UIButton *)sender {
    if(self.navigationController.viewControllers.count > 1) {
        [self.view endEditing:YES];
        [self.navigationController popViewControllerAnimated:NO];
    }else {
        [self fadePlayBackBundlesYouAction:sender];
        [self dismissViewControllerAnimated:NO completion:nil];
    }
}

- (void)fadePlayBackBundlesYouAction:(UIButton *)sender {
    [[UpsideAskManager shared] nowFlashBitsWindow];
    [SuitableTab fadePlayBackBundlesYouAction];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    
    self.view.overrideUserInterfaceStyle = UIUserInterfaceStyleLight;
    self.view.layer.cornerRadius = 2;
    self.view.backgroundColor = [SuitableTab bodyIllAlphaFaxSeekColor];
    [self.view addSubview:self.runAssignButton];
    [self.view addSubview:self.airDefinesButton];
    
    CGFloat runSize = SuitableTab.dependingThe.rankedInland;
    [_runAssignButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.equalTo(self.view).offset(SuitableTab.dependingThe.rowExercise);
        make.size.mas_equalTo(CGSizeMake(runSize, runSize));
    }];
    [_airDefinesButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(SuitableTab.dependingThe.rowExercise);
        make.right.equalTo(self.view).offset(-SuitableTab.dependingThe.rowExercise);
        make.size.mas_equalTo(CGSizeMake(runSize, runSize));
    }];
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(exactTradTaskCharSilenceCounterFrame:) name:UIKeyboardWillShowNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(exposeAmbiguousComposeOneDividerSound:) name:UIKeyboardWillHideNotification object:nil];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [self.view mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.mas_equalTo(self.view.superview);
        make.size.mas_equalTo([SuitableTab clampCaretMayArmpitOwnerServicesSize]);
    }];
}


- (void)exactTradTaskCharSilenceCounterFrame:(NSNotification *)notification {
    
    CGFloat duration = [notification.userInfo[UIKeyboardAnimationDurationUserInfoKey] floatValue];
    
    
    CGRect keyboardFrame = [notification.userInfo[UIKeyboardFrameEndUserInfoKey] CGRectValue];
    
    UIWindow *keyWindow = [UpsideAskManager shared].eulerAnyBankWindow;
    if (![keyWindow isMemberOfClass:NSClassFromString(SuitableTab.dependingThe.thermalTemplateBehaviorsDictationRealPaste)]) {
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wundeclared-selector"
        UIView *firstResponder = [keyWindow performSelector:@selector(firstResponder)];
#pragma clang diagnostic pop
        
        if (firstResponder  && [firstResponder isKindOfClass:UITextField.class]) {

            CGRect tfRect = [keyWindow convertRect:firstResponder.frame fromView:firstResponder.superview];
            
            if ((tfRect.origin.y + tfRect.size.height) > keyboardFrame.origin.y) {
                CGFloat trans = ((tfRect.origin.y + tfRect.size.height) - keyboardFrame.origin.y) + 10;
                
                ourCost(self);
                [UIView animateWithDuration:duration animations:^{
                    chestAtom(self);
                    self.navigationController.view.transform = CGAffineTransformMakeTranslation(0, -trans);
                }];
            }
        }
    }
}


- (void)exposeAmbiguousComposeOneDividerSound:(NSNotification *)notification{
    CGFloat duration = [notification.userInfo[UIKeyboardAnimationDurationUserInfoKey] floatValue];
    ourCost(self);
    [UIView animateWithDuration:duration animations:^{
        chestAtom(self);
        self.navigationController.view.transform = CGAffineTransformIdentity;
    }];
}

- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    [super touchesEnded:touches withEvent:event];
    [self.view endEditing:YES];
}

- (void)uniqueBatchManEncodedPashto:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    [self.view endEditing:YES];
}

- (void)dealloc {
    
    [self.view endEditing:YES];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIKeyboardWillShowNotification object:nil];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIKeyboardWillHideNotification object:nil];
}

@end
