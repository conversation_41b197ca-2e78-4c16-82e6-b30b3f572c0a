






#import "AvailLacrosseController.h"
#import "NumbersViewController.h"

@interface AvailLacrosseController ()

@end

@implementation AvailLacrosseController


- (BOOL)shouldAutorotate {
    return self.topViewController.shouldAutorotate;
}

- (UIInterfaceOrientationMask)supportedInterfaceOrientations {
    return self.topViewController.supportedInterfaceOrientations;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.interactivePopGestureRecognizer.enabled = NO;
    [self setNavigationBarHidden:YES];
    self.view.backgroundColor = [UIColor.blackColor colorWithAlphaComponent:.3];
}

- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    
    CGPoint point = [[touches anyObject] locationInView:self.view];
    
    UIView *tapView = self.topViewController.view;
    
    point = [tapView.layer convertPoint:point fromLayer:self.view.layer];
    
    NumbersViewController *vc = (NumbersViewController *)self.topViewController;
    if (![tapView.layer containsPoint:point]) {
        [vc uniqueBatchManEncodedPashto:touches withEvent:event];
    }else{  
        [super touchesBegan:touches withEvent:event];
    }
}

- (void)pushViewController:(NumbersViewController *)viewController animated:(BOOL)animated
{
    if (self.childViewControllers.count > 0) {
        viewController.runAssignButton.hidden = NO;
    }else {
        viewController.runAssignButton.hidden = YES;
    }
    [super pushViewController:viewController animated:animated];
    
}
- (void)dealloc {
    
}
@end
