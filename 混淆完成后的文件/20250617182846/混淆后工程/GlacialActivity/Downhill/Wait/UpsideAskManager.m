






#import "UpsideAskManager.h"
#import "MonthLawAllPinViewController.h"
#import "SobDoneOldConfig.h"

@interface UpsideAskManager()
@property (nonatomic, strong) NSMutableArray<UIWindow *> *updatingTremor;  
@property (nonatomic, strong) NSMutableArray<UIWindow *> *digestSunCan;  
@end

@implementation UpsideAskManager

- (instancetype)init {
    self = [super init];
    if (self) {
        _updatingTremor = [NSMutableArray array];
        _digestSunCan = [NSMutableArray array];
    }
    return self;
}

+ (instancetype)shared {
    static id shared = nil;
    static dispatch_once_t mathToken;
    dispatch_once(&mathToken, ^{
        shared = [[super alloc] init];
    });
    return shared;
}


- (UIWindow *)catOverageWindow {
    UIWindow *firstWindow = nil;
    
    if (@available(iOS 13.0, *)) {
        
        NSSet<UIScene *> *connectedScenes = [UIApplication sharedApplication].connectedScenes;
        for (UIScene *scene in connectedScenes) {
            
            if (scene.activationState == UISceneActivationStateForegroundActive &&
                [scene isKindOfClass:[UIWindowScene class]]) {
                
                UIWindowScene *windowScene = (UIWindowScene *)scene;
                
                if (windowScene.windows.count > 0) {
                    firstWindow = windowScene.windows.firstObject;
                }
                break;
            }
        }
    } else {
        
        NSArray<UIWindow *> *windows = [UIApplication sharedApplication].windows;
        if (windows.count > 0) {
            firstWindow = windows.firstObject;
        }
    }
    
    
    if (!firstWindow) {
        NSArray<UIWindow *> *windows = [UIApplication sharedApplication].windows;
        if (windows.count > 0) {
            firstWindow = windows.firstObject;
        }
    }
    
    return firstWindow;
}


- (UIWindow *)eulerAnyBankWindow {
    
    UIWindow *currentWindow = nil;
    
    if (@available(iOS 13.0, *)) {
        NSSet<UIScene *> *connectedScenes = [UIApplication sharedApplication].connectedScenes;
        for (UIScene *scene in connectedScenes) {
            if (scene.activationState == UISceneActivationStateForegroundActive &&
                [scene isKindOfClass:[UIWindowScene class]]) {
                UIWindowScene *windowScene = (UIWindowScene *)scene;
                
                
                if (@available(iOS 15.0, *)) {
                    currentWindow = windowScene.keyWindow;
                }
                
                else {
                    for (UIWindow *window in windowScene.windows) {
                        if (window.isKeyWindow) {
                            currentWindow = window;
                            break;
                        }
                    }
                }
                break;
            }
        }
    } else {
        
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
        currentWindow = [UIApplication sharedApplication].keyWindow;
#pragma clang diagnostic pop
    }
    
    
    if (!currentWindow) {
        NSArray<UIWindow *> *windows = [UIApplication sharedApplication].windows;
        for (UIWindow *window in windows) {
            if (window.isKeyWindow) {
                currentWindow = window;
                break;
            }
        }
    }
    
    return currentWindow;
}


- (void)thresholdSubmitOrdinaryNewsstandGeometricPriorityAllowViewController:(UIViewController *)stereo{
    dispatch_async(dispatch_get_main_queue(), ^{
        
        if ([UIApplication sharedApplication].applicationState == UIApplicationStateActive) {
            
            UIWindow *newWindow = [self mixArrowTremorDownWasDesktop:stereo];
            
            
            [self versionsContainedUnitFalloffAgeCombining:newWindow];
            
            [self.updatingTremor addObject:newWindow];
        } else {
            
            __weak typeof(self) weakSelf = self;
            
            __block __weak id obs = nil;
            
            obs = [[NSNotificationCenter defaultCenter] addObserverForName:UIApplicationDidBecomeActiveNotification
                                                                       object:nil
                                                                        queue:[NSOperationQueue mainQueue]
                                                                   usingBlock:^(NSNotification *note) {
                
                [[NSNotificationCenter defaultCenter] removeObserver:obs];
                [weakSelf thresholdSubmitOrdinaryNewsstandGeometricPriorityAllowViewController:stereo];
            }];
        }
    });
}

- (void)volumeInvertSlashesParseConnectGrammarViewController:(UIViewController *)stereo {
    dispatch_async(dispatch_get_main_queue(), ^{
        if ([UIApplication sharedApplication].applicationState == UIApplicationStateActive) {
            [self magnesiumRevisionKinLocationsTrialSheDeleting:stereo];
        } else {
            
            __weak typeof(self) weakSelf = self;
            
            __block __weak id obs = nil;
            
            obs = [[NSNotificationCenter defaultCenter] addObserverForName:UIApplicationDidBecomeActiveNotification
                                                                       object:nil
                                                                        queue:[NSOperationQueue mainQueue]
                                                                   usingBlock:^(NSNotification *note) {
                
                [[NSNotificationCenter defaultCenter] removeObserver:obs];
                
                [weakSelf magnesiumRevisionKinLocationsTrialSheDeleting:stereo];
            }];
        }
    });
}

- (void)agentReactorFitAreYouAllocateView:(UIView *)view {
    dispatch_async(dispatch_get_main_queue(), ^{
        if ([UIApplication sharedApplication].applicationState == UIApplicationStateActive) {
            [self magnesiumRevisionKinLocationsTrialSheDeleting:view];
        } else {
            
            __weak typeof(self) weakSelf = self;
            
            __block __weak id obs = nil;
            
            obs = [[NSNotificationCenter defaultCenter] addObserverForName:UIApplicationDidBecomeActiveNotification
                                                                       object:nil
                                                                        queue:[NSOperationQueue mainQueue]
                                                                   usingBlock:^(NSNotification *note) {
                
                [[NSNotificationCenter defaultCenter] removeObserver:obs];
                
                [weakSelf magnesiumRevisionKinLocationsTrialSheDeleting:view];
            }];
        }
    });
}

- (void)magnesiumRevisionKinLocationsTrialSheDeleting:(id)object {
    UIViewController *stereo = nil;
    
        
    if ([object isKindOfClass:[UIViewController class]]) {
        stereo = object;
    }
    
    if ([object isKindOfClass:[UIView class]]) {
        stereo = [MonthLawAllPinViewController new];
        stereo.view = object;
    }
    
    
    UIWindow *newWindow = [self mixArrowTremorDownWasDesktop:stereo];
    
    
    [self versionsContainedUnitFalloffAgeCombining:newWindow];
    
    
    [self.digestSunCan addObject:newWindow];
}

- (void)linearFivePrefixedListLegacyCan:(NSNotification *)note {
    
    [[NSNotificationCenter defaultCenter] removeObserver:self
                                                    name:UIApplicationDidBecomeActiveNotification
                                                  object:nil];
    [self agentReactorFitAreYouAllocateView:note.object];
}

- (void)nowFlashBitsWindow {
    [self homepageSubCustomEggSegueWindow];
}

- (void)homepageSubCustomEggSegueWindow {
    dispatch_async(dispatch_get_main_queue(), ^{
        if (self.digestSunCan.count == 0) return;

        
        UIWindow *lastWindow = [self.digestSunCan lastObject];
        [self.digestSunCan removeLastObject];

        
        if (lastWindow.isKeyWindow) {
            [self whoNotOnlyFairWindow];
        }

        
        lastWindow.hidden = YES;
    });
}

- (void)howGroupedStrokeSinSindhiConvertedViewController:(UIViewController *)rootViewController {
    dispatch_async(dispatch_get_main_queue(), ^{
        NSEnumerator *reverseEnumerator = [self.digestSunCan reverseObjectEnumerator];
        UIWindow *window = nil;
        
        
        while ((window = [reverseEnumerator nextObject])) {
            if (window.rootViewController == rootViewController) {
                
                if (window.isKeyWindow) {
                    [self whoNotOnlyFairWindow];
                }
                
                
                window.hidden = YES;
                [self.digestSunCan removeObject:window];
                
                
                reverseEnumerator = [self.digestSunCan reverseObjectEnumerator];
            }
        }
    });
}

- (void)doubleManagersGreekRespectsPeriodMixer {
    dispatch_async(dispatch_get_main_queue(), ^{
        
        for (UIWindow *window in [self.digestSunCan reverseObjectEnumerator]) {
            if (window.isKeyWindow) {
                [self whoNotOnlyFairWindow];
            }
            window.hidden = YES;
        }
        [self.digestSunCan removeAllObjects];
    });
}


- (UIWindow *)mixArrowTremorDownWasDesktop:(UIViewController *)stereo {
    UIWindow *window = nil;
    
    
    if (@available(iOS 13.0, *)) {
        for (UIScene *scene in [UIApplication sharedApplication].connectedScenes) {
            if (scene.activationState == UISceneActivationStateForegroundActive &&
                [scene isKindOfClass:[UIWindowScene class]]) {
                window = [[UIWindow alloc] initWithWindowScene:(UIWindowScene *)scene];
                break;
            }
        }
    }
    
    
    if (!window) {
        window = [[UIWindow alloc] initWithFrame:[UIScreen mainScreen].bounds];
    }
    
    
    window.backgroundColor = [UIColor clearColor];
    window.rootViewController = stereo;
    return window;
}

- (void)versionsContainedUnitFalloffAgeCombining:(UIWindow *)window {
    

    window.windowLevel = UIWindowLevelStatusBar + 100;
    [window makeKeyAndVisible];
}


- (void)whoNotOnlyFairWindow {
    UIWindow *paceWindow = [self ditheredTapWindow];
    [paceWindow makeKeyWindow];
    if (!paceWindow.isKeyWindow) {
        [paceWindow becomeKeyWindow];
    }
}

- (UIWindow *)ditheredTapWindow {
    __block UIWindow *paceWindow = nil;
    
    
    if (@available(iOS 13.0, *)) {
        NSArray<UIWindowScene *> *windowScenes = [self sentenceArgumentAssignHasTied];
        [windowScenes enumerateObjectsUsingBlock:^(UIWindowScene * _Nonnull scene, NSUInteger idx, BOOL * _Nonnull stop) {
            
            if (@available(iOS 15.0, *)) {
                paceWindow = scene.keyWindow;
            }
            
            if (!paceWindow) {
                paceWindow = [scene.windows firstObject];
            }
            if (paceWindow) *stop = YES;
        }];
    }
    
    else {
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
        paceWindow = [UIApplication sharedApplication].keyWindow;
#pragma clang diagnostic pop
    }
    
    
    if (!paceWindow) {
        paceWindow = [UIApplication sharedApplication].windows.firstObject;
    }
    
    return paceWindow;
}

- (NSArray<UIWindowScene *> *)sentenceArgumentAssignHasTied {
    NSPredicate *predicate = [NSPredicate predicateWithBlock:^BOOL(UIScene * _Nullable scene, NSDictionary<NSString *,id> * _Nullable bindings) {
        return scene.activationState == UISceneActivationStateForegroundActive;
    }];
    return [[UIApplication sharedApplication].connectedScenes filteredSetUsingPredicate:predicate].allObjects;
}


- (UIWindow *)sexWindow {
    return [self.digestSunCan lastObject];
}

- (NSInteger)kitNetCount {
    return self.digestSunCan.count;
}


@end
