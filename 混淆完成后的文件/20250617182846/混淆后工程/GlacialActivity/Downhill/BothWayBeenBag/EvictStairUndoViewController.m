






#import "EvictStairUndoViewController.h"
#import <WebKit/WebKit.h>
#import <WebKit/WKFoundation.h>
#import "NSString+NetUighur.h"
#import "NSString+KurdishBond.h"

@interface EvictStairUndoViewController ()<UIScrollViewDelegate,WKNavigationDelegate>

@property (nonatomic, strong) UISegmentedControl *radioJobTenTabControl;
@property (nonatomic, strong) UIView * adaptorEjectMealChannelWork;
@property (nonatomic, strong) UIView * fixHybridAwayYouFriend;

@property (nonatomic, strong) UIScrollView * clampingBigView;

@end

@implementation EvictStairUndoViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.airDefinesButton.hidden = YES;
    self.runAssignButton.hidden = YES;
    
    UISegmentedControl *segmentView = [[UISegmentedControl alloc] initWithItems:@[SuitableTab.needOldWideBig.copperSelectionGuaraniFiltersCaptureIntegrate,SuitableTab.needOldWideBig.animatorGreenSubtractPoliciesMediumDid]];
    segmentView.layer.masksToBounds = YES; 
    segmentView.layer.cornerRadius = 2;    
    [segmentView setTitleTextAttributes:@{NSForegroundColorAttributeName:[SuitableTab telephonyColor]} forState:UIControlStateSelected];
    [segmentView setTitleTextAttributes:@{NSForegroundColorAttributeName:[SuitableTab telephonyColor]} forState:UIControlStateNormal];
    [self.view addSubview:segmentView];
    [segmentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.view);
        make.top.mas_equalTo(SuitableTab.dependingThe.shareSumNet);
    }];
    [segmentView addTarget:self action:@selector(specifyForbid:) forControlEvents:UIControlEventValueChanged];
    self.radioJobTenTabControl = segmentView;
    
    _clampingBigView = [[UIScrollView alloc]init];
    _clampingBigView.pagingEnabled = YES;
    _clampingBigView.delegate = self;
    [self.view addSubview:_clampingBigView];
    [_clampingBigView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view).offset(SuitableTab.dependingThe.humanPinPart);
        make.right.equalTo(self.view).offset(-SuitableTab.dependingThe.humanPinPart);
        make.top.equalTo(self.view).offset(SuitableTab.dependingThe.liveFoundMix);
        make.bottom.equalTo(self.view).offset(-SuitableTab.dependingThe.useAddScalar);
    }];
    
    UIView *containerView = [UIView new];
    containerView.backgroundColor = UIColor.whiteColor;
    [self.clampingBigView addSubview:containerView];
    [containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.clampingBigView);
        make.height.equalTo(_clampingBigView);
    }];
    
    UIView * contentView1 = [self nodeRecordView:[SuitableTab wonPickerGeneratesNearestBaseball]];
    [containerView addSubview:contentView1];
    [contentView1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(0);
        make.top.bottom.equalTo(containerView);
        make.width.mas_equalTo(self.clampingBigView);
    }];
    self.adaptorEjectMealChannelWork = contentView1;
    
    UIView * contentView2 = [self nodeRecordView:[SuitableTab focusSharpenRevokedAgeBanner]];
    [containerView addSubview:contentView2];
    [contentView2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(contentView1.mas_right);
        make.bottom.top.equalTo(containerView);
        make.width.mas_equalTo(self.clampingBigView);
    }];
    self.fixHybridAwayYouFriend = contentView2;
    
    [containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(contentView2.mas_right);
    }];
    
    if (![self.pinRadioUse boolValue]) {
        UIButton *xxpk_noButton = [SuitableTab processorBookFarsiRejectDerivedColor:SuitableTab.needOldWideBig.drawNowSlope];
        [xxpk_noButton setBackgroundImage:[UIImage earMixHitTitleColor:[[UIColor lightGrayColor] colorWithAlphaComponent:0.5f]] forState:UIControlStateNormal];
        [xxpk_noButton addTarget:self action:@selector(suggestNowLastNapBendUsed:) forControlEvents:(UIControlEventTouchUpInside)];
        [self.view addSubview:xxpk_noButton];
        [xxpk_noButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.view).offset(-SuitableTab.dependingThe.rawLogoGrow);
            make.centerX.equalTo(self.view).multipliedBy(.65);
            make.height.mas_equalTo(SuitableTab.dependingThe.specifiedDry);
        }];
    }
    
    UIButton *xxpk_okButton =  [SuitableTab processorBookFarsiRejectDerivedColor:SuitableTab.needOldWideBig.idleWonWax];
    [xxpk_okButton addTarget:self action:@selector(footballInsulinTokenSequencerArrangerTwelve:) forControlEvents:(UIControlEventTouchUpInside)];
    [self.view addSubview:xxpk_okButton];
    [xxpk_okButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.view).offset(-SuitableTab.dependingThe.rawLogoGrow);
        make.centerX.equalTo(self.view).multipliedBy(![self.pinRadioUse boolValue]?1.35:1);
        make.height.mas_equalTo(SuitableTab.dependingThe.specifiedDry);
    }];
    
    segmentView.selectedSegmentIndex = 0;
    [self specifyForbid:segmentView];
}

-(void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView{
    [self.radioJobTenTabControl setSelectedSegmentIndex:scrollView.contentOffset.x/self.view.frame.size.width ==0?0:1];
    [self seasonAlternateRecoveryFrameCrossText:scrollView.contentOffset.x/self.view.frame.size.width ==0?0:1];
}

- (void)specifyForbid:(UISegmentedControl *)sender {
    [self seasonAlternateRecoveryFrameCrossText:sender.selectedSegmentIndex == 0?0:1];
    [self.clampingBigView setContentOffset:CGPointMake(sender.selectedSegmentIndex == 0?0:self.clampingBigView.frame.size.width, 0) animated:YES];
}

- (void)seasonAlternateRecoveryFrameCrossText:(NSInteger)type {
    NSString *contentUrl = nil;
    UIView *contentView = nil;
    contentUrl = type == 0 ? [SuitableTab wonPickerGeneratesNearestBaseball]:[SuitableTab focusSharpenRevokedAgeBanner];
    contentView = type == 0 ? self.adaptorEjectMealChannelWork:self.fixHybridAwayYouFriend;
    
    if (contentUrl.diskMilesBag) {
        return;
    }
    
    if ([[contentUrl pathExtension] containsString:SuitableTab.dependingThe.popPercentFemaleCenteringArmpit]) {
        UITextView *ctView = (UITextView *)contentView;
        if (ctView.text.length > 0) {
            return;
        }

        
        [DeleteUnitView borderLexiconView:contentView];

        
        NSURL *url = [NSURL URLWithString:contentUrl];
        NSURLSessionDataTask *task = [[NSURLSession sharedSession] dataTaskWithURL:url
                                                                 completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {
            
            dispatch_async(dispatch_get_main_queue(), ^{
                
                [DeleteUnitView blusteryWaySemicolonFriendsPreventsView:contentView];
                
                if (error || data.length == 0) {
                    
                    ctView.text = SuitableTab.needOldWideBig.hitTrainerBloodBarMagnitudeFlip;
                    return;
                }
                
                
                NSString *contentString = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
                ctView.text = contentString ?: SuitableTab.needOldWideBig.literEncodedChangedLoopComputerRequiringQualified;
            });
        }];
        
        [task resume];

    }else {
        WKWebView *wkview = (WKWebView *)contentView;
        if (!wkview.isLoading && wkview.estimatedProgress == 1) {
            [DeleteUnitView blusteryWaySemicolonFriendsPreventsView:contentView];
            return;
        }
        [DeleteUnitView borderLexiconView:contentView];
        NSString *urlstring =  [contentUrl.bandwidthOperationEmailFirstDenseSkipped stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
        NSURL *url = [NSURL URLWithString:urlstring];
        NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:url cachePolicy:NSURLRequestReloadIgnoringCacheData timeoutInterval:15.0];
        [wkview loadRequest:request];
    }
}

- (void)suggestNowLastNapBendUsed:(id)sender {
    [self potentialOutputsSaveStampPanAction:nil];
    if (self.oldestMountDisplayedAuditSee) {
        self.oldestMountDisplayedAuditSee(NO);
    }
}

- (void)footballInsulinTokenSequencerArrangerTwelve:(id)sender {
    [self potentialOutputsSaveStampPanAction:nil];
    if (self.oldestMountDisplayedAuditSee) {
        self.oldestMountDisplayedAuditSee(YES);
    }
}

- (UIView *)nodeRecordView:(NSString *)string {
    UIView *reasonSugar = nil;
    if ([[string pathExtension] containsString:SuitableTab.dependingThe.popPercentFemaleCenteringArmpit]) {
        UITextView * subPrior = [UITextView new];
        subPrior.editable = NO;
        subPrior.backgroundColor = UIColor.whiteColor;
        subPrior.textColor = UIColor.grayColor;
        reasonSugar = subPrior;
    }else {
        WKWebView *fadeDrawDry = [[WKWebView alloc] initWithFrame:CGRectZero];
        fadeDrawDry.backgroundColor = UIColor.clearColor;
        fadeDrawDry.scrollView.backgroundColor = UIColor.lightGrayColor;
        fadeDrawDry.opaque = YES;
        fadeDrawDry.scrollView.bounces =NO;
        fadeDrawDry.scrollView.showsVerticalScrollIndicator = NO;
        fadeDrawDry.scrollView.showsHorizontalScrollIndicator = NO;
        fadeDrawDry.navigationDelegate = self;
        reasonSugar = fadeDrawDry;
    }
    return reasonSugar;
}

- (void)webView:(WKWebView *)webView didFinishNavigation:(WKNavigation *)navigation {
    [DeleteUnitView blusteryWaySemicolonFriendsPreventsView:webView];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    UIEdgeInsets stepArmHue = [[UpsideAskManager shared] eulerAnyBankWindow].safeAreaInsets;
    stepArmHue.top    += 10;
    stepArmHue.left   += 10;
    stepArmHue.bottom += 10;
    stepArmHue.right  += 10;

    [self.view mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(stepArmHue);
    }];
}

@end
