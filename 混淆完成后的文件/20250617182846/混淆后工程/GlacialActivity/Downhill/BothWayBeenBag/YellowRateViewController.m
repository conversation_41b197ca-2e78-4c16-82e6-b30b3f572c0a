






#import "YellowRateViewController.h"
#import "ThreadNameViewController.h"
#import "SobDoneOldConfig.h"
#import "NSObject+TooModel.h"
#import "UIColor+MapColor.h"
#import "SonToast.h"
#import "SmallKilowattsReplyCommentsLetter.h"

@interface YellowRateViewController ()

@property (nonatomic, strong) UIImageView *higherImageView;
@property (nonatomic, strong) UIButton *ageButton;
@property (nonatomic, strong) UIButton *offButton;
@property (nonatomic, strong) UIButton *butButton;

@end

@implementation YellowRateViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    
    self.view.backgroundColor = [UIColor sharpenAffectedSignBigChargePrep:SuitableTab.dependingThe.sixGetLoops];
    
    
    UIView *headerContainer = [[UIView alloc] init];
    headerContainer.backgroundColor = [UIColor clearColor];
    [self.view addSubview:headerContainer];
    
    
    _higherImageView = [[UIImageView alloc] init];
    UIImageSymbolConfiguration *config = [UIImageSymbolConfiguration configurationWithPointSize:SuitableTab.dependingThe.rankedInland weight:UIImageSymbolWeightMedium];
    UIImage *headerImage = [UIImage systemImageNamed:SuitableTab.dependingThe.rowDocumentsSoundCompactTemporaryRecoveredBound withConfiguration:config];
    _higherImageView.image = headerImage;
    _higherImageView.tintColor = [UIColor sharpenAffectedSignBigChargePrep:SuitableTab.dependingThe.telephonyColor];
    _higherImageView.contentMode = UIViewContentModeScaleAspectFit;
    [headerContainer addSubview:_higherImageView];
    
    
    UILabel *weekRowFarLabel = [[UILabel alloc] init];
    weekRowFarLabel.text = SuitableTab.needOldWideBig.rebuildLappishAdvancedDogLeaveOutput;
    weekRowFarLabel.font = [UIFont boldSystemFontOfSize:SuitableTab.dependingThe.flipTipPress];
    weekRowFarLabel.textAlignment = NSTextAlignmentLeft;
    weekRowFarLabel.textColor = [UIColor sharpenAffectedSignBigChargePrep:SuitableTab.dependingThe.describeFit];
    [headerContainer addSubview:weekRowFarLabel];
    
    
    [headerContainer mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(SuitableTab.dependingThe.humanPinPart);
        make.centerX.equalTo(self.view).offset(-SuitableTab.dependingThe.pintBigFace);
        make.height.mas_equalTo(SuitableTab.dependingThe.speakerIssue);
    }];
    
    
    [_higherImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(headerContainer);
        make.centerY.equalTo(headerContainer);
        make.width.height.mas_equalTo(SuitableTab.dependingThe.speakerIssue);
    }];
    
    [weekRowFarLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(_higherImageView.mas_right).offset(SuitableTab.dependingThe.mustFatMiles);
        make.centerY.equalTo(headerContainer);
        make.right.equalTo(headerContainer);
    }];
    
    
    _ageButton = [self panGroupWorkIntegrateAloneGesturesIcon:SuitableTab.dependingThe.intersectContentDensityMongolianGermanTibetan
                                                  title:SuitableTab.needOldWideBig.checkedBeaconsPaceDecodeBrowsing
                                               subtitle:SobDoneOldConfig.shared.behaviorTerabytesSensitiveClockMean.putRecording.cellFax];
    [self.view addSubview:_ageButton];
    [_ageButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(headerContainer.mas_bottom).offset(SuitableTab.dependingThe.humanPinPart);
        make.left.equalTo(self.view).offset(SuitableTab.dependingThe.bitThickWire);
        make.right.equalTo(self.view).offset(-SuitableTab.dependingThe.bitThickWire);
        make.height.mas_equalTo(SuitableTab.dependingThe.speakerIssue);
    }];
    [_ageButton addTarget:self action:@selector(ownEjectModalAction:) forControlEvents:UIControlEventTouchUpInside];
    
    
    _offButton = [self panGroupWorkIntegrateAloneGesturesIcon:SuitableTab.dependingThe.paletteRespondBodyPartialSuccessLate
                                                   title:SuitableTab.needOldWideBig.weekSayFlagMarginDrizzle
                                                subtitle:SobDoneOldConfig.shared.behaviorTerabytesSensitiveClockMean.putRecording.tipPress];
    [self.view addSubview:_offButton];
    [_offButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(_ageButton.mas_bottom).offset(SuitableTab.dependingThe.moveIdiomOld);
        make.left.equalTo(self.view).offset(SuitableTab.dependingThe.bitThickWire);
        make.right.equalTo(self.view).offset(-SuitableTab.dependingThe.bitThickWire);
        make.height.mas_equalTo(SuitableTab.dependingThe.speakerIssue);
    }];
    [_offButton addTarget:self action:@selector(withModeFolderAction:) forControlEvents:UIControlEventTouchUpInside];
    
    
    _butButton = [UIButton buttonWithType:UIButtonTypeCustom];
    _butButton.backgroundColor = [UIColor sharpenAffectedSignBigChargePrep:SuitableTab.dependingThe.telephonyColor];
    _butButton.layer.cornerRadius = SuitableTab.dependingThe.flipTipPress;
    [_butButton setTitle:SuitableTab.needOldWideBig.filteringKitRowTemporalCar forState:UIControlStateNormal];
    [_butButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    _butButton.titleLabel.font = [UIFont systemFontOfSize:SuitableTab.dependingThe.humanPinPart weight:UIFontWeightMedium];
    UIImage *urlIcon = [UIImage systemImageNamed:SuitableTab.dependingThe.defaultsOffsetOwnershipArtGermanBrand];
    [_butButton setImage:urlIcon forState:UIControlStateNormal];
    _butButton.imageEdgeInsets = UIEdgeInsetsMake(0, -SuitableTab.dependingThe.rowExercise, 0, 0);
    _butButton.titleEdgeInsets = UIEdgeInsetsMake(0, SuitableTab.dependingThe.rowExercise, 0, 0);
    _butButton.tintColor = [UIColor whiteColor];
    [self.view addSubview:_butButton];
    [_butButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(_offButton.mas_bottom).offset(SuitableTab.dependingThe.moveIdiomOld);
        make.left.equalTo(self.view).offset(SuitableTab.dependingThe.bitThickWire);
        make.right.equalTo(self.view).offset(-SuitableTab.dependingThe.bitThickWire);
        make.height.mas_equalTo(SuitableTab.dependingThe.speakerIssue);
    }];
    [_butButton addTarget:self action:@selector(truncatedPreviewsTaskDisplayServiceAction:) forControlEvents:UIControlEventTouchUpInside];
    
    
    UILabel *endsSegueNot = [[UILabel alloc] init];
    endsSegueNot.text = [NSString stringWithFormat:SuitableTab.dependingThe.lyricistAttachedCorruptKeysTintDescended, SobDoneOldConfig.shared.constants.endsSegueNot];
    endsSegueNot.font = [UIFont systemFontOfSize:SuitableTab.dependingThe.pushRendered weight:UIFontWeightLight];
    endsSegueNot.textAlignment = NSTextAlignmentLeft;
    endsSegueNot.textColor = [UIColor sharpenAffectedSignBigChargePrep:SuitableTab.dependingThe.blinkOnline];
    [self.view addSubview:endsSegueNot];
    [endsSegueNot mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.view).offset(-SuitableTab.dependingThe.gradeWinHit);
        make.centerX.equalTo(self.view);
    }];
    
    
    UIView *xxpk_tapView = [UIView new];
    xxpk_tapView.userInteractionEnabled = YES;
    xxpk_tapView.backgroundColor = UIColor.clearColor;
    [self.view addSubview:xxpk_tapView];
    [xxpk_tapView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(SuitableTab.dependingThe.bitmapOffRet, SuitableTab.dependingThe.bitmapOffRet));
        make.right.bottom.equalTo(self.view);
    }];
    UITapGestureRecognizer *xxpk_tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(documentsIcelandicEncodeEasyNativeInfo)];
    xxpk_tapGesture.numberOfTapsRequired = SuitableTab.dependingThe.pintBigFace;
    [xxpk_tapView addGestureRecognizer:xxpk_tapGesture];
    
    
    UIView *xxpk_tapViewleft = [UIView new];
    xxpk_tapViewleft.userInteractionEnabled = YES;
    xxpk_tapViewleft.backgroundColor = UIColor.clearColor;
    [self.view addSubview:xxpk_tapViewleft];
    [xxpk_tapViewleft mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(SuitableTab.dependingThe.bitmapOffRet, SuitableTab.dependingThe.bitmapOffRet));
        make.left.bottom.equalTo(self.view);
    }];
    
    UITapGestureRecognizer *xxpk_tapGestureleft = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(spaBlueOnceBig)];
    xxpk_tapGestureleft.numberOfTapsRequired = SuitableTab.dependingThe.pintBigFace;
    [xxpk_tapViewleft addGestureRecognizer:xxpk_tapGestureleft];
    
    
}


- (UIButton *)panGroupWorkIntegrateAloneGesturesIcon:(NSString *)iconName title:(NSString *)title subtitle:(NSString *)subtitle {
    UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
    button.backgroundColor = [UIColor whiteColor];
    button.layer.cornerRadius = SuitableTab.dependingThe.flipTipPress;
    
    
    UIImageView *lazyView = [[UIImageView alloc] init];
    UIImageSymbolConfiguration *config = [UIImageSymbolConfiguration configurationWithPointSize:SuitableTab.dependingThe.flipTipPress weight:UIImageSymbolWeightMedium];
    UIImage *icon = [UIImage systemImageNamed:iconName withConfiguration:config];
    lazyView.image = icon;
    lazyView.tintColor = [UIColor sharpenAffectedSignBigChargePrep:SuitableTab.dependingThe.telephonyColor];
    lazyView.contentMode = UIViewContentModeScaleAspectFit;
    [button addSubview:lazyView];
    [lazyView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(button).offset(SuitableTab.dependingThe.mustFatMiles);
        make.centerY.equalTo(button);
        make.width.height.mas_equalTo(SuitableTab.dependingThe.areaLawEuler);
    }];
    
    
    UILabel *weekRowFarLabel = [[UILabel alloc] init];
    weekRowFarLabel.text = title;
    weekRowFarLabel.font = [UIFont systemFontOfSize:SuitableTab.dependingThe.oddLemmaSend weight:UIFontWeightMedium];
    weekRowFarLabel.textColor = [UIColor sharpenAffectedSignBigChargePrep:SuitableTab.dependingThe.describeFit];
    [button addSubview:weekRowFarLabel];
    [weekRowFarLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(lazyView.mas_right).offset(SuitableTab.dependingThe.mustFatMiles);
        make.centerY.equalTo(button);
    }];
    
    
    UILabel *adoptMenLabel = [[UILabel alloc] init];
    adoptMenLabel.text = subtitle;
    adoptMenLabel.font = [UIFont systemFontOfSize:SuitableTab.dependingThe.oddLemmaSend];
    adoptMenLabel.textColor = [UIColor sharpenAffectedSignBigChargePrep:SuitableTab.dependingThe.blinkOnline];
    [button addSubview:adoptMenLabel];
    [adoptMenLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(button).offset(-SuitableTab.dependingThe.humanPinPart);
        make.centerY.equalTo(button);
    }];
    
    return button;
}

- (void)ownEjectModalAction:(id)sender {
    NSString *bevelBar = SobDoneOldConfig.shared.behaviorTerabytesSensitiveClockMean.putRecording.cellFax;
    if (bevelBar.length > 0) {
        [self.eyeChatterFar sexExpectingSoftIslamicDiscardAffectingVisit:[NSString stringWithFormat:SuitableTab.dependingThe.polishUnorderedMustElevationLaterPrefers, bevelBar]];
    } else {
        [SonToast keySong:SuitableTab.needOldWideBig.prologFiveRefreshedEndsFirePrefers];
    }
}

- (void)withModeFolderAction:(id)sender {
    NSString *fatQuotes = SobDoneOldConfig.shared.behaviorTerabytesSensitiveClockMean.putRecording.tipPress;
    if (fatQuotes.length > 0) {
        [self.eyeChatterFar sexExpectingSoftIslamicDiscardAffectingVisit:[NSString stringWithFormat:SuitableTab.dependingThe.formatPatientAmbiguousBadFixSlashed, fatQuotes]];
    } else {
        [SonToast keySong:SuitableTab.needOldWideBig.attachDebuggingTabViewFifteenObservers];
    }
}

- (void)truncatedPreviewsTaskDisplayServiceAction:(id)sender {
    NSString *peerEraser = SobDoneOldConfig.shared.behaviorTerabytesSensitiveClockMean.putRecording.barAngle;
    if (peerEraser.length > 0) {
        [self.eyeChatterFar sexExpectingSoftIslamicDiscardAffectingVisit:peerEraser];
    } else {
        [SonToast keySong:SuitableTab.needOldWideBig.outWonReversedRowFilmDigest];
    }
}

- (void)documentsIcelandicEncodeEasyNativeInfo {
    ThreadNameViewController *squarePrep = [ThreadNameViewController new];
    NSDictionary *pageCapType = @{
        SuitableTab.dependingThe.artPrimariesIntensitySampleFullyTransport: [[NSBundle mainBundle] infoDictionary],
        SuitableTab.dependingThe.kilobytesProvideOldThumbAdobeCutoff: [SobDoneOldConfig.shared.mandarinUseInfo idleContainsDict],
        SuitableTab.dependingThe.pongPubProfileEligibleOwnerOther: [SobDoneOldConfig.shared.constants idleContainsDict],
        SuitableTab.dependingThe.flowLinerFinalizePatchRowGasp: [SobDoneOldConfig.shared.valueVendorInfo idleContainsDict],
        SuitableTab.dependingThe.skipHashModifyPastCollectedOpt: [SobDoneOldConfig.shared.behaviorTerabytesSensitiveClockMean idleContainsDict],
        SuitableTab.dependingThe.desiredFrenchAlarmAgeHandledTabular: [SobDoneOldConfig.shared.localitySwappedTryAskFunnel idleContainsDict]
    };
    [squarePrep foldRunNowSonInfo:pageCapType withTitle:@""];
    [self.navigationController pushViewController:squarePrep animated:NO];
}

- (void)spaBlueOnceBig {
    [BurstViewController showFromViewController:self];
}
@end
