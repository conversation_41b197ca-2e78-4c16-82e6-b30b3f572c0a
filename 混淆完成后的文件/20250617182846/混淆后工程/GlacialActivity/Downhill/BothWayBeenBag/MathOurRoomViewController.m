






#import "MathOurRoomViewController.h"
#import "SDWebImageManager.h"
#import "SonToast.h"
#import "NSString+NetUighur.h"

@interface MathOurRoomViewController ()

@property (nonatomic, strong) UIImageView *sockTagYearImageView;
@property (nonatomic, strong) UITextField *lossVideoTextField;
@property (nonatomic, strong) UITextField *sockEndRoomTextField;

@end

@implementation MathOurRoomViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    
    self.airDefinesButton.hidden = [self.pinRadioUse[0] boolValue];
    
    CGFloat topBottomMargin = SuitableTab.dependingThe.pushRendered;
    
    UILabel *tipLabel = [SuitableTab volatileDanishCallingExactAre:SuitableTab.needOldWideBig.loopMergeAltitudeChangedEasy];
    tipLabel.numberOfLines = 0;
    [self.view addSubview:tipLabel];
    [tipLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(SuitableTab.dependingThe.specifiedDry);
        make.left.mas_equalTo(SuitableTab.dependingThe.rankedInland);
        make.right.mas_equalTo(-SuitableTab.dependingThe.rankedInland);
    }];
    
    
    self.lossVideoTextField = [SuitableTab theMinorMaxField:SuitableTab.needOldWideBig.refinedTypeDrySpecialSystolicArt isSecure:NO];
    [self.view addSubview:self.lossVideoTextField];
    [self.lossVideoTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(tipLabel.mas_bottom).offset(topBottomMargin);
        make.left.mas_equalTo(SuitableTab.dependingThe.bitThickWire);
        make.right.mas_equalTo(-SuitableTab.dependingThe.bitThickWire);
        make.height.mas_equalTo(SuitableTab.dependingThe.youUndoneBar);
    }];
    
    
    self.sockEndRoomTextField = [SuitableTab theMinorMaxField:SuitableTab.needOldWideBig.tiedFrontFinderAutoSyntaxProviding isSecure:NO];;
    [self.view addSubview:self.sockEndRoomTextField];
    [self.sockEndRoomTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.lossVideoTextField.mas_bottom).offset(topBottomMargin);
        make.left.right.equalTo(self.lossVideoTextField);
        make.height.mas_equalTo(SuitableTab.dependingThe.youUndoneBar);
    }];
    
    
    UIImageView *rewardImageView = nil;
    if ([self.pinRadioUse[1] length] > 0) {
        
        rewardImageView = [[UIImageView alloc] init];
        rewardImageView.backgroundColor = UIColor.lightGrayColor;
        rewardImageView.contentMode = UIViewContentModeScaleAspectFit;
        [self.view addSubview:rewardImageView];
        [rewardImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.sockEndRoomTextField.mas_bottom).offset(topBottomMargin);
            make.width.mas_equalTo(self.sockEndRoomTextField);
            make.centerX.mas_equalTo(0);
        }];
        self.sockTagYearImageView = rewardImageView;
        
        [[SDWebImageManager sharedManager] loadImageWithURL:[NSURL URLWithString:self.pinRadioUse[1]] options:0 progress:nil completed:^(UIImage * _Nullable image2, NSData * _Nullable data, NSError * _Nullable error, SDImageCacheType cacheType, BOOL finished, NSURL * _Nullable imageURL) {
            dispatch_async(dispatch_get_main_queue(), ^{
                rewardImageView.image = image2;
                CGFloat ratio = image2.size.height / image2.size.width;
                [rewardImageView mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.height.mas_equalTo((SuitableTab.dependingThe.netOneLiftSoftwareTicketsWidth -SuitableTab.dependingThe.bitThickWire*2)*ratio);
                }];
                [self.view layoutIfNeeded];
                
                [self.view mas_remakeConstraints:^(MASConstraintMaker *make) {
                    make.center.mas_equalTo(0);
                    CGFloat height = self.sockTagYearImageView ? self.sockTagYearImageView.frame.size.height + SuitableTab.dependingThe.pushRendered +SuitableTab.dependingThe.filenamePetiteSeventeenMacintoshSymbolsHue : SuitableTab.dependingThe.filenamePetiteSeventeenMacintoshSymbolsHue;
                    
                    make.size.mas_equalTo(CGSizeMake(SuitableTab.dependingThe.netOneLiftSoftwareTicketsWidth, MIN(height, UIScreen.mainScreen.bounds.size.height)));
                    if (height > UIScreen.mainScreen.bounds.size.height) {
                        [self.sockTagYearImageView mas_updateConstraints:^(MASConstraintMaker *make) {
                            make.height.mas_equalTo(self.sockTagYearImageView.frame.size.height-(height-UIScreen.mainScreen.bounds.size.height));
                        }];
                    }
                }];
            });
        }];
    }
    
    
    UIButton *xxpk_realButton = [SuitableTab processorBookFarsiRejectDerivedColor:SuitableTab.needOldWideBig.aboveFreestyleHowPassivelyNear];
    [xxpk_realButton addTarget:self action:@selector(earTitleInferMomentaryQualifierUnwinding:) forControlEvents:(UIControlEventTouchUpInside)];
    [self.view addSubview:xxpk_realButton];
    [xxpk_realButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(-SuitableTab.dependingThe.moveIdiomOld);
        make.left.right.equalTo(self.lossVideoTextField);
        make.height.mas_equalTo(SuitableTab.dependingThe.bitmapOffRet);
    }];
    
}

- (void)earTitleInferMomentaryQualifierUnwinding:(id)sender {

}

@end
