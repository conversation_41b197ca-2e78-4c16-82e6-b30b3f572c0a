






#import "EstimatedAxesViewController.h"
#import "SonToast.h"
#import "AsleepInfo.h"

@interface EstimatedAxesViewController ()

@property (nonatomic, strong) UIImageView *capSpeechView;
@property (nonatomic, strong) UIButton *heavyEitherButton;
@property (nonatomic, strong) UIView *pagerTailView;
@property (nonatomic, strong) UILabel *sequenceLabel;
@property (nonatomic, strong) UITextField *binPartIntroTextField;
@property (nonatomic, strong) UITextField *badSamplesOwnTextField;
@end

@implementation EstimatedAxesViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.airDefinesButton.hidden = YES;
    
    if ([AsleepInfo driveSinMissingCursorDisableImage]) {
        self.capSpeechView = [[UIImageView alloc] initWithImage:[AsleepInfo driveSinMissingCursorDisableImage]];
        [self.view addSubview:self.capSpeechView];
        self.capSpeechView.hidden = YES;
        [self.capSpeechView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.mas_equalTo(SuitableTab.dependingThe.bitmapOffRet);
            make.left.equalTo(self.runAssignButton.mas_right);
            make.top.equalTo(self.view).offset(SuitableTab.dependingThe.mustFatMiles);
        }];
    }
    
    self.pagerTailView = [SuitableTab pagerTailView];
    self.pagerTailView.hidden = YES;
    [self.view addSubview:self.pagerTailView];
    [self.pagerTailView mas_makeConstraints:^(MASConstraintMaker *make) {
        if ([AsleepInfo driveSinMissingCursorDisableImage]) {
            make.centerY.equalTo(self.capSpeechView);
            make.left.equalTo(self.capSpeechView.mas_right).offset(SuitableTab.dependingThe.rowExercise);
        }else {
            make.top.equalTo(self.view).offset(SuitableTab.dependingThe.mustFatMiles);
            make.left.equalTo(self.runAssignButton.mas_right).offset(SuitableTab.dependingThe.rowExercise);
        }
        make.right.equalTo(self.airDefinesButton.mas_left);
        make.height.mas_equalTo(SuitableTab.dependingThe.bitmapOffRet);
    }];
    
    self.sequenceLabel = [SuitableTab volatileDanishCallingExactAre:SuitableTab.needOldWideBig.cutAllocateHitHeapFatal];
    self.sequenceLabel.numberOfLines = 0;
    [self.view addSubview:self.sequenceLabel];
    [self.sequenceLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(SuitableTab.dependingThe.videoFileHas);
        make.left.mas_equalTo(SuitableTab.dependingThe.rankedInland);
        make.right.mas_equalTo(-SuitableTab.dependingThe.rankedInland);
    }];
    
    
    self.binPartIntroTextField = [SuitableTab utilitiesStreamChallengePutSeparatedAccount];
    self.binPartIntroTextField.enabled = NO;
    self.binPartIntroTextField.text = self.pinRadioUse[SuitableTab.dependingThe.rearWalkYetName];
    [self selectRedLeaveCookieCompanyIndexView:self.binPartIntroTextField text:SuitableTab.needOldWideBig.hundredsSheSemanticKnowSubstring];
    [self.view addSubview:self.binPartIntroTextField];
    [self.binPartIntroTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.sequenceLabel.mas_bottom).offset(SuitableTab.dependingThe.pushRendered);
        make.left.mas_equalTo(SuitableTab.dependingThe.bitThickWire);
        make.right.mas_equalTo(-SuitableTab.dependingThe.bitThickWire);
        make.height.mas_equalTo(SuitableTab.dependingThe.youUndoneBar);
    }];
    
    
    self.badSamplesOwnTextField = [SuitableTab utilitiesStreamChallengePutSeparatedAccount];
    self.badSamplesOwnTextField.enabled = NO;
    self.badSamplesOwnTextField.text = self.pinRadioUse[SuitableTab.dependingThe.ageFloatWonKey];
    [self selectRedLeaveCookieCompanyIndexView:self.badSamplesOwnTextField text:SuitableTab.needOldWideBig.toggleSlicePhoneticDietaryUnit];
    [self.view addSubview:self.badSamplesOwnTextField];
    [self.badSamplesOwnTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.binPartIntroTextField.mas_bottom).offset(SuitableTab.dependingThe.pushRendered);
        make.left.right.equalTo(self.binPartIntroTextField);
        make.height.mas_equalTo(SuitableTab.dependingThe.youUndoneBar);
    }];
    
    
    UIButton *xxpk_saveButton = [SuitableTab processorBookFarsiRejectDerivedColor:SuitableTab.needOldWideBig.sedentaryMartialTryCompanyCloud];
    [xxpk_saveButton addTarget:self action:@selector(thatPathBlinkRemovalManAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:xxpk_saveButton];
    [xxpk_saveButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.badSamplesOwnTextField.mas_bottom).offset(SuitableTab.dependingThe.pushRendered);
        make.left.right.equalTo(self.badSamplesOwnTextField);
        make.height.mas_equalTo(SuitableTab.dependingThe.bitmapOffRet);
    }];
    
    
    self.heavyEitherButton = [SuitableTab panoramasDefinedBelowSlowSecure:SuitableTab.needOldWideBig.sampleComposeLooperWarnAscendedConnected];
    [self.heavyEitherButton setContentEdgeInsets:UIEdgeInsetsMake(0, 0, 0, 0)];
    [self.heavyEitherButton addTarget:self action:@selector(effectiveProtocolsWithBlurArtsAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.heavyEitherButton];
    [self.heavyEitherButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(xxpk_saveButton.mas_bottom).offset(SuitableTab.dependingThe.planeRepeat);
        make.centerX.equalTo(self.view);
    }];
}

- (void)selectRedLeaveCookieCompanyIndexView:(UITextField *)textField text:(NSString *)text
{
    CGRect frame = {{0,0},CGSizeMake(SuitableTab.dependingThe.bitmapOffRet, SuitableTab.dependingThe.youUndoneBar)};
    UILabel *leftview = [[UILabel alloc] initWithFrame:frame];
    leftview.text = text;
    leftview.textColor = UIColor.redColor;
    textField.leftViewMode = UITextFieldViewModeAlways;
    textField.leftView = leftview;
}

- (void)effectiveProtocolsWithBlurArtsAction:(UIButton *)sender {
    [[UpsideAskManager shared] howGroupedStrokeSinSindhiConvertedViewController:self.navigationController];
}

- (void)thatPathBlinkRemovalManAction:(UIButton *)sender {
    sender.hidden = YES;
    self.sequenceLabel.hidden = YES;
    self.pagerTailView.hidden = NO;
    self.capSpeechView.hidden = NO;
    self.heavyEitherButton.hidden = YES;
    
    [self.view mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.center.mas_equalTo(self.view.superview);
        make.size.mas_equalTo(CGSizeMake(SuitableTab.dependingThe.netOneLiftSoftwareTicketsWidth, SuitableTab.dependingThe.filenamePetiteSeventeenMacintoshSymbolsHue-SuitableTab.dependingThe.areaLawEuler));
    }];
    [self.binPartIntroTextField mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.pagerTailView.mas_bottom).offset(SuitableTab.dependingThe.pushRendered);
    }];
    [self.view layoutIfNeeded];
    
    BOOL water = [[[[NSBundle mainBundle] infoDictionary] allKeys] containsObject:SuitableTab.dependingThe.cocoaCarButOutcomeNextEdgeEpsilon];
    if (!water) {
        self.airDefinesButton.hidden = NO;
        [ManAlertView standMeanEvictionSheMenLeast:nil message:SuitableTab.needOldWideBig.speakOffForeverSerifScheme completion:nil];
        return;
    }
    CGSize size = self.view.frame.size;
    size.height -= SuitableTab.dependingThe.useAddScalar;
    UIGraphicsBeginImageContextWithOptions(size, NO, 0.0);
    [self.view.layer renderInContext:UIGraphicsGetCurrentContext()];
    UIImage *image = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    
    UIImageWriteToSavedPhotosAlbum(image, self, @selector(image:buildSparseAdvisedPushSexResting:contextInfo:), (__bridge void *)self);
}

- (void)image:(UIImage *)image buildSparseAdvisedPushSexResting:(NSError *)error contextInfo:(void *)contextInfo
{
    
    if(!error){
        [[UpsideAskManager shared] howGroupedStrokeSinSindhiConvertedViewController:self.navigationController];
        [SonToast keySong:SuitableTab.needOldWideBig.hockeyDesignWetSpecifiedRest];
    }else {
        self.airDefinesButton.hidden = NO;
        [ManAlertView standMeanEvictionSheMenLeast:nil message:SuitableTab.needOldWideBig.speakOffForeverSerifScheme completion:nil];
    }
}

@end
