






#import "ThreadNameViewController.h"
#import "SobDoneOldConfig.h"
#import "NSObject+TooModel.h"
#import "UIColor+MapColor.h"
#import "SonToast.h"

@interface ThreadNameViewController ()<UITableViewDataSource, UITableViewDelegate>

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) NSMutableArray<NSDictionary *> *thermalFlightPartDutchSmart; 
@property (nonatomic, strong) NSArray<NSArray<NSString *> *> *optionPayments; 
@property (nonatomic, strong) NSMutableArray<NSString *> *sectionTitles; 

@end

@implementation ThreadNameViewController


- (void)viewDidLoad {
    [super viewDidLoad];
    
    
    _thermalFlightPartDutchSmart = [NSMutableArray array];
    _optionPayments = @[];
    _sectionTitles = [NSMutableArray array];
    
    [self ourRedoFunView];
}

- (void)viewWillAppear:(BOOL)animated {
    
    UIEdgeInsets stepArmHue = [[UpsideAskManager shared] eulerAnyBankWindow].safeAreaInsets;
    
    stepArmHue.top    += 10;
    stepArmHue.left   += 10;
    stepArmHue.bottom += 10;
    stepArmHue.right  += 10;

    [self.view mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(stepArmHue);
    }];
}


- (void)ourRedoFunView {
    _tableView = [[UITableView alloc] initWithFrame:self.view.bounds style:UITableViewStyleGrouped];
    _tableView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
    _tableView.dataSource = self;
    _tableView.delegate = self;
    _tableView.backgroundColor = [UIColor clearColor];
    [self.view addSubview:_tableView];
    [_tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.runAssignButton.mas_bottom);
        make.left.right.bottom.equalTo(self.view);
    }];
    
    
    [_tableView registerClass:[UITableViewCell class] forCellReuseIdentifier:NSStringFromClass(self.class)];
}


- (NSArray<NSString *> *)nineClockJouleDictionary:(NSDictionary *)dict {
    return [[dict allKeys] sortedArrayUsingSelector:@selector(caseInsensitiveCompare:)];
}

- (void)foldRunNowSonInfo:(NSDictionary *)info withTitle:(NSString *)title {
    if (!info || ![info isKindOfClass:[NSDictionary class]]) {
        return;
    }
    
    
    dispatch_async(dispatch_get_main_queue(), ^{
        @synchronized (self) {
            
            [self->_thermalFlightPartDutchSmart addObject:[info copy]];
            NSArray *sortedKeys = [self nineClockJouleDictionary:info];
            self->_optionPayments = [self->_optionPayments arrayByAddingObject:sortedKeys];
            [self->_sectionTitles addObject:title];
            
            
            [self.tableView reloadData];
        }
    });
}


- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return _thermalFlightPartDutchSmart.count;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return _optionPayments[section].count;
}

- (NSString *)tableView:(UITableView *)tableView titleForHeaderInSection:(NSInteger)section {
    return _sectionTitles[section];
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass(self.class) forIndexPath:indexPath];
    
    NSString *key;
    id value;
    NSInteger sectionIndex = indexPath.section;
    key = _optionPayments[sectionIndex][indexPath.row];
    value = _thermalFlightPartDutchSmart[sectionIndex][key];
    BOOL oneClock = [value isKindOfClass:[NSDictionary class]] || [value isKindOfClass:[NSArray class]];
    cell.backgroundColor = [UIColor clearColor];
    
    
    for (UIView *subview in cell.contentView.subviews) {
        [subview removeFromSuperview];
    }
    
    
    UILabel *ownLabel = [[UILabel alloc] init];
    ownLabel.font = [UIFont monospacedSystemFontOfSize:14 weight:UIFontWeightMedium];
    ownLabel.textColor = [UIColor darkGrayColor];
    ownLabel.text = key;
    ownLabel.numberOfLines = 0;
    [cell.contentView addSubview:ownLabel];
    
    
    UILabel *aliveLabel = [[UILabel alloc] init];
    aliveLabel.font = [UIFont monospacedSystemFontOfSize:14 weight:UIFontWeightRegular];
    aliveLabel.textColor = [UIColor blackColor];
    aliveLabel.numberOfLines = 0;
    aliveLabel.textAlignment = NSTextAlignmentRight;
    [cell.contentView addSubview:aliveLabel];
    
    
    [ownLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(cell.contentView).offset(SuitableTab.dependingThe.humanPinPart);
        make.top.equalTo(cell.contentView).offset(SuitableTab.dependingThe.mustFatMiles);
        make.bottom.equalTo(cell.contentView).offset(-SuitableTab.dependingThe.mustFatMiles);
        make.width.equalTo(cell.contentView.mas_width).multipliedBy(oneClock?SuitableTab.dependingThe.wasLoopLabel:SuitableTab.dependingThe.portraitsLeap);
    }];
    
    [aliveLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(cell.contentView).offset(-SuitableTab.dependingThe.humanPinPart);
        make.top.equalTo(cell.contentView).offset(SuitableTab.dependingThe.mustFatMiles);
        make.bottom.equalTo(cell.contentView).offset(-SuitableTab.dependingThe.mustFatMiles);
        make.left.equalTo(ownLabel.mas_right).offset(SuitableTab.dependingThe.mustFatMiles);
    }];
    
    
    if (oneClock) {
        cell.accessoryType = UITableViewCellAccessoryDisclosureIndicator;
    } else {
        aliveLabel.text = [value description];
        cell.accessoryType = UITableViewCellAccessoryNone;
    }
    
    return cell;
}


- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    
    id value;
    NSString *key;
    
    NSInteger sectionIndex = indexPath.section;
    key = _optionPayments[sectionIndex][indexPath.row];
    value = _thermalFlightPartDutchSmart[sectionIndex][key];
    
    
    if ([value isKindOfClass:[NSDictionary class]]) {
        [self doneReportDictionary:value withTitle:key];
    } else if ([value isKindOfClass:[NSArray class]]) {
        [self mostIntentArray:value withTitle:key];
    } else {
        
        UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
        [pasteboard setString:[value description]];
        [SonToast keySong:SuitableTab.dependingThe.telephonyIndoorRepairProvinceHoldCursive];
    }
}


- (void)doneReportDictionary:(NSDictionary *)dict withTitle:(NSString *)title {
    ThreadNameViewController *queryFor = [[ThreadNameViewController alloc] init];
    [self.navigationController pushViewController:queryFor animated:NO];
    [queryFor foldRunNowSonInfo:dict withTitle:title];
}

- (void)mostIntentArray:(NSArray *)array withTitle:(NSString *)title {
    
    NSMutableDictionary *foundDict = [NSMutableDictionary dictionary];
    for (NSInteger i = 0; i < array.count; i++) {
        foundDict[[NSString stringWithFormat:@"[%ld]", (long)i]] = array[i];
    }
    
    ThreadNameViewController *queryFor = [[ThreadNameViewController alloc] init];
    [self.navigationController pushViewController:queryFor animated:NO];
    [queryFor foldRunNowSonInfo:foundDict withTitle:[NSString stringWithFormat:@"%@ (Array)", title]];
}

@end
