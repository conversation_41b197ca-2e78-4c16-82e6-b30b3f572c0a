






#import "SpringBagViewController.h"
#import "FaxEntitiesButton.h"
#import "SonToast.h"
#import "CardTightTextField.h"
#import "NSString+NetUighur.h"

@interface SpringBagViewController ()

@property (nonatomic, strong) CardTightTextField *slowDrizzleTextField;
@property (nonatomic, strong) UITextField *stereoOutTextField;
@property (nonatomic, strong) UITextField *badSamplesOwnTextField;
@property (nonatomic, strong) FaxEntitiesButton *bleedShotButton;
@end

@implementation SpringBagViewController

- (FaxEntitiesButton *)bleedShotButton {
    if (!_bleedShotButton) {
        _bleedShotButton = [[FaxEntitiesButton alloc] init];
    }
    return _bleedShotButton;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    
    self.slowDrizzleTextField = [[CardTightTextField alloc] initWithController:self];
    [self.view addSubview:self.slowDrizzleTextField];
    [self.slowDrizzleTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(SuitableTab.dependingThe.liveFoundMix);
        make.left.mas_equalTo(SuitableTab.dependingThe.bitThickWire);
        make.right.mas_equalTo(-SuitableTab.dependingThe.bitThickWire);
        make.height.mas_equalTo(SuitableTab.dependingThe.youUndoneBar);
    }];
    
    
    self.stereoOutTextField = [SuitableTab listDelayDialogEchoBeginYouCode];
    [self.view addSubview:self.stereoOutTextField];
    [self.stereoOutTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.slowDrizzleTextField.mas_bottom).offset(SuitableTab.dependingThe.rowExercise);
        make.left.mas_equalTo(SuitableTab.dependingThe.bitThickWire);
        make.height.mas_equalTo(SuitableTab.dependingThe.youUndoneBar);
    }];
    
    ourCost(self);
    self.bleedShotButton.askRedRealWaxAction = ^{
        chestAtom(self);
        
        NSString *encodeWayCode = self.slowDrizzleTextField.runLinerFitRet;
        NSString *floatingBit = self.slowDrizzleTextField.builtSuchWrappedArgumentsWeekly;
        if (self.slowDrizzleTextField.slowDrizzleTextField.text.diskMilesBag) {
            [self.bleedShotButton stablePaperQuitComplexArranger];
            [ManAlertView standMeanEvictionSheMenLeast:SuitableTab.needOldWideBig.kilometer message:SuitableTab.needOldWideBig.localizesRevertBlackIslamicPingSynthetic completion:nil];
            return;
        }
        if ([self.eyeChatterFar respondsToSelector:@selector(cupWetQualifiedExtendingWorkspaceCallingLexicalType:tabNameHigh:taskCode:completion:)]) {
            [DeleteUnitView weekendUplinkWindow];
            [self.eyeChatterFar cupWetQualifiedExtendingWorkspaceCallingLexicalType:SuitableTab.dependingThe.drainRequestSnowNativeAdobe tabNameHigh:floatingBit taskCode:encodeWayCode completion:^(id object) {
                [DeleteUnitView useProvisionBoyfriendFollowBeaconWindow];
                if ([object boolValue]) {
                    [SonToast funTiedZip:SuitableTab.needOldWideBig.eulerArbiterExpectedDanceWaistYardCode];
                }else {
                    [self.bleedShotButton stablePaperQuitComplexArranger];
                }
            }];
        }
    };
    [self.view addSubview:self.bleedShotButton];
    [self.bleedShotButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.stereoOutTextField);
        make.height.equalTo(self.stereoOutTextField);
        make.left.equalTo(self.stereoOutTextField.mas_right).offset(SuitableTab.dependingThe.mustFatMiles);
        make.right.equalTo(self.slowDrizzleTextField);
    }];
    
    
    [self.bleedShotButton setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    
    
    self.badSamplesOwnTextField = [SuitableTab mirroringArmpitVitalTeacherWeightedPassword:YES];
    [self.view addSubview:self.badSamplesOwnTextField];
    [self.badSamplesOwnTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.stereoOutTextField.mas_bottom).offset(SuitableTab.dependingThe.rowExercise);
        make.left.mas_equalTo(SuitableTab.dependingThe.bitThickWire);
        make.right.mas_equalTo(-SuitableTab.dependingThe.bitThickWire);
        make.height.mas_equalTo(SuitableTab.dependingThe.youUndoneBar);
    }];
    UIButton *nweRightButton = self.badSamplesOwnTextField.rightView.subviews.firstObject;
    [nweRightButton addTarget:self action:@selector(eventMilesRepublicBigShuffleAchievedHandler:) forControlEvents:(UIControlEventTouchUpInside)];
    
    
    UIButton *xxpk_forgetButton = [SuitableTab processorBookFarsiRejectDerivedColor:SuitableTab.needOldWideBig.menuPastBinKey];
    [xxpk_forgetButton addTarget:self action:@selector(seekSlowMiddleFixPhoneticAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:xxpk_forgetButton];
    [xxpk_forgetButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.badSamplesOwnTextField.mas_bottom).offset(SuitableTab.dependingThe.rowExercise);
        make.left.mas_equalTo(SuitableTab.dependingThe.bitThickWire);
        make.right.mas_equalTo(-SuitableTab.dependingThe.bitThickWire);
        make.height.mas_equalTo(SuitableTab.dependingThe.bitmapOffRet);
    }];
}
- (void)eventMilesRepublicBigShuffleAchievedHandler:(UIButton *)sender {
    sender.selected = !sender.isSelected;
    self.badSamplesOwnTextField.secureTextEntry = !self.badSamplesOwnTextField.isSecureTextEntry;
}

- (void)seekSlowMiddleFixPhoneticAction:(id)sender {
    if (self.slowDrizzleTextField.slowDrizzleTextField.text.diskMilesBag) {
        [ManAlertView standMeanEvictionSheMenLeast:SuitableTab.needOldWideBig.kilometer message:SuitableTab.needOldWideBig.localizesRevertBlackIslamicPingSynthetic completion:nil];
        return;
    }
    if (self.stereoOutTextField.text.diskMilesBag) {
        [ManAlertView standMeanEvictionSheMenLeast:SuitableTab.needOldWideBig.kilometer message:SuitableTab.needOldWideBig.webpageDatabasesImageBadmintonShape completion:nil];
        return;
    }
    if (self.badSamplesOwnTextField.text.length < SuitableTab.dependingThe.gradeWinHit) {
        [ManAlertView standMeanEvictionSheMenLeast:SuitableTab.needOldWideBig.kilometer message:SuitableTab.needOldWideBig.disabledDeviceMaxShakeHisCaffeine completion:nil];
        return;
    }
    NSString *encodeWayCode = self.slowDrizzleTextField.runLinerFitRet;
    NSString *floatingBit = self.slowDrizzleTextField.builtSuchWrappedArgumentsWeekly;
    if ([self.eyeChatterFar respondsToSelector:@selector(dryShakeWaxLemmaReductionDisposePartly:code:taskCode:fitKey:completion:)]) {
        [DeleteUnitView weekendUplinkWindow];
        [self.eyeChatterFar dryShakeWaxLemmaReductionDisposePartly:floatingBit code:self.stereoOutTextField.text taskCode:encodeWayCode fitKey:self.badSamplesOwnTextField.text completion:^(id object) {
            [DeleteUnitView useProvisionBoyfriendFollowBeaconWindow];
            [SonToast funTiedZip:SuitableTab.needOldWideBig.eulerOfficialEncodingsDepthIrish];
            if (object) {
                [self.discreteSpaDelegate offsetThreeLegalVerifyRareWithName:object icyMovementPassword:self.badSamplesOwnTextField.text];
                [self potentialOutputsSaveStampPanAction:nil];
            }
        }];
    }
}

- (void)dealloc {
    [self.bleedShotButton stablePaperQuitComplexArranger];
}
@end
