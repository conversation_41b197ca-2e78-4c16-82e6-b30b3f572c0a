






#import "FatPostalViewController.h"
#import "FaxEntitiesButton.h"
#import "EvictStairUndoViewController.h"
#import "SonToast.h"
#import "XXGProtocolLabel.h"
#import "CardTightTextField.h"
#import "NSString+NetUighur.h"

@interface FatPostalViewController ()

@property (nonatomic, strong) CardTightTextField *slowDrizzleTextField;
@property (nonatomic, strong) UITextField *stereoOutTextField;
@property (nonatomic, strong) FaxEntitiesButton *bleedShotButton;
@property (nonatomic,strong) XXGProtocolLabel *fillerEraDogLabel;

@end

@implementation FatPostalViewController

- (FaxEntitiesButton *)bleedShotButton {
    if (!_bleedShotButton) {
        _bleedShotButton = [[FaxEntitiesButton alloc] init];
    }
    return _bleedShotButton;
}

- (XXGProtocolLabel *)fillerEraDogLabel {
    if (!_fillerEraDogLabel) {
        _fillerEraDogLabel = [XXGProtocolLabel backupPacketsLabel:NO];
    }
    return _fillerEraDogLabel;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    
    self.slowDrizzleTextField = [[CardTightTextField alloc] initWithController:self];
    [self.view addSubview:self.slowDrizzleTextField];
    [self.slowDrizzleTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(SuitableTab.dependingThe.liveFoundMix);
        make.left.mas_equalTo(SuitableTab.dependingThe.bitThickWire);
        make.right.mas_equalTo(-SuitableTab.dependingThe.bitThickWire);
        make.height.mas_equalTo(SuitableTab.dependingThe.youUndoneBar);
    }];
    
    
    self.stereoOutTextField = [SuitableTab listDelayDialogEchoBeginYouCode];
    [self.view addSubview:self.stereoOutTextField];
    [self.stereoOutTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.slowDrizzleTextField.mas_bottom).offset(SuitableTab.dependingThe.pushRendered);
        make.left.mas_equalTo(SuitableTab.dependingThe.bitThickWire);
        make.height.mas_equalTo(SuitableTab.dependingThe.youUndoneBar);
    }];
    
    ourCost(self);
    self.bleedShotButton.askRedRealWaxAction = ^{
        chestAtom(self);
        NSString *encodeWayCode = self.slowDrizzleTextField.runLinerFitRet;
        NSString *floatingBit = self.slowDrizzleTextField.builtSuchWrappedArgumentsWeekly;
        if (self.slowDrizzleTextField.slowDrizzleTextField.text.diskMilesBag) {
            [self.bleedShotButton stablePaperQuitComplexArranger];
            [ManAlertView standMeanEvictionSheMenLeast:SuitableTab.needOldWideBig.kilometer message:SuitableTab.needOldWideBig.localizesRevertBlackIslamicPingSynthetic completion:nil];
            return;
        }
        if ([self.eyeChatterFar respondsToSelector:@selector(cupWetQualifiedExtendingWorkspaceCallingLexicalType:tabNameHigh:taskCode:completion:)]) {
            [DeleteUnitView weekendUplinkWindow];
            [self.eyeChatterFar cupWetQualifiedExtendingWorkspaceCallingLexicalType:SuitableTab.dependingThe.subjectQueryingBookmarksDisplayedWin tabNameHigh:floatingBit taskCode:encodeWayCode completion:^(id object) {
                [DeleteUnitView useProvisionBoyfriendFollowBeaconWindow];
                if ([object boolValue]) {
                    [SonToast funTiedZip:SuitableTab.needOldWideBig.eulerArbiterExpectedDanceWaistYardCode];
                }else {
                    [self.bleedShotButton stablePaperQuitComplexArranger];
                }
            }];
        }
    };
    [self.view addSubview:self.bleedShotButton];
    [self.bleedShotButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.stereoOutTextField);
        make.height.equalTo(self.stereoOutTextField);
        make.left.equalTo(self.stereoOutTextField.mas_right).offset(SuitableTab.dependingThe.mustFatMiles);
        make.right.equalTo(self.slowDrizzleTextField);
    }];
    
    
    [self.bleedShotButton setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    
    
    UIButton *typeArmOldArtButton = [SuitableTab processorBookFarsiRejectDerivedColor:SuitableTab.needOldWideBig.ejectOverallBrandUsesClientChanging];
    [typeArmOldArtButton addTarget:self action:@selector(tooEntitledModernStrokedAngleAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:typeArmOldArtButton];
    [typeArmOldArtButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.stereoOutTextField.mas_bottom).offset(SuitableTab.dependingThe.pushRendered);
        make.left.mas_equalTo(SuitableTab.dependingThe.bitThickWire);
        make.right.mas_equalTo(-SuitableTab.dependingThe.bitThickWire);
        make.height.mas_equalTo(SuitableTab.dependingThe.bitmapOffRet);
    }];
    
    [self.view addSubview:self.fillerEraDogLabel];
    [self.fillerEraDogLabel setWaistHasKinLockingCreatedExtra:^{
        chestAtom(self);
        [self hallFeetGramStickyDashDitherAction];
    }];
    [self.fillerEraDogLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(-SuitableTab.dependingThe.flipTipPress);
        make.left.mas_equalTo(SuitableTab.dependingThe.areaLawEuler);
        make.right.mas_equalTo(-SuitableTab.dependingThe.areaLawEuler);
    }];
}

- (void)tooEntitledModernStrokedAngleAction:(UIButton *)sender {
    if (self.slowDrizzleTextField.slowDrizzleTextField.text.diskMilesBag) {
        [ManAlertView standMeanEvictionSheMenLeast:SuitableTab.needOldWideBig.kilometer message:SuitableTab.needOldWideBig.localizesRevertBlackIslamicPingSynthetic completion:nil];
        return;
    }
    if (self.stereoOutTextField.text.diskMilesBag) {
        [ManAlertView standMeanEvictionSheMenLeast:SuitableTab.needOldWideBig.kilometer message:SuitableTab.needOldWideBig.webpageDatabasesImageBadmintonShape completion:nil];
        return;
    }
    NSString *encodeWayCode = self.slowDrizzleTextField.runLinerFitRet;
    NSString *floatingBit = self.slowDrizzleTextField.builtSuchWrappedArgumentsWeekly;
    if ([self.eyeChatterFar respondsToSelector:@selector(mightGatheringCampaignKnowImpliedRatioBusySignal:code:taskCode:completion:)]) {
        [DeleteUnitView weekendUplinkWindow];
        [self.eyeChatterFar mightGatheringCampaignKnowImpliedRatioBusySignal:floatingBit code:self.stereoOutTextField.text taskCode:encodeWayCode completion:^(id object) {
            [DeleteUnitView useProvisionBoyfriendFollowBeaconWindow];
        }];
    }
}


- (void)hallFeetGramStickyDashDitherAction {
    EvictStairUndoViewController *outMaxUnsafe = [EvictStairUndoViewController new];
    outMaxUnsafe.pinRadioUse = @(YES);
    outMaxUnsafe.eyeChatterFar = self.eyeChatterFar;
    [outMaxUnsafe setOldestMountDisplayedAuditSee:^(BOOL result) {
        self.fillerEraDogLabel.haveBandCenter = result;
    }];
    [self.navigationController pushViewController:outMaxUnsafe animated:NO];
}

- (void)dealloc {
    [self.bleedShotButton stablePaperQuitComplexArranger];
}
@end
