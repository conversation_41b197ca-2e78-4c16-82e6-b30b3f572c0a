






#import "BackwardPlainViewController.h"
#import "FaxEntitiesButton.h"
#import "SonToast.h"
#import "CardTightTextField.h"
#import "NSString+NetUighur.h"
@import WebKit;

@interface BackwardPlainViewController ()

@property (nonatomic, strong) CardTightTextField *slowDrizzleTextField;
@property (nonatomic, strong) UITextField *stereoOutTextField;
@property (nonatomic, strong) FaxEntitiesButton *bleedShotButton;

@end

@implementation BackwardPlainViewController


- (FaxEntitiesButton *)bleedShotButton {
    if (!_bleedShotButton) {
        _bleedShotButton = [[FaxEntitiesButton alloc] init];
    }
    return _bleedShotButton;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.airDefinesButton.hidden = [self.pinRadioUse[0] boolValue];
    
    UILabel *tipLabel = [SuitableTab volatileDanishCallingExactAre:SuitableTab.needOldWideBig.combiningModelInnerProxiesContrastQuotation];
    tipLabel.numberOfLines = 0;
    [self.view addSubview:tipLabel];
    [tipLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(SuitableTab.dependingThe.rankedInland);
        make.left.mas_equalTo(SuitableTab.dependingThe.rankedInland);
        make.right.mas_equalTo(-SuitableTab.dependingThe.rankedInland);
    }];
    
    
    self.slowDrizzleTextField = [[CardTightTextField alloc] initWithController:self];
    [self.view addSubview:self.slowDrizzleTextField];
    [self.slowDrizzleTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(tipLabel.mas_bottom).offset(SuitableTab.dependingThe.pushRendered);
        make.left.mas_equalTo(SuitableTab.dependingThe.bitThickWire);
        make.right.mas_equalTo(-SuitableTab.dependingThe.bitThickWire);
        make.height.mas_equalTo(SuitableTab.dependingThe.youUndoneBar);
    }];
    
    
    self.stereoOutTextField = [SuitableTab listDelayDialogEchoBeginYouCode];
    [self.view addSubview:self.stereoOutTextField];
    [self.stereoOutTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.slowDrizzleTextField.mas_bottom).offset(SuitableTab.dependingThe.moveIdiomOld);
        make.left.mas_equalTo(SuitableTab.dependingThe.bitThickWire);
        make.height.mas_equalTo(SuitableTab.dependingThe.youUndoneBar);
    }];
    
    ourCost(self);
    self.bleedShotButton.askRedRealWaxAction = ^{
        chestAtom(self);
        NSString *encodeWayCode = self.slowDrizzleTextField.runLinerFitRet;
        NSString *floatingBit = self.slowDrizzleTextField.builtSuchWrappedArgumentsWeekly;
        if (self.slowDrizzleTextField.slowDrizzleTextField.text.diskMilesBag) {
            [self.bleedShotButton stablePaperQuitComplexArranger];
            [ManAlertView standMeanEvictionSheMenLeast:SuitableTab.needOldWideBig.kilometer message:SuitableTab.needOldWideBig.localizesRevertBlackIslamicPingSynthetic completion:nil];
            return;
        }
        if ([self.eyeChatterFar respondsToSelector:@selector(cupWetQualifiedExtendingWorkspaceCallingLexicalType:tabNameHigh:taskCode:completion:)]) {
            [DeleteUnitView weekendUplinkWindow];
            [self.eyeChatterFar cupWetQualifiedExtendingWorkspaceCallingLexicalType:SuitableTab.dependingThe.maxBoldWakeSon tabNameHigh:floatingBit taskCode:encodeWayCode completion:^(id object) {
                [DeleteUnitView useProvisionBoyfriendFollowBeaconWindow];
                if ([object boolValue]) {
                    [SonToast funTiedZip:SuitableTab.needOldWideBig.eulerArbiterExpectedDanceWaistYardCode];
                }else {
                    [self.bleedShotButton stablePaperQuitComplexArranger];
                }
            }];
        }
    };
    [self.view addSubview:self.bleedShotButton];
    [self.bleedShotButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.stereoOutTextField);
        make.height.equalTo(self.stereoOutTextField);
        make.left.equalTo(self.stereoOutTextField.mas_right).offset(SuitableTab.dependingThe.mustFatMiles);
        make.right.equalTo(self.slowDrizzleTextField);
    }];
    
    
    [self.bleedShotButton setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    
    
    UIButton *xxpk_bindButton = [SuitableTab processorBookFarsiRejectDerivedColor:SuitableTab.needOldWideBig.respectsPieceNorthMotionDrain];
    [xxpk_bindButton addTarget:self action:@selector(blendEndpointsPreviousCombineDeriveAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:xxpk_bindButton];
    [xxpk_bindButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.stereoOutTextField.mas_bottom).offset(SuitableTab.dependingThe.moveIdiomOld);
        make.left.mas_equalTo(SuitableTab.dependingThe.bitThickWire);
        make.right.mas_equalTo(-SuitableTab.dependingThe.bitThickWire);
        make.height.mas_equalTo(SuitableTab.dependingThe.bitmapOffRet);
    }];
}

- (void)blendEndpointsPreviousCombineDeriveAction:(id)sender {
    if (self.slowDrizzleTextField.slowDrizzleTextField.text.diskMilesBag) {
        [ManAlertView standMeanEvictionSheMenLeast:SuitableTab.needOldWideBig.kilometer message:SuitableTab.needOldWideBig.localizesRevertBlackIslamicPingSynthetic completion:nil];
        return;
    }
    if (self.stereoOutTextField.text.diskMilesBag) {
        [ManAlertView standMeanEvictionSheMenLeast:SuitableTab.needOldWideBig.kilometer message:SuitableTab.needOldWideBig.webpageDatabasesImageBadmintonShape completion:nil];
        return;
    }
    NSString *encodeWayCode = self.slowDrizzleTextField.runLinerFitRet;
    NSString *floatingBit = self.slowDrizzleTextField.builtSuchWrappedArgumentsWeekly;
    if ([self.eyeChatterFar respondsToSelector:@selector(undoneSenderDownloadAltimeterTextCompactBordered:code:taskCode:completion:)]) {
        [DeleteUnitView weekendUplinkWindow];
        [self.eyeChatterFar undoneSenderDownloadAltimeterTextCompactBordered:floatingBit code:self.stereoOutTextField.text taskCode:encodeWayCode completion:^(id object) {
            [DeleteUnitView useProvisionBoyfriendFollowBeaconWindow];
            if ([object boolValue]) {
                [[UpsideAskManager shared] howGroupedStrokeSinSindhiConvertedViewController:self.navigationController];
                [SonToast funTiedZip:SuitableTab.needOldWideBig.clinicalAdjustExpireUighurFullyCover];
                if ([self.pinRadioUse[1] isKindOfClass:[WKWebView class]]) {
                    WKWebView *xxpk_vkview = (WKWebView *)self.pinRadioUse[1];
                    [xxpk_vkview reload];
                }
            }
        }];
    }
}

- (void)dealloc {
    [self.bleedShotButton stablePaperQuitComplexArranger];
}

@end
