








#import "OnlyTimeAsleep.h"

#define WonTryCapBeen self.frame.size.width

@interface OnlyTimeAsleep()



@property (nonatomic, strong) NSMutableArray                            *gainPenArray;



@property (nonatomic, strong) NSMutableArray <SheBoxRareCardCell *>      *starArray;



@property (strong, nonatomic) NSMutableArray <SheBoxRareCardCell *>      *titleRedoTag;



@property (assign, nonatomic) NSInteger                                 count;



@property (nonatomic, assign) SucceedRevealPinchArtistOld                          status;



@property (nonatomic, assign) NSInteger                                 channelCount;



@property (nonatomic, assign) CGFloat                                   higher;

@end

@implementation OnlyTimeAsleep

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.status = BounceOwnAttitudeNearbyJoiningLog;
    }
    return self;
}

- (void)replyOurMale
{
    
    if (self.starArray.firstObject) {
        
        
        SheBoxRareCardCell *barrageView = self.starArray.firstObject;
        
        barrageView.frame = CGRectMake(WonTryCapBeen, 0, barrageView.postTopSize.width, barrageView.postTopSize.height);
        
        self.higher = barrageView.higher;
        
        self.channelCount = barrageView.channelCount;
        
        
        NSInteger row = [self cacheHitActivateUnorderedIntentCampaignBridging:barrageView];
        
        
        if (row >= 0) {
            
            
            [self.starArray removeObjectAtIndex:0];
            
            
            if (![self.subviews containsObject:barrageView]) {
                [self addSubview:barrageView];
            }
            barrageView.frame = CGRectMake(WonTryCapBeen,  row * (barrageView.postTopSize.height + _higher), barrageView.postTopSize.width, barrageView.postTopSize.height);
            
            
            [_gainPenArray setObject:barrageView atIndexedSubscript:row];
            
            
            if ([self.delegate respondsToSelector:@selector(canMostlyOutView:willDisplayCell:)]) {
                [self.delegate canMostlyOutView:self willDisplayCell:barrageView];
            }
            
            
            [self.titleRedoTag addObject:barrageView];
            
            [barrageView kernelCheckerOrangeUnchangedRoute:^{
                
                
                [barrageView setTransform:CGAffineTransformMakeTranslation(- barrageView.frame.size.width-WonTryCapBeen, 0)];
                
            } completion:^(BOOL finished) {
                
                [barrageView removeFromSuperview];
                
                
                [self.titleRedoTag removeObject:barrageView];
                
                
                if ([self.delegate respondsToSelector:@selector(canMostlyOutView:didEndDisplayingCell:)]) {
                    [self.delegate canMostlyOutView:self didEndDisplayingCell:barrageView];
                }
                
                
                if (--self.count <= 0) {
                    if ([self.delegate respondsToSelector:@selector(pulseUnderageHelperPresenceCommentSignerExecutorLeaky:)]) {
                        [self.delegate pulseUnderageHelperPresenceCommentSignerExecutorLeaky:self];
                    }
                    self.count = 0;
                }
                
                

            }];
        }
    }
    
    [self performSelector:@selector(replyOurMale) withObject:nil afterDelay:0.45f];
}


- (void)slopeFreeAudio:(NSArray <SheBoxRareCardCell *> *)barrages
{
    self.count += barrages.count;
    [self.starArray addObjectsFromArray:barrages];
}

- (void)start
{
    if (self.status == ShipmentJustifiedFlightKernelsAuditedHer) {
        return;
    }
    self.status = ShipmentJustifiedFlightKernelsAuditedHer;
    
    [self replyOurMale];
}

- (void)stop
{
    if (self.status == BounceOwnAttitudeNearbyJoiningLog) {
        return;
    }
    self.status = BounceOwnAttitudeNearbyJoiningLog;
    
    if (self.titleRedoTag.count) {
        [self.titleRedoTag makeObjectsPerformSelector:@selector(pause)];
    }
    
    if (self.starArray.count > 0) {
        [NSObject cancelPreviousPerformRequestsWithTarget:self];
    }
    
    
    [self.titleRedoTag  makeObjectsPerformSelector:@selector(removeFromSuperview)];
    self.channelCount       = 0;
    self.count              = 0;
    [self.titleRedoTag  removeAllObjects];
    [self.starArray     removeAllObjects];
    [self.gainPenArray  removeAllObjects];
    
    self.titleRedoTag       = nil;
    self.starArray          = nil;
    self.gainPenArray       = nil;
}


- (NSInteger)cacheHitActivateUnorderedIntentCampaignBridging:(SheBoxRareCardCell *)newBarrage
{
    for (int row = 0; row<_gainPenArray.count; row++) {
        NSObject *object = _gainPenArray[row];
        if ([object isKindOfClass:[NSNumber class]]) { 
            
            return row;
            
        }else if ([object isKindOfClass:[SheBoxRareCardCell class]]) { 
            
            SheBoxRareCardCell *oldBarrage = (SheBoxRareCardCell*)object;
            
            if ([self cacheHitActivateUnorderedIntentCampaignBridging:oldBarrage golfInput:newBarrage]) {
                
                return row;
            }
        }
    }
    
    return -1;
}


- (BOOL)cacheHitActivateUnorderedIntentCampaignBridging:(SheBoxRareCardCell *)oldBarrage golfInput:(SheBoxRareCardCell *)newBarrage
{
    
    if (oldBarrage.status == NoiseExtraTeamOptFunkUnique) {
        return NO;
    }
    
    
    CGRect rect = [oldBarrage.layer.presentationLayer frame];
    if (rect.origin.x>WonTryCapBeen - oldBarrage.frame.size.width) {
        
        return NO;
    }else if (rect.size.width == 0)
    {
        
        return NO;
    }
    else if (oldBarrage.frame.size.width > newBarrage.frame.size.width) {
        
        return YES;
    }else
    {
        
        CGFloat time = WonTryCapBeen/(WonTryCapBeen+newBarrage.frame.size.width)*newBarrage.dryFitSuffix;
        
        CGFloat endX = rect.origin.x - time/(oldBarrage.dryFitSuffix)*(WonTryCapBeen + oldBarrage.frame.size.width);
        if (endX < -oldBarrage.frame.size.width) {
            
            return YES;
        }
    }
    return NO;
}


- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event
{
    UITouch *touch = [touches anyObject];
    CGPoint clickPoint  = [touch locationInView:self];
    for (SheBoxRareCardCell *barrageView in [self subviews])
    {
        if ([barrageView.layer.presentationLayer hitTest:clickPoint])
        {
            
            if ([self.delegate respondsToSelector:@selector(canMostlyOutView:eyeTamilUseCell:)]) {
                [self.delegate canMostlyOutView:self eyeTamilUseCell:barrageView];
            }
            break;
        }
    }
}




- (NSMutableArray<SheBoxRareCardCell *> *)starArray {
    if (!_starArray) {
        _starArray = [[NSMutableArray alloc] init];
    }
    return _starArray;
}


- (NSMutableArray<SheBoxRareCardCell *> *)titleRedoTag {
    if (!_titleRedoTag) {
        _titleRedoTag = [[NSMutableArray alloc] init];
    }
    return _titleRedoTag;
}


- (void)setChannelCount:(NSInteger)channelCount
{
    
    if (self.gainPenArray.count < channelCount) { 
        
        for (NSInteger row = self.gainPenArray.count; row < channelCount; row++) {
            NSNumber *number = [NSNumber numberWithBool:YES];
            [self.gainPenArray setObject:number atIndexedSubscript:row];
        }
        
    }else {
        
        for (NSInteger row = channelCount; row < self.gainPenArray.count; row++) {
            [self.gainPenArray removeObjectAtIndex:row];
        }
    }
    
    _channelCount = channelCount;
    
}


- (NSMutableArray *)gainPenArray {
    if (!_gainPenArray) {
        _gainPenArray = [[NSMutableArray alloc] init];
    }
    return _gainPenArray;
}

@end
