

#import "LooseAlphaView.h"
#import "AreSunPlaceTabCell.h"

@implementation LooseAlphaView

- (void)dismissMiterStartInsertionCurlValueModel:(BackEyeShareInfo *)model {
    
    NSMutableArray *radio = [NSMutableArray new];
    for (int i = 0; i<model.sheBrother; i++) {
        AreSunPlaceTabCell *cell = [[AreSunPlaceTabCell alloc]init];
        cell.dryFitSuffix = model.messageMax;
        cell.channelCount = 1;
        cell.higher = 6;
        cell.kelvinWrapping = CGFLOAT_MIN;
        CGRect trapRect = [model.ownWarpDense boundingRectWithSize:CGSizeMake(CGFLOAT_MAX, CGFLOAT_MAX) options:NSStringDrawingUsesLineFragmentOrigin attributes:[NSDictionary dictionaryWithObject:[UIFont systemFontOfSize:model.elevenTransientFileWristPrologWindows] forKey:NSFontAttributeName] context:nil];
        cell.postTopSize = CGSizeMake(trapRect.size.width+8, trapRect.size.height+4);
        cell.model = model;
        [radio addObject:cell];
    }

    [self slopeFreeAudio:radio];
    
}

@end
