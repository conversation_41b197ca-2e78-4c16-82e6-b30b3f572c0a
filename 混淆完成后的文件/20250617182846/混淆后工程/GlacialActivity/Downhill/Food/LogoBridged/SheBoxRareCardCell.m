







#import "SheBoxRareCardCell.h"
#define ourCost(obj) __weak typeof(obj) weak##obj = obj;
#define chestAtom(obj) __strong typeof(obj) obj = weak##obj;

@interface SheBoxRareCardCell()



@property (nonatomic, strong) NSTimer *timer;

@property (nonatomic, assign) BOOL topPoolGrayBig;

@end

@implementation SheBoxRareCardCell

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:CGRectZero];
    if (self) {
        
        _postTopSize = CGSizeMake(200, 40);
        _dryFitSuffix = 4;
        _model = nil;
        _channelCount = 3;
        _higher = 0;
        _kelvinWrapping = 0;
        _status = BounceOwnAttitudeNearbyJoiningLog;
        _topPoolGrayBig = NO;
        
    }
    return self;
}

- (void)syntaxPrinterRemainingRearPrefixValue
{
    CGFloat barrageX = [[self.layer presentationLayer] frame].origin.x;
    CGFloat barrageWidth = self.frame.size.width;
    
    
    CGFloat speed = (self.superview.frame.size.width + barrageWidth) / self.dryFitSuffix;
    
    
    CGFloat beginExitTime = barrageWidth / speed;
    
    if (_kelvinWrapping > 0) {
        self.status = NoiseExtraTeamOptFunkUnique;
        if (-1< barrageX < 1) {
            
            if (_topPoolGrayBig) { return;}
            _topPoolGrayBig = YES;
            [self pause];
            [self performSelector:@selector(resume) withObject:nil afterDelay:_kelvinWrapping];
            [self performSelector:@selector(iodineStatus) withObject:nil afterDelay:_kelvinWrapping - beginExitTime];
        }
    }
}
- (void)iodineStatus
{
    self.status = ShipmentJustifiedFlightKernelsAuditedHer;
}

- (void)kernelCheckerOrangeUnchangedRoute:(void(^)(void))animations completion:(void(^)(BOOL))completion
{
    self.status = ShipmentJustifiedFlightKernelsAuditedHer;
    
    _timer = [NSTimer timerWithTimeInterval:0.01 target:self selector:@selector(syntaxPrinterRemainingRearPrefixValue) userInfo:nil repeats:YES];
    [[NSRunLoop currentRunLoop] addTimer:_timer forMode:NSRunLoopCommonModes];
    
    
    ourCost(self);
    [UIView animateWithDuration:self.dryFitSuffix delay:0 options:(UIViewAnimationOptionCurveLinear | UIViewAnimationOptionAllowUserInteraction) animations:^{
        
        if (animations) {
            animations();
        }
        
    } completion:^(BOOL finished) {
        chestAtom(self);
        self->_status = ShipmentJustifiedFlightKernelsAuditedHer;
        
        if (completion) {
            completion(finished);
        }
        
        if(self->_timer) {
            [self->_timer invalidate];
            self->_timer = nil;
        }
        
    }];
}

- (void)pause
{
    
    CFTimeInterval pauseTime = [self.layer convertTime:CACurrentMediaTime() fromLayer:nil];
    
    
    self.layer.timeOffset = pauseTime;
    
    
    self.layer.speed = 0;
}

- (void)resume
{
    
    CFTimeInterval pauseTime = self.layer.timeOffset;
    
    CFTimeInterval timeSincePause = CACurrentMediaTime() - pauseTime;
    
    self.layer.timeOffset = 0;
    
    self.layer.beginTime = timeSincePause;
    
    self.layer.speed = 1;
}


@end
