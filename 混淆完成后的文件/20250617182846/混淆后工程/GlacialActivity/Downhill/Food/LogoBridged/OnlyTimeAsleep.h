







#import <UIKit/UIKit.h>
#import "SheBoxRareCardCell.h"

@protocol EditorsGallonsDelegate;


@interface OnlyTimeAsleep : UIView


@property (weak, nonatomic) id<EditorsGallonsDelegate> delegate;


- (void)slopeFreeAudio:(NSArray <SheBoxRareCardCell *> *)barrages;


- (void)start;


- (void)stop;

@end


@protocol EditorsGallonsDelegate <NSObject>

@optional


- (void)canMostlyOutView:(OnlyTimeAsleep *)barrageView eyeTamilUseCell:(SheBoxRareCardCell *)cell;


- (void)pulseUnderageHelperPresenceCommentSignerExecutorLeaky:(OnlyTimeAsleep *)barrageView;


- (void)canMostlyOutView:(OnlyTimeAsleep *)barrageView willDisplayCell:(SheBoxRareCardCell *)cell;


- (void)canMostlyOutView:(OnlyTimeAsleep *)barrageView didEndDisplayingCell:(SheBoxRareCardCell *)cell;

@end
