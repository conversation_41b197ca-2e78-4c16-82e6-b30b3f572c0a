






#import "AreSunPlaceTabCell.h"
#import "BackEyeShareInfo.h"
#import "Masonry.h"
#import "UIColor+MapColor.h"

@implementation AreSunPlaceTabCell

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        
        self.backgroundColor = [UIColor clearColor];
        
        self.clipsToBounds = YES;
        
        [self addSubview:self.showPanLabel];
        
        [self.showPanLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.mas_equalTo(UIEdgeInsetsMake(0, 4, 0, 0));
        }];
        
    }
    return self;
}

- (void)setModel:(BackEyeShareInfo *)model
{
    _model = model;
    _showPanLabel.text = model.ownWarpDense;
    _showPanLabel.font = [UIFont systemFontOfSize:model.elevenTransientFileWristPrologWindows];
    _showPanLabel.textColor = [UIColor sharpenAffectedSignBigChargePrep:model.unionExpiredArmDryForeverAuto];
    self.backgroundColor = [[UIColor sharpenAffectedSignBigChargePrep:model.scaleRowsTertiarySheCupUpscale] colorWithAlphaComponent:model.signerSpeakHeadphoneAtomDiscountsRepair];
    self.layer.cornerRadius = 2;
}

- (UILabel *)showPanLabel {
    if (!_showPanLabel) {
        _showPanLabel = [[UILabel alloc] init];
        _showPanLabel.backgroundColor = [UIColor clearColor];
        _showPanLabel.textAlignment = NSTextAlignmentLeft;
    }
    return _showPanLabel;
}

@end
