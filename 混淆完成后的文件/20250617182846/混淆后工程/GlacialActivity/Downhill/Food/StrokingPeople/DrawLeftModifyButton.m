






#import "DrawLeftModifyButton.h"
#import "SuitableTab.h"
#import "UIImage+ArtImage.h"
#import "AvailLacrosseController.h"
#import "UIImage+ArtImage.h"
#import "UIColor+MapColor.h"

@implementation DrawLeftModifyButton

- (instancetype)initRetDingbatsViewController:(UIViewController *)viewController {
    self = [super init];
    if (self) {
        self.hitDenyYahooViewController = viewController;
        [self compactEndsSupportMayFound];
    }
    return self;
}


- (void)compactEndsSupportMayFound {
    
    NSArray *jabberStreamedRotateEmbeddingSearch = [UsageEntitledFinnishDynamicSort plateSpeechDarkerSleepHold:[DanishNine class]];
    
    
    NSString *currentCountryCode = [[NSLocale currentLocale] objectForKey:NSLocaleCountryCode];
    
    __block DanishNine *matchedCountry = nil;
    [jabberStreamedRotateEmbeddingSearch enumerateObjectsUsingBlock:^(DanishNine *country, NSUInteger idx, BOOL *stop) {
        if ([country.weeklySindhiCode caseInsensitiveCompare:currentCountryCode] == NSOrderedSame) {
            matchedCountry = country;
            *stop = YES; 
        }
    }];
    self.selectIcyBlobConsoleYear = matchedCountry;
    
    
    NSString *title = [NSString stringWithFormat:@"%@%@",SuitableTab.dependingThe.loadingUnsafe, matchedCountry.encodeWayCode];
    [self setTitle:title forState:UIControlStateNormal];
    
    
    UIImage *originalImage = [UIImage faeroeseSynthesisVariablesBeenTransformName:SuitableTab.dependingThe.sumMusicThumbTooFormattedAllow];
    
    
    CGSize targetImageSize = CGSizeMake(13, 13); 
    
    
    UIImage *scaledImage = [self variablesColorImage:originalImage respondsSize:targetImageSize];
    
    
    [self setImage:scaledImage forState:UIControlStateNormal];
    [self setImage:[scaledImage imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate] forState:UIControlStateHighlighted]; 
    
    
    self.imageView.contentMode = UIViewContentModeScaleAspectFit;
    
    
    self.semanticContentAttribute = UISemanticContentAttributeForceRightToLeft; 
    CGFloat spacing = 3.0; 
    self.imageEdgeInsets = UIEdgeInsetsMake(0, spacing, 0, -spacing);  
    self.titleEdgeInsets = UIEdgeInsetsMake(0, -spacing, 0, spacing);   
    
    
    [self setBackgroundImage:[UIImage earMixHitTitleColor:[SuitableTab.telephonyColor discoverLibraryQuarterPlusSheAlpha:8]] forState:UIControlStateNormal];
    [self setBackgroundImage:[UIImage earMixHitTitleColor:[[UIColor lightGrayColor] colorWithAlphaComponent:0.5f]]
                   forState:UIControlStateHighlighted];
    self.titleLabel.font = [UIFont systemFontOfSize:16];
    self.layer.cornerRadius = 2.f;
    self.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMinXMaxYCorner;
    self.layer.masksToBounds = YES;
    
    
    self.contentEdgeInsets = UIEdgeInsetsMake(8, 12, 8, 12); 
    
    
    [self sizeToFit];
    
    
    [self addTarget:self action:@selector(exactUnlockClicked) forControlEvents:UIControlEventTouchUpInside];
}


- (UIImage *)variablesColorImage:(UIImage *)image respondsSize:(CGSize)targetSize {
    
    UIGraphicsBeginImageContextWithOptions(targetSize, NO, 0.0);
    
    
    CGFloat auditGlyph = targetSize.width / image.size.width;
    CGFloat ratioOldest = targetSize.height / image.size.height;
    CGFloat scaleFactor = MIN(auditGlyph, ratioOldest);
    
    
    CGRect invertRect = CGRectMake(0, 0,
                                  image.size.width * scaleFactor,
                                  image.size.height * scaleFactor);
    
    
    CGPoint origin = CGPointMake((targetSize.width - invertRect.size.width) / 2.0,
                               (targetSize.height - invertRect.size.height) / 2.0);
    [image drawInRect:CGRectMake(origin.x, origin.y,
                                invertRect.size.width,
                                invertRect.size.height)];
    
    UIImage *canImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    
    return canImage;
}


- (void)exactUnlockClicked {
    TransformBlockHiddenBackwardAuthorsRadiansViewController *vc = [TransformBlockHiddenBackwardAuthorsRadiansViewController new];
    vc.openBleedDelegate = self;
    [self.hitDenyYahooViewController.navigationController pushViewController:vc animated:NO];
}

- (void)expectsMildRelayDecisionMaskPreservesStructureLinear:(DanishNine *)country {
    NSString *title = [NSString stringWithFormat:@"%@%@",SuitableTab.dependingThe.loadingUnsafe, country.encodeWayCode];
    [self setTitle:title forState:UIControlStateNormal];
    self.selectIcyBlobConsoleYear = country;
}

- (void)dealloc {
    
}
@end
