






#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN


typedef void(^SystolicCellIterationLearnDecimalRectified)(NSInteger buttonIndex);

@interface ManAlertView : UIView



+ (void)standMeanEvictionSheMenLeast:(NSString *_Nullable)title
                        message:(NSString *)message
                   safeQuerying:(NSArray<NSString *> *)safeQuerying
                     completion:(SystolicCellIterationLearnDecimalRectified _Nullable)completion;

+ (void)standMeanEvictionSheMenLeast:(NSString *_Nullable)title message:(NSString *)message completion:(SystolicCellIterationLearnDecimalRectified _Nullable)completion;


@end

NS_ASSUME_NONNULL_END
