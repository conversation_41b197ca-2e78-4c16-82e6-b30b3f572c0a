






#import "FaxEntitiesButton.h"
#import "SuitableTab.h"
#import "UIImage+ArtImage.h"

@interface FaxEntitiesButton ()


@property (nonatomic, strong) NSTimer *wetDryMildFadeTimer;

@property (nonatomic, assign) NSInteger sugarImplicitCenterRedirectNotMercury;

@property (nonatomic, copy) NSString *askSheetCallbackExcludedAnimation;

@end

@implementation FaxEntitiesButton

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self compactEndsSupportMayFound];
    }
    return self;
}

- (instancetype)initWithCoder:(NSCoder *)coder {
    self = [super initWithCoder:coder];
    if (self) {
        [self compactEndsSupportMayFound];
    }
    return self;
}


- (void)compactEndsSupportMayFound {
    
    self.functionDraftBagFitPurpleSubgroup = 60;
    self.askSheetCallbackExcludedAnimation = SuitableTab.needOldWideBig.trialProductInfoAdapterSettlingPrimaryCode;
    [self setTitle:self.askSheetCallbackExcludedAnimation forState:UIControlStateNormal];
    [self setBackgroundImage:[UIImage earMixHitTitleColor:SuitableTab.telephonyColor] forState:UIControlStateNormal];
    [self setBackgroundImage:[UIImage earMixHitTitleColor:[[UIColor lightGrayColor] colorWithAlphaComponent:0.5f]] forState:UIControlStateHighlighted];
    self.titleLabel.font = [UIFont systemFontOfSize:16];
    self.layer.cornerRadius = 2.f;
    self.layer.masksToBounds = YES;
    
    self.contentEdgeInsets = UIEdgeInsetsMake(0, 5, 0, 5);
    
    [self sizeToFit];
    
    
    [self addTarget:self action:@selector(exactUnlockClicked) forControlEvents:UIControlEventTouchUpInside];
}


- (void)exactUnlockClicked {
    [self sparseBufferTagsCostHint];
    if (self.askRedRealWaxAction) {
        self.askRedRealWaxAction();
    }
}


- (void)sparseBufferTagsCostHint {
    self.enabled = NO;
    self.sugarImplicitCenterRedirectNotMercury = self.functionDraftBagFitPurpleSubgroup;
    [self primaryDirectlyAdvancedInfinitePencilIgnore];
    
    
    self.wetDryMildFadeTimer = [NSTimer scheduledTimerWithTimeInterval:1.0
                                                                 target:self
                                                               selector:@selector(adjustedDetectionShoulderAssertionPayments:)
                                                               userInfo:nil
                                                                repeats:YES];
}


- (void)adjustedDetectionShoulderAssertionPayments:(NSTimer *)timer {
    self.sugarImplicitCenterRedirectNotMercury--;
    if (self.sugarImplicitCenterRedirectNotMercury <= 0) {
        [self stablePaperQuitComplexArranger];
    } else {
        [self primaryDirectlyAdvancedInfinitePencilIgnore];
    }
}


- (void)primaryDirectlyAdvancedInfinitePencilIgnore {
    NSString *title = [NSString stringWithFormat:@"%@(%ld)",SuitableTab.needOldWideBig.topAdjectiveMindfulRankedManualInfoCode, (long)self.sugarImplicitCenterRedirectNotMercury];
    [self setTitle:title forState:UIControlStateDisabled];
}


- (void)stablePaperQuitComplexArranger {
    [self.wetDryMildFadeTimer invalidate];
    self.wetDryMildFadeTimer = nil;
    self.enabled = YES;
    [self setTitle:self.askSheetCallbackExcludedAnimation forState:UIControlStateNormal];
}

- (void)dealloc {
    
    [self.wetDryMildFadeTimer invalidate];
    self.wetDryMildFadeTimer = nil;
}

@end
