






#import "ClimbedAskTightSquaresDivideCell.h"
#import "SuitableTab.h"
#import "Masonry.h"

@implementation ClimbedAskTightSquaresDivideCell

-(id)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if(self){
        
        self.backgroundColor = UIColor.whiteColor;
        self.contentView.backgroundColor = UIColor.whiteColor;
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        
        
        self.didSeekingView = [UIImageView new];
        self.didSeekingView.tintColor = [SuitableTab telephonyColor];
        self.didSeekingView.layer.cornerRadius = 15;
        [self.contentView addSubview:self.didSeekingView];
        [self.didSeekingView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(SuitableTab.dependingThe.mustFatMiles);
            make.centerY.mas_equalTo(self);
            make.width.height.mas_equalTo(SuitableTab.dependingThe.rankedInland);
        }];
        
        
        self.wonSplatName = [UILabel new];
        self.wonSplatName.font = [UIFont boldSystemFontOfSize:16];
        self.wonSplatName.textColor = UIColor.darkGrayColor;
        [self.contentView addSubview:self.wonSplatName];
        [self.wonSplatName mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.didSeekingView.mas_right).offset(SuitableTab.dependingThe.mustFatMiles);
            make.bottom.equalTo(self.contentView.mas_centerY);
        }];
        
        
        self.tenGesturesTime = [UILabel new];
        self.tenGesturesTime.font =  [UIFont systemFontOfSize:11];
        self.tenGesturesTime.textColor = UIColor.grayColor;
        [self.contentView addSubview:self.tenGesturesTime];
        [self.tenGesturesTime mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.contentView.mas_centerY).offset(SuitableTab.dependingThe.pintBigFace);
            make.left.equalTo(self.wonSplatName);
        }];
    }
    return self;
}

@end
