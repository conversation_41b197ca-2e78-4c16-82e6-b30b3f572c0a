






#import "SonToast.h"
#import "UpsideAskManager.h"


static UIColor *twoOldestVisualPenKeyColor = nil;
static UIColor *legacyWaxEggColor = nil;
static UIFont *cutHandStamp = nil;
static CGFloat digitRestDrainRadius = 6.0;
static UIEdgeInsets stampSocketWarpWrapperEngineAxes = {10, 16, 10, 16};

@interface SonToast()
@property (nonatomic, strong) UILabel *showPanLabel;
@property (nonatomic, strong) NSTimer *flightsTimer;
@property (nonatomic, assign) FoldTrainerGradeHomeScrolled position;
@end

@implementation SonToast


- (instancetype)initIdleMessage:(NSString *)message {
    self = [super initWithFrame:CGRectZero];
    if (self) {
        self.userInteractionEnabled = NO;
        self.backgroundColor = UIColor.clearColor;
        
        
        UIView *container = [UIView new];
        container.backgroundColor = twoOldestVisualPenKeyColor ?:
            [[UIColor blackColor] colorWithAlphaComponent:0.85];
        container.layer.cornerRadius = digitRestDrainRadius;
        container.clipsToBounds = YES;
        container.translatesAutoresizingMaskIntoConstraints = NO;
        [self addSubview:container];
        
        
        _showPanLabel = [UILabel new];
        _showPanLabel.text = message;
        _showPanLabel.textColor = legacyWaxEggColor ?: UIColor.whiteColor;
        _showPanLabel.font = cutHandStamp ?: [UIFont systemFontOfSize:14];
        _showPanLabel.textAlignment = NSTextAlignmentCenter;
        _showPanLabel.numberOfLines = 0;
        _showPanLabel.translatesAutoresizingMaskIntoConstraints = NO;
        [container addSubview:_showPanLabel];
        
        
        [NSLayoutConstraint activateConstraints:@[
            
            [container.leadingAnchor constraintEqualToAnchor:_showPanLabel.leadingAnchor
                                                   constant:-stampSocketWarpWrapperEngineAxes.left],
            [container.trailingAnchor constraintEqualToAnchor:_showPanLabel.trailingAnchor
                                                    constant:stampSocketWarpWrapperEngineAxes.right],
            [container.topAnchor constraintEqualToAnchor:_showPanLabel.topAnchor
                                              constant:-stampSocketWarpWrapperEngineAxes.top],
            [container.bottomAnchor constraintEqualToAnchor:_showPanLabel.bottomAnchor
                                                 constant:stampSocketWarpWrapperEngineAxes.bottom],
            
            
            [container.widthAnchor constraintLessThanOrEqualToConstant:
                [UIScreen mainScreen].bounds.size.width - 40]
        ]];
    }
    return self;
}


+ (void)show:(NSString *)message
    duration:(NSTimeInterval)duration
    position:(FoldTrainerGradeHomeScrolled)position
{
    
    dispatch_async(dispatch_get_main_queue(), ^{
        SonToast *vowel = [[SonToast alloc] initIdleMessage:message];
        vowel.position = position;
        [vowel discreteAlertSequenceThirdCircular];
        [vowel spatialEraserSawVitalItalic:duration];
    });
}

- (void)spatialEraserSawVitalItalic:(NSTimeInterval)duration {
    UIWindow *window = [UpsideAskManager.shared eulerAnyBankWindow];
    [window addSubview:self];
    
    
    self.translatesAutoresizingMaskIntoConstraints = NO;
    [NSLayoutConstraint activateConstraints:@[
        [self.leadingAnchor constraintEqualToAnchor:window.leadingAnchor],
        [self.trailingAnchor constraintEqualToAnchor:window.trailingAnchor],
        [self.topAnchor constraintEqualToAnchor:window.topAnchor],
        [self.bottomAnchor constraintEqualToAnchor:window.bottomAnchor]
    ]];
    
    
    [self bitmapMiterNameLongTipAnimation];
    
    
    if (duration > 0) {
        __weak typeof(self) weakSelf = self;
        self.flightsTimer = [NSTimer scheduledTimerWithTimeInterval:duration repeats:YES block:^(NSTimer * _Nonnull timer) {
            [weakSelf dismiss];
        }];
    }
}

- (void)dismiss {
    [self.flightsTimer invalidate];
    [self danishLoseJumpAwayTypeSubmitMouse:^{
        [self removeFromSuperview];
    }];
}


- (void)bitmapMiterNameLongTipAnimation {
    CGAffineTransform transform;
    switch (self.position) {
        case BurstTitleStylizeCutInterval:
            transform = CGAffineTransformMakeTranslation(0, -100);
            break;
        case BrotherReclaimAlphaSenseExtendsRecorded:
            transform = CGAffineTransformMakeTranslation(0, 100);
            break;
        default:
            transform = CGAffineTransformMakeScale(0.8, 0.8);
            break;
    }
    
    self.alpha = 0;
    self.showPanLabel.superview.transform = transform;
    
    [UIView animateWithDuration:0.3
                          delay:0
         usingSpringWithDamping:0.7
          initialSpringVelocity:0.1
                        options:UIViewAnimationOptionCurveEaseOut
                     animations:^{
        self.alpha = 1;
        self.showPanLabel.superview.transform = CGAffineTransformIdentity;
    } completion:nil];
}

- (void)danishLoseJumpAwayTypeSubmitMouse:(void(^)(void))completion {
    CGAffineTransform transform;
    switch (self.position) {
        case BurstTitleStylizeCutInterval:
            transform = CGAffineTransformMakeTranslation(0, -self.showPanLabel.superview.frame.size.height - 50);
            break;
        case BrotherReclaimAlphaSenseExtendsRecorded:
            transform = CGAffineTransformMakeTranslation(0, self.showPanLabel.superview.frame.size.height + 50);
            break;
        default:
            transform = CGAffineTransformMakeScale(0.8, 0.8);
            break;
    }
    
    [UIView animateWithDuration:0.25
                     animations:^{
        self.alpha = 0;
        self.showPanLabel.superview.transform = transform;
    } completion:^(BOOL finished) {
        if (completion) completion();
    }];
}


- (void)discreteAlertSequenceThirdCircular {
    UIView *container = self.showPanLabel.superview;
    
    
    switch (self.position) {
        case BurstTitleStylizeCutInterval: {
            [container.topAnchor constraintEqualToAnchor:self.safeAreaLayoutGuide.topAnchor
                                               constant:30].active = YES;
            break;
        }
        case OneAirSubDiscreteOptCenter: {
            [container.centerYAnchor constraintEqualToAnchor:self.centerYAnchor].active = YES;
            break;
        }
        case BrotherReclaimAlphaSenseExtendsRecorded: {
            [container.bottomAnchor constraintEqualToAnchor:self.safeAreaLayoutGuide.bottomAnchor
                                                  constant:-30].active = YES;
            break;
        }
    }
    
    
    [container.centerXAnchor constraintEqualToAnchor:self.centerXAnchor].active = YES;
}


+ (void)rectangleCalculateSourceCommitUnpluggedBoldfaceColor:(UIColor *)color {
    twoOldestVisualPenKeyColor = color;
}

+ (void)meanShortTeethColor:(UIColor *)color {
    legacyWaxEggColor = color;
}

+ (void)inuitMilePrior:(UIFont *)font {
    cutHandStamp = font;
}

+ (void)floatBoyfriendReadablePopLawRadius:(CGFloat)radius {
    digitRestDrainRadius = radius;
}


+ (void)keySong:(NSString *)message {
    [self show:message duration:2.0 position:BurstTitleStylizeCutInterval];
}

+ (void)lastCenter:(NSString *)message {
    [self show:message duration:2.0 position:OneAirSubDiscreteOptCenter];
}

+ (void)funTiedZip:(NSString *)message {
    [self show:message duration:2.0 position:BrotherReclaimAlphaSenseExtendsRecorded];
}

- (void)dealloc {
    
}

@end
