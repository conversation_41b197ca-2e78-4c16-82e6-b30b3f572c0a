






#import "DeleteUnitView.h"
#import "UpsideAskManager.h"
#import "SuitableTab.h"
#import "Masonry.h"

@interface DeleteUnitView ()

@property (nonatomic, strong) UIView *mustHuePriorBackgroundView;
@property (nonatomic, strong) UIActivityIndicatorView *pinEffectivePresentMajorMetrics;
@property (nonatomic, strong) UILabel *itsLiftLabel;
@end

@implementation DeleteUnitView


static DeleteUnitView *sharedLoadingView = nil;



- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self bloodView];
    }
    return self;
}
- (instancetype)initWithCoder:(NSCoder *)coder {
    self = [super initWithCoder:coder];
    if (self) {
        [self bloodView];
    }
    return self;
}
- (void)bloodView {
    
    
    self.mustHuePriorBackgroundView = [UIView new];
    self.mustHuePriorBackgroundView.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.5];
    self.mustHuePriorBackgroundView.layer.cornerRadius = 2.0;
    self.mustHuePriorBackgroundView.clipsToBounds = YES;
    [self addSubview:self.mustHuePriorBackgroundView];
    
    
    self.pinEffectivePresentMajorMetrics = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleLarge];
    self.pinEffectivePresentMajorMetrics.color = SuitableTab.telephonyColor;
    [self.mustHuePriorBackgroundView addSubview:self.pinEffectivePresentMajorMetrics];
    
    
    self.itsLiftLabel = [[UILabel alloc] init];
    self.itsLiftLabel.text = SuitableTab.needOldWideBig.stepchildFor;
    self.itsLiftLabel.textColor = [UIColor whiteColor];
    self.itsLiftLabel.font = [UIFont systemFontOfSize:14];
    self.itsLiftLabel.numberOfLines = 0;
    self.itsLiftLabel.textAlignment = NSTextAlignmentCenter;
    [self.mustHuePriorBackgroundView addSubview:self.itsLiftLabel];
    
    
    [self.mustHuePriorBackgroundView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(SuitableTab.dependingThe.wireDancePolo, SuitableTab.dependingThe.wireDancePolo));
        make.center.equalTo(self);
    }];
    
    [self.pinEffectivePresentMajorMetrics mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(SuitableTab.dependingThe.areaLawEuler);
        make.centerX.equalTo(self.mustHuePriorBackgroundView.mas_centerX);
    }];
    
    [self.itsLiftLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.pinEffectivePresentMajorMetrics.mas_bottom).offset(SuitableTab.dependingThe.areaLawEuler);
        make.centerX.equalTo(self.mustHuePriorBackgroundView.mas_centerX);
        make.left.equalTo(self.mustHuePriorBackgroundView.mas_left).offset(SuitableTab.dependingThe.rowExercise);
        make.right.equalTo(self.mustHuePriorBackgroundView.mas_right).offset(-SuitableTab.dependingThe.rowExercise);
    }];
    
    
    self.hidden = YES;
}



- (void)startAnimating {
    self.hidden = NO;
    [self.pinEffectivePresentMajorMetrics startAnimating];
}

- (void)stopAnimating {
    [self.pinEffectivePresentMajorMetrics stopAnimating];
    self.hidden = YES;
}

- (void)exceedsWasText:(NSString *)text {
    self.itsLiftLabel.text = text;
    
    
    CGFloat goalWidth = [text boundingRectWithSize:CGSizeMake(CGFLOAT_MAX, CGFLOAT_MAX)
                                          options:NSStringDrawingUsesLineFragmentOrigin
                                       attributes:@{NSFontAttributeName: self.itsLiftLabel.font}
                                          context:nil].size.width;
    UIWindow *window = [[UpsideAskManager shared] eulerAnyBankWindow];
    CGFloat factKinWidth = MIN(MAX(120, goalWidth + 2 * 8), window.bounds.size.width);
    [self.mustHuePriorBackgroundView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(factKinWidth);
    }];
    
    [self layoutIfNeeded];
}


+ (void)weekendUplinkWindow {
    [self pointersRunMostComparedBackupParentText:SuitableTab.needOldWideBig.stepchildFor];
}

+ (void)pointersRunMostComparedBackupParentText:(NSString *)text {
    dispatch_async(dispatch_get_main_queue(), ^{
        UIWindow *window = [[UpsideAskManager shared] eulerAnyBankWindow];
        
        if (!sharedLoadingView) {
            CGSize size = UIScreen.mainScreen.bounds.size;
            sharedLoadingView = [[DeleteUnitView alloc] initWithFrame:CGRectMake(0, 0, size.width, size.height)];
            sharedLoadingView.center = window.center;
        }
        if (!sharedLoadingView.superview) {
            [window addSubview:sharedLoadingView];
        }
        [sharedLoadingView exceedsWasText:text];
        [sharedLoadingView startAnimating];
    });
}

+ (void)useProvisionBoyfriendFollowBeaconWindow {
    dispatch_async(dispatch_get_main_queue(), ^{
        [sharedLoadingView stopAnimating];
        [sharedLoadingView removeFromSuperview];
        sharedLoadingView = nil;
    });
}


+ (DeleteUnitView *)borderLexiconView:(UIView *)view {
    return [self borderLexiconView:view withText:SuitableTab.needOldWideBig.stepchildFor];
}

+ (DeleteUnitView *)borderLexiconView:(UIView *)view withText:(NSString *)text {
    __block DeleteUnitView *flipBitView = nil;
    dispatch_async(dispatch_get_main_queue(), ^{
        
        flipBitView = [[DeleteUnitView alloc] initWithFrame:CGRectMake(0, 0, view.frame.size.width, view.frame.size.height)];
        flipBitView.center = CGPointMake(CGRectGetMidX(view.bounds), CGRectGetMidY(view.bounds));
        [flipBitView exceedsWasText:text];
        [flipBitView startAnimating];
        [view addSubview:flipBitView];
    });
    return flipBitView;
}

+ (void)blusteryWaySemicolonFriendsPreventsView:(UIView *)view {
    dispatch_async(dispatch_get_main_queue(), ^{
        
        for (UIView *subview in view.subviews) {
            if ([subview isKindOfClass:[DeleteUnitView class]]) {
                [(DeleteUnitView *)subview stopAnimating];
                [subview removeFromSuperview];
            }
        }
    });
}

@end
