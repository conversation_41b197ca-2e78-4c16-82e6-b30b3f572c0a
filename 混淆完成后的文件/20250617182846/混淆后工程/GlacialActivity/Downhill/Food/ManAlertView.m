






#import "ManAlertView.h"
#import "SuitableTab.h"
#import "UpsideAskManager.h"
#import "Masonry.h"

#define ourCost(obj) __weak typeof(obj) weak##obj = obj;
#define chestAtom(obj) __strong typeof(obj) obj = weak##obj;

@interface ManAlertView()

@property (nonatomic, strong) UIView *weekPostNapSheView;
@property (nonatomic, copy) SystolicCellIterationLearnDecimalRectified completion;
@property (nonatomic, strong) UIStackView *pendingHumanView;

@end

@implementation ManAlertView

- (void)dealloc {
    
}

- (instancetype)initWithFrame:(CGRect)frame
                          title:(NSString *)title
                        message:(NSString *)message
                   safeQuerying:(NSArray<NSString *> *)safeQuerying
                     completion:(SystolicCellIterationLearnDecimalRectified)completion {
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.5];
        self.completion = completion;
        
        
        self.weekPostNapSheView = [[UIView alloc] init];
        self.weekPostNapSheView.backgroundColor = [SuitableTab telephonyColor];
        self.weekPostNapSheView.layer.cornerRadius = 8.0;
        self.weekPostNapSheView.clipsToBounds = YES;
        self.weekPostNapSheView.translatesAutoresizingMaskIntoConstraints = NO;
        [self addSubview:self.weekPostNapSheView];
        
        
        [NSLayoutConstraint activateConstraints:@[
            [self.weekPostNapSheView.centerXAnchor constraintEqualToAnchor:self.centerXAnchor],
            [self.weekPostNapSheView.centerYAnchor constraintEqualToAnchor:self.centerYAnchor],
            [self.weekPostNapSheView.widthAnchor constraintEqualToConstant:270]
        ]];
        
        
        UIView *previousView = nil;
        CGFloat verticalPadding = 20;
        
        
        if (title.length > 0) {
            UILabel *titleLabel = [[UILabel alloc] init];
            titleLabel.text = title;
            titleLabel.textColor = UIColor.whiteColor;
            titleLabel.font = [UIFont boldSystemFontOfSize:18];
            titleLabel.textAlignment = NSTextAlignmentCenter;
            titleLabel.numberOfLines = 0;
            titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
            [self.weekPostNapSheView addSubview:titleLabel];
            
            [NSLayoutConstraint activateConstraints:@[
                [titleLabel.topAnchor constraintEqualToAnchor:self.weekPostNapSheView.topAnchor constant:verticalPadding],
                [titleLabel.leadingAnchor constraintEqualToAnchor:self.weekPostNapSheView.leadingAnchor constant:16],
                [titleLabel.trailingAnchor constraintEqualToAnchor:self.weekPostNapSheView.trailingAnchor constant:-16]
            ]];
            
            previousView = titleLabel;
        }
        
        
        if (message.length > 0) {
            UILabel *showPanLabel = [[UILabel alloc] init];
            showPanLabel.text = message;
            showPanLabel.textColor = UIColor.whiteColor;
            showPanLabel.font = [UIFont systemFontOfSize:15];
            showPanLabel.textAlignment = NSTextAlignmentCenter;
            showPanLabel.numberOfLines = 0;
            showPanLabel.translatesAutoresizingMaskIntoConstraints = NO;
            [self.weekPostNapSheView addSubview:showPanLabel];
            
            NSLayoutYAxisAnchor *topAnchor = previousView ? previousView.bottomAnchor : self.weekPostNapSheView.topAnchor;
            CGFloat topPadding = previousView ? 10 : verticalPadding;
            [NSLayoutConstraint activateConstraints:@[
                [showPanLabel.topAnchor constraintEqualToAnchor:topAnchor constant:topPadding],
                [showPanLabel.leadingAnchor constraintEqualToAnchor:self.weekPostNapSheView.leadingAnchor constant:16],
                [showPanLabel.trailingAnchor constraintEqualToAnchor:self.weekPostNapSheView.trailingAnchor constant:-16]
            ]];
            previousView = showPanLabel;
        }
        
        
        self.pendingHumanView = [[UIStackView alloc] init];
        self.pendingHumanView.axis = UILayoutConstraintAxisVertical;
        self.pendingHumanView.spacing = 1;  
        self.pendingHumanView.distribution = UIStackViewDistributionFillEqually;
        self.pendingHumanView.translatesAutoresizingMaskIntoConstraints = NO;
        [self.weekPostNapSheView addSubview:self.pendingHumanView];
        
        
        NSLayoutYAxisAnchor *buttonsTopAnchor = previousView ? previousView.bottomAnchor : self.weekPostNapSheView.topAnchor;
        CGFloat buttonsTopPadding = previousView ? verticalPadding : verticalPadding;
        
        [NSLayoutConstraint activateConstraints:@[
            [self.pendingHumanView.topAnchor constraintEqualToAnchor:buttonsTopAnchor constant:buttonsTopPadding],
            [self.pendingHumanView.leadingAnchor constraintEqualToAnchor:self.weekPostNapSheView.leadingAnchor],
            [self.pendingHumanView.trailingAnchor constraintEqualToAnchor:self.weekPostNapSheView.trailingAnchor],
            [self.pendingHumanView.bottomAnchor constraintEqualToAnchor:self.weekPostNapSheView.bottomAnchor]
        ]];
        
        
       
       if (safeQuerying.count == 2) {
           
           self.pendingHumanView = [[UIStackView alloc] init];
           self.pendingHumanView.axis = UILayoutConstraintAxisHorizontal;
           self.pendingHumanView.distribution = UIStackViewDistributionFillEqually;
           self.pendingHumanView.spacing = 1;  
           self.pendingHumanView.translatesAutoresizingMaskIntoConstraints = NO;
           [self.weekPostNapSheView addSubview:self.pendingHumanView];
           
           NSLayoutYAxisAnchor *buttonsTopAnchor = previousView ? previousView.bottomAnchor : self.weekPostNapSheView.topAnchor;
           [NSLayoutConstraint activateConstraints:@[
               [self.pendingHumanView.topAnchor constraintEqualToAnchor:buttonsTopAnchor constant:verticalPadding],
               [self.pendingHumanView.leadingAnchor constraintEqualToAnchor:self.weekPostNapSheView.leadingAnchor],
               [self.pendingHumanView.trailingAnchor constraintEqualToAnchor:self.weekPostNapSheView.trailingAnchor],
               [self.pendingHumanView.bottomAnchor constraintEqualToAnchor:self.weekPostNapSheView.bottomAnchor]
           ]];
           
           
           for (NSInteger i = 0; i < safeQuerying.count; i++) {
               NSString *btnTitle = safeQuerying[i];
               UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
               [button setTitle:btnTitle forState:UIControlStateNormal];
               button.titleLabel.font = [UIFont systemFontOfSize:17];
               [button setTitleColor:[SuitableTab telephonyColor] forState:UIControlStateNormal];
               [button setTitleColor:UIColor.lightGrayColor forState:UIControlStateHighlighted];
               button.backgroundColor = [UIColor whiteColor];
               button.tag = i;
               [button addTarget:self action:@selector(optViewPager:) forControlEvents:UIControlEventTouchUpInside];
               button.translatesAutoresizingMaskIntoConstraints = NO;
               [button.heightAnchor constraintEqualToConstant:40].active = YES;
               [self.pendingHumanView addArrangedSubview:button];
           }
       } else {
           
           self.pendingHumanView = [[UIStackView alloc] init];
           self.pendingHumanView.axis = UILayoutConstraintAxisVertical;
           self.pendingHumanView.spacing = 1;
           self.pendingHumanView.distribution = UIStackViewDistributionFillEqually;
           self.pendingHumanView.translatesAutoresizingMaskIntoConstraints = NO;
           [self.weekPostNapSheView addSubview:self.pendingHumanView];
           
           NSLayoutYAxisAnchor *buttonsTopAnchor = previousView ? previousView.bottomAnchor : self.weekPostNapSheView.topAnchor;
           [NSLayoutConstraint activateConstraints:@[
               [self.pendingHumanView.topAnchor constraintEqualToAnchor:buttonsTopAnchor constant:verticalPadding],
               [self.pendingHumanView.leadingAnchor constraintEqualToAnchor:self.weekPostNapSheView.leadingAnchor],
               [self.pendingHumanView.trailingAnchor constraintEqualToAnchor:self.weekPostNapSheView.trailingAnchor],
               [self.pendingHumanView.bottomAnchor constraintEqualToAnchor:self.weekPostNapSheView.bottomAnchor]
           ]];
           
           for (NSInteger i = 0; i < safeQuerying.count; i++) {
               NSString *btnTitle = safeQuerying[i];
               UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
               [button setTitle:btnTitle forState:UIControlStateNormal];
               button.titleLabel.font = [UIFont systemFontOfSize:17];
               [button setTitleColor:[SuitableTab telephonyColor] forState:UIControlStateNormal];
               [button setTitleColor:UIColor.lightGrayColor forState:UIControlStateHighlighted];
               button.backgroundColor = [UIColor whiteColor];
               button.tag = i;
               [button addTarget:self action:@selector(optViewPager:) forControlEvents:UIControlEventTouchUpInside];
               button.translatesAutoresizingMaskIntoConstraints = NO;
               [button.heightAnchor constraintEqualToConstant:40].active = YES;
               [self.pendingHumanView addArrangedSubview:button];
           }
       }
    }
    return self;
}

- (void)optViewPager:(UIButton *)sender {
    if (self.completion) {
        self.completion(sender.tag);
    }
    
    
    [UIView animateWithDuration:0.25 animations:^{
        self.alpha = 0;
    } completion:^(BOOL finished) {
        [UpsideAskManager.shared nowFlashBitsWindow];
    }];
}

+ (void)standMeanEvictionSheMenLeast:(NSString *)title
                        message:(NSString *)message
                   safeQuerying:(NSArray<NSString *> *)safeQuerying
                     completion:(SystolicCellIterationLearnDecimalRectified)completion {
    
    ManAlertView *music = [[ManAlertView alloc] initWithFrame:[UIScreen mainScreen].bounds
                                                 title:title
                                               message:message
                                          safeQuerying:safeQuerying
                                            completion:completion];
    
    
    [UpsideAskManager.shared agentReactorFitAreYouAllocateView:music];
    
    
    music.alpha = 0.0;
    [UIView animateWithDuration:0.25 animations:^{
        music.alpha = 1.0;
    }];
}

+ (void)standMeanEvictionSheMenLeast:(NSString *)title message:(NSString *)message completion:(SystolicCellIterationLearnDecimalRectified)completion {
    [self standMeanEvictionSheMenLeast:title message:message safeQuerying:@[SuitableTab.needOldWideBig.highRow] completion:completion];
}

@end
