






#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface NSObject (LexiconListModifierCurvePrime)

//- (id)bleedMinderEraEquallySortingBut:(SEL)selector withObject:(id _Nullable)object,...NS_REQUIRES_NIL_TERMINATION;
- (id)bleedMinderEraEquallySortingBut:(SEL)aSelector;

- (id)bleedMinderEraEquallySortingBut:(SEL)aSelector
                withObject:(id)object1;

- (id)bleedMinderEraEquallySortingBut:(SEL)aSelector
                withObject:(id)object1
                withObject:(id)object2;

- (id)bleedMinderEraEquallySortingBut:(SEL)aSelector
                withObject:(id)object1
                withObject:(id)object2
                withObject:(id)object3;

- (id)bleedMinderEraEquallySortingBut:(SEL)aSelector
                withObject:(id)object1
                withObject:(id)object2
                withObject:(id)object3
                withObject:(id)object4;

- (id)bleedMinderEraEquallySortingBut:(SEL)aSelector
                withObject:(id)object1
                withObject:(id)object2
                withObject:(id)object3
                withObject:(id)object4
                withObject:(id)object5;

- (id)bleedMinderEraEquallySortingBut:(SEL)aSelector
                withObject:(id)object1
                withObject:(id)object2
                withObject:(id)object3
                withObject:(id)object4
                withObject:(id)object5
                withObject:(id)object6;
@end

NS_ASSUME_NONNULL_END
