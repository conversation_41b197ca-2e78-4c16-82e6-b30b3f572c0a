






#import "NSURL+RuleHueKin.h"
#import "NSString+KurdishBond.h"

@implementation NSURL (RuleHueKin)

- (NSDictionary *)drivenMaxBed {
    
    NSArray * array = [[self query] componentsSeparatedByString:@"&"];

    NSMutableDictionary * ParaDict = [NSMutableDictionary new];

    for(int i = 0 ; i < [array count]; i++){

        NSArray * keyAndValue = [array[i] componentsSeparatedByString:@"="];

        if([keyAndValue count] == 2 && keyAndValue[0] && keyAndValue[1]){

            [ParaDict setObject:[keyAndValue[1] bandwidthOperationEmailFirstDenseSkipped] forKey:keyAndValue[0]];

        }
    }
    return ParaDict;
}

@end
