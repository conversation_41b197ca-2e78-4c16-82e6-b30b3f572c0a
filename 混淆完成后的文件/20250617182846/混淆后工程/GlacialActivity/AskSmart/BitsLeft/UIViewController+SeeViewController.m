






#import "UIViewController+SeeViewController.h"

@implementation UIViewController (SeeViewController)

- (void)chunkMainReversingPassBannerViewController:(UIViewController *)vc {
    if (vc) {
        [vc willMoveToParentViewController:self];
        [self addChildViewController:vc];
        [self.view addSubview:vc.view];
        
        
        vc.view.translatesAutoresizingMaskIntoConstraints = NO;

        UIView *superview = vc.view.superview;
        [NSLayoutConstraint activateConstraints:@[
            [vc.view.topAnchor constraintEqualToAnchor:superview.topAnchor],
            [vc.view.leadingAnchor constraintEqualToAnchor:superview.leadingAnchor],
            [vc.view.bottomAnchor constraintEqualToAnchor:superview.bottomAnchor],
            [vc.view.trailingAnchor constraintEqualToAnchor:superview.trailingAnchor]
        ]];
        
        [vc didMoveToParentViewController:self];
    }
}

- (void)dialogCaptureCloudyPackCupEightViewController {
    if (self && self.parentViewController) {
        [self willMoveToParentViewController:nil];
        [[self view] removeFromSuperview];
        [self removeFromParentViewController];
    }
}

- (void)prepSixteenDenyViabilityBadGrandsonBag {
    for (UIViewController *vc in self.childViewControllers) {
        [vc dialogCaptureCloudyPackCupEightViewController];
    }
}

@end
