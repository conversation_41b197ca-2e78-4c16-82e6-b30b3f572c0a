






#import "UIImage+ArtImage.h"
#import "NSData+CurlHas.h"
#import "NSString+NetUighur.h"
#import "UsageEntitledFinnishDynamicSort.h"

@implementation UIImage (ArtImage)

+ (UIImage *)earMixHitTitleColor:(UIColor *)color {
    
    CGRect rect=CGRectMake(0.0f,0.0f, 1.0f,1.0f);
    UIGraphicsBeginImageContext(rect.size);
    CGContextRef context = UIGraphicsGetCurrentContext();
    CGContextSetFillColorWithColor(context, [color CGColor]);
    CGContextFillRect(context, rect);
    UIImage *pinImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return pinImage;
}

+ (UIImage *)faeroeseSynthesisVariablesBeenTransformName:(NSString *)imageName {
    
    if (!imageName) {
        return nil;
    }
    
    UIImage *image = nil;
    
    NSString *eyePath= [[UsageEntitledFinnishDynamicSort youFooter] stringByAppendingPathComponent:imageName];
    
    if (eyePath.activatedRectifiedGuideEditorsScale) {
        
        image = [UIImage imageWithContentsOfFile:eyePath];
    }
    
    if (!image) {
        
        NSData *bracketedData = [NSData dataWithContentsOfFile:eyePath];
       
       
        image = [bracketedData advancesPrintedOverrideMaterialAppendingPenCollation];
    }
    
    return image;
}

- (UIImage *)dragFocalHelpersContainSelfColor:(UIColor *)tintColor {
   
    if (!tintColor) return self;
    
    
    UIGraphicsImageRendererFormat *format = [UIGraphicsImageRendererFormat defaultFormat];
    format.scale = self.scale;
    format.opaque = NO;
    
    UIGraphicsImageRenderer *renderer = [[UIGraphicsImageRenderer alloc] initWithSize:self.size format:format];
    
    return [renderer imageWithActions:^(UIGraphicsImageRendererContext * _Nonnull context) {
        
        [tintColor setFill];
        
        
        CGRect bounds = CGRectMake(0, 0, self.size.width, self.size.height);
        [self drawInRect:bounds];
        
        
        CGContextSetBlendMode(context.CGContext, kCGBlendModeSourceIn);
        CGContextFillRect(context.CGContext, bounds);
    }];
}
@end
