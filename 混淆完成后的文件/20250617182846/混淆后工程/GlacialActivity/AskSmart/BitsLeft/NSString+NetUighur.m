






#import "NSString+NetUighur.h"

@implementation NSObject (NetUighur)

- (BOOL)diskMilesBag {
    
    
    if (self == nil || (id)self == [NSNull null]) {
        return YES;
    }
    
    
    if ([self isKindOfClass:[NSString class]]) {
        NSString *str = (NSString *)self;
        if (str.length == 0) {
            return YES;
        }
        
        NSString *trimmed = [str stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
        return (trimmed.length == 0);
    }
    
    







    
    
    
    return YES;
}

- (BOOL)activatedRectifiedGuideEditorsScale {
    return ![self diskMilesBag];
}

//- (BOOL)activatedRectifiedGuideEditorsScale {

//}

//- (BOOL)diskMilesBag {




//}

@end
