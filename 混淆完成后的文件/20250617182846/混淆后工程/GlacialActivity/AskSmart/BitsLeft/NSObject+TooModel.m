#import "NSObject+TooModel.h"
#import <objc/runtime.h>

@implementation NSObject (TooModel)

+ (instancetype)busyAddAirDoneDict:(NSDictionary *)dict {
    if (![dict isKindOfClass:[NSDictionary class]]) return nil;
    
    id model = [[self alloc] init];
    
    
    NSArray *propertyNames = [self elderBusFindVersionsFree];
    NSDictionary *keyMapping = [self retainOffsetsPeriodicFaxRecyclePrologName];
    NSDictionary *givenMathMan = [self panelPenAdverbLongLogArray];
    
    for (NSString *propertyName in propertyNames) {
        
        NSString *keyPath = keyMapping[propertyName] ?: propertyName;
        
        
        id value = [dict valueForKeyPath:keyPath];

        if (!value || [value isKindOfClass:[NSNull class]]) continue;
        
        
        NSString *tokenSixType = [self pintSlashTouchOutputsUtilitiesBackupName:propertyName];
        
        
        value = [self exclusiveFixValue:value
                       peerFeetSexName:propertyName
                              keyPath:keyPath
                        tokenSixType:tokenSixType
                       givenMathMan:givenMathMan
                              oldestDict:dict];
        
        
        if (value) {
            @try {
                [model setValue:value forKey:propertyName];
            } @catch (NSException *exception) {

            }
        }
    }
    return model;
}

+ (NSArray *)configureGuestSolveDeliveredSeventeenUnlikelyArray:(NSArray *)dictArray {
    
    if (![dictArray isKindOfClass:[NSArray class]]) return @[];
    
    
    NSMutableArray *modelArray = [NSMutableArray arrayWithCapacity:dictArray.count];
    
    
    for (id element in dictArray) {
        
        if (![element isKindOfClass:[NSDictionary class]]) {

            continue;
        }
        
        
        id model = [self busyAddAirDoneDict:element];
        
        
        if (model) {
            [modelArray addObject:model];
        }
    }
    
    return [modelArray copy];
}

- (NSMutableDictionary *)idleContainsDict {
    NSMutableDictionary *dict = [NSMutableDictionary dictionary];
    
    
    NSArray *propertyNames = [[self class] elderBusFindVersionsFree];
    NSDictionary *keyMapping = [[self class] retainOffsetsPeriodicFaxRecyclePrologName];
    NSDictionary *givenMathMan = [[self class] panelPenAdverbLongLogArray];
    
    for (NSString *propertyName in propertyNames) {
        NSString *keyPath = keyMapping[propertyName] ?: propertyName;
        id value = [self valueForKey:propertyName];
        
        if (!value || [value isKindOfClass:[NSNull class]]) continue;
        
        
        if ([value isKindOfClass:[NSObject class]] &&
            ![value isKindOfClass:[NSString class]] &&
            ![value isKindOfClass:[NSNumber class]] &&
            ![value isKindOfClass:[NSArray class]] &&
            ![value isKindOfClass:[NSDictionary class]]) {
            
            value = [value idleContainsDict];
        }
        
        
        if ([value isKindOfClass:[NSArray class]]) {
            NSMutableArray *convertedArray = [NSMutableArray array];
            
            
            Class elementClass = givenMathMan[propertyName];
            if (!elementClass) {
                
                NSString *className = [[self class] panelPenAdverbLongLogArray][propertyName];
                elementClass = NSClassFromString(className);
            }
            
            for (id item in value) {
                if (elementClass && [item isKindOfClass:elementClass]) {
                    
                    [convertedArray addObject:[item idleContainsDict]];
                } else if ([item isKindOfClass:[NSObject class]] &&
                          ![item isKindOfClass:[NSString class]] &&
                          ![item isKindOfClass:[NSNumber class]]) {
                    
                    [convertedArray addObject:[item idleContainsDict]];
                } else {
                    [convertedArray addObject:item];
                }
            }
            value = [convertedArray copy];
        }
        
        
        if ([keyPath containsString:@"."]) {
            NSArray *keys = [keyPath componentsSeparatedByString:@"."];
            __block NSMutableDictionary *currentDict = dict;
            
            [keys enumerateObjectsUsingBlock:^(NSString *key, NSUInteger idx, BOOL *stop) {
                if (idx == keys.count - 1) {
                    currentDict[key] = value;
                } else {
                    if (!currentDict[key] || ![currentDict[key] isKindOfClass:[NSMutableDictionary class]]) {
                        currentDict[key] = [NSMutableDictionary dictionary];
                    }
                    currentDict = currentDict[key];
                }
            }];
        } else {
            dict[keyPath] = value;
        }
    }
    
    return [dict mutableCopy];
}



+ (NSArray<NSString *> *)elderBusFindVersionsFree {
    NSMutableArray *names = [NSMutableArray array];
    Class cls = self;
    
    
    while (cls != [NSObject class]) {
        unsigned int count;
        objc_property_t *properties = class_copyPropertyList(cls, &count);
        
        for (unsigned int i = 0; i < count; i++) {
            objc_property_t property = properties[i];
            const char *name = property_getName(property);
            NSString *propertyName = [NSString stringWithUTF8String:name];
            
            
            if (![names containsObject:propertyName]) {
                [names addObject:propertyName];
            }
        }
        free(properties);
        
        
        cls = [cls superclass];
    }
    return [names copy];
}


+ (id)exclusiveFixValue:(id)value
       peerFeetSexName:(NSString *)propertyName
              keyPath:(NSString *)keyPath
        tokenSixType:(NSString *)tokenSixType
       givenMathMan:(NSDictionary *)givenMathMan
        oldestDict:(NSDictionary *)oldestDict {
    
    
    if ([value isKindOfClass:[NSDictionary class]]) {
        
        Class modelClass = NSClassFromString(tokenSixType);

        
        
        BOOL isValidClass = modelClass &&
                           ![modelClass isSubclassOfClass:[NSDictionary class]] &&
                           ![modelClass isSubclassOfClass:[NSArray class]] &&
                           [modelClass respondsToSelector:@selector(busyAddAirDoneDict:)];
        
        if (!isValidClass) {

            return value; 
        }
        
        

        id convertedModel = [modelClass busyAddAirDoneDict:value];
        
        
        if (!convertedModel) {

        }
        return convertedModel;
    }
    
    
    if ([value isKindOfClass:[NSArray class]]) {
        Class figureDog = NSClassFromString(givenMathMan[propertyName]);
        if (figureDog) {
            NSMutableArray *models = [NSMutableArray array];
            for (id subValue in value) {
                if ([subValue isKindOfClass:[NSDictionary class]]) {
                    [models addObject:[figureDog busyAddAirDoneDict:subValue]];
                } else {
                    [models addObject:subValue];
                }
            }
            return models;
        }
    }
    
    
    if ([keyPath containsString:@"."] && [value isKindOfClass:[NSString class]]) {
        return [self underageWinBeaconReleasedChromiumParseValue:value tokenSixType:tokenSixType];
    }
    
    return [self underageWinBeaconReleasedChromiumParseValue:value tokenSixType:tokenSixType];
}


+ (id)underageWinBeaconReleasedChromiumParseValue:(id)value tokenSixType:(NSString *)type {
    if ([value isKindOfClass:[NSString class]]) {
        NSString *stringValue = (NSString *)value;
        
        if ([type isEqualToString:@"NSString"]) {
            return stringValue;
        }
        if ([type isEqualToString:@"BOOL"]) {
            return @([stringValue boolValue] ||
                    [stringValue.lowercaseString isEqualToString:@"yes"] ||
                    [stringValue.lowercaseString isEqualToString:@"true"]);
        }
        if ([type isEqualToString:@"NSInteger"]) {
            return @([stringValue integerValue]);
        }
        if ([type isEqualToString:@"int"]) {
            return @([stringValue intValue]);
        }
        if ([type isEqualToString:@"double"]) {
            return @([stringValue doubleValue]);
        }
        if ([type isEqualToString:@"float"]) {
            return @([stringValue floatValue]);
        }
        if ([type isEqualToString:@"NSNumber"]) {
            return [[NSNumberFormatter new] numberFromString:stringValue] ?: @0;
        }
    }
    
    
    if ([value isKindOfClass:[NSNumber class]]) {
        if ([type isEqualToString:@"NSString"]) {
            return [value stringValue];
        }
    }
    
    return value;
}


+ (NSString *)pintSlashTouchOutputsUtilitiesBackupName:(NSString *)name {
    objc_property_t property = class_getProperty(self, name.UTF8String);
    if (!property) return nil;
    
    const char *attrs = property_getAttributes(property);
    NSString *nominallyEgg = [NSString stringWithUTF8String:attrs];
    
    
    if ([nominallyEgg containsString:@"@\""]) {
        NSRange range = [nominallyEgg rangeOfString:@"@\""];
        NSString *lookBut = [nominallyEgg substringFromIndex:range.location+2];
        lookBut = [lookBut componentsSeparatedByString:@"\""].firstObject;
        return lookBut;
    }
    
    
    const char muteCode = attrs[1];
    switch (muteCode) {
        case 'B': return @"BOOL";
        case 'q': return @"NSInteger";
        case 'i': return @"int";
        case 'd': return @"double";
        case 'f': return @"float";
        default: return nil;
    }
}


+ (NSDictionary *)retainOffsetsPeriodicFaxRecyclePrologName {
    return @{};
}


+ (NSDictionary *)panelPenAdverbLongLogArray {
    return @{};
}


- (void)setValue:(id)value forUndefinedKey:(NSString *)key {}

@end
