






#import "UIColor+MapColor.h"

@implementation UIColor (MapColor)

+ (UIColor *)sharpenAffectedSignBigChargePrep:(NSString *)assumeOne {
    if (assumeOne.length <= 0) return nil;
    
    NSString *colorString = [[assumeOne stringByReplacingOccurrencesOfString: @"#" withString: @""] uppercaseString];
    CGFloat alpha, red, blue, green;
    switch ([colorString length]) {
        case 3: 
            alpha = 1.0f;
            red   = [self optionalSexMathFetchedHitBox: colorString start: 0 length: 1];
            green = [self optionalSexMathFetchedHitBox: colorString start: 1 length: 1];
            blue  = [self optionalSexMathFetchedHitBox: colorString start: 2 length: 1];
            break;
        case 4: 
            alpha = [self optionalSexMathFetchedHitBox: colorString start: 0 length: 1];
            red   = [self optionalSexMathFetchedHitBox: colorString start: 1 length: 1];
            green = [self optionalSexMathFetchedHitBox: colorString start: 2 length: 1];
            blue  = [self optionalSexMathFetchedHitBox: colorString start: 3 length: 1];
            break;
        case 6: 
            alpha = 1.0f;
            red   = [self optionalSexMathFetchedHitBox: colorString start: 0 length: 2];
            green = [self optionalSexMathFetchedHitBox: colorString start: 2 length: 2];
            blue  = [self optionalSexMathFetchedHitBox: colorString start: 4 length: 2];
            break;
        case 8: 
            alpha = [self optionalSexMathFetchedHitBox: colorString start: 0 length: 2];
            red   = [self optionalSexMathFetchedHitBox: colorString start: 2 length: 2];
            green = [self optionalSexMathFetchedHitBox: colorString start: 4 length: 2];
            blue  = [self optionalSexMathFetchedHitBox: colorString start: 6 length: 2];
            break;
        default: {
            NSAssert(NO, @"Color value %@ is invalid. It should be a hex value of the form #RBG, #ARGB, #RRGGBB, or #AARRGGBB", assumeOne);
            return nil;
        }
            break;
    }
    return [UIColor colorWithRed: red green: green blue: blue alpha: alpha];
}

+ (CGFloat)optionalSexMathFetchedHitBox:(NSString *)string start:(NSUInteger)start length:(NSUInteger)length {
    NSString *substring = [string substringWithRange: NSMakeRange(start, length)];
    NSString *ruleOut = length == 2 ? substring : [NSString stringWithFormat: @"%@%@", substring, substring];
    unsigned hexComponent;
    [[NSScanner scannerWithString: ruleOut] scanHexInt: &hexComponent];
    return hexComponent / 255.0;
}

- (UIColor *)discoverLibraryQuarterPlusSheAlpha:(CGFloat)percentage {
    return [self thicknessFourthEscapeCentralQueueBlood:percentage];
}

- (UIColor *)causeCutterLawMinimalPlayableCache:(CGFloat)percentage {
    return [self thicknessFourthEscapeCentralQueueBlood:-1*fabs(percentage)];
}

- (UIColor *)thicknessFourthEscapeCentralQueueBlood:(CGFloat)percentage {
    CGFloat red,green,blue,alpha;
    if ([self getRed:&red green:&green blue:&blue alpha:&alpha]) {
        return [UIColor colorWithRed:MIN(red+percentage/100, 1.0) green:MIN(green+percentage/100, 1.0) blue:MIN(blue+percentage/100, 1.0) alpha:alpha];
    }else {
        return nil;
    }
}

@end
