






#import "UIDevice+TabDevice.h"
#import "UpsideAskManager.h"
@import UIKit;

@implementation UIDevice (TabDevice)

static NSInteger mayHow = -1;
+ (BOOL)mayHow {
    if (mayHow < 0) {
        mayHow = [UIDevice currentDevice].userInterfaceIdiom == UIUserInterfaceIdiomPad ? 1 : 0;
    }
    return mayHow > 0;
}

+ (BOOL)thumbOpt {
    if (@available(iOS 11.0, *)) {
        
        UIWindow *window = UpsideAskManager.shared.eulerAnyBankWindow;
        
        UIEdgeInsets safeArea = window.safeAreaInsets;
        
        
        BOOL organize = ([UIDevice currentDevice].userInterfaceIdiom == UIUserInterfaceIdiomPhone);
        
        
        return organize && (
            safeArea.top > 20.0 ||          
            safeArea.left > 0 ||            
            safeArea.right > 0              
        );
    }
    return NO; 
}

@end
