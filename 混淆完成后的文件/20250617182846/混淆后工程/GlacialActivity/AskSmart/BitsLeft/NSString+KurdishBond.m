






#import "NSString+KurdishBond.h"

@implementation NSString (KurdishBond)

- (NSString *)redoConnectEndsSaveMaxFollow {
    NSCharacterSet *allowedCharacters = [[NSCharacterSet characterSetWithCharactersInString:@"!*'();:@&=+$,/?%#[]"] invertedSet];
    return [self stringByAddingPercentEncodingWithAllowedCharacters:allowedCharacters];
}

- (NSString *)bandwidthOperationEmailFirstDenseSkipped {
    NSString *tagExistRain = [self stringByReplacingOccurrencesOfString:@"+" withString:@" "];
    return [tagExistRain stringByRemovingPercentEncoding];
}

@end
