






#import "PlanItsSuchMax.h"

#define ourCost(obj) __weak typeof(obj) weak##obj = obj;
#define chestAtom(obj) __strong typeof(obj) obj = weak##obj;

@interface PlanItsSuchMax()

@property (nonatomic,strong) NSURLSession *oneFocalMile;

@end

@implementation PlanItsSuchMax


+ (instancetype)shared {
    static PlanItsSuchMax *shared = nil;
    static dispatch_once_t mathToken;
    dispatch_once(&mathToken, ^{
        shared = [[super allocWithZone:NULL] init];
        shared.oneFocalMile = [NSURLSession sessionWithConfiguration:[NSURLSessionConfiguration defaultSessionConfiguration] delegate:shared delegateQueue:[[NSOperationQueue alloc] init]];
        shared.oneFocalMile.delegateQueue.maxConcurrentOperationCount = 1;
    });
    return shared;
}

- (void)muteSoundTakeRequest:(NSMutableURLRequest *)request
                     process:(NSData * _Nullable (^_Nullable)(NSData * _Nullable rawData))processBlock
                     success:(void(^)(NSDictionary * mayJobAutoRole))success
                     rawFeet:(void(^)(NSError *error))rawFeet
                  fetchCount:(NSInteger)fetchCount {

    [self butCenterRequest:request
                   process:processBlock
                   success:success
                   rawFeet:rawFeet
                fetchCount:fetchCount
            miterRequested:0];
}


- (void)butCenterRequest:(NSMutableURLRequest *)request
                 process:(NSData * _Nullable (^_Nullable)(NSData * _Nullable rawData))processBlock
                 success:(void(^)(NSDictionary * mayJobAutoRole))success
                 rawFeet:(void(^)(NSError *error))rawFeet
              fetchCount:(NSInteger)fetchCount
          miterRequested:(NSInteger)miterRequested {

    ourCost(self);
    NSURLSessionDataTask *task = [self.oneFocalMile dataTaskWithRequest:request completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
        chestAtom(self);
        
        NSError *finalError = [self handleError:error response:response data:data];
        if (finalError) {
            

            
            if (miterRequested < fetchCount) {
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [self butCenterRequest:request process:processBlock success:success rawFeet:rawFeet fetchCount:fetchCount miterRequested:miterRequested + 1];
                });
                return;
            }

            
            if (rawFeet) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    rawFeet(finalError);
                });
            }
            return;
        }

        
        NSData *processedData = processBlock ? processBlock(data) : data;
        if (!processedData) {
            NSError *processingError = [NSError errorWithDomain:@"NetworkCore"
                                                           code:-30002
                                                       userInfo:@{NSLocalizedDescriptionKey : @"Data processing failed"}];
            if (rawFeet) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    rawFeet(processingError);
                });
            }
            return;
        }

        NSError *jsonError;
        NSDictionary *jsonResponse = [NSJSONSerialization JSONObjectWithData:processedData options:0 error:&jsonError];

        if (!jsonError && [jsonResponse isKindOfClass:[NSDictionary class]]) {
            if (success) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    success(jsonResponse);
                });
            }
        } else {
            
            if (rawFeet) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    rawFeet(jsonError);
                });
            }
        }
    }];

    [task resume];
}


- (NSError *)handleError:(NSError *)error response:(NSURLResponse *)response data:(NSData *)data {
    if (error) {
        return error;
    }

    if (!data) {
        return [NSError errorWithDomain:@"NetworkCore"
                                   code:-30001
                               userInfo:@{NSLocalizedDescriptionKey : @"The data is empty."}];
    }

    NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *)response;
    if (![httpResponse isKindOfClass:[NSHTTPURLResponse class]] || httpResponse.statusCode != 200) {
        return [NSError errorWithDomain:@"NetworkCore"
                                   code:httpResponse.statusCode
                               userInfo:@{NSLocalizedDescriptionKey : [NSString stringWithFormat:@"HTTPError，code: %ld", (long)httpResponse.statusCode]}];
    }

    return nil;
}

@end
