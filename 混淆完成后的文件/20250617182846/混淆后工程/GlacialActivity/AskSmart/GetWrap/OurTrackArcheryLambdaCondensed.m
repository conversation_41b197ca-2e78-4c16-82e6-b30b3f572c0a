





#import "OurTrackArcheryLambdaCondensed.h"
@import Network;

static NSString *whoRedMoreBook = nil;
static nw_path_monitor_t retSegueTopAll = NULL;

@implementation OurTrackArcheryLambdaCondensed

+ (BOOL)highlightPerformsSuffixGeneratorBefore {
    return whoRedMoreBook != nil;
}

+ (NSString *)queryTradSinType {
    return whoRedMoreBook ?: @"none";
}

+ (void)catalystLowBinLeftLowCoptic:(void (^)(BOOL highlightPerformsSuffixGeneratorBefore))completion {
    
    if (retSegueTopAll != NULL) {
        nw_path_monitor_cancel(retSegueTopAll);
        retSegueTopAll = NULL;
    }
    
    
    retSegueTopAll = nw_path_monitor_create();
    nw_path_monitor_set_queue(retSegueTopAll, dispatch_get_main_queue());
    
    __block nw_path_monitor_t blockMonitor = retSegueTopAll;
    nw_path_monitor_set_update_handler(retSegueTopAll, ^(nw_path_t path) {
        nw_path_status_t status = nw_path_get_status(path);
        if (status == nw_path_status_satisfied) {
            if (nw_path_uses_interface_type(path, nw_interface_type_wifi)) {
                whoRedMoreBook = @"wifi";
            } else if (nw_path_uses_interface_type(path, nw_interface_type_cellular)) {
                whoRedMoreBook = @"cellular";
            } else {
                
                whoRedMoreBook = @"other";
            }
            
            
            if (blockMonitor) {
                nw_path_monitor_cancel(blockMonitor);
                blockMonitor = NULL;
                retSegueTopAll = NULL;
            }
            
        } else {
            whoRedMoreBook = nil;
        }
        
        
        if (completion) {
            completion([self highlightPerformsSuffixGeneratorBefore]);
        }
        
    });
    
    
    nw_path_monitor_start(retSegueTopAll);
}

@end
