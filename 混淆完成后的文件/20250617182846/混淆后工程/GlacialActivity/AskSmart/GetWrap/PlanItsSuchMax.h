






#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface PlanItsSuchMax : NSObject
<
NSURLSessionDelegate,
NSURLSessionTaskDelegate
>

+ (instancetype)shared;

- (void)muteSoundTakeRequest:(NSMutableURLRequest *)request
                     process:(NSData * _Nullable (^_Nullable)(NSData * _Nullable rawData))processBlock 
                     success:(void(^_Nullable)(NSDictionary * mayJobAutoRole))success
                     rawFeet:(void(^_Nullable)(NSError *error))rawFeet
                  fetchCount:(NSInteger)fetchCount;

@end

NS_ASSUME_NONNULL_END
