






#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface OurTrackArcheryLambdaCondensed : NSObject

@property (class, nonatomic, readonly, assign) BOOL highlightPerformsSuffixGeneratorBefore;

@property (class, nonatomic, readonly, copy) NSString *queryTradSinType;

+ (void)catalystLowBinLeftLowCoptic:(void (^)(BOOL highlightPerformsSuffixGeneratorBefore))completio;

@end

NS_ASSUME_NONNULL_END
