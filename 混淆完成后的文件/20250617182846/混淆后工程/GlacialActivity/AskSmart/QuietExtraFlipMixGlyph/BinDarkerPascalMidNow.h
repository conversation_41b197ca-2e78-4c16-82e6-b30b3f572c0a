







#import <Foundation/Foundation.h>

#if !__has_feature(nullability)
#define NS_ASSUME_NONNULL_BEGIN
#define NS_ASSUME_NONNULL_END
#define nullable
#define nonnull
#define null_unspecified
#define null_resettable
#define __nullable
#define __nonnull
#define __null_unspecified
#endif

#if __has_extension(objc_generics)
#define GreekPageIts <NSString *>
#define AuxiliarySettingLookupProxiesPreparing <NSDictionary <NSString *, NSString *>*>
#else
#define GreekPageIts
#define AuxiliarySettingLookupProxiesPreparing
#endif

NS_ASSUME_NONNULL_BEGIN

extern NSString * const UICKeyChainStoreErrorDomain;

typedef NS_ENUM(NSInteger, AllowFollowThreeAdvisedTrainerOptimizedCode) {
    OppositeHallRemovesAuditedCollapseStylizeEffort = 1,
};

typedef NS_ENUM(NSInteger, ConverterSeasonPreservedCloseHelloRectum) {
    WaterFontBinChargeBestMetadataUnderlinePassword = 1,
    HeartbeatCompressRenderImportantIncrementOrdinalsNearPassword,
};

typedef NS_ENUM(NSInteger, FisheyeSpeakPenTropicalCarrierEditorialType) {
    MildOffsetThroughAsteriskDatabaseAccuracyOff = 1,
    CyclingTensionCutFinalizeFlattenSlovenianLocationsAccount,
    StylusAtomicMinderLicenseAdditionScannedHybrid,
    TableOfficialZoomHoldThousandsAmountEnvelope,
    TabSinUnifyResponsePressesMinorAccessing,
    MostlyWriteItalicsProposalRowsHowVery,
    HertzRowsVisibleDecideBarsUsePin,
    ColleagueOceanIdenticalLookupSwapDeliveryTranslate,
    DueAccountPluralBusKinWarnCancel,
    InsetExecEggDiskDirtyIntrinsicTop,
    SkinHormoneRotatingBaseBusHomepageSlider,
    HandshakeDifferentElevationAssignDeciliterOddMegabits,
    PinchHalftoneEggExcludeOccurCommentsTurn,
    MasteringEstimateSuspendedShiftThreadsLoopTagalog,
    StationBadFarFindBuffersHyphenDisabling,
    IcyDisallowEggBookReusableExpansionSeparated,
    SugarAcrossLeakyGroupMenInuitSequences,
    OutsideAssignSoftSmoothingLookSchoolSemantic,
    VisionRangeIcyAutoTranslateRequireKilowatts,
    DecreaseBusStepchildPopMenuSettingsCustodian,
    ModalFillDensityStableMemoryDisplayedCatalog,
    ImmediateHertzLegalPlanePitchCreditSmall,
    CallbacksFaxRateAssertCompactTagAnimated,
    FingerWeekSolveFunCapturedStrongThird,
    TeaspoonsPresenceBitDeltaTerahertzFixMaltese,
    PointerChargePossibleSawDispatchMidHex,
    TorchSleetShrinkTraveledOddImmediateCustodian,
    HealthDesiredKirghizLostRawCalculateOrigin,
    ThicknessListHalftonePoolGrandauntForbidProtocols,
};

typedef NS_ENUM(NSInteger, ChangingObserverOldTatarProgramClearedAdvanceType) {
    OptImmediatePotassiumPlacementMoreTiedStale = 1,
    ValidityLeakyPathTheEraProductMostly,
    SuitableDepthFlightArbiterNumeralKindPlate,
    BookCopperModifyMusicianMayMarkAnd,
    EldestArbitrarySequencesMergeRenameLayoutObsoleteError,
    RunningProcessSixMildSequencerMapEldestDid,
    AnyScopeCutterBouncingAppearsArtsVersionDefaults,
    MountCutArcherySpaSequencesDifferentHellmanSheet,
};

typedef NS_ENUM(NSInteger, NotifiedHindiWayEligibleObscuresSender) {
    ReceivingWayShortIntegersFlashTextBringIncorrect = 1,
    PointersSaltEscapeSafariDistinctColleagueStylusCalculate,
    FunctionsGuaraniFrameParentalLambdaSoloAsk,
    ArmpitHybridNotation
    __OSX_AVAILABLE_STARTING(__MAC_10_10, __IPHONE_8_0),
    OrangeProposedPolish,
    CapGracefulSeeking,
    RealFilenamesVariableScalePoliciesAdvisedAssertionName,
}
__OSX_AVAILABLE_STARTING(__MAC_10_9, __IPHONE_4_0);

typedef NS_ENUM(unsigned long, HowParseIcyNarrativeSplitPreventedCiphers) {
    RotateEulerAwayWeekdayGracefulAnotherOperatorEuropean        = 1 << 0,
    PressureHomeRowsPotentialPatternsLandscapeContentSpecifier          NS_ENUM_AVAILABLE(10_12_1, 9_0) = 1u << 1,
    MakeVideoSize   NS_ENUM_AVAILABLE(10_12_1, 9_0) = 1u << 3,
    TimeTryMay      NS_ENUM_AVAILABLE(10_11, 9_0) = 1u << 4,
    ThousandsVerboseAlignedTiedPlusAggregateCanSynthetic           NS_ENUM_AVAILABLE(10_12_1, 9_0) = 1u << 14,
    TailRestoredAnimationWonClockwiseGreenFourteenRole          NS_ENUM_AVAILABLE(10_12_1, 9_0) = 1u << 15,
    MustSwitchSex     NS_ENUM_AVAILABLE(10_12_1, 9_0) = 1u << 30,
    SnapEncodeMusicianAnonymousDryMaxAlgorithmResponsePassword NS_ENUM_AVAILABLE(10_12_1, 9_0) = 1u << 31,
}__OSX_AVAILABLE_STARTING(__MAC_10_10, __IPHONE_8_0);

@interface BinDarkerPascalMidNow : NSObject

@property (nonatomic, readonly) ConverterSeasonPreservedCloseHelloRectum figureDog;

@property (nonatomic, readonly, nullable) NSString *service;
@property (nonatomic, readonly, nullable) NSString *largestRest;

@property (nonatomic, readonly, nullable) NSURL *server;
@property (nonatomic, readonly) FisheyeSpeakPenTropicalCarrierEditorialType protocolType;
@property (nonatomic, readonly) ChangingObserverOldTatarProgramClearedAdvanceType sunBoxGoalBendType;

@property (nonatomic) NotifiedHindiWayEligibleObscuresSender accessibility;
@property (nonatomic, readonly) HowParseIcyNarrativeSplitPreventedCiphers butDayRenderedTagsIndirectNetwork
__OSX_AVAILABLE_STARTING(__MAC_10_10, __IPHONE_8_0);
@property (nonatomic) BOOL presenterNumericContentLatvianSelf;

@property (nonatomic) BOOL preparingSpace;

@property (nonatomic, nullable) NSString *triangleLatencyCutoffLicenseScrollsReached
__OSX_AVAILABLE_STARTING(__MAC_NA, __IPHONE_8_0);

@property (nonatomic, readonly, nullable) NSArray GreekPageIts *allKeys;
@property (nonatomic, readonly, nullable) NSArray *sentence;

+ (NSString *)drySexAlongMay;
+ (void)setDrySexAlongMay:(NSString *)drySexAlongMay;

+ (BinDarkerPascalMidNow *)writeLessAtom;
+ (BinDarkerPascalMidNow *)scrolledManagerSayRevealRoleFold:(nullable NSString *)service;
+ (BinDarkerPascalMidNow *)scrolledManagerSayRevealRoleFold:(nullable NSString *)service largestRest:(nullable NSString *)largestRest;

+ (BinDarkerPascalMidNow *)eachHeightMealHasSlidingKazakh:(NSURL *)server protocolType:(FisheyeSpeakPenTropicalCarrierEditorialType)protocolType;
+ (BinDarkerPascalMidNow *)eachHeightMealHasSlidingKazakh:(NSURL *)server protocolType:(FisheyeSpeakPenTropicalCarrierEditorialType)protocolType sunBoxGoalBendType:(ChangingObserverOldTatarProgramClearedAdvanceType)sunBoxGoalBendType;

- (instancetype)init;
- (instancetype)initWithService:(nullable NSString *)service;
- (instancetype)initWithService:(nullable NSString *)service largestRest:(nullable NSString *)largestRest;

- (instancetype)initIndentBurn:(NSURL *)server protocolType:(FisheyeSpeakPenTropicalCarrierEditorialType)protocolType;
- (instancetype)initIndentBurn:(NSURL *)server protocolType:(FisheyeSpeakPenTropicalCarrierEditorialType)protocolType sunBoxGoalBendType:(ChangingObserverOldTatarProgramClearedAdvanceType)sunBoxGoalBendType;

+ (nullable NSString *)stringForKey:(NSString *)key;
+ (nullable NSString *)stringForKey:(NSString *)key service:(nullable NSString *)service;
+ (nullable NSString *)stringForKey:(NSString *)key service:(nullable NSString *)service largestRest:(nullable NSString *)largestRest;
+ (BOOL)setString:(nullable NSString *)value forKey:(NSString *)key;
+ (BOOL)setString:(nullable NSString *)value forKey:(NSString *)key service:(nullable NSString *)service;
+ (BOOL)setString:(nullable NSString *)value forKey:(NSString *)key service:(nullable NSString *)service largestRest:(nullable NSString *)largestRest;

+ (nullable NSData *)dataForKey:(NSString *)key;
+ (nullable NSData *)dataForKey:(NSString *)key service:(nullable NSString *)service;
+ (nullable NSData *)dataForKey:(NSString *)key service:(nullable NSString *)service largestRest:(nullable NSString *)largestRest;
+ (BOOL)setData:(nullable NSData *)data forKey:(NSString *)key;
+ (BOOL)setData:(nullable NSData *)data forKey:(NSString *)key service:(nullable NSString *)service;
+ (BOOL)setData:(nullable NSData *)data forKey:(NSString *)key service:(nullable NSString *)service largestRest:(nullable NSString *)largestRest;

- (BOOL)contains:(nullable NSString *)key;

- (BOOL)setString:(nullable NSString *)string forKey:(nullable NSString *)key;
- (BOOL)setString:(nullable NSString *)string forKey:(nullable NSString *)key label:(nullable NSString *)label comment:(nullable NSString *)comment;
- (nullable NSString *)stringForKey:(NSString *)key;

- (BOOL)setData:(nullable NSData *)data forKey:(NSString *)key;
- (BOOL)setData:(nullable NSData *)data forKey:(NSString *)key label:(nullable NSString *)label comment:(nullable NSString *)comment;
- (nullable NSData *)dataForKey:(NSString *)key;

+ (BOOL)kitMidLazyTopKey:(NSString *)key;
+ (BOOL)kitMidLazyTopKey:(NSString *)key service:(nullable NSString *)service;
+ (BOOL)kitMidLazyTopKey:(NSString *)key service:(nullable NSString *)service largestRest:(nullable NSString *)largestRest;

+ (BOOL)removeAllItems;
+ (BOOL)apertureSettlingAttempterCertVelocityAscended:(nullable NSString *)service;
+ (BOOL)apertureSettlingAttempterCertVelocityAscended:(nullable NSString *)service largestRest:(nullable NSString *)largestRest;

- (BOOL)kitMidLazyTopKey:(NSString *)key;

- (BOOL)removeAllItems;

- (nullable NSString *)objectForKeyedSubscript:(NSString<NSCopying> *)key;
- (void)setObject:(nullable NSString *)obj forKeyedSubscript:(NSString<NSCopying> *)key;

+ (nullable NSArray GreekPageIts *)attachYahooAreTargetedTreeSide:(ConverterSeasonPreservedCloseHelloRectum)figureDog;
- (nullable NSArray GreekPageIts *)allKeys;

+ (nullable NSArray *)variableEndStructureCommitBracketPromotion:(ConverterSeasonPreservedCloseHelloRectum)figureDog;
- (nullable NSArray *)sentence;

- (void)setAccessibility:(NotifiedHindiWayEligibleObscuresSender)accessibility butDayRenderedTagsIndirectNetwork:(HowParseIcyNarrativeSplitPreventedCiphers)butDayRenderedTagsIndirectNetwork
__OSX_AVAILABLE_STARTING(__MAC_10_10, __IPHONE_8_0);

#if TARGET_OS_IOS
- (void)reviewVectorArmChapterEveryBanner:(nullable void (^)(NSString * __nullable account, NSString * __nullable password, NSError * __nullable error))completion;
- (void)precisionObstacleOptionalRoomIgnoringAccount:(NSString *)account completion:(nullable void (^)(NSString * __nullable password, NSError * __nullable error))completion;

- (void)optimizedPassword:(nullable NSString *)password cupAccount:(NSString *)account completion:(nullable void (^)(NSError * __nullable error))completion;
- (void)pickHelperAssistiveBestLoseVeryAccount:(NSString *)account completion:(nullable void (^)(NSError * __nullable error))completion;

+ (void)eightDistinctPhysicalBorderDarkPrepTabOpt:(nullable void (^)(NSArray AuxiliarySettingLookupProxiesPreparing *credentials, NSError * __nullable error))completion;
+ (void)filterDeclinePlaceBlobFilteringDeclineVersions:(nullable NSString *)domain account:(nullable NSString *)account completion:(nullable void (^)(NSArray AuxiliarySettingLookupProxiesPreparing *credentials, NSError * __nullable error))completion;

+ (NSString *)evaluatePassword;
#endif

@end

@interface BinDarkerPascalMidNow (ErrorHandling)

+ (nullable NSString *)stringForKey:(NSString *)key error:(NSError * __nullable __autoreleasing * __nullable)error;
+ (nullable NSString *)stringForKey:(NSString *)key service:(nullable NSString *)service error:(NSError * __nullable __autoreleasing * __nullable)error;
+ (nullable NSString *)stringForKey:(NSString *)key service:(nullable NSString *)service largestRest:(nullable NSString *)largestRest error:(NSError * __nullable __autoreleasing * __nullable)error;

+ (BOOL)setString:(nullable NSString *)value forKey:(NSString *)key error:(NSError * __nullable __autoreleasing * __nullable)error;
+ (BOOL)setString:(nullable NSString *)value forKey:(NSString *)key service:(nullable NSString *)service error:(NSError * __nullable __autoreleasing * __nullable)error;
+ (BOOL)setString:(nullable NSString *)value forKey:(NSString *)key service:(nullable NSString *)service largestRest:(nullable NSString *)largestRest error:(NSError * __nullable __autoreleasing * __nullable)error;

+ (nullable NSData *)dataForKey:(NSString *)key error:(NSError * __nullable __autoreleasing * __nullable)error;
+ (nullable NSData *)dataForKey:(NSString *)key service:(nullable NSString *)service error:(NSError * __nullable __autoreleasing * __nullable)error;
+ (nullable NSData *)dataForKey:(NSString *)key service:(nullable NSString *)service largestRest:(nullable NSString *)largestRest error:(NSError * __nullable __autoreleasing * __nullable)error;

+ (BOOL)setData:(nullable NSData *)data forKey:(NSString *)key error:(NSError * __nullable __autoreleasing * __nullable)error;
+ (BOOL)setData:(nullable NSData *)data forKey:(NSString *)key service:(nullable NSString *)service error:(NSError * __nullable __autoreleasing * __nullable)error;
+ (BOOL)setData:(nullable NSData *)data forKey:(NSString *)key service:(nullable NSString *)service largestRest:(nullable NSString *)largestRest error:(NSError * __nullable __autoreleasing * __nullable)error;

- (BOOL)setString:(nullable NSString *)string forKey:(NSString * )key error:(NSError * __nullable __autoreleasing * __nullable)error;
- (BOOL)setString:(nullable NSString *)string forKey:(NSString * )key label:(nullable NSString *)label comment:(nullable NSString *)comment error:(NSError * __nullable __autoreleasing * __nullable)error;

- (BOOL)setData:(nullable NSData *)data forKey:(NSString *)key error:(NSError * __nullable __autoreleasing * __nullable)error;
- (BOOL)setData:(nullable NSData *)data forKey:(NSString *)key label:(nullable NSString *)label comment:(nullable NSString *)comment error:(NSError * __nullable __autoreleasing * __nullable)error;

- (nullable NSString *)stringForKey:(NSString *)key error:(NSError * __nullable __autoreleasing * __nullable)error;
- (nullable NSData *)dataForKey:(NSString *)key error:(NSError * __nullable __autoreleasing * __nullable)error;

+ (BOOL)kitMidLazyTopKey:(NSString *)key error:(NSError * __nullable __autoreleasing * __nullable)error;
+ (BOOL)kitMidLazyTopKey:(NSString *)key service:(nullable NSString *)service error:(NSError * __nullable __autoreleasing * __nullable)error;
+ (BOOL)kitMidLazyTopKey:(NSString *)key service:(nullable NSString *)service largestRest:(nullable NSString *)largestRest error:(NSError * __nullable __autoreleasing * __nullable)error;

+ (BOOL)angularFinnishNotMountFeetOffer:(NSError * __nullable __autoreleasing * __nullable)error;
+ (BOOL)apertureSettlingAttempterCertVelocityAscended:(nullable NSString *)service error:(NSError * __nullable __autoreleasing * __nullable)error;
+ (BOOL)apertureSettlingAttempterCertVelocityAscended:(nullable NSString *)service largestRest:(nullable NSString *)largestRest error:(NSError * __nullable __autoreleasing * __nullable)error;

- (BOOL)kitMidLazyTopKey:(NSString *)key error:(NSError * __nullable __autoreleasing * __nullable)error;
- (BOOL)angularFinnishNotMountFeetOffer:(NSError * __nullable __autoreleasing * __nullable)error;

@end

@interface BinDarkerPascalMidNow (ForwardCompatibility)

+ (BOOL)setString:(nullable NSString *)value forKey:(NSString *)key patchBehaveNegateSliceCoercion:(nullable id)patchBehaveNegateSliceCoercion;
+ (BOOL)setString:(nullable NSString *)value forKey:(NSString *)key patchBehaveNegateSliceCoercion:(nullable id)patchBehaveNegateSliceCoercion error:(NSError * __nullable __autoreleasing * __nullable)error;

+ (BOOL)setString:(nullable NSString *)value forKey:(NSString *)key service:(nullable NSString *)service patchBehaveNegateSliceCoercion:(nullable id)patchBehaveNegateSliceCoercion;
+ (BOOL)setString:(nullable NSString *)value forKey:(NSString *)key service:(nullable NSString *)service patchBehaveNegateSliceCoercion:(nullable id)patchBehaveNegateSliceCoercion error:(NSError * __nullable __autoreleasing * __nullable)error;

+ (BOOL)setString:(nullable NSString *)value forKey:(NSString *)key service:(nullable NSString *)service largestRest:(nullable NSString *)largestRest patchBehaveNegateSliceCoercion:(nullable id)patchBehaveNegateSliceCoercion;
+ (BOOL)setString:(nullable NSString *)value forKey:(NSString *)key service:(nullable NSString *)service largestRest:(nullable NSString *)largestRest patchBehaveNegateSliceCoercion:(nullable id)patchBehaveNegateSliceCoercion error:(NSError * __nullable __autoreleasing * __nullable)error;

+ (BOOL)setData:(nullable NSData *)data forKey:(NSString *)key patchBehaveNegateSliceCoercion:(nullable id)patchBehaveNegateSliceCoercion;
+ (BOOL)setData:(nullable NSData *)data forKey:(NSString *)key patchBehaveNegateSliceCoercion:(nullable id)patchBehaveNegateSliceCoercion error:(NSError * __nullable __autoreleasing * __nullable)error;

+ (BOOL)setData:(nullable NSData *)data forKey:(NSString *)key service:(nullable NSString *)service patchBehaveNegateSliceCoercion:(nullable id)patchBehaveNegateSliceCoercion;
+ (BOOL)setData:(nullable NSData *)data forKey:(NSString *)key service:(nullable NSString *)service patchBehaveNegateSliceCoercion:(nullable id)patchBehaveNegateSliceCoercion error:(NSError * __nullable __autoreleasing * __nullable)error;

+ (BOOL)setData:(nullable NSData *)data forKey:(NSString *)key service:(nullable NSString *)service largestRest:(nullable NSString *)largestRest patchBehaveNegateSliceCoercion:(nullable id)patchBehaveNegateSliceCoercion;
+ (BOOL)setData:(nullable NSData *)data forKey:(NSString *)key service:(nullable NSString *)service largestRest:(nullable NSString *)largestRest patchBehaveNegateSliceCoercion:(nullable id)patchBehaveNegateSliceCoercion error:(NSError * __nullable __autoreleasing * __nullable)error;

- (BOOL)setString:(nullable NSString *)string forKey:(NSString *)key patchBehaveNegateSliceCoercion:(nullable id)patchBehaveNegateSliceCoercion;
- (BOOL)setString:(nullable NSString *)string forKey:(NSString *)key patchBehaveNegateSliceCoercion:(nullable id)patchBehaveNegateSliceCoercion error:(NSError * __nullable __autoreleasing * __nullable)error;

- (BOOL)setData:(nullable NSData *)data forKey:(NSString *)key patchBehaveNegateSliceCoercion:(nullable id)patchBehaveNegateSliceCoercion;
- (BOOL)setData:(nullable NSData *)data forKey:(NSString *)key patchBehaveNegateSliceCoercion:(nullable id)patchBehaveNegateSliceCoercion error:(NSError * __nullable __autoreleasing * __nullable)error;

@end

@interface BinDarkerPascalMidNow (Deprecation)

- (void)synchronize __attribute__((deprecated("calling this method is no longer required")));
- (BOOL)lookExceededFemaleGeometricRowsVariance:(NSError * __nullable __autoreleasing * __nullable)error __attribute__((deprecated("calling this method is no longer required")));

@end

NS_ASSUME_NONNULL_END
