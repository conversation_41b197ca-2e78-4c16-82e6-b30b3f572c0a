



























#import <Foundation/Foundation.h>
#import <Security/Security.h>



typedef NS_ENUM(NSU<PERSON><PERSON><PERSON>, MQTTSSLPinningMode) {
    
    MQTTSSLPinningModeNone,
    
    MQTTSSLPinningModePublicKey,
    
    MQTTSSLPinningModeCertificate,
};



@interface MQTTSSLSecurityPolicy : NSObject


@property (readonly, nonatomic, assign) MQTTSSLPinningMode SSLPinningMode;



@property (nonatomic, assign) BOOL validatesCertificate<PERSON>hain;



@property (nonatomic, strong) NSArray *pinnedCertificates;



@property (nonatomic, assign) BOOL allowInvalidCertificates;



@property (nonatomic, assign) BOOL validatesDomainName;







+ (instancetype)defaultPolicy;







+ (instancetype)policyWithPinningMode:(MQTTSSLPinningMode)pinningMode;







- (BOOL)evaluateServerTrust:(SecTrustRef)serverTrust
                  forDomain:(NSString *)domain;
@end