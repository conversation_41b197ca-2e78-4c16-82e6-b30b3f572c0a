







#import <Foundation/Foundation.h>
#if TARGET_OS_IPHONE == 1
#import <UIKit/UIKit.h>
#endif
#import "MQTTSession.h"
#import "MQTTSessionLegacy.h"
#import "MQTTSSLSecurityPolicy.h"

@class MQTTSessionManager;



@protocol MQTTSessionManagerDelegate <NSObject>



typedef NS_ENUM(int, MQTTSessionManagerState) {
    MQTTSessionManagerStateStarting,
    MQTTSessionManagerStateConnecting,
    MQTTSessionManagerStateError,
    MQTTSessionManagerStateConnected,
    MQTTSessionManagerStateClosing,
    MQTTSessionManagerStateClosed
};

@optional



- (void)handleMessage:(NSData *)data onTopic:(NSString *)topic retained:(BOOL)retained;



- (void)sessionManager:(MQTTSessionManager *)sessionManager
     didReceiveMessage:(NSData *)data
               onTopic:(NSString *)topic
              retained:(BOOL)retained;



- (void)messageDelivered:(UInt16)msgID;



- (void)sessionManager:(MQTTSessionManager *)sessionManager didDeliverMessage:(UInt16)msgID;



- (void)sessionManager:(MQTTSessionManager *)sessionManager didChangeState:(MQTTSessionManagerState)newState;



- (void)sessionManagerReconnect:(MQTTSessionManager *)sessionManager;

@end



@interface MQTTSessionManager : NSObject <MQTTSessionDelegate>



@property (strong, nonatomic, readonly) MQTTSession *session;



@property (readonly) NSString *host;



@property (readonly) UInt32 port;



@property (weak, nonatomic) id<MQTTSessionManagerDelegate> delegate;



@property (readonly) BOOL requiresTearDown;



@property (strong, nonatomic) NSDictionary<NSString *, NSNumber *> *subscriptions;



@property (readonly, strong, nonatomic) NSDictionary<NSString *, NSNumber *> *effectiveSubscriptions;



@property (nonatomic, readonly) MQTTSessionManagerState state;



@property (nonatomic, readonly) NSError *lastErrorCode;




- (MQTTSessionManager *)initWithPersistence:(BOOL)persistent
                              maxWindowSize:(NSUInteger)maxWindowSize
                                maxMessages:(NSUInteger)maxMessages
                                    maxSize:(NSUInteger)maxSize
                 maxConnectionRetryInterval:(NSTimeInterval)maxRetryInterval
                        connectInForeground:(BOOL)connectInForeground
                             streamSSLLevel:(NSString *)streamSSLLevel
                                      queue:(dispatch_queue_t)queue NS_DESIGNATED_INITIALIZER;




- (void)connectTo:(NSString *)host
             port:(NSInteger)port
              tls:(BOOL)tls
        keepalive:(NSInteger)keepalive
            clean:(BOOL)clean
             auth:(BOOL)auth
             user:(NSString *)user
             pass:(NSString *)pass
             will:(BOOL)will
        willTopic:(NSString *)willTopic
          willMsg:(NSData *)willMsg
          willQos:(MQTTQosLevel)willQos
   willRetainFlag:(BOOL)willRetainFlag
     withClientId:(NSString *)clientId
   securityPolicy:(MQTTSSLSecurityPolicy *)securityPolicy
     certificates:(NSArray *)certificates
    protocolLevel:(MQTTProtocolVersion)protocolLevel
   connectHandler:(MQTTConnectHandler)connectHandler;



- (void)connectToLast:(MQTTConnectHandler)connectHandler;



 - (void)updateSessionConfig:(NSString *)host
                       port:(NSInteger)port
                       user:(NSString *)user
                       pass:(NSString *)pass
                   clientId:(NSString *)clientId
                   keepalive:(NSInteger)keepalive;



- (UInt16)sendData:(NSData *)data topic:(NSString *)topic qos:(MQTTQosLevel)qos retain:(BOOL)retainFlag;



- (void)disconnectWithDisconnectHandler:(MQTTDisconnectHandler)disconnectHandler;

@end
