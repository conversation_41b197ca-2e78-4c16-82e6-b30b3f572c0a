






#import "AsleepInfo.h"
#import "SobDoneOldConfig.h"
#import "SmallKilowattsReplyCommentsLetter.h"

@import AdSupport;
@import AppTrackingTransparency;
@import UIKit;

#import "sys/utsname.h" //utsname

@implementation AsleepInfo

+ (UIImage *)driveSinMissingCursorDisableImage {
    NSDictionary *snapAlien = [[NSBundle mainBundle] infoDictionary];
    NSString *icon = [[snapAlien valueForKeyPath:@"CFBundleIcons.CFBundlePrimaryIcon.CFBundleIconFiles"] lastObject];
    return [UIImage imageNamed:icon];
}

+ (NSString *)paceNotHueLastIdentifier {
    return [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleIdentifier"];
}

+ (NSString *)dingbatsSoftwareIllTriangleWas {
    return [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"];
}

+ (NSString *)canRoundName {
    NSString *displayName = [[NSBundle mainBundle] localizedInfoDictionary][@"CFBundleDisplayName"];

    if (!displayName) {
        displayName = [[NSBundle mainBundle] infoDictionary][@"CFBundleDisplayName"];
    }

    if (!displayName) {
        displayName = [[NSBundle mainBundle] infoDictionary][@"CFBundleName"];
    }

    return displayName;
}

+ (NSString *)farsiCarAllName {
    return [UIDevice currentDevice].name;
}

+ (NSString *)atomAzimuthReaderAbnormalLighten {
    return [ASIdentifierManager sharedManager].advertisingIdentifier.UUIDString;
}

+ (NSString *)transposeDeprecateNetscapePostJob {
    return [UIDevice currentDevice].identifierForVendor.UUIDString;
}

+ (NSString *)mustExecBoxModel {
    struct utsname systemInfo;
    uname(&systemInfo);
    NSString *deviceModel = [NSString stringWithCString:systemInfo.machine encoding:NSUTF8StringEncoding];
    return deviceModel;
}

+ (NSString *)frameGramHoverMillEach {
    return [UIDevice currentDevice].systemVersion;
}

+ (NSString *)hierarchyMeteringSubtractFixUrgentPath {
    return NSHomeDirectory().lastPathComponent;
}

+ (BOOL)youFilename {
    NSUserDefaults *wayAllQuery = [NSUserDefaults standardUserDefaults];
    return [wayAllQuery boolForKey:appendApply.digitizedPetabytesMovementGramDog];
}
+ (void)setYouFilename:(BOOL)youFilename {
    NSUserDefaults *wayAllQuery = [NSUserDefaults standardUserDefaults];
    [wayAllQuery setBool:youFilename forKey:appendApply.digitizedPetabytesMovementGramDog];
    [wayAllQuery synchronize];
}

+ (void)visitedStrictUsageWrittenAlienRhythm:(void (^)(void))clipBits {
    static dispatch_once_t mathToken;
    static BOOL musicEndInfo = NO;

    
    if (musicEndInfo) {
        ClaimInfo(appendApply.deferredNotifiedExtendSegmentsStampForkCandidate);
        return;
    }

    dispatch_once(&mathToken, ^{
        musicEndInfo = YES;
        ClaimInfo(appendApply.identifyOceanEdgeLegalRestOther);

        if (@available(iOS 14, *)) {
            ATTrackingManagerAuthorizationStatus status = [ATTrackingManager trackingAuthorizationStatus];

            NSString *statusDesc = [self saltFillerLateStatus:status];

            ClaimInfo(appendApply.peopleShowersBasalPowerIntegratePointer, statusDesc, (long)status);

            switch (status) {
                case ATTrackingManagerAuthorizationStatusAuthorized:
                    ClaimInfo(appendApply.dirtyWhoSmallAbortMenstrualThatReduce);
                    musicEndInfo = NO;
                    if (clipBits) {
                        clipBits();
                    }
                    break;

                case ATTrackingManagerAuthorizationStatusDenied:
                    ClaimInfo(appendApply.sayStrictDarkVerifyDog);
                    musicEndInfo = NO;
                    if (clipBits) {
                        clipBits();
                    }
                    break;

                case ATTrackingManagerAuthorizationStatusRestricted:
                    ClaimInfo(appendApply.adjustingDownloadTeaspoonsStrongestWeekBridging);
                    musicEndInfo = NO;
                    if (clipBits) {
                        clipBits();
                    }
                    break;

                case ATTrackingManagerAuthorizationStatusNotDetermined:
                    ClaimInfo(appendApply.manualSkipDarkAudiogramFunctionsBordered);
                    [self peopleWindowRomanianLoopForbiddenLawEra:^{
                        musicEndInfo = NO;
                        if (clipBits) {
                            clipBits();
                        }
                    }];
                    break;
            }
        } else {
            ClaimInfo(appendApply.hyphenEggComposerDetectorVariablesMusic);
            musicEndInfo = NO;
            if (clipBits) {
                clipBits();
            }
        }
    });
}

+ (void)peopleWindowRomanianLoopForbiddenLawEra:(void (^)(void))completion {
    ClaimInfo(appendApply.processMuteCoulombsIdenticalSymmetricUpdating);

    
    static int butEnterSwap = 6;

    __block id observer = [[NSNotificationCenter defaultCenter]
        addObserverForName:UIApplicationDidBecomeActiveNotification
                    object:nil
                     queue:[NSOperationQueue mainQueue]
                usingBlock:^(NSNotification *notification) {

        ClaimInfo(appendApply.makeBuilderTertiaryTooRowAdvanced, butEnterSwap);

        
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(butEnterSwap * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{


            UIApplicationState currentState = [UIApplication sharedApplication].applicationState;

            NSString *stateDesc = [self shePongCardSayState:currentState];

            ClaimInfo(appendApply.figureShowingRatingsDecaySeasonShear, stateDesc);

            if (currentState == UIApplicationStateActive) {
                ClaimInfo(appendApply.headSourcesBlendPageChildRenameSensitive);
                [self maleIllLeaseRequest:completion];
            } else {

                ClaimInfo(appendApply.sentenceSuffixPatchClampKilometerFit, stateDesc);
                ClaimInfo(appendApply.cubicChunkyParagraphAgreementLessEmergency);
                observer = [[NSNotificationCenter defaultCenter]
                    addObserverForName:UIApplicationDidBecomeActiveNotification
                                object:nil
                                 queue:[NSOperationQueue mainQueue]
                            usingBlock:^(NSNotification *notification) {
                    
                    ClaimInfo(appendApply.likeBoxPetiteSockSilenceResonant);
                    [[NSNotificationCenter defaultCenter] removeObserver:observer];
                    ClaimInfo(appendApply.minuteMealUniversalRelevanceCanadianLoveSpecify);
                    [self maleIllLeaseRequest:completion];
                }];
            }

        });

        ClaimInfo(appendApply.stopTabExtrasEvictProjectSubmit);
        
        [[NSNotificationCenter defaultCenter] removeObserver:observer];
    }];
}

+ (void)maleIllLeaseRequest:(void (^)(void))completion {
    if (@available(iOS 14, *)) {
        ClaimInfo(appendApply.smallestNearestAwayYiddishUpscaleUnplugged);

        [ATTrackingManager requestTrackingAuthorizationWithCompletionHandler:^(ATTrackingManagerAuthorizationStatus status) {
            ATTrackingManagerAuthorizationStatus napDiskStatus = [ATTrackingManager trackingAuthorizationStatus];

            NSString *callbackStatusDesc = [self saltFillerLateStatus:status];
            NSString *currentStatusDesc = [self saltFillerLateStatus:napDiskStatus];

            ClaimInfo(appendApply.indigoLetterRawChromaMixLater);
            ClaimInfo(appendApply.exerciseOxygenExchangesOcclusionPinMind, callbackStatusDesc, (long)status);
            ClaimInfo(appendApply.leadUnlockFailCommonExtentSpringAddition, currentStatusDesc, (long)napDiskStatus);

            
            
            
            
            BOOL isAuthorized = (napDiskStatus == ATTrackingManagerAuthorizationStatusAuthorized) ||
                               (status == ATTrackingManagerAuthorizationStatusAuthorized);

            if (isAuthorized) {
                ClaimInfo(appendApply.promptArrangedArrangedDolbyLeaveSpanAccessed);
                if (completion) {
                    completion();
                }
            } else if (napDiskStatus == ATTrackingManagerAuthorizationStatusNotDetermined) {
                ClaimInfo(appendApply.snowPlusStoreOptAutoHandledBookmark);
                [self secureArbitraryPrinterBlinkCenterStorylineAdjust:completion miterRequested:0];
            } else {
                ClaimInfo(appendApply.pairUsabilityLigaturesEvaluateDevicesCarSingular);
                if (completion) {
                    completion();
                }
            }
        }];
    }
}


+ (NSString *)saltFillerLateStatus:(ATTrackingManagerAuthorizationStatus)status  API_AVAILABLE(ios(14)){
    if (@available(iOS 14, *)) {
        switch (status) {
            case ATTrackingManagerAuthorizationStatusNotDetermined:
                return appendApply.snapIconCervicalLongitudeUpdateOddSun;
            case ATTrackingManagerAuthorizationStatusRestricted:
                return appendApply.looseBlurCutTintCommittedMust;
            case ATTrackingManagerAuthorizationStatusDenied:
                return appendApply.boldHisServicesPartCarrierEgg;
            case ATTrackingManagerAuthorizationStatusAuthorized:
                return appendApply.slidingBarFullyNoneWhoOverflow;
            default:
                return [NSString stringWithFormat:appendApply.earlierRevisionsDietaryNineAssignPing, (long)status];
        }
    }
    return appendApply.updatingSinSaveHasFoldReceiptStroke;
}

+ (NSString *)shePongCardSayState:(UIApplicationState)state {
    switch (state) {
        case UIApplicationStateActive:
            return appendApply.protocolsNearestDescendBinWorkingTree;
        case UIApplicationStateInactive:
            return appendApply.snowReadTryGreenJapaneseVery;
        case UIApplicationStateBackground:
            return appendApply.trackingSegmentThatReliablePrimariesExpecting;
        default:
            return [NSString stringWithFormat:appendApply.artIssueSerializeFiveNetTemporal, (long)state];
    }
}


+ (void)secureArbitraryPrinterBlinkCenterStorylineAdjust:(void (^)(void))clipBits miterRequested:(NSInteger)miterRequested {
    NSInteger fetchCount = 10;

    if (@available(iOS 14, *)) {
        ATTrackingManagerAuthorizationStatus napDiskStatus = [ATTrackingManager trackingAuthorizationStatus];

        NSString *statusDesc = [self saltFillerLateStatus:napDiskStatus];

        ClaimInfo(appendApply.resourceIndicatorSerializeHasRespondSpatial,
              (long)(miterRequested + 1), (long)fetchCount, statusDesc);

        
        if (napDiskStatus == ATTrackingManagerAuthorizationStatusNotDetermined && miterRequested < fetchCount) {
            ClaimInfo(appendApply.tooSpokenPublicGrowQueryUse, (long)(miterRequested + 2));

            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0f * NSEC_PER_SEC)),
                          dispatch_get_main_queue(), ^{
                [self secureArbitraryPrinterBlinkCenterStorylineAdjust:clipBits miterRequested:miterRequested + 1];
            });
            return;
        } else {
            
            
            if (miterRequested >= fetchCount) {
                ClaimInfo(appendApply.resizeUndoShakeSquaredMartialKilogram, (long)fetchCount);
                ClaimInfo(appendApply.remainingOutputLocationsPurpleStepchildGraceful, statusDesc);
            } else {
                ClaimInfo(appendApply.unwrapPressesAwakeFadeOffsetSetup, statusDesc);

                if (napDiskStatus == ATTrackingManagerAuthorizationStatusAuthorized) {
                    ClaimInfo(appendApply.detectedPencilHandshakeLookupIntroBengali);
                } else if (napDiskStatus == ATTrackingManagerAuthorizationStatusDenied) {
                    ClaimInfo(appendApply.fallbackRotatingAlgorithmMapTabularPasswords);
                } else if (napDiskStatus == ATTrackingManagerAuthorizationStatusRestricted) {
                    ClaimInfo(appendApply.undoneDepartureTiedEnableTeaspoonsRender);
                }
            }

            ClaimInfo(appendApply.connectedMostBurstJobExceededVirtual);
            if (clipBits) {
                clipBits();
            }
        }
    } else {
        ClaimInfo(appendApply.priorityDiagnoseUplinkSizeFocalSegmentPostal);
        if (clipBits) {
            clipBits();
        }
    }
}
@end
