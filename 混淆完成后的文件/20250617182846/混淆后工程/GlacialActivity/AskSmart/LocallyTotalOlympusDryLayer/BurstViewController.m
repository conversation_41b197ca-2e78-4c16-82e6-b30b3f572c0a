

#import "BurstViewController.h"
#import "Piece.h"
#import "ScreenWrapEraEmergencyCapsSix.h"
#import "SwappedTryArabicBottomMagnesium.h"
#import "SobDoneOldConfig.h"

@interface BurstViewController ()
@property (nonatomic, strong) UITextView *textView;
@property (nonatomic, strong) ScreenWrapEraEmergencyCapsSix *subgroupsEnglishForSeekingPhonetic;
@property (nonatomic, strong) SwappedTryArabicBottomMagnesium *hisAllowableButtonClockwiseSerbian;
@property (nonatomic, strong) NSDate *modeTalkDate; 
@end

static SwappedTryArabicBottomMagnesium *_sharedFileDestination = nil;
static ScreenWrapEraEmergencyCapsSix *_sharedConsoleDestination = nil;

@implementation BurstViewController

+ (void)heapDropYou {
    
    [Piece rematchAllOwnAirSuspendedAnalysis];

    _sharedConsoleDestination = [[ScreenWrapEraEmergencyCapsSix alloc] init];
    _sharedConsoleDestination.scriptLevel = MeasuredFusionBezelMalteseVitamin;

    [Piece sixteenSwapAccordingRegionsSelect:_sharedConsoleDestination];

    _sharedFileDestination = [[SwappedTryArabicBottomMagnesium alloc] init];
    _sharedFileDestination.scriptLevel = BinDescendSub;
    _sharedFileDestination.busFree = 7;
    _sharedFileDestination.wideYetBoxFisheyeFork = YES;
    [Piece sixteenSwapAccordingRegionsSelect:_sharedFileDestination];

    [_sharedFileDestination mapCatPeerLost];
}

+ (SwappedTryArabicBottomMagnesium *)exerciseFiberOperateSpeechBrowseTamil {
    return _sharedFileDestination;
}
+ (ScreenWrapEraEmergencyCapsSix *)leftPermittedShowersModifierMoireStart {
    return _sharedConsoleDestination;
}

- (ScreenWrapEraEmergencyCapsSix *)subgroupsEnglishForSeekingPhonetic {
    return _sharedConsoleDestination;
}

+ (void)showFromViewController:(UIViewController *)parentVC {
    BurstViewController *quiet = [[BurstViewController alloc] init];
    UINavigationController *sub = [[UINavigationController alloc] initWithRootViewController:quiet];
    sub.modalPresentationStyle = UIModalPresentationFullScreen;
    [parentVC presentViewController:sub animated:YES completion:nil];
}

- (void)viewDidLoad {
    [super viewDidLoad];

    self.title = appendApply.alienDiskPostRestorePanoramaMetric;
    self.view.backgroundColor = [UIColor systemBackgroundColor];

    self.navigationItem.leftBarButtonItem = [[UIBarButtonItem alloc]
                                            initWithBarButtonSystemItem:UIBarButtonSystemItemCancel
                                            target:self
                                            action:@selector(slideAction)];

    self.navigationItem.rightBarButtonItems = @[
        [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemRefresh
                                                      target:self
                                                      action:@selector(fixHangAction)],
        [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemAction
                                                      target:self
                                                      action:@selector(noiseAction)],
        [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemBookmarks
                                                      target:self
                                                      action:@selector(paceSignalAction)]
    ];
    self.navigationController.navigationBar.layoutMargins = UIEdgeInsetsMake(0, 0, 0, -10);

    _textView = [[UITextView alloc] init];
    _textView.font = [UIFont systemFontOfSize:11];
    _textView.editable = NO;
    _textView.backgroundColor = [UIColor systemBackgroundColor];
    _textView.textColor = [UIColor labelColor];
    _textView.translatesAutoresizingMaskIntoConstraints = NO;
    _textView.showsVerticalScrollIndicator = YES;
    _textView.showsHorizontalScrollIndicator = YES;
    _textView.alwaysBounceVertical = YES;
    
    _textView.scrollEnabled = YES;
    [self.view addSubview:_textView];

    [NSLayoutConstraint activateConstraints:@[
        [_textView.topAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.topAnchor],
        [_textView.leadingAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.leadingAnchor constant:8],
        [_textView.trailingAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.trailingAnchor constant:-8],
        [_textView.bottomAnchor constraintEqualToAnchor:self.view.bottomAnchor]
    ]];

    self.hisAllowableButtonClockwiseSerbian = [BurstViewController exerciseFiberOperateSpeechBrowseTamil];

    [self stepFunk];
}

- (void)stepFunk {
    if (!self.hisAllowableButtonClockwiseSerbian) {
        _textView.text = appendApply.lazyPassiveTraverseScopeCoalescedVolatile;
        return;
    }

    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        NSString *logs;
        if (self.modeTalkDate) {
            logs = [self.hisAllowableButtonClockwiseSerbian skinBlurDryDate:self.modeTalkDate];
        } else {
            logs = [self.hisAllowableButtonClockwiseSerbian chatMargins];
        }

        dispatch_async(dispatch_get_main_queue(), ^{
            if (logs.length > 0) {
                self.textView.text = logs;
                
                [self.textView scrollRangeToVisible:NSMakeRange(logs.length - 1, 1)];
            } else {
                self.textView.text = appendApply.yellowHitMembersNotLowerGigabits;
            }

            [self qualityPick];
        });
    });
}

- (void)slideAction {
    [self dismissViewControllerAnimated:YES completion:nil];
}

- (void)fixHangAction {
    [self stepFunk];
}

- (void)qualityPick {
    if (self.modeTalkDate) {
        NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
        formatter.dateFormat = appendApply.yahooRegisterSolveNegateSobMidFeatures;
        NSString *ageComment = [formatter stringFromDate:self.modeTalkDate];

        NSCalendar *calendar = [NSCalendar currentCalendar];
        if ([calendar isDateInToday:self.modeTalkDate]) {
            self.title = appendApply.orderedActionJobTightNarrativePrevious;
        } else if ([calendar isDateInYesterday:self.modeTalkDate]) {
            self.title = appendApply.cancelledEarClampingRefusedAlcoholDeviationArea;
        } else {
            self.title = ageComment;
        }
    } else {
        self.title = appendApply.operandMismatchPersianWakePresetIncrement;
    }
}

- (void)paceSignalAction {
    if (!self.hisAllowableButtonClockwiseSerbian) {
        return;
    }

    NSArray<NSDate *> *availableDates = [self.hisAllowableButtonClockwiseSerbian tooEasyMood];
    if (availableDates.count == 0) {
        UIAlertController *music = [UIAlertController alertControllerWithTitle:appendApply.sameCellNegotiateHeightSocketElastic
                                                                       message:appendApply.yellowHitMembersNotLowerGigabits
                                                                preferredStyle:UIAlertControllerStyleAlert];
        [music addAction:[UIAlertAction actionWithTitle:appendApply.italicSurgeAppliesIllegalProgramSpeak style:UIAlertActionStyleDefault handler:nil]];
        [self presentViewController:music animated:YES completion:nil];
        return;
    }

    UIAlertController *actionSheet = [UIAlertController alertControllerWithTitle:appendApply.halftoneSixEyeDarkenMoleAddressExtras
                                                                         message:nil
                                                                  preferredStyle:UIAlertControllerStyleActionSheet];

    [actionSheet addAction:[UIAlertAction actionWithTitle:appendApply.operandMismatchPersianWakePresetIncrement
                                                    style:UIAlertActionStyleDefault
                                                  handler:^(UIAlertAction *action) {
        self.modeTalkDate = nil;
        [self stepFunk];
    }]];

    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    formatter.dateFormat = appendApply.yahooRegisterSolveNegateSobMidFeatures;

    NSCalendar *calendar = [NSCalendar currentCalendar];

    for (NSDate *date in availableDates) {
        NSString *title;
        if ([calendar isDateInToday:date]) {
            title = appendApply.orderedActionJobTightNarrativePrevious;
        } else if ([calendar isDateInYesterday:date]) {
            title = appendApply.cancelledEarClampingRefusedAlcoholDeviationArea;
        } else {
            title = [formatter stringFromDate:date];
        }

        [actionSheet addAction:[UIAlertAction actionWithTitle:title
                                                        style:UIAlertActionStyleDefault
                                                      handler:^(UIAlertAction *action) {
            self.modeTalkDate = date;
            [self stepFunk];
        }]];
    }

    [actionSheet addAction:[UIAlertAction actionWithTitle:appendApply.busHeartOptimizeSnapLossEgg style:UIAlertActionStyleCancel handler:nil]];

    if ([UIDevice currentDevice].userInterfaceIdiom == UIUserInterfaceIdiomPad) {
        actionSheet.popoverPresentationController.barButtonItem = self.navigationItem.rightBarButtonItems.lastObject;
    }

    [self presentViewController:actionSheet animated:YES completion:nil];
}

- (void)noiseAction {
    if (!self.hisAllowableButtonClockwiseSerbian) {
        return;
    }

    NSArray *pieceMin = [self.hisAllowableButtonClockwiseSerbian portalDatum];
    if (pieceMin.count == 0) {
        UIAlertController *music = [UIAlertController alertControllerWithTitle:appendApply.sameCellNegotiateHeightSocketElastic
                                                                       message:appendApply.lemmaFitVowelKitAddressMirrored
                                                                preferredStyle:UIAlertControllerStyleAlert];
        [music addAction:[UIAlertAction actionWithTitle:appendApply.italicSurgeAppliesIllegalProgramSpeak style:UIAlertActionStyleDefault handler:nil]];
        [self presentViewController:music animated:YES completion:nil];
        return;
    }

    UIAlertController *actionSheet = [UIAlertController alertControllerWithTitle:appendApply.doneVerySemanticsNegativeLatitudeOrdinalsHockey
                                                                         message:nil
                                                                  preferredStyle:UIAlertControllerStyleActionSheet];

    [actionSheet addAction:[UIAlertAction actionWithTitle:appendApply.internalRepeatSequencerNowSexualHuePint
                                                    style:UIAlertActionStyleDefault
                                                  handler:^(UIAlertAction *action) {
        [self yellowClient];
    }]];

    for (NSURL *fileURL in pieceMin) {
        NSString *fileName = fileURL.lastPathComponent;
        [actionSheet addAction:[UIAlertAction actionWithTitle:[NSString stringWithFormat:appendApply.semicolonSpectralPhaseStripFactorHasSay, fileName]
                                                        style:UIAlertActionStyleDefault
                                                      handler:^(UIAlertAction *action) {
            [self pressSonFile:fileURL];
        }]];
    }

    [actionSheet addAction:[UIAlertAction actionWithTitle:appendApply.busHeartOptimizeSnapLossEgg style:UIAlertActionStyleCancel handler:nil]];

    if ([UIDevice currentDevice].userInterfaceIdiom == UIUserInterfaceIdiomPad) {
        actionSheet.popoverPresentationController.barButtonItem = self.navigationItem.rightBarButtonItems.lastObject;
    }

    [self presentViewController:actionSheet animated:YES completion:nil];
}

- (void)yellowClient {
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        
        NSString *allLogs = [self.hisAllowableButtonClockwiseSerbian oneLossyOffWon];

        dispatch_async(dispatch_get_main_queue(), ^{
            if (allLogs.length > 0) {
                UIActivityViewController *swedishAge = [[UIActivityViewController alloc]
                                                       initWithActivityItems:@[allLogs]
                                                       applicationActivities:nil];

                if ([UIDevice currentDevice].userInterfaceIdiom == UIUserInterfaceIdiomPad) {
                    swedishAge.popoverPresentationController.barButtonItem = self.navigationItem.rightBarButtonItems.lastObject;
                }

                [self presentViewController:swedishAge animated:YES completion:nil];
            }
        });
    });
}

- (void)pressSonFile:(NSURL *)fileURL {
    UIActivityViewController *swedishAge = [[UIActivityViewController alloc]
                                           initWithActivityItems:@[fileURL]
                                           applicationActivities:nil];

    if ([UIDevice currentDevice].userInterfaceIdiom == UIUserInterfaceIdiomPad) {
        swedishAge.popoverPresentationController.barButtonItem = self.navigationItem.rightBarButtonItems.lastObject;
    }

    [self presentViewController:swedishAge animated:YES completion:nil];
}

@end
