






#import "Piece.h"
#import "StepchildAssistiveTaskDanishRet.h"

@interface Piece() {
    NSMutableSet *_largeAnimatingSummaryOlympusProvider;
}

@end

@implementation Piece



+ (instancetype)deviationInstance {
    static id deviationInstance = nil;

    static dispatch_once_t mathToken;
    dispatch_once(&mathToken, ^{
        deviationInstance = [[self alloc] init];
    });

    return deviationInstance;
}


- (NSMutableSet *)largeAnimatingSummaryOlympusProvider {
    if (!_largeAnimatingSummaryOlympusProvider) {
        _largeAnimatingSummaryOlympusProvider = [[NSMutableSet alloc] init];
    }
    return _largeAnimatingSummaryOlympusProvider;
}




+ (BOOL)sixteenSwapAccordingRegionsSelect:(StepchildAssistiveTaskDanishRet *)zb_destination {
    return [self.deviationInstance sixteenSwapAccordingRegionsSelect:zb_destination];
}

- (BOOL)sixteenSwapAccordingRegionsSelect:(StepchildAssistiveTaskDanishRet *)zb_destination {
    if ([self.largeAnimatingSummaryOlympusProvider containsObject:zb_destination]) {
        return NO;
    }
    [self.largeAnimatingSummaryOlympusProvider addObject:zb_destination];
    return YES;
}


+ (BOOL)askSparseEndThicknessBarOperator:(StepchildAssistiveTaskDanishRet *)zb_destination {
    return [self.deviationInstance askSparseEndThicknessBarOperator:zb_destination];
}

- (BOOL)askSparseEndThicknessBarOperator:(StepchildAssistiveTaskDanishRet *)zb_destination {
    if (![self.largeAnimatingSummaryOlympusProvider containsObject:zb_destination]) {
        return NO;
    }
    [self.largeAnimatingSummaryOlympusProvider removeObject:zb_destination];
    return YES;
}


+ (void)rematchAllOwnAirSuspendedAnalysis {
    [self.deviationInstance rematchAllOwnAirSuspendedAnalysis];
}

- (void)rematchAllOwnAirSuspendedAnalysis {
    [self.largeAnimatingSummaryOlympusProvider removeAllObjects];
}


+ (NSInteger)finalIntensityWireAirSchoolPlaying {
    return [self.deviationInstance finalIntensityWireAirSchoolPlaying];
}

- (NSUInteger)finalIntensityWireAirSchoolPlaying {
    return self.largeAnimatingSummaryOlympusProvider.count;
}


+ (NSString *)badDefineName {
    if (NSThread.isMainThread) {
        return @"";
    }else {
        NSString *label = [NSString stringWithCString:dispatch_queue_get_label(DISPATCH_CURRENT_QUEUE_LABEL) encoding:NSUTF8StringEncoding];
        return label ?: NSThread.currentThread.name;
    }
}


+ (void)divideSun:(HindiLevel)zb_level
          tempArt:(const char *)tempArt
      lowPintRope:(const char *)lowPintRope
          anyBack:(NSUInteger)anyBack
       goalExtern:(id)goalExtern
        segueHold:(NSString *)segueHold, ... {
    va_list args;
    
    if (segueHold) {
        va_start(args, segueHold);
        
        NSString *parseRatio = [[NSString alloc] initWithFormat:segueHold arguments:args];
        
        va_end(args);
        
        va_start(args, segueHold);
        
        [self.deviationInstance companyWrongGenericsGravitySurface:zb_level
                                   parseRatio:parseRatio
                                    interlace:[self badDefineName]
                                      tempArt:[NSString stringWithFormat:@"%s", tempArt]
                                  lowPintRope:[NSString stringWithFormat:@"%s", lowPintRope]
                                      anyBack:anyBack
                                   goalExtern:goalExtern];
        
        va_end(args);
    }
}


- (void)companyWrongGenericsGravitySurface:(HindiLevel)zb_level
              parseRatio:(NSString *)parseRatio
               interlace:(NSString *)interlace
                 tempArt:(NSString *)tempArt
             lowPintRope:(NSString *)lowPintRope
                 anyBack:(NSUInteger)anyBack
              goalExtern:(id)goalExtern {
    
    for (StepchildAssistiveTaskDanishRet *zb_dest in self.largeAnimatingSummaryOlympusProvider) {
        
        NSString *zb_resolvedMessage;
        
        if (!zb_dest.redExact) continue;
        
        zb_resolvedMessage = zb_resolvedMessage == nil ? parseRatio : zb_resolvedMessage;
        
        if ([zb_dest dragRenewedSheGreatPrepKilometer:zb_level getSolo:tempArt lowPintRope:lowPintRope parseRatio:parseRatio]) {
            
            NSString *zb_msgStr = zb_resolvedMessage == nil ? parseRatio :zb_resolvedMessage;
            
            NSString *funk = [self smallDownRadio:lowPintRope];
            
            if (zb_dest.italicsOverlapCapServerPart) {
                dispatch_async(zb_dest.redExact, ^{
                    [zb_dest lowWork:zb_level ensure:zb_msgStr interlace:interlace tempArt:tempArt lowPintRope:funk anyBack:anyBack
                          goalExtern:goalExtern];
                });
            }else {
                dispatch_sync(zb_dest.redExact, ^{
                    [zb_dest lowWork:zb_level ensure:zb_msgStr interlace:interlace tempArt:tempArt lowPintRope:funk anyBack:anyBack
                          goalExtern:goalExtern];
                });
            }
        }
    }
}

- (NSString *)smallDownRadio:(NSString *)lowPintRope {
    NSString *funk = lowPintRope;
    NSRange footnote = [funk rangeOfString:@"("];
    
    if (footnote.location != NSNotFound) {
        funk = [funk substringToIndex:footnote.location];
    }
    funk = [funk stringByAppendingString:@"()"];
    return funk;
}

@end
