ASAccountAuthenticationModificationController
UISheetPresentationControllerDetentIdentifier
AVAudioSessionSilenceSecondaryAudioHintType
UIScrollViewContentInsetAdjustmentBehavior
UIImagePickerControllerCameraCaptureMode
ASAdvertisingAttributionReportEndpoint
UIImagePickerControllerCameraFlashMode
NSURLSessionDelayedRequestDisposition
UINavigationItemLargeTitleDisplayMode
NSUbiquitousKeyValueStoreChangeReason
SFSpeechAudioBufferRecognitionRequest
NSURLSessionAuthChallengeDisposition
PKPaymentAuthorizationViewController
ATTrackingManagerAuthorizationStatus
UIAccessibilityCustomSystemRotorType
UICollectionViewCompositionalLayout
UIAccessibilityCustomRotorDirection
UIControlContentHorizontalAlignment
UIImagePickerControllerCameraDevice
AVQueuedSampleBufferRenderingStatus
UILocalNotificationDefaultSoundName
NSFileManagerItemReplacementOptions
UIImagePickerControllerQualityType
UICollectionViewItemHighlightState
UICollectionViewDiffableDataSource
UIVideoEditorControllerQualityType
NSMetadataQueryAttributeValueTuple
INUIAddVoiceShortcutViewController
UIControlContentVerticalAlignment
AVAudioSessionInterruptionOptions
UIImagePickerControllerSourceType
AVAssetImageGeneratorApertureMode
UNTimeIntervalNotificationTrigger
UISwipeGestureRecognizerDirection
CGDataProviderSequentialCallbacks
ASAuthorizationPasswordCredential
UIUserNotificationActivationMode
UICollectionViewLayoutAttributes
UICollectionViewTransitionLayout
UIUserNotificationActionBehavior
UIScreenEdgePanGestureRecognizer
NSNotificationSuspensionBehavior
MKLocalSearchCompleterResultType
ASAuthorizationAppleIDCredential
AVAudioSessionRouteSharingPolicy
UICollectionViewScrollDirection
NSURLSessionResponseDisposition
AVAudioSessionRouteChangeReason
NSPropertyListMutabilityOptions
ASAuthorizationPasswordProvider
UICollectionViewDropCoordinator
UISearchContainerViewController
UIPopoverPresentationController
AVCaptureVideoStabilizationMode
UIBandSelectionInteractionState
UINotificationFeedbackGenerator
NSDistributedNotificationCenter
UIUserNotificationActionContext
NSPersonNameComponentsFormatter
UIScrollViewKeyboardDismissMode
UINavigationBarNSToolbarSection
UICollectionViewScrollPosition
NSURLRequestNetworkServiceType
UIAccessibilityScrollDirection
UIUserInterfaceLayoutDirection
UIMenuControllerArrowDirection
MFMessageComposeViewController
ASAuthorizationAppleIDProvider
UIAccessibilityNavigationStyle
AVAudioSessionInterruptionType
UIViewKeyframeAnimationOptions
UICollectionElementKindSection
UNNotificationServiceExtension
PHAssetCollectionChangeRequest
QLPreviewControllerDataSource
SFSpeechURLRecognitionRequest
VNDetectTextRectanglesRequest
UNLocationNotificationTrigger
CGDataProviderDirectCallbacks
NSArrayBinarySearchingOptions
UITableViewAutomaticDimension
UITableViewCellSelectionStyle
UIViewControllerRepresentable
UNCalendarNotificationTrigger
AVAudioSessionCategoryOptions
VNDetectFaceRectanglesRequest
NSDirectoryEnumerationOptions
NSComparisonPredicateModifier
UICollectionViewCellDragState
CNContactPickerViewController
MKSearchCompletionFilterType
UISelectionFeedbackGenerator
UIAccessibilityContainerType
UIScrollViewIndexDisplayMode
UITextAutocapitalizationType
NSURLAuthenticationChallenge
SKStoreProductViewController
UIScrollViewDecelerationRate
UIScreenOverscanCompensation
NSComparisonPredicateOptions
AVVideoCompressionProperties
UNMutableNotificationContent
UITableViewCellAccessoryType
UIActivityIndicatorViewStyle
UILongPressGestureRecognizer
NSPersistentStoreCoordinator
UICollectionElementCategory
UITextSmartInsertDeleteType
SKProductSubscriptionPeriod
UIInterpolatingMotionEffect
HKStatisticsCollectionQuery
NSKeyedArchiveRootObjectKey
QLPreviewControllerDelegate
NSFileManagerUnmountOptions
UITableViewHeaderFooterView
MFMailComposeViewController
AVAssetImageGeneratorResult
UISwipeActionsConfiguration
UIRotationGestureRecognizer
UITableViewCellEditingStyle
UICollectionViewDragPreview
NSPropertyListSerialization
CAAnimationCalculationMode
UICollectionViewReloadItem
UICollectionViewDropIntent
ASWebAuthenticationSession
NSScriptCommandDescription
UIImageSymbolConfiguration
UISemanticContentAttribute
AVCaptureVideoPreviewLayer
AVAssetExportSessionStatus
GKGameCenterViewController
UITableViewDropCoordinator
CAEmitterLayerEmitterShape
NSKeyValueObservingOptions
UIInterfaceOrientationMask
NSUndoManagerGroupingLevel
NSDictionaryReadingOptions
NSDictionaryWritingOptions
UIBackgroundTaskIdentifier
CLBeaconIdentityConstraint
UICollectionViewController
NSStringEnumerationOptions
UICollectionViewFlowLayout
NSMetadataQueryResultGroup
MKStandardMapEmphasisStyle
NSPropertyListWriteOptions
UIListContentConfiguration
NSRegularExpressionOptions
UIScrollViewIndicatorStyle
NSURLCredentialPersistence
NSFetchedResultsController
CATextLayerTruncationMode
UINavigationBarAppearance
NSRelationshipDescription
INUIHostedViewControlling
NSNumberFormatterBehavior
PKAddPassesViewController
NSKeyValueSetMutationKind
NSLocaleLanguageDirection
MKAnnotationViewDragState
CAEmitterLayerEmitterMode
UIBackgroundConfiguration
CAMediaTimingFunctionName
NSPointerFunctionsOptions
UITableViewScrollPosition
ASAuthorizationController
AVPlayerTimeControlStatus
NSPropertyListReadOptions
UIViewAnimationTransition
UISegmentedControlSegment
UIImpactFeedbackGenerator
UITableViewSeparatorStyle
AVCaptureWhiteBalanceMode
SFSpeechRecognitionResult
NSProcessInfoThermalState
MKPointOfInterestCategory
UIBackgroundRefreshStatus
NSDateComponentsFormatter
UISearchDisplayController
MKDirectionsTransportType
NSCollectionLayoutSection
ASAuthorizationCredential
UILargeContentViewerStyle
NSMutableAttributedString
NSScriptExecutionContext
UIUserInterfaceSizeClass
UIGestureRecognizerState
MTLComputeCommandEncoder
UITextAutocorrectionType
NSHTTPCookieAcceptPolicy
UIPinchGestureRecognizer
UNNotificationAttachment
AVCaptureAutoFocusSystem
UITextInputPasswordRules
NSOperationQueuePriority
SKCloudServiceController
CAEmitterCellEmitterType
NSNotificationCoalescing
UIViewConfigurationState
UIEditMenuArrowDirection
UIViewTintAdjustmentMode
UISwipeGestureRecognizer
UIModalPresentationStyle
UISpringTimingParameters
UIRemoteNotificationType
CATextLayerAlignmentMode
CAEmitterLayerRenderMode
UNUserNotificationCenter
CGGradientDrawingOptions
UITableViewCellStateMask
CKModifyRecordsOperation
UIPresentationController
NSUserNotificationCenter
UICollectionReusableView
UICellConfigurationState
NSFileProviderEnumerator
GKAchievementDescription
VNSequenceRequestHandler
NSURLSessionDownloadTask
BGAppRefreshTaskRequest
SKReceiptRefreshRequest
UIDocumentSaveOperation
BGProcessingTaskRequest
UIPopoverArrowDirection
UITextSelectionAffinity
UICubicTimingParameters
UITableViewRowAnimation
NSURLRequestCachePolicy
CNContactViewController
UISegmentedControlStyle
SLComposeViewController
NSFileProviderExtension
AVCaptureDevicePosition
NSPredicateOperatorType
UIPointerEffectTintMode
UIActivityIndicatorView
CXProviderConfiguration
UIGraphicsImageRenderer
VNDetectBarcodesRequest
WKUserContentController
CAAnimationRotationMode
NSBundleResourceRequest
UIAccessibilityZoomType
CGDataConsumerCallbacks
AVPlayerActionAtItemEnd
MTLRenderCommandEncoder
UITextSpellCheckingType
CKFetchRecordsOperation
NSDateFormatterBehavior
NSCollectionLayoutGroup
MTLComputePipelineState
UISearchResultsUpdating
NSProcessInfoPowerState
UIStackViewDistribution
SFSpeechRecognitionTask
CAScrollLayerScrollMode
SNClassifySoundRequest
CALayerContentsGravity
WKScriptMessageHandler
NSNumberFormatterStyle
UIForceTouchCapability
CGPDFAccessPermissions
AVPlayerViewController
CGInterpolationQuality
CAEdgeAntialiasingMask
UIPrintInfoOrientation
UIAlertControllerStyle
NSURLSessionUploadTask
NSManagedObjectContext
UITapGestureRecognizer
CGImagePixelFormatInfo
NSStringCompareOptions
MTLRenderPipelineState
AVAudioSessionCategory
NSURLCredentialStorage
SNClassificationResult
UINavigationController
UIModalTransitionStyle
UITextStorageDirection
CLLocationCoordinate2D
UNNotificationCategory
NSCollectionLayoutItem
CGFontPostScriptFormat
CGColorRenderingIntent
UICollectionViewLayout
NSPersonNameComponents
UIViewAnimationOptions
UIContentConfiguration
UIViewPropertyAnimator
UITableViewDragPreview
NSAttributeDescription
SFTranscriptionSegment
UITextWritingDirection
UILayoutConstraintAxis
UIPanGestureRecognizer
AVCaptureSessionPreset
UIInterfaceOrientation
NSValueTransformerName
UIUserNotificationType
NSAppleEventDescriptor
WKWebViewConfiguration
ASAuthorizationRequest
UIApplicationDelegate
INVoiceShortcutCenter
NSTextCheckingOptions
NSComparisonPredicate
UIPrintJobDisposition
NSKeyValueObservation
NSLinguisticTagScheme
UNNotificationRequest
CAMediaTimingFillMode
UIBarButtonSystemItem
UNNotificationContent
PHVideoRequestOptions
HKAnchoredObjectQuery
VNImageRequestHandler
NSPersistentContainer
UISplitViewController
UIPrintInfoOutputType
PHImageRequestOptions
UIAccessibilityTraits
CAConstraintAttribute
SNAudioStreamAnalyzer
UNNotificationTrigger
UITableViewController
UITextSmartDashesType
NSURLSessionTaskState
UIButtonConfiguration
NSFileProviderManager
NSDirectoryEnumerator
NSInvocationOperation
CALayerContentsFormat
UITextLayoutDirection
PHCachingImageManager
NWAdvertiseDescriptor
AVCaptureExposureMode
UITextSmartQuotesType
NLContextualEmbedding
CAMediaTimingFunction
UIConfigurationState
UIImageRenderingMode
NSDateFormatterStyle
NSJSONReadingOptions
PHAssetChangeRequest
WKNavigationDelegate
UIKeyboardAppearance
NSNotificationCenter
UITableViewCellStyle
CAShapeLayerLineJoin
PKPaymentSummaryItem
UISceneConfiguration
UIViewAnimatingState
UIStackViewAlignment
NSByteCountFormatter
CGImageByteOrderInfo
UICollectionViewCell
CAShapeLayerFillRule
NSManagedObjectModel
UIUserInterfaceIdiom
NSFileProviderDomain
NSOrthographyOptions
QLThumbnailGenerator
SKPaymentTransaction
AVVideoCodecSettings
UNNotificationAction
NSJSONWritingOptions
UIPageViewController
UIBarButtonItemStyle
NSURLSessionDataTask
NSDataReadingOptions
UITableViewRowAction
NSDataWritingOptions
UIStatusBarAnimation
MKPinAnnotationColor
NLLanguageRecognizer
NSTextCheckingResult
HKCharacteristicType
CALayerMaskedCorners
UIViewAnimationCurve
UIImageConfiguration
NSPropertyListFormat
UIUserInterfaceStyle
NSURLProtectionSpace
UIDocumentChangeKind
UIFeedbackGenerator
NSNetServiceOptions
UIMotionEffectGroup
NSDistributedObject
SecAccessControlRef
NSCompoundPredicate
NSHTTPCookieStorage
MKFeatureVisibility
AVAssetWriterStatus
UIPopoverController
NSMutableOrderedSet
UIGestureRecognizer
CATransitionSubtype
IntentConfiguration
ASIdentifierManager
UIDeviceOrientation
CBPeripheralManager
CAShapeLayerLineCap
NSDataSearchOptions
NSTimeZoneNameStyle
UITextSelectionRect
AVVideoProfileLevel
CBAdvertisementData
UIProgressViewStyle
NSJSONSerialization
UIHostingController
NSMutableDictionary
MKPinAnnotationView
CAKeyframeAnimation
MKDistanceFormatter
UNNotificationSound
CurrentValueSubject
StaticConfiguration
UITextFieldViewMode
NSMutableURLRequest
NSNetServiceBrowser
NSLayoutYAxisAnchor
AVAssetReaderStatus
CAValueFunctionName
CMAccelerometerData
NSOrderedSetOptions
WidgetConfiguration
NSEntityDescription
NSLayoutXAxisAnchor
UIImageResizingMode
AVCaptureDeviceType
APActivationPayload
NSRegularExpression
NSAppleEventManager
NSCachedURLResponse
CAGradientLayerType
NSDataDetectorTypes
NSNotificationQueue
AVCaptureColorSpace
QLPreviewController
UIViewRepresentable
UIGraphicsRenderer
NSHashTableOptions
DCAppAttestService
UITabBarController
LPMetadataProvider
NSFileProviderItem
NSErrorUserInfoKey
UIVisualEffectView
AVCaptureFlashMode
NSLayoutConstraint
AVCaptureFocusMode
MKUserTrackingMode
NSAssertionHandler
UITabBarAppearance
CMMagnetometerData
UISegmentedControl
CXAnswerCallAction
WKWebsiteDataStore
NSUserNotification
NSValueTransformer
NSQualityOfService
OSLogEntrySignpost
UIAlertActionStyle
NSUserDefaultsType
NSPointerFunctions
UIImageOrientation
QLThumbnailRequest
UISearchController
CAAutoresizingMask
NSClassDescription
CNContactFormatter
NSExtensionContext
NSMigrationManager
SFSpeechRecognizer
AVPlayerItemStatus
UIStatusBarManager
NSNetServicesError
CALayerCornerCurve
UIApplicationState
MKLaunchOptionsKey
NSLinguisticTagger
AVCaptureTorchMode
AVAudioSessionMode
UITabBarSystemItem
UIViewAutoresizing
AVVideoComposition
NSComparisonResult
NSAttributedString
NSTextCheckingType
PassthroughSubject
UIContextualAction
CGImageDestination
UITraitEnvironment
NSPersistentStore
NSHTTPURLResponse
UITextGranularity
SKProductsRequest
CXStartCallAction
UIDropInteraction
NSMutableIndexSet
NSLengthFormatter
WKHTTPCookieStore
NSDistributedLock
NSKeyedUnarchiver
NSEnergyFormatter
CLLocationManager
NSCalendarOptions
HKStatisticsQuery
SecCertificateRef
CAReplicatorLayer
NSMethodSignature
CNContactProperty
HKCorrelationType
CMActivityManager
SKProductDiscount
VNFaceObservation
UIDatePickerStyle
UIPrintInfoDuplex
NSStringTransform
CGTextDrawingMode
NSMatchingOptions
UITextContentType
UIAlertController
UIDragInteraction
UIViewContentMode
UITextBorderStyle
NSLayoutDimension
SKPaymentDiscount
CGPathDrawingMode
UISystemAnimation
CNContactRelation
MKOverlayRenderer
NSProtocolChecker
UIPrinterJobTypes
NSMapTableOptions
UITextInputTraits
NSFileCoordinator
NSNumberFormatter
UITouchProperties
PHAssetCollection
UITraitCollection
ATTrackingManager
CGColorSpaceModel
CASpringAnimation
CXCallController
PKPaymentRequest
UIVibrancyEffect
NSBlockOperation
EKRecurrenceRule
CATransformLayer
CGImageAlphaInfo
MTLCommandBuffer
CAFrameRateRange
INIntentResponse
AVVideoFieldMode
AVCaptureSession
NSXMLParserError
AVKeyValueStatus
TimelineProvider
NSDateComponents
UIDatePickerMode
NSKeyValueChange
CBCharacteristic
UINavigationItem
HKQuantitySample
UISearchBarStyle
CBCentralManager
MKAnnotationView
GKTurnBasedMatch
BGProcessingTask
AVVideoCodecType
BGAppRefreshTask
CAAnimationGroup
MKMapFeatureType
UILayoutPriority
CNMutableContact
NSExpressionType
UIMenuController
CABasicAnimation
UIFontDescriptor
UIStatusBarStyle
UIViewController
UITableViewStyle
CATransitionType
PHCollectionList
UIRefreshControl
CKQueryOperation
NSOperationQueue
NSStringEncoding
CLCircularRegion
NSURLSessionTask
NSSortDescriptor
UICollectionView
CNPostalAddress
AVCaptureOutput
CXEndCallAction
UITextDirection
SFTranscription
NSMetadataQuery
NSScriptCommand
NSManagedObject
UTTypeReference
UIBarAppearance
NSURLCredential
NSExtensionItem
BGTaskScheduler
UIFontTextStyle
NSURLConnection
AVPlayerHDRMode
UISearchBarIcon
PKPaymentMethod
NSRecursiveLock
AVCaptureDevice
UIReturnKeyType
CMMotionManager
CGPDFObjectType
NSKeyedArchiver
NSExceptionName
CAGradientLayer
NSDateFormatter
NSConditionLock
APActivationURL
NSMassFormatter
UINavigationBar
CAValueFunction
NSPurgeableData
UIBarButtonItem
UIControlEvents
UISceneDelegate
UIDocumentState
NSLinguisticTag
NSUnitConverter
NSURLComponents
NSMutableString
CGPDFDataFormat
UITableViewCell
CGPatternTiling
MTLCommandQueue
NSMatchingFlags
AVAudioRecorder
NSDecimalNumber
LPLinkMetadata
NSURLQueryItem
NSUserActivity
UIDropProposal
NSDateInterval
EKCalendarItem
NSOutputStream
NSPostingStyle
UISceneSession
PHPhotoLibrary
UIMotionEffect
AVAudioSession
UIPointerShape
CNContactStore
UIPressesEvent
CGFunctionType
NSMutableArray
CGDataConsumer
NSDataDetector
CKUserIdentity
PHImageManager
MKOverlayLevel
HKQuantityType
AnyCancellable
NSPointerArray
CMRotationRate
NSNotification
CAEmitterLayer
NSHostNSStream
NSUserDefaults
PHFetchOptions
NSFetchRequest
NSItemProvider
CMAcceleration
SKPaymentQueue
CGTextEncoding
SecIdentityRef
CMDeviceMotion
AVCaptureInput
MKUserLocation
UIDisplayGamut
NSMappingModel
UITextPosition
NSLayoutAnchor
PKPaymentToken
UIProgressView
NSStreamStatus
UIKeyboardType
CLBeaconRegion
UIControlState
CGDataProvider
AVPlayerStatus
NSMetadataItem
HKCategoryType
UIEventSubtype
NSURLErrorCode
AVPlayerLooper
NSCalendarUnit
CAEmitterCell
GKAchievement
NSInputStream
NWPathMonitor
PKPassLibrary
NSURLProtocol
NSStreamEvent
UIWindowScene
VNObservation
UIBarPosition
UIFontMetrics
NSAppleScript
NSFileWrapper
CGMutablePath
HKSampleQuery
UIPageControl
NSMessagePort
NSSpellServer
MKTileOverlay
OSLogEntryLog
CAScrollLayer
UITextChecker
QLPreviewItem
NSURLResponse
GKLocalPlayer
CGPDFDocument
NSUndoManager
CAMediaTiming
NSOrthography
UISliderStyle
PHFetchResult
TimelineEntry
INInteraction
MKLocalSearch
GKLeaderboard
NSProcessInfo
BGTaskRequest
AVAudioEngine
UIAlertAction
UIApplication
ABAddressBook
UILayoutGuide
AVAudioPlayer
CNPhoneNumber
AVComposition
NSRunLoopMode
UIDragPreview
AVAudioFormat
HKWorkoutType
AVPlayerLayer
HKHealthStore
CMStepCounter
CGImageSource
UIDragSession
NSMutableData
NSFileManager
EKParticipant
CADisplayLink
CATransform3D
AnySubscriber
CGShadingType
NSMeasurement
UIActionSheet
NSCountedSet
CBDescriptor
UIBezierPath
CGBitmapInfo
WKUIDelegate
AnyPublisher
NSFileHandle
PKEraserTool
NSUnarchiver
CGColorSpace
UIEdgeInsets
CAShapeLayer
NSNetService
UIPressPhase
UITouchPhase
PKCanvasView
CKRecordZone
SecPolicyRef
NSCocoaError
NWParameters
UIDatePicker
UIRectCorner
NSInvocation
CATransition
NSMutableSet
CXCallAction
UIScrollView
NSPOSIXError
UIImageAsset
NSDictionary
UITabBarItem
MKDirections
AVPlayerItem
NSExpression
AnchorEntity
MKAnnotation
SecRandomRef
UIBarMetrics
WidgetFamily
NSURLRequest
NSOrderedSet
EKEventStore
WKUserScript
NSSocketPort
NSSetOptions
UIFontWeight
NSURLSession
PKToolPicker
CBPeripheral
NSConnection
NWConnection
UIButtonType
PHCollection
SKStorefront
UIBlurEffect
UIPickerView
NSHTTPCookie
PKInkingTool
GKMatchmaker
UIResponder
NLEmbedding
CATextLayer
NSMachError
NSSemaphore
NSPredicate
NSDimension
NSCondition
UITableView
ModelEntity
UIImageView
UITextInput
UIEventType
CMAltimeter
UISearchBar
UITextField
NLGazetteer
UIAlertView
NLTokenizer
NSFormatter
UITextRange
UIStackView
PKLassoTool
CAAnimation
NSHashTable
AVMediaType
MTLFunction
CKReference
UITouchType
SecTrustRef
MKPlacemark
CMPedometer
CKContainer
UIPressType
CGBlendMode
NSIndexPath
CLPlacemark
UIFontWidth
Cancellable
NSOperation
NSException
CMGyroData
UIDragItem
AVURLAsset
NSTimeZone
EKCalendar
NWListener
NSArchiver
EKReminder
NSIndexSet
CLGeocoder
UITextView
NSMachPort
UIBarStyle
LPLinkView
CXProvider
OSLogEntry
CMAttitude
MTLLibrary
MTLTexture
NSCalendar
CGFunction
MKPolyline
OSLogStore
NSURLCache
AnySubject
NSProgress
UIMenuItem
UTTagClass
NSMapTable
CKLocation
AVFileType
AVAudioMix
CLLocation
CGGradient
CGLineJoin
UIRectEdge
RealityKit
SKDownload
CKDatabase
NWEndpoint
CLHeading
CBService
PKDrawing
CGPattern
HKWorkout
NSScanner
CGLineCap
CGContext
MKOverlay
MTLBuffer
PKPayment
SKPayment
MKMapItem
CNContact
SLRequest
CGPDFPage
MKPolygon
MKMapType
SKProduct
VNRequest
CGShading
UIStepper
PKContact
UIToolbar
UIBarItem
SecKeyRef
MTLDevice
NSRunLoop
MKMapView
NWBrowser
WKWebView
UIWebView
UIControl
CKRecord
PKStroke
UISwitch
EKSource
HKSample
MKCircle
UITabBar
AVPlayer
NSBundle
UIWindow
UIScreen
CLBeacon
NSLocale
GKPlayer
ABSource
NSThread
ABRecord
NSNumber
DCDevice
PHChange
CLRegion
UIButton
NSString
UIOffset
UISlider
NLTagger
CGPDFBox
NSObject
INIntent
ABPerson
NSStream
ABGroup
CKShare
NSCache
HKQuery
CGColor
NSProxy
NSError
AVAsset
CKAsset
NLModel
DCError
UIEvent
CGImage
UIScene
CALayer
MKRoute
NSTimer
NSCoder
UITouch
CKQuery
NSArray
UILabel
PHAsset
EKAlarm
UIColor
NSValue
CLVisit
CGLayer
GKMatch
GKScore
UIImage
UIPress
EKEvent
PKTool
Entity
BGTask
NSUUID
ARView
CBUUID
NSPort
NWPath
NSPipe
CXCall
NSNull
NSUnit
PKPass
NSTask
CGPath
UIView
UIFont
NSData
NSLock
NSHost
CGFont
NSDate
UTType
PKInk
OSLog
NSSet
NSURL