UICollectionViewLayoutAttributes
UICollectionViewTransitionLayout
UIScreenEdgePanGestureRecognizer
NSDistributedNotificationCenter
NSPersonNameComponentsFormatter
UIPopoverPresentationController
UINotificationFeedbackGenerator
MFMessageComposeViewController
UILongPressGestureRecognizer
UISelectionFeedbackGenerator
NSPersistentStoreCoordinator
SKStoreProductViewController
NSPropertyListSerialization
UITableViewHeaderFooterView
UIRotationGestureRecognizer
UIInterpolatingMotionEffect
MFMailComposeViewController
UICollectionViewController
UICollectionViewFlowLayout
NSFetchedResultsController
AVCaptureVideoPreviewLayer
NSMutableAttributedString
NSDateComponentsFormatter
UISearchDisplayController
UINavigationBarAppearance
UIImpactFeedbackGenerator
NSRelationshipDescription
NSURLSessionDownloadTask
NSUserNotificationCenter
UIPresentationController
UICollectionReusableView
UIPinchGestureRecognizer
UISwipeGestureRecognizer
UISpringTimingParameters
UNUserNotificationCenter
UIActivityIndicatorView
UIGraphicsImageRenderer
UICubicTimingParameters
SLComposeViewController
NSURLSessionUploadTask
NSPersonNameComponents
UINavigationController
UICollectionViewLayout
UITapGestureRecognizer
UIPanGestureRecognizer
UIViewPropertyAnimator
NSManagedObjectContext
NSAttributeDescription
AVPlayerViewController
NSDirectoryEnumerator
NSInvocationOperation
NSKeyValueObservation
NSComparisonPredicate
UISplitViewController
UITableViewController
UIApplicationDelegate
CAMediaTimingFunction
NSPersistentContainer
UNNotificationRequest
UNNotificationContent
NSURLSessionDataTask
NSNotificationCenter
NSTextCheckingResult
NSByteCountFormatter
UIPageViewController
UICollectionViewCell
UIViewAnimatingState
UISceneConfiguration
NSManagedObjectModel
SKPaymentTransaction
NSMutableDictionary
NSMutableOrderedSet
NSMutableURLRequest
NSHTTPCookieStorage
NSNotificationQueue
NSJSONSerialization
NSRegularExpression
NSNetServiceBrowser
NSCompoundPredicate
UIPopoverController
UIPopoverController
UIGestureRecognizer
UITextSelectionRect
UIMotionEffectGroup
UIFeedbackGenerator
CAKeyframeAnimation
NSEntityDescription
MKPinAnnotationView
NSAttributedString
NSPointerFunctions
NSAssertionHandler
NSExtensionContext
NSUserNotification
UITabBarController
UISegmentedControl
UISearchController
UIGraphicsRenderer
UIStatusBarManager
UITabBarAppearance
UIVisualEffectView
CGImageDestination
NSMigrationManager
AVVideoComposition
CNContactFormatter
NSNumberFormatter
NSHTTPURLResponse
NSFileCoordinator
NSDistributedLock
NSKeyedUnarchiver
NSLengthFormatter
NSEnergyFormatter
NSMutableIndexSet
UIAlertController
UITextInputTraits
UIDragInteraction
UIDropInteraction
CAReplicatorLayer
CASpringAnimation
NSPersistentStore
MKOverlayRenderer
SKProductsRequest
PHAssetCollection
NSDateComponents
NSURLSessionTask
NSOperationQueue
NSBlockOperation
NSSortDescriptor
UIViewController
UIRefreshControl
UICollectionView
UINavigationItem
UIMenuController
UIFontDescriptor
UIMenuController
UIVibrancyEffect
CATransformLayer
CABasicAnimation
CAAnimationGroup
AVCaptureSession
MKAnnotationView
HKQuantitySample
NSMutableString
NSPurgeableData
NSDecimalNumber
NSDateFormatter
NSURLComponents
NSURLConnection
NSConditionLock
NSRecursiveLock
NSKeyedArchiver
NSMassFormatter
NSUnitConverter
NSExtensionItem
UITableViewCell
UINavigationBar
UIBarButtonItem
UISceneDelegate
UIBarAppearance
CAGradientLayer
CAValueFunction
NSManagedObject
AVAudioRecorder
AVCaptureDevice
AVCaptureOutput
NSMutableArray
NSPointerArray
NSDateInterval
NSURLQueryItem
NSNotification
NSDataDetector
NSUserDefaults
NSUserActivity
NSItemProvider
NSOutputStream
UIProgressView
UISceneSession
UITextPosition
UIDropProposal
UIMotionEffect
CGDataProvider
CGDataConsumer
CAEmitterLayer
NSFetchRequest
NSMappingModel
AVAudioSession
AVCaptureInput
MKUserLocation
SKPaymentQueue
CNContactStore
PHPhotoLibrary
PHImageManager
NSMutableData
NSURLResponse
NSFileManager
NSFileWrapper
NSProcessInfo
NSMeasurement
NSInputStream
NSMessagePort
UIPageControl
UIActionSheet
UIAlertAction
UIApplication
UIWindowScene
UITextChecker
UIDragPreview
UIDragSession
CGMutablePath
CGImageSource
CGPDFDocument
CAScrollLayer
CAEmitterCell
CADisplayLink
CAMediaTiming
AVPlayerLayer
AVAudioPlayer
AVAudioEngine
AVComposition
MKTileOverlay
MKLocalSearch
GKLocalPlayer
GKAchievement
GKLeaderboard
ABAddressBook
HKHealthStore
NSDictionary
NSMutableSet
NSCountedSet
NSOrderedSet
NSURLRequest
NSURLSession
NSHTTPCookie
NSFileHandle
NSUnarchiver
NSNetService
NSSocketPort
NSExpression
UIPickerView
UIDatePicker
UIScrollView
UITabBarItem
UIImageAsset
UIBezierPath
UIBlurEffect
CGColorSpace
CAShapeLayer
CATransition
AVPlayerItem
MKAnnotation
MKDirections
EKEventStore
CKRecordZone
NSHashTable
NSException
NSOperation
NSCondition
NSSemaphore
NSFormatter
NSDimension
NSPredicate
NSIndexPath
UIImageView
UITextField
UISearchBar
UIStackView
UITableView
UIAlertView
UITextInput
UITextRange
CATextLayer
CAAnimation
MKPlacemark
CKContainer
NSMapTable
NSCalendar
NSTimeZone
NSURLCache
NSArchiver
NSProgress
NSMachPort
NSIndexSet
UITextView
UIMenuItem
UIDragItem
CGGradient
CGFunction
AVURLAsset
AVAudioMix
MKPolyline
EKCalendar
EKReminder
CKDatabase
NSRunLoop
NSScanner
UIStepper
UIWebView
WKWebView
UIToolbar
CGContext
CGShading
CGPattern
CGPDFPage
MKMapView
MKMapItem
MKPolygon
MKOverlay
SKProduct
SKPayment
SLRequest
CNContact
HKWorkout
NSObject
NSString
NSNumber
NSLocale
NSBundle
NSThread
NSStream
UIWindow
UIScreen
UIButton
UISlider
UISwitch
UITabBar
AVPlayer
MKCircle
ABPerson
ABRecord
HKSample
CKRecord
NSProxy
NSArray
NSValue
NSTimer
NSError
NSCoder
UILabel
UIImage
UIColor
UIScene
CGImage
CGColor
CGLayer
CALayer
AVAsset
MKRoute
GKScore
EKEvent
PHAsset
NSData
NSNull
NSDate
NSPipe
NSTask
NSLock
NSUnit
NSHost
NSPort
NSUUID
UIView
UIFont
CGPath
CGFont
NSSet
NSURL