#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 block 类型是否能通过 pointer_types 正确处理
"""

import sys
import os

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

def test_block_types():
    """测试 block 类型处理"""
    
    # 模拟必要的依赖
    class MockEPrintAnnotation:
        @staticmethod
        def search_annotation_and_delete(content):
            return content
        
        @staticmethod
        def search_print_and_delete(content):
            return content
    
    # 创建模拟模块
    sys.modules['ObjectiveC.oc_function.e_print_annotation'] = MockEPrintAnnotation()
    sys.modules['ObjectiveC.oc_custom.custom_util'] = type('MockUtil', (), {'list_system_pointer_types': []})()
    
    # 导入优化后的模块
    import custom_core
    
    # 测试包含各种 block 参数的代码，包括新添加的类型
    test_content = '''
    @implementation TestClass

    - (void)methodWithCompletion:(void(^)(NSError *completionError))completion {
        // completion block parameter
        completion(nil);
    }

    - (void)methodWithHandler:(NSURLSessionDataTaskCompletionHandler)completionHandler {
        // NSURLSessionDataTaskCompletionHandler type
        completionHandler(nil, nil, nil);
    }

    - (void)methodWithAccessor:(void(^)(NSString *accessorParam))accessor
                    byAccessor:(void(^)(UIView *view, NSString *viewParam))byAccessor {
        // multiple block parameters
        accessor(@"test");
        byAccessor(nil, @"view");
    }

    - (void)localVariablesMethod {
        NSString *localVar = @"test";
        BOOL flag = YES;

        // 长类型名变量
        UIApplicationStateRestorationManager *restorationManager = nil;
        NSURLSessionDataTaskCompletionHandler completionHandler = nil;

        void(^customBlock)(NSString *) = ^(NSString *blockParam) {
            NSLog(@"Block param: %@", blockParam);
            int blockVar = 10;
        };
    }

    @end
    '''
    
    print("=== 测试 Block 类型处理 ===")
    print(f"测试内容:")
    print(test_content)
    
    # 检查 pointer_types 中是否包含 block 相关类型
    pointer_types = custom_core.get_pointer_types()
    block_types = [t for t in pointer_types if 'handler' in t.lower() or 'completion' in t.lower() or 'accessor' in t.lower()]
    
    print(f"\\npointer_types 中的 block 相关类型:")
    for bt in block_types[:10]:  # 只显示前10个
        print(f"  {bt}")
    if len(block_types) > 10:
        print(f"  ... 还有 {len(block_types) - 10} 个")
    
    # 测试局部变量提取
    print(f"\\n局部变量提取结果:")
    local_vars = custom_core.extract_variables_unified(test_content, extract_types=['local'])
    print(f"  提取到的变量数: {len(local_vars)}")
    print(f"  变量列表: {local_vars}")
    
    # 检查是否包含预期的变量
    expected_vars = [
        'completionError', 'accessorParam', 'viewParam', 'blockParam',  # block 参数
        'localVar', 'flag', 'blockVar',  # 普通局部变量
        'restorationManager', 'completionHandler'  # 长类型名变量
    ]
    
    found_vars = [var for var in expected_vars if var in local_vars]
    missing_vars = [var for var in expected_vars if var not in local_vars]

    print(f"\\n预期的变量:")
    print(f"  找到: {found_vars}")
    if missing_vars:
        print(f"  缺失: {missing_vars}")

    print(f"\\n测试结果: {'✅ 成功' if len(missing_vars) == 0 else '❌ 部分失败'}")

    # 特别检查长类型名是否被正确识别
    long_types = ['UIApplicationStateRestorationManager', 'NSURLSessionDataTaskCompletionHandler']
    found_long_types = [t for t in long_types if t in pointer_types]
    print(f"\\n长类型名检查:")
    print(f"  在 pointer_types 中找到: {found_long_types}")
    print(f"  缺失: {[t for t in long_types if t not in found_long_types]}")

if __name__ == "__main__":
    test_block_types()
