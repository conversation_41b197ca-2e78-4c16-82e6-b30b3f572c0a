#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面测试：验证是否恢复了所有重要的匹配逻辑
"""

import sys
import os

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

def test_comprehensive_patterns():
    """全面测试各种复杂的变量声明模式"""
    
    # 模拟必要的依赖
    class MockEPrintAnnotation:
        @staticmethod
        def search_annotation_and_delete(content):
            return content
        
        @staticmethod
        def search_print_and_delete(content):
            return content
    
    # 创建模拟模块
    sys.modules['ObjectiveC.oc_function.e_print_annotation'] = MockEPrintAnnotation()
    sys.modules['ObjectiveC.oc_custom.custom_util'] = type('MockUtil', (), {'list_system_pointer_types': []})()
    
    # 导入优化后的模块
    import custom_core
    
    # 创建包含各种复杂模式的测试内容
    test_content = '''
    @interface TestClass : NSObject
    @end
    
    @implementation TestClass
    
    - (void)complexLocalVariablesMethod {
        // 基本类型
        NSString *localVar = @"test";
        NSArray *array = [[NSArray alloc] init];
        BOOL flag = YES;
        NSInteger counter = 0;
        CGFloat value = 3.14f;
        unsigned int unsignedVar = 10;
        signed long signedVar = -5;
        
        // 不带赋值的声明
        UIView *view;
        NSError *error;
        NSMutableDictionary *dict;
        
        // 方法调用赋值
        NSString *result = [self someMethod];
        UILabel *label = [[UILabel alloc] init];
        NSData *data = [NSData dataWithContentsOfFile:@"path"];
        
        // 属性访问赋值
        NSString *title = self.titleLabel.text;
        CGRect frame = self.view.frame;
        
        // 复杂初始化
        NSMutableArray *mutableArray = [[NSMutableArray alloc] initWithCapacity:10];
        UIViewController *controller = [[UIViewController alloc] initWithNibName:nil bundle:nil];
        
        // 错误处理
        NSString *content = [NSString stringWithContentsOfFile:@"path" encoding:NSUTF8StringEncoding error:&error];
        BOOL success = [self performOperationWithOptions:options error:&error];
        
        // 泛型
        NSArray<NSString *> *stringArray = @[];
        NSMutableDictionary<NSString *, NSNumber *> *numberDict = [[NSMutableDictionary alloc] init];
        
        // __block 变量
        __block NSInteger blockCounter = 0;
        __block NSString *blockString = @"block";
        __block UIView *blockView = nil;
        
        // Block 声明和使用
        void(^testBlock)(NSString *) = ^(NSString *param) {
            NSLog(@"Block param: %@", param);
            int blockVar = 10;
            NSMutableArray *blockArray = [[NSMutableArray alloc] init];
            CGFloat blockValue = 2.5f;
        };
        
        // for 循环
        for (NSInteger i = 0; i < 10; i++) {
            NSString *loopVar = [NSString stringWithFormat:@"item%ld", i];
            UIView *loopView = [[UIView alloc] init];
        }
        
        // for-in 循环
        for (NSString *item in array) {
            NSLog(@"Item: %@", item);
        }
        
        for (UIView *subview in self.view.subviews) {
            // subview processing
        }
        
        // 长类型名
        UIApplicationStateRestorationManager *restorationManager = nil;
        NSURLSessionDataTaskCompletionHandler completionHandler = nil;
        
        // 复杂方法调用
        NSString *processedString = [self processString:@"input" withOptions:options error:&error];
    }
    
    + (void)classMethodWithStaticVars {
        static NSString *staticString = @"static";
        static const BOOL staticFlag = YES;
        static NSMutableArray *staticArray = nil;
        static UIView *staticView;
    }
    
    - (void)methodWithCompletion:(void(^)(NSError *completionError))completion {
        // completion block parameter
        completion(nil);
    }
    
    - (void)methodWithAccessor:(void(^)(NSString *accessorParam))accessor 
                    byAccessor:(void(^)(UIView *view, NSString *viewParam))byAccessor {
        // multiple block parameters
    }
    
    @end
    
    // 静态变量
    static NSString *globalStaticString = @"global";
    static const NSInteger globalStaticNumber = 42;
    static NSMutableDictionary *globalStaticDict = nil;
    static UIViewController *globalStaticController;
    
    // 外部常量
    extern NSString *externString;
    extern const NSInteger externNumber;
    extern UIView *externView;
    extern NSArray<NSString *> *externStringArray;
    '''
    
    print("=== 全面测试各种变量声明模式 ===")
    print(f"测试内容大小: {len(test_content)} 字符")
    print(f"测试内容行数: {len(test_content.split('\\n'))} 行")
    
    # 测试不同类型的提取
    test_cases = [
        ('local', ['local']),
        ('static', ['static']),
        ('const', ['const']),
        ('mixed', ['local', 'static', 'const'])
    ]
    
    for test_name, extract_types in test_cases:
        print(f"\\n{test_name.upper()} 类型提取:")
        
        result = custom_core.extract_variables_unified(test_content, extract_types)
        
        print(f"  提取变量数: {len(result)}")
        print(f"  变量列表: {result}")
        
        # 分析提取结果
        if test_name == 'local':
            expected_patterns = [
                'localVar', 'array', 'flag', 'counter', 'value',  # 基本类型
                'view', 'error', 'dict',  # 不带赋值
                'result', 'label', 'data',  # 方法调用
                'title', 'frame',  # 属性访问
                'mutableArray', 'controller',  # 复杂初始化
                'content', 'success',  # 错误处理
                'stringArray', 'numberDict',  # 泛型
                'blockCounter', 'blockString', 'blockView',  # __block
                'blockVar', 'blockArray', 'blockValue',  # block 内变量
                'loopVar', 'loopView',  # for 循环
                'item', 'subview',  # for-in 循环
                'unsignedVar', 'signedVar',  # unsigned/signed
            ]
            
            found_patterns = [p for p in expected_patterns if p in result]
            missing_patterns = [p for p in expected_patterns if p not in result]
            
            print(f"  找到的预期模式: {len(found_patterns)}/{len(expected_patterns)}")
            if missing_patterns:
                print(f"  缺失的模式: {missing_patterns}")
        
        elif test_name == 'static':
            expected_static = ['staticString', 'staticFlag', 'staticArray', 'staticView', 
                             'globalStaticString', 'globalStaticNumber', 'globalStaticDict', 'globalStaticController']
            found_static = [s for s in expected_static if s in result]
            print(f"  找到的静态变量: {len(found_static)}/{len(expected_static)}")
            
        elif test_name == 'const':
            expected_const = ['externString', 'externNumber', 'externView', 'externStringArray']
            found_const = [c for c in expected_const if c in result]
            print(f"  找到的常量: {len(found_const)}/{len(expected_const)}")
    
    print("\\n=== 测试完成 ===")

if __name__ == "__main__":
    test_comprehensive_patterns()
