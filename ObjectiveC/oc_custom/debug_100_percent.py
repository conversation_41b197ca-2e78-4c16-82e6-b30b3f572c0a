#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试脚本：分析为什么局部变量提取不是100%
"""

import sys
import os
import re

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

def debug_100_percent():
    """调试100%提取问题"""
    
    # 模拟必要的依赖
    class MockEPrintAnnotation:
        @staticmethod
        def search_annotation_and_delete(content):
            return content
        
        @staticmethod
        def search_print_and_delete(content):
            return content
    
    # 创建模拟模块
    sys.modules['ObjectiveC.oc_function.e_print_annotation'] = MockEPrintAnnotation()
    sys.modules['ObjectiveC.oc_custom.custom_util'] = type('MockUtil', (), {'list_system_pointer_types': []})()
    
    # 导入优化后的模块
    import custom_core
    
    # 正确格式的测试内容（使用真正的换行符）
    test_content = """@interface TestClass : NSObject
@end

@implementation TestClass

- (void)complexLocalVariablesMethod {
    // 基本类型
    NSString *localVar = @"test";
    NSArray *array = [[NSArray alloc] init];
    BOOL flag = YES;
    NSInteger counter = 0;
    CGFloat value = 3.14f;
    unsigned int unsignedVar = 10;
    signed long signedVar = -5;
    
    // 不带赋值的声明
    UIView *view;
    NSError *error;
    NSMutableDictionary *dict;
    
    // 方法调用赋值
    NSString *result = [self someMethod];
    UILabel *label = [[UILabel alloc] init];
    NSData *data = [NSData dataWithContentsOfFile:@"path"];
    
    // 属性访问赋值
    NSString *title = self.titleLabel.text;
    CGRect frame = self.view.frame;
    
    // 复杂初始化
    NSMutableArray *mutableArray = [[NSMutableArray alloc] initWithCapacity:10];
    UIViewController *controller = [[UIViewController alloc] initWithNibName:nil bundle:nil];
    
    // 错误处理
    NSString *content = [NSString stringWithContentsOfFile:@"path" encoding:NSUTF8StringEncoding error:&error];
    BOOL success = [self performOperationWithOptions:options error:&error];
    
    // 泛型
    NSArray<NSString *> *stringArray = @[];
    NSMutableDictionary<NSString *, NSNumber *> *numberDict = [[NSMutableDictionary alloc] init];
    
    // __block 变量
    __block NSInteger blockCounter = 0;
    __block NSString *blockString = @"block";
    __block UIView *blockView = nil;
    
    // Block 声明和使用
    void(^testBlock)(NSString *) = ^(NSString *param) {
        NSLog(@"Block param: %@", param);
        int blockVar = 10;
        NSMutableArray *blockArray = [[NSMutableArray alloc] init];
        CGFloat blockValue = 2.5f;
    };
    
    // for 循环
    for (NSInteger i = 0; i < 10; i++) {
        NSString *loopVar = [NSString stringWithFormat:@"item%ld", i];
        UIView *loopView = [[UIView alloc] init];
    }
    
    // for-in 循环
    for (NSString *item in array) {
        NSLog(@"Item: %@", item);
    }
    
    for (UIView *subview in self.view.subviews) {
        // subview processing
    }
    
    // 长类型名
    UIApplicationStateRestorationManager *restorationManager = nil;
    NSURLSessionDataTaskCompletionHandler completionHandler = nil;
    
    // 复杂方法调用
    NSString *processedString = [self processString:@"input" withOptions:options error:&error];
}

+ (void)classMethodWithStaticVars {
    static NSString *staticString = @"static";
    static const BOOL staticFlag = YES;
    static NSMutableArray *staticArray = nil;
    static UIView *staticView;
}

- (void)methodWithCompletion:(void(^)(NSError *completionError))completion {
    // completion block parameter
    completion(nil);
}

- (void)methodWithAccessor:(void(^)(NSString *accessorParam))accessor 
                byAccessor:(void(^)(UIView *view, NSString *viewParam))byAccessor {
    // multiple block parameters
}

@end

// 静态变量
static NSString *globalStaticString = @"global";
static const NSInteger globalStaticNumber = 42;
static NSMutableDictionary *globalStaticDict = nil;
static UIViewController *globalStaticController;

// 外部常量
extern NSString *externString;
extern const NSInteger externNumber;
extern UIView *externView;
extern NSArray<NSString *> *externStringArray;
"""
    
    print("=== 调试100%提取问题 ===")
    print(f"测试内容大小: {len(test_content)} 字符")
    print(f"测试内容行数: {len(test_content.split(chr(10)))} 行")
    
    # 预期的局部变量列表
    expected_local_vars = [
        # 基本类型
        'localVar', 'array', 'flag', 'counter', 'value', 'unsignedVar', 'signedVar',
        # 不带赋值的声明
        'view', 'error', 'dict',
        # 方法调用赋值
        'result', 'label', 'data',
        # 属性访问赋值
        'title', 'frame',
        # 复杂初始化
        'mutableArray', 'controller',
        # 错误处理
        'content', 'success',
        # 泛型
        'stringArray', 'numberDict',
        # __block 变量
        'blockCounter', 'blockString', 'blockView',
        # Block 内变量
        'param', 'blockVar', 'blockArray', 'blockValue',
        # for 循环
        'i', 'loopVar', 'loopView',
        # for-in 循环
        'item', 'subview',
        # 长类型名
        'restorationManager', 'completionHandler',
        # 复杂方法调用
        'processedString',
        # 方法签名中的 block 参数
        'completionError', 'accessorParam', 'viewParam'
    ]
    
    print(f"\\n预期局部变量数: {len(expected_local_vars)}")
    print(f"预期变量列表: {expected_local_vars}")
    
    # 实际提取结果
    actual_result = custom_core.extract_variables_unified(test_content, extract_types=['local'])
    print(f"\\n实际提取变量数: {len(actual_result)}")
    print(f"实际变量列表: {actual_result}")
    
    # 分析差异
    found_vars = [var for var in expected_local_vars if var in actual_result]
    missing_vars = [var for var in expected_local_vars if var not in actual_result]
    extra_vars = [var for var in actual_result if var not in expected_local_vars]
    
    print(f"\\n=== 分析结果 ===")
    print(f"找到的变量: {len(found_vars)}/{len(expected_local_vars)} ({len(found_vars)/len(expected_local_vars)*100:.1f}%)")
    print(f"找到的变量: {found_vars}")
    
    if missing_vars:
        print(f"\\n缺失的变量 ({len(missing_vars)}个): {missing_vars}")
    
    if extra_vars:
        print(f"\\n额外的变量 ({len(extra_vars)}个): {extra_vars}")
    
    # 分类分析缺失的变量
    if missing_vars:
        print(f"\\n=== 缺失变量分类分析 ===")
        
        # 按类型分类
        basic_types = ['flag', 'counter', 'value', 'unsignedVar', 'signedVar']
        pointer_types = ['localVar', 'array', 'view', 'error', 'dict', 'result', 'label', 'data', 
                        'title', 'mutableArray', 'controller', 'content', 'stringArray', 'numberDict',
                        'blockString', 'blockView', 'blockArray', 'loopVar', 'loopView', 
                        'restorationManager', 'completionHandler', 'processedString']
        block_vars = ['param', 'blockVar', 'blockValue']
        block_modifiers = ['blockCounter']
        loop_vars = ['i', 'item', 'subview']
        method_sig_vars = ['completionError', 'accessorParam', 'viewParam']
        
        categories = [
            ('基本类型', basic_types),
            ('指针类型', pointer_types),
            ('Block内变量', block_vars),
            ('__block变量', block_modifiers),
            ('循环变量', loop_vars),
            ('方法签名Block参数', method_sig_vars)
        ]
        
        for category_name, category_vars in categories:
            missing_in_category = [var for var in missing_vars if var in category_vars]
            if missing_in_category:
                print(f"  {category_name}: {missing_in_category}")
    
    print(f"\\n=== 调试完成 ===")
    return len(found_vars) == len(expected_local_vars)

if __name__ == "__main__":
    success = debug_100_percent()
    print(f"\\n{'✅ 100% 提取成功!' if success else '❌ 需要修复提取逻辑'}")
