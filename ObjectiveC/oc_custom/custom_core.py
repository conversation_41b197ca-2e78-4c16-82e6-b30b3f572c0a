"""
核心处理模块：提供文件内容处理的核心功能
"""
import os
import re
import yaml
from ObjectiveC.oc_custom import custom_util
from ObjectiveC.oc_function import e_print_annotation
from ObjectiveC.oc_custom import objc_types_config

def clean_content(content):
    """
    统一的内容清理函数：去注释、去打印、去空字符串

    Args:
        content (str): 原始文件内容

    Returns:
        str: 清理后的内容
    """
    # 去注释和打印
    content = e_print_annotation.search_annotation_and_delete(content)
    content = e_print_annotation.search_print_and_delete(content)
    content = content.replace('@""','')
    return content

def get_pointer_types():
    """
    统一的 pointer_types 获取函数
    优先使用 objc_types_config，备用 custom_util，最后使用内置列表

    Returns:
        list: 指针类型列表，按长度降序排列
    """
    try:
        # 方式1：使用 objc_types_config
        pointer_types = objc_types_config.get_all_pointer_types()

        # # 尝试保存到配置文件（如果目录存在）
        # try:
        #     save_system_pointer_types_path = os.getcwd() + '/配置文件/ObjectiveC配置文件/' + 'system_pointer_types.txt'
        #     # 确保目录存在
        #     os.makedirs(os.path.dirname(save_system_pointer_types_path), exist_ok=True)
        #     # pointer_types 按照字符长到小排序，按行写进 save_system_pointer_types_path
        #     sorted_types = sorted(pointer_types, key=lambda x: len(x), reverse=True)
        #     with open(save_system_pointer_types_path, 'w', encoding='utf-8') as f:
        #         f.write('\n'.join(sorted_types))
        # except (OSError, IOError):
        #     # 如果无法写入文件，继续使用内存中的类型列表
        #     pass

        return sorted(pointer_types, key=lambda x: len(x), reverse=True)

    except ImportError:
        # 方式2：使用 custom_util.list_system_pointer_types
        try:
            pointer_types = custom_util.list_system_pointer_types
            if pointer_types:
                return sorted(pointer_types, key=lambda x: len(x), reverse=True)
        except (AttributeError, NameError):
            pass

    # 方式3：备用类型列表
    backup_types = [
        'NSString', 'NSArray', 'NSDictionary', 'NSMutableArray', 'NSMutableDictionary',
        'NSError', 'NSData', 'NSMutableData', 'NSDate', 'NSURL', 'NSNumber',
        'UIView', 'UILabel', 'UIButton', 'UIImageView', 'UITextField', 'UITextView',
        'UITableView', 'UICollectionView', 'UIScrollView', 'UIViewController',
        'UIImage', 'UIColor', 'UIFont', 'CALayer', 'NSObject', 'NSValue',
        'NSSet', 'NSMutableSet', 'NSOrderedSet', 'NSMutableOrderedSet',
        'UIWindow', 'UINavigationController', 'UITabBarController',
        # 添加常见的长类型名
        'ATTrackingManagerAuthorizationStatus', 'UIUserInterfaceIdiom', 'UIDeviceOrientation',
        'NSURLSessionAuthChallengeDisposition', 'NSURLCredentialPersistence',
        'NSFileCoordinator', 'NSFileHandle'
    ]
    return sorted(backup_types, key=lambda x: len(x), reverse=True)

def get_describe_words():
    """
    获取属性和方法的描述词列表
    这些描述词用于清理属性声明和方法声明中的可用性标注

    Returns:
        list: 描述词列表
    """
    try:
        yaml_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "oc_function", "a_yaml.yaml")
        with open(yaml_path, 'r', encoding='utf-8') as yaml_file:
            yaml_data = yaml.load(yaml_file.read(), Loader=yaml.FullLoader)
            if yaml_data and isinstance(yaml_data, dict) and 'PropertyDescribeWord' in yaml_data:
                return yaml_data['PropertyDescribeWord']
    except Exception as e:
        print(f"读取描述词列表时出错: {str(e)}")

    # 设置一些常见的描述词作为备用
    return [
        "__OSX_AVAILABLE_STARTING", "__OSX_AVAILABLE_BUT_DEPRECATED",
        "NS_AVAILABLE", "NS_DEPRECATED", "API_AVAILABLE", "API_UNAVAILABLE",
        "NS_SWIFT_NAME", "__TVOS_AVAILABLE", "__WATCHOS_AVAILABLE",
        "NS_DESIGNATED_INITIALIZER", "NS_UNAVAILABLE", "DEPRECATED_ATTRIBUTE",
        "__deprecated", "__attribute__", "CF_RETURNS_RETAINED", "CF_RETURNS_NOT_RETAINED"
    ]

def process_property_content(content):
    """
    专门用于处理和提取属性内容的函数

    Args:
        content (str): 原始文件内容

    Returns:
        tuple: (processed_content, unique_properties, property_custom_accessors)
    """
    # 统一的内容清理
    content = clean_content(content)

    # 获取描述词列表
    property_describe_words = get_describe_words()

    # 预处理属性声明行，根据是否有*号采用不同的空格处理策略
    processed_lines = []
    
    for line in content.split('\n'):
        if '@property' in line:
            # 保存原始行，用于最终确认是否处理正确
            original_line = line
            
            # 处理@property行
            # 分割可能的描述词后缀
            for word in property_describe_words:
                if word in line:
                    line = line.split(word)[0]
                    break
                    
            # 确保分号结尾
            if not line.rstrip().endswith(';'):
                line = line.rstrip() + ';'
            
            # 简化处理：删除所有尖括号<>及其中的内容
            # 先处理嵌套的泛型情况，例如 NSArray<NSArray<NSString *> *>
            while '<' in line and '>' in line:
                start = line.find('<')
                end = line.rfind('>')
                if start < end:
                    line = line[:start] + line[end + 1:]
                else:
                    # 防止不匹配的情况导致死循环
                    break
            
            # 首先判断是否为Block类型属性，然后再判断是否带*号
            if '^' in line or '(^' in line:
                # 带^号的Block类型属性：需要特殊处理以保留结构
                # 1. 处理@property(...)部分
                prop_decl_match = re.search(r'(@property\s*\([^)]*\))', line)
                if prop_decl_match:
                    prop_decl = prop_decl_match.group(1)
                    clean_prop_decl = re.sub(r'\s+', '', prop_decl)
                    line = line.replace(prop_decl, clean_prop_decl)
                
                # 2. 标准化^符号周围的空格
                line = re.sub(r'\(\s*\^\s*', '(^ ', line)
                
                # 3. 保留Block名称和参数列表之间的结构
                line = re.sub(r'\)\s*\(', ')(', line)
                
                # 4. 去除其他多余的空格，但保留关键结构
                line = re.sub(r'\s+', ' ', line)
                line = re.sub(r'\s+;', ';', line)
            elif '*' in line:
                # 带*号的属性：可以去除所有空格，只保留*号周围的空格
                # 1. 标记*号位置
                line = line.replace('*', ' * ')
                
                # 2. 处理@property(...)部分
                prop_decl_match = re.search(r'(@property\s*\([^)]*\))', line)
                if prop_decl_match:
                    prop_decl = prop_decl_match.group(1)
                    clean_prop_decl = re.sub(r'\s+', '', prop_decl)
                    line = line.replace(prop_decl, clean_prop_decl)
                
                # 3. 去除类型名称和*号之间的空格
                line = re.sub(r'(\w+)\s+\*', r'\1 *', line)
                
                # 4. 去除其他多余的空格
                line = re.sub(r'\s+', ' ', line)
                line = re.sub(r'\(\s+', '(', line)
                line = re.sub(r'\s+\)', ')', line)
                line = re.sub(r'\s+;', ';', line)
            else:
                # 不带*号和^号的属性：需要保留类型和属性名之间的空格
                # 1. 处理@property(...)部分，去除声明内的空格
                prop_decl_match = re.search(r'(@property\s*\([^)]*\))', line)
                if prop_decl_match:
                    prop_decl = prop_decl_match.group(1)
                    clean_prop_decl = re.sub(r'\s+', '', prop_decl)
                    
                    # 2. 提取属性声明后的部分（类型和属性名）
                    remaining = line[line.index(prop_decl) + len(prop_decl):].strip()
                    
                    # 3. 检测类型和属性名
                    # 常见基本类型列表
                    basic_types = ['BOOL', 'CGFloat', 'NSInteger', 'NSUInteger', 'int', 'float', 'double', 'NSTimeInterval']
                    
                    # 尝试匹配基本类型
                    found_type = False
                    for type_name in basic_types:
                        if remaining.startswith(type_name):
                            # 基本类型，如：@property(...) BOOL isEnabled;
                            type_part = type_name
                            name_part = remaining[len(type_name):].strip()
                            # 重构为干净的格式：@property(...) TYPE NAME;
                            line = f"{clean_prop_decl} {type_part} {name_part.split(';')[0]};"
                            found_type = True
                            break
                    
                    if not found_type:
                        # 尝试根据空格分割识别自定义类型
                        parts = remaining.split()
                        if len(parts) >= 2:
                            # 自定义类型，如：@property(...) CustomType name;
                            type_part = parts[0]
                            name_part = ' '.join(parts[1:]).split(';')[0]
                            line = f"{clean_prop_decl} {type_part} {name_part};"
                        else:
                            # 无法通过空格分割，可能是无空格格式如 TransactionStatusxxpk_status
                            # 尝试基于首字母大小写分割
                            type_name_match = re.search(r'([A-Z][a-zA-Z0-9]+)([a-z][a-zA-Z0-9_]*)', remaining)
                            if type_name_match:
                                type_part = type_name_match.group(1)
                                name_part = type_name_match.group(2).split(';')[0]
                                line = f"{clean_prop_decl} {type_part} {name_part};"
        
        processed_lines.append(line)
    
    content = '\n'.join(processed_lines)
    
    # 由于已经标准化了属性声明，现在可以使用更简化的正则表达式
    property_patterns = [
        # 匹配带*号的普通属性: @property(...) Type * propertyName;
        r'@property\([^)]*\)[^*;]*\*\s*(\w+);',
        
        # 匹配不带*号的属性: @property(...) Type propertyName;
        r'@property\([^)]*\)\s+\w+\s+(\w+);',
        
        # 匹配Block类型的属性: @property(...) returnType(^blockName)(...);
        r'@property\([^)]*\)[^(]*\(\s*\^\s*(\w+)\s*\)(?:\([^)]*\))?;',
    ]
    
    # 存储属性定义和自定义setter/getter
    property_custom_accessors = {}
    
    # 提取属性自定义setter/getter
    for line in content.split('\n'):
        if '@property' in line:
            # 提取属性名
            prop_match = None
            for pattern in property_patterns:
                match = re.search(pattern, line)
                if match:
                    prop_match = match.group(1)
                    break
            
            if not prop_match:
                continue
                
            # 检查自定义setter和getter
            custom_setter = re.search(r'setter\s*=\s*(\w+)', line)
            custom_getter = re.search(r'getter\s*=\s*(\w+)', line)
            
            setter_name = custom_setter.group(1) if custom_setter else None
            getter_name = custom_getter.group(1) if custom_getter else None
            
            property_custom_accessors[prop_match] = (setter_name, getter_name)
    
    # 应用所有正则表达式模式来提取属性名
    all_properties = []
    for pattern in property_patterns:
        matches = re.findall(pattern, content)
        all_properties.extend([match for match in matches if match])
    
    # 去重处理
    unique_properties = set(all_properties)
    
    # 排序：不带下划线的排在前面，带下划线的排在后面
    sorted_properties = sorted(unique_properties, key=lambda x: (x.startswith('_'), x))
    
    return content, sorted_properties, property_custom_accessors 

def process_instance_variables_content(content):
    """
    专门用于处理和提取实例变量内容的函数
    
    Args:
        content (str): 原始文件内容
        
    Returns:
        list: 排序后的实例变量列表
    """
    # 统一的内容清理
    content = clean_content(content)
    
    # 匹配类扩展中的实例变量定义块
    # 优化正则表达式以匹配更多格式的类扩展声明，包括带有协议的声明
    ivar_block_pattern = r'@interface\s+\w+\s*(?:\(\s*\w*\s*\))?\s*(?:<[^>]*>)?\s*\{([^}]*)\}'
    blocks = re.findall(ivar_block_pattern, content, re.DOTALL)
    
    all_ivars = []
    
    # 处理每个实例变量块
    for block in blocks:
        # 分割成多行处理
        lines = block.strip().split('\n')
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # 移除注释部分
            line = re.sub(r'//.*$', '', line)
            
            # 修改正则表达式，确保只匹配完整的变量名，而不是变量名中的一部分
            # 例如：BOOL xxpk_serviceButton; 或 CGPoint _xxpk_originalPosition;
            type_var_pattern = r'(?:[\w*]+\s+)+([\w]+(?:_[\w]+)*)\s*;'
            type_var_matches = re.findall(type_var_pattern, line)
            all_ivars.extend(type_var_matches)
            
            # 如果已经通过类型匹配找到，就不再进行其他匹配了
            if type_var_matches:
                continue
                
            # 匹配常见的实例变量声明模式（不含类型或类型被前面的正则捕获）
            # 1. 基本模式：变量名;
            # 2. 带下划线的模式：_变量名;
            # 3. 多变量声明：变量1, 变量2, 变量3;
            var_patterns = [
                r'\b([\w]+(?:_[\w]+)*)\s*;',  # 简单变量名
                r'([\w]+(?:_[\w]+)*)\s*,',   # 多变量声明中的变量名
            ]
            
            for pattern in var_patterns:
                matches = re.findall(pattern, line)
                all_ivars.extend(matches)

    # 去重处理
    unique_ivars = list(set(all_ivars))
    
    # 排序：不带下划线的排在前面，带下划线的排在后面
    sorted_ivars = sorted(unique_ivars, key=lambda x: (x.startswith('_'), x))

    return sorted_ivars

def process_local_variables_content(content):
    """
    专门用于处理和提取方法内局部变量的函数
    现在使用统一的 extract_variables_unified 函数，避免代码重复

    Args:
        content (str): 原始文件内容

    Returns:
        list: 排序后的局部变量列表
    """
    # 直接使用统一的变量提取函数，指定提取 local 类型
    return extract_variables_unified(content, extract_types=['local'])



def extract_variables_unified(content, extract_types=['static']):
    """
    统一的变量提取函数，支持常量、局部变量、block变量等
    性能优化版本：预编译正则表达式，简化匹配逻辑
    优化：避免匹配属性声明，整合block处理逻辑到local中

    Args:
        content (str): 原始文件内容
        extract_types (list): 要提取的类型 ['static', 'local', 'const']
                              注意：'block' 类型已整合到 'local' 中处理

    Returns:
        list: 排序后的变量列表
    """
    if not content or not extract_types:
        return []

    # 统一的内容清理
    content = clean_content(content)

    # 快速预处理：移除属性声明部分，避免误匹配
    content = re.sub(r'@property\s*\([^)]*\)[^;]*;', '', content, flags=re.MULTILINE)
    # 更精确地移除 @interface 声明，避免误删方法体
    content = re.sub(r'@interface\s+\w+[^@]*?@end', '', content, flags=re.DOTALL)
    # 移除单独的 @interface 行（不包含实例变量的情况）
    content = re.sub(r'@interface\s+\w+[^{@\n]*\n', '', content, flags=re.MULTILINE)

    all_variables = []

    # 根据类型选择不同的优化策略
    if 'local' in extract_types:
        all_variables.extend(_extract_local_variables_fast(content))

    if 'static' in extract_types:
        all_variables.extend(_extract_static_variables_fast(content))

    if 'const' in extract_types:
        all_variables.extend(_extract_const_variables_fast(content))

    # 快速去重和过滤
    return _filter_and_sort_variables(all_variables, 'local' in extract_types)


# 预编译的正则表达式（模块级别，避免重复编译）
# 分模块组织，便于维护和扩展

# 1. 基础匹配模式
_BASIC_PATTERNS = {
    'method_body': re.compile(r'[-\+]\s*\([^)]*\)\s*[^{]*\{([^{}]*(?:\{[^{}]*\}[^{}]*)*)\}', re.DOTALL),
    'block_content': re.compile(r'\{([^{}]*(?:\{[^{}]*\}[^{}]*)*)\}', re.DOTALL),
    'variable_name': re.compile(r'^[a-zA-Z_][a-zA-Z0-9_]*$'),
    'method_start': re.compile(r'^\s*[-\+]\s*\('),
}

# 2. 局部变量匹配模式
_LOCAL_PATTERNS = {
    'basic_types': re.compile(r'(?:BOOL|NSInteger|NSUInteger|CGFloat|int|float|double|long|short|char|size_t|NSTimeInterval|CGPoint|CGSize|CGRect|NSRange|UIEdgeInsets)\s+(\w+)\s*[=;]'),
    'pointer_types': re.compile(r'([A-Z]\w+)\s*\*\s*(\w+)\s*[=;]'),
    'block_param': re.compile(r'\^[^{]*\(\s*\w+\s+(\w+)\s*\)'),
    'block_var': re.compile(r'__block\s+\w+\s+(\w+)\s*='),
    'for_loop': re.compile(r'for\s*\(\s*(?:NSInteger|NSUInteger|int)\s+(\w+)\s*='),

    # 额外的 block 内部参数模式
    'block_internal_pointer': re.compile(r'\^[^{]*\(\s*\w+\s*\*\s*(\w+)\s*\)'),
    'block_internal_type': re.compile(r'\^[^{]*\(\s*[A-Z]\w+\s+(\w+)\s*\)'),

    # 补充模式：处理更多变量声明情况
    'pointer_no_assign': re.compile(r'([A-Z]\w+)\s*\*\s*(\w+)\s*;'),
    'pointer_method_call': re.compile(r'([A-Z]\w+)\s*\*\s*(\w+)\s*=\s*\['),
    'pointer_property': re.compile(r'([A-Z]\w+)\s*\*\s*(\w+)\s*=\s*\w+\.'),
    'basic_no_assign': re.compile(r'(?:BOOL|NSInteger|NSUInteger|CGFloat|int|float|double|long|short|char|size_t|NSTimeInterval|CGPoint|CGSize|CGRect|NSRange|UIEdgeInsets)\s+(\w+)\s*;'),
    'complex_init': re.compile(r'(\w+)\s*\*\s*(\w+)\s*=\s*\[\['),
    'error_param': re.compile(r'error:\s*&\s*(\w+)'),
    'generic_pointer': re.compile(r'([A-Z]\w+)\s*<[^>]*>\s*\*\s*(\w+)\s*='),

    # 复杂匹配模式
    'complex_method_call': re.compile(r'(\w+)\s*\*\s*(\w+)\s*=\s*\[\w+\s+\w+[^;]*error:\s*&\w+[^;]*\]'),
    'method_options': re.compile(r':\s*(\w+)\s+options:'),
    'method_error': re.compile(r':\s*(\w+)\s+error:'),
    'long_type_method': re.compile(r'([A-Z][a-zA-Z0-9_]{10,})\s+(\w+)\s*=\s*\['),
    'long_type_general': re.compile(r'([A-Z]\w{10,})\s+(\w+)\s*='),
    'basic_method_call': re.compile(r'([A-Z]\w+)\s+(\w+)\s*=\s*\['),
    'unsigned_signed': re.compile(r'(?:unsigned|signed)\s+(?:int|long|short|char)\s+(\w+)\s*[=;]'),

    # for 循环补充模式
    'for_nsuinteger': re.compile(r'for\s*\(\s*NSUInteger\s+(\w+)\s*='),
    'for_in_loop': re.compile(r'for\s*\(\s*(\w+)\s+\*\s*(\w+)\s+in\s+'),

    # 方法签名中的 block 参数
    'method_sig_void_pointer': re.compile(r'\(void\s*\(\s*\^\s*\)\s*\([^)]*\*\s*(\w+)\s*\)\s*\)'),
    'method_sig_void_type': re.compile(r'\(void\s*\(\s*\^\s*\)\s*\([^)]*\s+(\w+)\s*\)\s*\)'),
    'method_sig_return_pointer': re.compile(r'\(\s*\w+\s*\(\s*\^\s*\)\s*\([^)]*\*\s*(\w+)\s*\)\s*\)'),
    'method_sig_return_type': re.compile(r'\(\s*\w+\s*\(\s*\^\s*\)\s*\([^)]*\s+(\w+)\s*\)\s*\)'),

    # block 内的变量声明
    'block_var_pointer': re.compile(r'\^[^{]*\{[^}]*?(\w+)\s*\*\s*(\w+)\s*='),
    'block_var_basic': re.compile(r'\^[^{]*\{[^}]*?(?:NSInteger|NSUInteger|CGFloat|BOOL|int|float|double|id)\s+(\w+)\s*='),
    'block_var_simple': re.compile(r'(?:\^|\{)[^}]*?(\w+)\s+(\w+)\s*='),
}

# 3. 静态变量匹配模式
_STATIC_PATTERNS = {
    'basic': re.compile(r'static\s+\w+\s+(\w+)\s*[;=]'),
    'pointer': re.compile(r'static\s+\w+\s*\*\s*(\w+)\s*[;=]'),
    'const_basic': re.compile(r'static\s+const\s+\w+\s+(\w+)\s*[;=]'),
}

# 4. 常量变量匹配模式
_CONST_PATTERNS = {
    'extern_basic': re.compile(r'extern\s+\w+\s+(\w+)\s*[;=]'),
    'extern_pointer': re.compile(r'extern\s+\w+\s*\*\s*(\w+)\s*[;=]'),
    'extern_const': re.compile(r'extern\s+const\s+\w+\s*\*?\s*(\w+)\s*[;=]'),
}

# 5. 宏定义匹配模式
_DEFINE_PATTERNS = {
    'multiline': re.compile(r'#define\s+(\w+)(?:\([^)]*\))?\s+[^\\]*(?:\\\s*\n[^\\]*)*', re.MULTILINE),
    'function_macro': re.compile(r'#define\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\([^)]*\)\s+.*'),
    'string_macro': re.compile(r'#define\s+([a-zA-Z_][a-zA-Z0-9_]*)\s+@?"[^"]*"'),
    'number_macro': re.compile(r'#define\s+([a-zA-Z_][a-zA-Z0-9_]*)\s+[-+]?\d+(?:\.\d+)?[fFlL]?(?:\s|$)'),
    'paren_expr': re.compile(r'#define\s+([a-zA-Z_][a-zA-Z0-9_]*)\s+\([^)]+\)'),
    'bit_operation': re.compile(r'#define\s+([a-zA-Z_][a-zA-Z0-9_]*)\s+\([^)]*<<[^)]*\)'),
    'conditional': re.compile(r'#define\s+([a-zA-Z_][a-zA-Z0-9_]*)\s+.*\?.*:.*'),
    'type_cast': re.compile(r'#define\s+([a-zA-Z_][a-zA-Z0-9_]*)\s+\(\([^)]+\).*\)'),
    'function_call': re.compile(r'#define\s+([a-zA-Z_][a-zA-Z0-9_]*)\s+[a-zA-Z_][a-zA-Z0-9_]*\([^)]*\)'),
    'compound_expr': re.compile(r'#define\s+([a-zA-Z_][a-zA-Z0-9_]*)\s+.*[+\-*/].*'),
    'array_struct': re.compile(r'#define\s+([a-zA-Z_][a-zA-Z0-9_]*)\s+\{[^}]*\}'),
    'special_symbol': re.compile(r'#define\s+([a-zA-Z_][a-zA-Z0-9_]*)\s+[^\w\s].*'),
    'attribute': re.compile(r'#define\s+([a-zA-Z_][a-zA-Z0-9_]*)\s+__[a-zA-Z_]+.*'),
    'stringify': re.compile(r'#define\s+([a-zA-Z_][a-zA-Z0-9_]*)\s+#[a-zA-Z_].*'),
    'concatenate': re.compile(r'#define\s+([a-zA-Z_][a-zA-Z0-9_]*)\s+.*##.*'),
    'multi_identifier': re.compile(r'#define\s+([a-zA-Z_][a-zA-Z0-9_]*)\s+[a-zA-Z_][a-zA-Z0-9_.]*'),
    'empty': re.compile(r'#define\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*$'),
    'basic_value': re.compile(r'#define\s+([a-zA-Z_][a-zA-Z0-9_]*)\s+\S.*'),
    'general': re.compile(r'#define\s+([a-zA-Z_][a-zA-Z0-9_]*)(?:\s.*)?'),
}

# 6. 合并所有模式字典（向后兼容）
_COMPILED_PATTERNS = {
    **_BASIC_PATTERNS,
    **{f'local_{k}': v for k, v in _LOCAL_PATTERNS.items()},
    **{f'static_{k}': v for k, v in _STATIC_PATTERNS.items()},
    **{f'const_{k}': v for k, v in _CONST_PATTERNS.items()},
    **{f'define_{k}': v for k, v in _DEFINE_PATTERNS.items()},
}


def _extract_local_variables_fast(content):
    """快速提取局部变量（增强版，包含更多匹配模式）"""
    variables = []

    # 获取 pointer_types 用于更精确的类型匹配
    pointer_types = get_pointer_types()

    # 1. 快速提取方法体内容（使用预编译模式）
    method_bodies = _COMPILED_PATTERNS['method_body'].findall(content)

    # 同时提取所有 block 内容（处理嵌套情况）
    all_blocks = _COMPILED_PATTERNS['block_content'].findall(content)

    # 合并方法体和 block 内容
    all_code_blocks = method_bodies + all_blocks

    for body in all_code_blocks:
        # 预处理：移除静态变量声明，避免局部变量提取包含静态变量
        # 使用正则表达式移除 static 声明行
        filtered_body = re.sub(r'^\s*static\s+[^;]+;', '', body, flags=re.MULTILINE)
        # 同时移除多行的 static 声明
        filtered_body = re.sub(r'static\s+[^=;]+[=;][^;]*;', '', filtered_body)

        # 基本类型变量
        variables.extend(_COMPILED_PATTERNS['local_basic_types'].findall(filtered_body))

        # 指针类型变量（取变量名部分）
        pointer_matches = _COMPILED_PATTERNS['local_pointer_types'].findall(filtered_body)
        variables.extend([match[1] for match in pointer_matches if len(match) >= 2])

        # Block 参数（包括 block 内部的参数）
        variables.extend(_COMPILED_PATTERNS['local_block_param'].findall(filtered_body))

        # 额外的 block 内部参数模式（使用预编译模式）
        variables.extend(_COMPILED_PATTERNS['local_block_internal_pointer'].findall(filtered_body))
        variables.extend(_COMPILED_PATTERNS['local_block_internal_type'].findall(filtered_body))

        # __block 变量
        variables.extend(_COMPILED_PATTERNS['local_block_var'].findall(filtered_body))

        # 补充模式：处理更多变量声明情况（使用预编译模式）
        # 不带赋值的指针声明
        pointer_matches = _COMPILED_PATTERNS['local_pointer_no_assign'].findall(filtered_body)
        variables.extend([match[1] for match in pointer_matches if len(match) >= 2])

        # 方法调用赋值
        method_matches = _COMPILED_PATTERNS['local_pointer_method_call'].findall(filtered_body)
        variables.extend([match[1] for match in method_matches if len(match) >= 2])

        # 属性访问赋值
        property_matches = _COMPILED_PATTERNS['local_pointer_property'].findall(filtered_body)
        variables.extend([match[1] for match in property_matches if len(match) >= 2])

        # 基本类型不带赋值
        variables.extend(_COMPILED_PATTERNS['local_basic_no_assign'].findall(filtered_body))

        # 复杂初始化
        init_matches = _COMPILED_PATTERNS['local_complex_init'].findall(filtered_body)
        variables.extend([match[1] for match in init_matches if len(match) >= 2])

        # 错误参数
        variables.extend(_COMPILED_PATTERNS['local_error_param'].findall(filtered_body))

        # 泛型指针
        generic_matches = _COMPILED_PATTERNS['local_generic_pointer'].findall(filtered_body)
        variables.extend([match[1] for match in generic_matches if len(match) >= 2])

        # 复杂匹配模式
        complex_matches = _COMPILED_PATTERNS['local_complex_method_call'].findall(filtered_body)
        variables.extend([match[1] for match in complex_matches if len(match) >= 2])

        # 方法参数中的变量
        variables.extend(_COMPILED_PATTERNS['local_method_options'].findall(filtered_body))
        variables.extend(_COMPILED_PATTERNS['local_method_error'].findall(filtered_body))

        # 长类型名声明
        long_method_matches = _COMPILED_PATTERNS['local_long_type_method'].findall(filtered_body)
        variables.extend([match[1] for match in long_method_matches if len(match) >= 2])

        long_general_matches = _COMPILED_PATTERNS['local_long_type_general'].findall(filtered_body)
        variables.extend([match[1] for match in long_general_matches if len(match) >= 2])

        # 基本类型方法调用
        basic_method_matches = _COMPILED_PATTERNS['local_basic_method_call'].findall(filtered_body)
        variables.extend([match[1] for match in basic_method_matches if len(match) >= 2])

        # unsigned/signed 修饰的类型
        variables.extend(_COMPILED_PATTERNS['local_unsigned_signed'].findall(filtered_body))

        # 基于 pointer_types 的精确匹配（恢复原有的完整模式）
        for ptype in pointer_types:
            # 普通指针：NSString *varName =
            pattern = rf'{ptype}\s*\*\s*(\w+)\s*='
            variables.extend(re.findall(pattern, filtered_body))

            # 不带赋值的声明：PointerType *varName;
            pattern = rf'{ptype}\s*\*\s*(\w+)\s*;'
            variables.extend(re.findall(pattern, filtered_body))

            # 方法调用赋值：PointerType *varName = [method];
            pattern = rf'{ptype}\s*\*\s*(\w+)\s*=\s*\['
            variables.extend(re.findall(pattern, filtered_body))

            # 属性访问：PointerType *varName = obj.property;
            pattern = rf'{ptype}\s*\*\s*(\w+)\s*=\s*\w+\.'
            variables.extend(re.findall(pattern, filtered_body))

            # 泛型指针：NSArray<Type *> *varName = 或 NSArray <Type *>* varName =
            pattern = rf'{ptype}\s*<[^>]*>\s*\*\s*(\w+)\s*='
            variables.extend(re.findall(pattern, filtered_body))

            # 恢复原有的基于 pointer_types 的更多模式
            # 基本类型声明（不带星号）：PointerType varName = [method];
            pattern = rf'{ptype}\s+(\w+)\s*=\s*\['
            variables.extend(re.findall(pattern, filtered_body))

            # 复杂初始化：PointerType *varName = [[Class alloc] init...];
            pattern = rf'{ptype}\s*\*\s*(\w+)\s*=\s*\[\['
            variables.extend(re.findall(pattern, filtered_body))

            # 错误处理模式：PointerType *varName = [method error:&error];
            pattern = rf'{ptype}\s*\*\s*(\w+)\s*=\s*\[.*error:'
            variables.extend(re.findall(pattern, filtered_body))

    # 2. for 循环变量（在整个内容中查找，使用预编译模式）
    # 对整个内容应用静态变量过滤
    filtered_content = re.sub(r'^\s*static\s+[^;]+;', '', content, flags=re.MULTILINE)
    filtered_content = re.sub(r'static\s+[^=;]+[=;][^;]*;', '', filtered_content)

    variables.extend(_COMPILED_PATTERNS['local_for_loop'].findall(filtered_content))
    variables.extend(_COMPILED_PATTERNS['local_for_nsuinteger'].findall(filtered_content))

    # for-in 循环变量
    for_in_matches = _COMPILED_PATTERNS['local_for_in_loop'].findall(filtered_content)
    variables.extend([match[1] for match in for_in_matches if len(match) >= 2])

    # 3. 提取方法签名中的 block 参数（使用预编译模式）
    variables.extend(_COMPILED_PATTERNS['local_method_sig_void_pointer'].findall(filtered_content))
    variables.extend(_COMPILED_PATTERNS['local_method_sig_void_type'].findall(filtered_content))
    variables.extend(_COMPILED_PATTERNS['local_method_sig_return_pointer'].findall(filtered_content))
    variables.extend(_COMPILED_PATTERNS['local_method_sig_return_type'].findall(filtered_content))

    # 4. 匹配 block 内的变量声明（使用预编译模式）
    # block内的指针变量声明
    block_pointer_matches = _COMPILED_PATTERNS['local_block_var_pointer'].findall(filtered_content)
    variables.extend([match[1] for match in block_pointer_matches if len(match) >= 2])

    # block内的基本类型变量声明
    variables.extend(_COMPILED_PATTERNS['local_block_var_basic'].findall(filtered_content))

    # 简化的block变量匹配
    block_simple_matches = _COMPILED_PATTERNS['local_block_var_simple'].findall(filtered_content)
    variables.extend([match[1] for match in block_simple_matches if len(match) >= 2])

    return variables


def _extract_static_variables_fast(content):
    """快速提取静态变量（使用预编译模式优化）"""
    variables = []

    # 获取 pointer_types 用于更精确的类型匹配
    pointer_types = get_pointer_types()

    # 基本静态变量（使用预编译模式）
    variables.extend(_COMPILED_PATTERNS['static_basic'].findall(content))

    # 静态指针变量
    variables.extend(_COMPILED_PATTERNS['static_pointer'].findall(content))

    # 带 const 的静态变量
    variables.extend(_COMPILED_PATTERNS['static_const_basic'].findall(content))

    # 恢复原有的基于 pointer_types 的精确匹配
    for ptype in pointer_types:
        # static PointerType *varName; 或 static PointerType *varName = value;
        pattern = rf'static\s+{ptype}\s*\*\s*(\w+)\s*[;=]'
        variables.extend(re.findall(pattern, content))

        # static const PointerType *varName; 或 static const PointerType *varName = value;
        pattern = rf'static\s+const\s+{ptype}\s*\*\s*(\w+)\s*[;=]'
        variables.extend(re.findall(pattern, content))

        # static PointerType * const varName; 或 static PointerType * const varName = value;
        pattern = rf'static\s+{ptype}\s*\*\s*const\s+(\w+)\s*[;=]'
        variables.extend(re.findall(pattern, content))

        # static PointerType varName; 或 static PointerType varName = value;
        pattern = rf'static\s+{ptype}\s+(\w+)\s*[;=]'
        variables.extend(re.findall(pattern, content))

    return variables


def _extract_const_variables_fast(content):
    """快速提取常量变量（使用预编译模式优化）"""
    variables = []

    # 获取 pointer_types 用于更精确的类型匹配
    pointer_types = get_pointer_types()

    # extern 常量（使用预编译模式）
    variables.extend(_COMPILED_PATTERNS['const_extern_basic'].findall(content))

    # extern 指针常量
    variables.extend(_COMPILED_PATTERNS['const_extern_pointer'].findall(content))

    # extern const 组合
    variables.extend(_COMPILED_PATTERNS['const_extern_const'].findall(content))

    # 恢复原有的基于 pointer_types 的精确匹配
    for ptype in pointer_types:
        # extern PointerType *const varName;
        pattern = rf'extern\s+{ptype}\s*\*\s*const\s+(\w+)\s*[;=]'
        variables.extend(re.findall(pattern, content))

        # extern const PointerType *varName;
        pattern = rf'extern\s+const\s+{ptype}\s*\*\s*(\w+)\s*[;=]'
        variables.extend(re.findall(pattern, content))

        # extern PointerType varName;
        pattern = rf'extern\s+{ptype}\s+(\w+)\s*[;=]'
        variables.extend(re.findall(pattern, content))

    return variables


def _filter_and_sort_variables(variables, is_local=False):
    """快速过滤和排序变量（使用预编译模式优化）"""
    if not variables:
        return []

    # 快速去重
    unique_vars = list(dict.fromkeys(variables))  # 保持顺序的去重

    # 基本过滤
    filtered_vars = []
    skip_words = {
        'self', 'super', 'nil', 'YES', 'NO', 'NULL', 'true', 'false',
        'void', 'int', 'float', 'double', 'BOOL', 'NSInteger', 'NSUInteger', 'CGFloat',
        'for', 'while', 'if', 'else', 'return', 'const', 'static', 'extern'
    } if is_local else {'void', 'int', 'float', 'double', 'BOOL'}

    # 使用预编译的变量名模式
    var_pattern = _COMPILED_PATTERNS['variable_name']
    min_length = 1  # 恢复原来的最小长度设置，不区分类型

    for var in unique_vars:
        if (var and
            var not in skip_words and
            len(var) >= min_length and
            var_pattern.match(var)):
            filtered_vars.append(var)

    # 排序：下划线开头的排在后面
    return sorted(filtered_vars, key=lambda x: (x.startswith('_'), x))


def process_constants_content(content):
    """
    专门用于处理和提取常量内容的函数
    整合原有功能：支持static常量、extern常量、宏定义等
    优化：删除修饰词，避免匹配属性声明，支持 pointer_types

    Args:
        content (str): 原始文件内容

    Returns:
        list: 排序后的常量列表
    """
    # 统一的内容清理
    content = clean_content(content)

    all_constants = []

    # 1. 使用统一方法提取 static 和 extern 常量（支持 pointer_types）
    unified_constants = extract_variables_unified(content, extract_types=['static', 'const'])
    all_constants.extend(unified_constants)

    # 2. 专门处理 #define 宏定义（恢复原有的复杂宏定义支持）
    define_constants = extract_define_macros(content)
    all_constants.extend(define_constants)

    # 3. 补充提取：基于 pointer_types 的特殊常量模式
    pointer_types = get_pointer_types()
    for ptype in pointer_types:
        # 匹配如：static BOOL _sendLog = YES; 这样的下划线前缀常量
        pattern = rf'static\s+{ptype}\s+(_\w+)\s*[;=]'
        matches = re.findall(pattern, content, re.MULTILINE)
        all_constants.extend(matches)

    # 4. 去重但不过滤，只要符合基本格式的都保留
    unique_constants = []
    seen_constants = set()

    for constant in all_constants:
        if (constant and
            constant not in seen_constants and
            len(constant) > 0 and
            re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', constant)):
            unique_constants.append(constant)
            seen_constants.add(constant)

    # 常量按字母顺序排序，下划线开头的排在后面
    return sorted(unique_constants, key=lambda x: (x.startswith('_'), x))


def extract_define_macros(content):
    """
    专门提取 #define 宏定义的函数（使用预编译模式优化）
    支持复杂宏定义匹配，包括多行宏、函数式宏、条件宏等

    Args:
        content (str): 原始文件内容

    Returns:
        list: 宏定义名称列表
    """
    define_constants = []

    # 提取多行宏定义（使用预编译模式）
    multiline_macros = _COMPILED_PATTERNS['define_multiline'].findall(content)
    define_constants.extend(multiline_macros)

    # 单行宏定义模式（使用预编译模式，恢复原有的18种全面匹配）
    define_pattern_keys = [
        'define_function_macro',    # 1. 函数式宏定义
        'define_string_macro',      # 2. 字符串宏定义
        'define_number_macro',      # 3. 数值宏定义
        'define_paren_expr',        # 4. 括号表达式宏定义
        'define_bit_operation',     # 5. 位运算宏定义
        'define_conditional',       # 6. 条件宏定义
        'define_type_cast',         # 7. 类型转换宏定义
        'define_function_call',     # 8. 函数调用宏定义
        'define_compound_expr',     # 9. 复合表达式宏定义
        'define_array_struct',      # 10. 数组/结构体宏定义
        'define_special_symbol',    # 11. 特殊符号宏定义
        'define_attribute',         # 12. 属性宏定义
        'define_stringify',         # 13. 字符串化宏定义
        'define_concatenate',       # 14. 连接宏定义
        'define_multi_identifier',  # 15. 多个标识符宏定义
        'define_empty',             # 16. 空宏定义
        'define_basic_value',       # 17. 基本宏定义
        'define_general',           # 18. 通用宏定义
    ]

    for pattern_key in define_pattern_keys:
        matches = _COMPILED_PATTERNS[pattern_key].findall(content)
        for match in matches:
            if match:  # 确保匹配不为空
                define_constants.append(match)

    return define_constants








def process_methods_content(content):
    """
    专门用于处理和提取方法内容的函数，支持多参数方法匹配

    Args:
        content (str): 原始文件内容

    Returns:
        list: 排序后的方法列表
    """
    # 统一的内容清理
    content = clean_content(content)

    # 获取描述词列表，用于清理方法声明中的可用性标注
    describe_words = get_describe_words()

    all_methods = []

    # 步骤1：预处理 - 合并多行方法声明并清理描述词
    lines = content.split('\n')
    processed_lines = []
    current_method = ""
    in_method_declaration = False

    for line in lines:
        stripped_line = line.strip()

        # 检测方法声明开始
        if re.match(r'^\s*[-\+]\s*\(', line):
            # 保存之前的方法
            if current_method:
                processed_lines.append(current_method)

            # 清理描述词
            cleaned_line = stripped_line
            for word in describe_words:
                if word in cleaned_line:
                    cleaned_line = cleaned_line.split(word)[0].strip()
                    break

            current_method = cleaned_line

            # 检查是否在同一行就结束了（以 ; 或 { 结尾）
            if cleaned_line.endswith(';') or cleaned_line.endswith('{'):
                # 方法声明在同一行完成
                processed_lines.append(current_method)
                current_method = ""
                in_method_declaration = False
            else:
                in_method_declaration = True

        elif in_method_declaration:
            # 检测方法声明结束
            if (stripped_line.endswith(';') or
                stripped_line.endswith('{') or
                stripped_line.startswith('@') or
                stripped_line.startswith('#') or
                re.match(r'^\s*[-\+]\s*\(', stripped_line) or
                not stripped_line):  # 空行也表示结束

                if not re.match(r'^\s*[-\+]\s*\(', stripped_line) and stripped_line:
                    current_method += " " + stripped_line

                # 清理并保存完整的方法声明
                current_method = re.sub(r'\s+', ' ', current_method).strip()
                processed_lines.append(current_method)
                current_method = ""
                in_method_declaration = False

                # 如果当前行是新的方法声明，开始处理
                if re.match(r'^\s*[-\+]\s*\(', stripped_line):
                    cleaned_line = stripped_line
                    for word in describe_words:
                        if word in cleaned_line:
                            cleaned_line = cleaned_line.split(word)[0].strip()
                            break
                    current_method = cleaned_line
                    in_method_declaration = True
                else:
                    processed_lines.append(line)
            else:
                # 继续合并方法声明
                current_method += " " + stripped_line
        else:
            processed_lines.append(line)

    # 处理最后一个方法
    if current_method:
        current_method = re.sub(r'\s+', ' ', current_method).strip()
        processed_lines.append(current_method)

    processed_content = '\n'.join(processed_lines)

    # 步骤2：提取所有方法名（包括多参数方法的所有部分）

    # 首先找到所有的方法声明行
    method_lines = []
    for line in processed_content.split('\n'):
        line = line.strip()
        if re.match(r'^\s*[-\+]\s*\(', line):
            method_lines.append(line)

    # 内联方法名有效性检查函数
    def _is_valid_method_name_inline(method_name):
        """检查是否是有效的方法名"""
        if not method_name:
            return False
        # 只包含字母、数字和下划线
        if not re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', method_name):
            return False
        # 不能只是下划线
        if method_name.replace('_', '') == '':
            return False
        return True

    # 处理每个方法声明行，提取方法名的所有部分
    for method_line in method_lines:
        # 内联提取方法名部分的逻辑
        method_parts = []

        # 移除方法前缀和返回类型，只保留方法签名部分
        signature_match = re.search(r'[-\+]\s*\([^)]*\)\s*(.+)', method_line)
        if signature_match:
            signature = signature_match.group(1).strip()

            # 移除结尾的分号或大括号
            signature = re.sub(r'[;{]\s*$', '', signature).strip()

            if signature:
                # 情况1：无参数方法 - methodName
                if ':' not in signature:
                    method_name = signature.split()[0]  # 取第一个单词
                    if method_name and _is_valid_method_name_inline(method_name):
                        method_parts.append(method_name)
                else:
                    # 情况2：有参数的方法 - 需要提取所有方法名部分
                    parts = signature.split(':')

                    for i, part in enumerate(parts[:-1]):  # 最后一个部分通常是参数，不包含方法名
                        part = part.strip()

                        if i == 0:
                            # 第一部分：methodName
                            method_name = part.split()[0] if part else ""
                            if method_name and _is_valid_method_name_inline(method_name):
                                method_parts.append(method_name)
                        else:
                            # 后续部分：可能包含方法名，格式如 "param withMethodName" 或 "param) withMethodName"
                            words = part.split()
                            if words:
                                # 取最后一个单词作为方法名部分
                                potential_method_name = words[-1]
                                # 移除可能的括号
                                potential_method_name = re.sub(r'[()]+', '', potential_method_name)
                                if (potential_method_name and
                                    _is_valid_method_name_inline(potential_method_name) and
                                    len(potential_method_name) > 1):
                                    method_parts.append(potential_method_name)

        all_methods.extend(method_parts)

    # 步骤3：过滤和排序
    # 去重
    unique_methods = list(set(all_methods))

    # 过滤掉常见的非方法名词汇
    skip_words = {
        # 基本类型
        'void', 'int', 'float', 'double', 'BOOL', 'NSInteger', 'NSUInteger', 'CGFloat',
        # 常用类
        'NSString', 'NSArray', 'NSDictionary', 'NSObject', 'NSMutableArray', 'NSMutableDictionary',
        'UIView', 'UIViewController', 'UILabel', 'UIButton', 'UIImageView', 'UIScrollView',
        # 系统标识符
        'id', 'Class', 'SEL', 'IMP', 'nil', 'YES', 'NO', 'NULL', 'true', 'false',
        # 关键字
        'const', 'static', 'extern', 'inline', 'typedef', 'struct', 'enum',
        'if', 'else', 'for', 'while', 'do', 'switch', 'case', 'default',
        'return', 'break', 'continue', 'goto', 'sizeof', 'typeof',
        # 常见的非方法名
        'copy', 'strong', 'weak', 'assign', 'retain', 'nonatomic', 'atomic', 'readonly', 'readwrite'
    }

    filtered_methods = []
    for method in unique_methods:
        if (method not in skip_words and
            len(method) > 1 and
            _is_valid_method_name_inline(method) and  # 使用内联函数检查
            not method.isupper()):  # 不是全大写（通常是常量）
            filtered_methods.append(method)

    # 将set开头的排在后面
    filtered_methods.sort(key=lambda x: (x.startswith('set'), x))
    return filtered_methods

def process_enums_content(content):
    """
    专门用于处理和提取枚举内容的函数，包括枚举类型名和所有枚举值
    支持可用性标注清理，在一个函数内完整处理

    Args:
        content (str): 原始文件内容

    Returns:
        list: 排序后的枚举列表（包含枚举类型名和所有枚举值）
    """
    # 统一的内容清理
    content = clean_content(content)

    # 获取描述词列表，用于清理可用性标注
    describe_words = get_describe_words()

    all_enums = []

    # 内联函数：清理可用性标注
    def clean_annotations(text):
        """清理文本中的可用性标注"""
        if not text:
            return text

        # 清理常见的可用性标注模式
        patterns = [
            r'__OSX_AVAILABLE_STARTING\([^)]*\)',
            r'NS_ENUM_AVAILABLE\([^)]*\)',
            r'NS_AVAILABLE\([^)]*\)',
            r'API_AVAILABLE\([^)]*\)',
            r'__deprecated',
            r'__attribute__\([^)]*\)'
        ]

        for pattern in patterns:
            text = re.sub(pattern, '', text)

        # 清理描述词
        for word in describe_words:
            if word in text:
                word_pattern = re.escape(word) + r'(?:\([^)]*\))?'
                text = re.sub(word_pattern, '', text)

        # 清理多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        return text.strip()

    # 内联函数：从枚举体中提取所有枚举值
    def extract_enum_values_inline(enum_body):
        """内联函数：从枚举体中提取枚举值"""
        enum_values = []
        if not enum_body:
            return enum_values

        # 清理枚举体内容，移除注释
        enum_body = re.sub(r'//.*$', '', enum_body, flags=re.MULTILINE)
        enum_body = re.sub(r'/\*.*?\*/', '', enum_body, flags=re.DOTALL)
        enum_body = enum_body.strip()

        # 清理可用性标注
        enum_body = clean_annotations(enum_body)

        # 移除多余的空白字符和换行
        enum_body = re.sub(r'\s+', ' ', enum_body)

        # 使用更简单但更可靠的方法：先按逗号分割，然后处理每个部分
        # 处理可能的嵌套括号情况
        parts = []
        current_part = ""
        paren_count = 0
        bracket_count = 0

        for char in enum_body:
            if char == '(':
                paren_count += 1
            elif char == ')':
                paren_count -= 1
            elif char == '[':
                bracket_count += 1
            elif char == ']':
                bracket_count -= 1
            elif char == ',' and paren_count == 0 and bracket_count == 0:
                if current_part.strip():
                    parts.append(current_part.strip())
                current_part = ""
                continue

            current_part += char

        # 添加最后一个部分
        if current_part.strip():
            parts.append(current_part.strip())

        # 从每个部分提取枚举值名称
        for part in parts:
            part = part.strip()
            if not part:
                continue

            # 再次清理可用性标注（针对单个枚举值）
            part = clean_annotations(part)
            if not part:
                continue

            # 提取枚举值名称（去掉赋值部分）
            # 支持各种赋值格式：EnumValue, EnumValue = 1, EnumValue = 1 << 2, etc.
            value_match = re.match(r'^(\w+)(?:\s*=.*)?$', part, re.DOTALL)
            if value_match:
                enum_value = value_match.group(1).strip()
                if (enum_value and
                    re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', enum_value) and
                    len(enum_value) > 0):  # 允许单字符枚举值
                    enum_values.append(enum_value)

        return enum_values

    # 1. 处理 NS_ENUM 和 NS_OPTIONS 枚举定义
    # 使用更精确的正则表达式，支持多行匹配、嵌套大括号和可用性标注
    # 修复：支持 unsigned long, unsigned int 等多词类型
    ns_enum_pattern = r'typedef\s+(NS_ENUM|NS_OPTIONS)\s*\(\s*([^,]+),\s*(\w+)\s*\)\s*\{([^{}]*(?:\{[^{}]*\}[^{}]*)*)\}[^;]*;?'
    ns_enum_matches = re.findall(ns_enum_pattern, content, re.DOTALL)

    for _, _, enum_name, enum_body in ns_enum_matches:
        # 清理枚举类型名的可用性标注
        enum_name = clean_annotations(enum_name.strip())
        if enum_name:
            # 添加枚举类型名
            all_enums.append(enum_name)

        # 提取所有枚举值
        enum_values = extract_enum_values_inline(enum_body)
        all_enums.extend(enum_values)

    # 2. 处理传统 C 枚举定义
    c_enum_patterns = [
        # typedef enum EnumName { ... } EnumName;
        (r'typedef\s+enum\s+(\w+)\s*\{([^{}]*(?:\{[^{}]*\}[^{}]*)*)\}\s*(\w+)[^;]*;', 'named_typedef'),
        # typedef enum { ... } EnumName;
        (r'typedef\s+enum\s*\{([^{}]*(?:\{[^{}]*\}[^{}]*)*)\}\s*(\w+)[^;]*;', 'anonymous_typedef'),
        # typedef enum : NSUInteger { ... } EnumName;
        (r'typedef\s+enum\s*:\s*\w+\s*\{([^{}]*(?:\{[^{}]*\}[^{}]*)*)\}\s*(\w+)[^;]*;', 'typed_enum'),
        # enum EnumName { ... };
        (r'enum\s+(\w+)\s*\{([^{}]*(?:\{[^{}]*\}[^{}]*)*)\}[^;]*;?', 'simple_enum')
    ]

    for pattern, pattern_type in c_enum_patterns:
        matches = re.findall(pattern, content, re.DOTALL)

        for match in matches:
            if pattern_type == 'named_typedef':
                enum_name1, enum_body, enum_name2 = match
                enum_name1 = clean_annotations(enum_name1)
                enum_name2 = clean_annotations(enum_name2)

                if enum_name1:
                    all_enums.append(enum_name1)
                if enum_name2 and enum_name2 != enum_name1:
                    all_enums.append(enum_name2)
            else:
                if pattern_type == 'simple_enum':
                    enum_name, enum_body = match
                else:  # anonymous_typedef, typed_enum
                    enum_body, enum_name = match

                enum_name = clean_annotations(enum_name)
                if enum_name:
                    all_enums.append(enum_name)

            # 提取枚举值
            enum_values = extract_enum_values_inline(enum_body)
            all_enums.extend(enum_values)

    # 过滤掉常见的非枚举词汇
    skip_words = {
        'NSInteger', 'NSUInteger', 'int', 'typedef', 'enum', 'NS_ENUM', 'NS_OPTIONS',
        'YES', 'NO', 'nil', 'NULL', 'true', 'false', 'void', 'const', 'static',
        'unsigned', 'signed', 'long', 'short', 'char', 'float', 'double'
    }

    # 去重并过滤
    unique_enums = []
    seen_enums = set()

    for enum_name in all_enums:
        if (enum_name and
            enum_name not in seen_enums and
            enum_name not in skip_words and
            len(enum_name) > 1 and
            re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', enum_name)):
            unique_enums.append(enum_name)
            seen_enums.add(enum_name)
            
    # 枚举按字母顺序排序，下划线开头的排在后面
    return sorted(unique_enums, key=lambda x: (x.startswith('_'), x))

def process_delegates_content(content):
    """
    专门用于处理和提取代理内容的函数

    Args:
        content (str): 原始文件内容

    Returns:
        list: 排序后的代理列表
    """
    # 统一的内容清理
    content = clean_content(content)

    all_delegates = []

    # 1. 匹配 @protocol 协议定义
    protocol_patterns = [
        r'@protocol\s+(\w+)\s*<[^>]*>',  # @protocol DelegateName <NSObject>
        r'@protocol\s+(\w+)\s*;',        # @protocol DelegateName;
        r'@protocol\s+(\w+)\s*\n',       # @protocol DelegateName (换行)
        r'@protocol\s+(\w+)\s*$'         # @protocol DelegateName (行尾)
    ]

    for pattern in protocol_patterns:
        matches = re.findall(pattern, content, re.MULTILINE)
        all_delegates.extend(matches)

    # 2. 匹配属性中的代理声明
    # @property (nonatomic, weak) id<DelegateName> delegate;
    property_delegate_patterns = [
        r'@property\s*\([^)]*\)\s*id\s*<\s*(\w+)\s*>\s*\w+\s*;',
        r'@property\s*\([^)]*\)\s*(\w+)\s*\*\s*\w+\s*;'  # 可能的代理属性
    ]

    for pattern in property_delegate_patterns:
        matches = re.findall(pattern, content)
        # 过滤掉明显不是代理的类型
        for match in matches:
            if ('delegate' in match.lower() or
                'protocol' in match.lower() or
                match.endswith('Delegate') or
                match.endswith('Protocol')):
                all_delegates.append(match)

    # 3. 匹配方法参数中的代理类型
    # - (void)setDelegate:(id<DelegateName>)delegate;
    method_delegate_pattern = r'id\s*<\s*(\w+)\s*>\s*\w+'
    method_matches = re.findall(method_delegate_pattern, content)
    all_delegates.extend(method_matches)

    # 4. 匹配变量声明中的代理类型
    # id<DelegateName> delegate;
    variable_delegate_pattern = r'id\s*<\s*(\w+)\s*>\s*\w+\s*;'
    variable_matches = re.findall(variable_delegate_pattern, content)
    all_delegates.extend(variable_matches)

    # 过滤掉常见的非代理词汇
    skip_words = {
        'NSObject', 'NSCopying', 'NSCoding', 'NSSecureCoding', 'NSMutableCopying',
        'UITableViewDataSource', 'UITableViewDelegate', 'UICollectionViewDataSource',
        'UICollectionViewDelegate', 'UIScrollViewDelegate', 'UITextFieldDelegate',
        'UITextViewDelegate', 'UIPickerViewDataSource', 'UIPickerViewDelegate',
        'NSURLSessionDelegate', 'NSURLSessionDataDelegate', 'NSURLSessionTaskDelegate',
        'id', 'void', 'BOOL', 'NSInteger', 'NSUInteger', 'CGFloat'
    }

    # 去重并过滤
    unique_delegates = []
    for delegate_name in set(all_delegates):
        if (delegate_name and
            delegate_name not in skip_words and
            len(delegate_name) > 2 and
            re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', delegate_name)):
            unique_delegates.append(delegate_name)

    # 代理按字母顺序排序，下划线开头的排在后面
    sorted_delegates = sorted(unique_delegates, key=lambda x: (x.startswith('_'), x))

    return sorted_delegates

def process_blocks_content(content):
    """
    专门用于处理和提取Block内容的函数

    Args:
        content (str): 原始文件内容

    Returns:
        list: 排序后的Block列表
    """
    # 统一的内容清理
    content = clean_content(content)

    all_blocks = []

    # 1. 匹配 typedef Block 定义
    typedef_block_patterns = [
        # typedef void(^BlockTypeName)(NSString *param);
        r'typedef\s+\w+\s*\(\s*\^\s*(\w+)\s*\)\s*\([^)]*\)\s*;',
        # typedef NSString *(^BlockTypeName)(NSInteger param);
        r'typedef\s+\w+\s*\*\s*\(\s*\^\s*(\w+)\s*\)\s*\([^)]*\)\s*;',
        # typedef id(^BlockTypeName)(void);
        r'typedef\s+id\s*\(\s*\^\s*(\w+)\s*\)\s*\([^)]*\)\s*;',
        # typedef BOOL(^BlockTypeName)(NSError *error);
        r'typedef\s+BOOL\s*\(\s*\^\s*(\w+)\s*\)\s*\([^)]*\)\s*;'
    ]

    for pattern in typedef_block_patterns:
        matches = re.findall(pattern, content)
        all_blocks.extend(matches)

    # 2. 匹配属性中的 Block 定义
    # @property (nonatomic, copy) void(^blockName)(NSString *param);
    property_block_patterns = [
        r'@property\s*\([^)]*\)\s*\w+\s*\(\s*\^\s*(\w+)\s*\)\s*\([^)]*\)\s*;',
        r'@property\s*\([^)]*\)\s*\w+\s*\*\s*\(\s*\^\s*(\w+)\s*\)\s*\([^)]*\)\s*;',
        r'@property\s*\([^)]*\)\s*id\s*\(\s*\^\s*(\w+)\s*\)\s*\([^)]*\)\s*;',
        r'@property\s*\([^)]*\)\s*BOOL\s*\(\s*\^\s*(\w+)\s*\)\s*\([^)]*\)\s*;'
    ]

    for pattern in property_block_patterns:
        matches = re.findall(pattern, content)
        all_blocks.extend(matches)

    # 3. 匹配方法参数中的 Block
    # - (void)methodWithBlock:(void(^)(NSString *))blockName;
    method_block_patterns = [
        r':\s*\w+\s*\(\s*\^\s*\)\s*\(\s*[^)]*\s*\)\s*(\w+)',
        r':\s*\w+\s*\*\s*\(\s*\^\s*\)\s*\(\s*[^)]*\s*\)\s*(\w+)',
        r':\s*id\s*\(\s*\^\s*\)\s*\(\s*[^)]*\s*\)\s*(\w+)',
        r':\s*BOOL\s*\(\s*\^\s*\)\s*\(\s*[^)]*\s*\)\s*(\w+)'
    ]

    for pattern in method_block_patterns:
        matches = re.findall(pattern, content)
        all_blocks.extend(matches)

    # 4. 匹配变量声明中的 Block
    # void(^blockName)(NSString *param) = ^(NSString *param) { ... };
    variable_block_patterns = [
        r'\w+\s*\(\s*\^\s*(\w+)\s*\)\s*\([^)]*\)\s*=',
        r'\w+\s*\*\s*\(\s*\^\s*(\w+)\s*\)\s*\([^)]*\)\s*=',
        r'id\s*\(\s*\^\s*(\w+)\s*\)\s*\([^)]*\)\s*=',
        r'BOOL\s*\(\s*\^\s*(\w+)\s*\)\s*\([^)]*\)\s*='
    ]

    for pattern in variable_block_patterns:
        matches = re.findall(pattern, content)
        all_blocks.extend(matches)

    # 过滤掉常见的非Block词汇
    skip_words = {
        'void', 'id', 'BOOL', 'NSString', 'NSInteger', 'NSUInteger', 'CGFloat',
        'NSError', 'NSArray', 'NSDictionary', 'NSObject', 'typedef',
        'YES', 'NO', 'nil', 'NULL', 'true', 'false'
    }

    # 去重并过滤
    unique_blocks = []
    for block_name in set(all_blocks):
        if (block_name and
            block_name not in skip_words and
            len(block_name) > 1 and
            re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', block_name) and
            ('block' in block_name.lower() or
             'completion' in block_name.lower() or
             'handler' in block_name.lower() or
             'callback' in block_name.lower() or
             block_name.endswith('Block') or
             block_name.endswith('Handler') or
             block_name.endswith('Completion'))):
            unique_blocks.append(block_name)

    # Block按字母顺序排序，下划线开头的排在后面
    sorted_blocks = sorted(unique_blocks, key=lambda x: (x.startswith('_'), x))

    return sorted_blocks