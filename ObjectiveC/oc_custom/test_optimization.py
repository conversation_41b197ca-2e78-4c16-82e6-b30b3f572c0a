#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 extract_variables_unified 优化后的功能
"""

import sys
import os

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

def test_extract_variables_unified():
    """测试优化后的 extract_variables_unified 函数"""
    
    # 模拟必要的依赖
    class MockEPrintAnnotation:
        @staticmethod
        def search_annotation_and_delete(content):
            return content
        
        @staticmethod
        def search_print_and_delete(content):
            return content
    
    # 创建模拟模块
    sys.modules['ObjectiveC.oc_function.e_print_annotation'] = MockEPrintAnnotation()
    sys.modules['ObjectiveC.oc_custom.custom_util'] = type('MockUtil', (), {'list_system_pointer_types': []})()
    
    # 现在导入 custom_core
    import custom_core
    
    # 测试代码
    test_content = '''
    - (void)testMethod {
        NSString *localVar = @"test";
        BOOL flag = YES;
        
        void(^testBlock)(NSString *) = ^(NSString *param) {
            NSLog(@"Block param: %@", param);
            int blockVar = 10;
        };
        
        for (NSInteger i = 0; i < 10; i++) {
            // loop variable
        }
    }
    
    static NSString *staticConstant = @"constant";
    extern NSString *externConstant;
    '''
    
    print("=== 测试 extract_variables_unified 优化 ===")
    
    # 测试 local 类型提取（包含 block 变量）
    print("\n1. 测试 local 类型提取（包含 block 变量）:")
    try:
        local_vars = custom_core.extract_variables_unified(test_content, extract_types=['local'])
        print(f"   提取到的局部变量: {local_vars}")
        print(f"   数量: {len(local_vars)}")
    except Exception as e:
        print(f"   错误: {e}")
        import traceback
        traceback.print_exc()
    
    # 测试 static 类型提取
    print("\n2. 测试 static 类型提取:")
    try:
        static_vars = custom_core.extract_variables_unified(test_content, extract_types=['static'])
        print(f"   提取到的静态变量: {static_vars}")
        print(f"   数量: {len(static_vars)}")
    except Exception as e:
        print(f"   错误: {e}")
    
    # 测试 const 类型提取
    print("\n3. 测试 const 类型提取:")
    try:
        const_vars = custom_core.extract_variables_unified(test_content, extract_types=['const'])
        print(f"   提取到的常量: {const_vars}")
        print(f"   数量: {len(const_vars)}")
    except Exception as e:
        print(f"   错误: {e}")
    
    # 测试混合类型提取
    print("\n4. 测试混合类型提取:")
    try:
        mixed_vars = custom_core.extract_variables_unified(test_content, extract_types=['local', 'static', 'const'])
        print(f"   提取到的所有变量: {mixed_vars}")
        print(f"   数量: {len(mixed_vars)}")
    except Exception as e:
        print(f"   错误: {e}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_extract_variables_unified()
