#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试导入问题
"""

import sys
import os

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

def debug_import():
    """调试导入问题"""
    
    # 模拟必要的依赖
    class MockEPrintAnnotation:
        @staticmethod
        def search_annotation_and_delete(content):
            return content
        
        @staticmethod
        def search_print_and_delete(content):
            return content
    
    # 创建模拟模块
    sys.modules['ObjectiveC.oc_function.e_print_annotation'] = MockEPrintAnnotation()
    sys.modules['ObjectiveC.oc_custom.custom_util'] = type('MockUtil', (), {'list_system_pointer_types': []})()
    
    print("=== 调试 get_pointer_types 导入问题 ===")
    
    # 测试直接导入配置文件
    try:
        from ObjectiveC.oc_custom import objc_types_config
        print("✅ 直接导入 objc_types_config 成功")
        types = objc_types_config.get_all_pointer_types()
        print(f"   获取到 {len(types)} 个类型")
    except Exception as e:
        print(f"❌ 直接导入失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 测试在 custom_core 中的导入
    try:
        import custom_core
        print("✅ 导入 custom_core 成功")
        
        # 手动测试 get_pointer_types 函数的各个分支
        print("\\n测试 get_pointer_types 函数:")
        
        # 方式1：测试 objc_types_config 导入
        try:
            from ObjectiveC.oc_custom import objc_types_config as test_config
            pointer_types = test_config.get_all_pointer_types()
            print(f"  方式1 成功: 获取到 {len(pointer_types)} 个类型")
        except ImportError as e:
            print(f"  方式1 失败 (ImportError): {e}")
        except Exception as e:
            print(f"  方式1 失败 (其他错误): {e}")
        
        # 方式2：测试 custom_util
        try:
            import ObjectiveC.oc_custom.custom_util as test_util
            if hasattr(test_util, 'list_system_pointer_types'):
                pointer_types = test_util.list_system_pointer_types
                print(f"  方式2: custom_util 有 {len(pointer_types) if pointer_types else 0} 个类型")
            else:
                print("  方式2: custom_util 没有 list_system_pointer_types 属性")
        except Exception as e:
            print(f"  方式2 失败: {e}")
        
        # 实际调用 get_pointer_types
        actual_types = custom_core.get_pointer_types()
        print(f"\\n实际 get_pointer_types 返回: {len(actual_types)} 个类型")
        
        # 显示前几个类型
        print(f"前10个类型: {actual_types[:10]}")
        
    except Exception as e:
        print(f"❌ 导入 custom_core 失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_import()
