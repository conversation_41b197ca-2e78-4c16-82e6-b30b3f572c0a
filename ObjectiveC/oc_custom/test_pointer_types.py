#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 pointer_types 是否包含新添加的类型
"""

import sys
import os

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

def test_pointer_types():
    """测试 pointer_types 加载"""
    
    # 模拟必要的依赖
    class MockEPrintAnnotation:
        @staticmethod
        def search_annotation_and_delete(content):
            return content
        
        @staticmethod
        def search_print_and_delete(content):
            return content
    
    # 创建模拟模块
    sys.modules['ObjectiveC.oc_function.e_print_annotation'] = MockEPrintAnnotation()
    sys.modules['ObjectiveC.oc_custom.custom_util'] = type('MockUtil', (), {'list_system_pointer_types': []})()
    
    # 导入模块
    import custom_core
    from ObjectiveC.oc_custom import objc_types_config
    
    print("=== 测试 pointer_types 加载 ===")
    
    # 1. 直接从配置文件获取
    config_types = objc_types_config.get_all_pointer_types()
    print(f"配置文件中的类型数量: {len(config_types)}")
    
    # 2. 通过 custom_core 获取
    core_types = custom_core.get_pointer_types()
    print(f"custom_core 中的类型数量: {len(core_types)}")
    
    # 3. 检查新添加的类型
    new_types = [
        'NSURLSessionDataTaskCompletionHandler',
        'UIApplicationStateRestorationManager',
        'NSURLSessionUploadTaskCompletionHandler',
        'NSURLSessionDownloadTaskCompletionHandler'
    ]
    
    print(f"\\n检查新添加的类型:")
    for new_type in new_types:
        in_config = new_type in config_types
        in_core = new_type in core_types
        print(f"  {new_type}:")
        print(f"    在配置文件中: {in_config}")
        print(f"    在 custom_core 中: {in_core}")
    
    # 4. 显示一些长类型名
    long_types = [t for t in core_types if len(t) > 20]
    print(f"\\n长类型名（>20字符）示例:")
    for lt in long_types[:10]:
        print(f"  {lt} ({len(lt)} 字符)")
    
    # 5. 检查是否有重复
    if len(config_types) != len(set(config_types)):
        print(f"\\n⚠️  配置文件中有重复类型")
    else:
        print(f"\\n✅ 配置文件中无重复类型")

if __name__ == "__main__":
    test_pointer_types()
