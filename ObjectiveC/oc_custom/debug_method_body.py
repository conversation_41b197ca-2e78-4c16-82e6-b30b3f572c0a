#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试方法体提取问题
"""

import sys
import os
import re

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

def debug_method_body():
    """调试方法体提取"""
    
    # 模拟必要的依赖
    class MockEPrintAnnotation:
        @staticmethod
        def search_annotation_and_delete(content):
            return content
        
        @staticmethod
        def search_print_and_delete(content):
            return content
    
    # 创建模拟模块
    sys.modules['ObjectiveC.oc_function.e_print_annotation'] = MockEPrintAnnotation()
    sys.modules['ObjectiveC.oc_custom.custom_util'] = type('MockUtil', (), {'list_system_pointer_types': []})()
    
    # 导入优化后的模块
    import custom_core
    
    # 简单的测试内容
    test_content = """@implementation TestClass

- (void)simpleMethod {
    NSString *localVar = @"test";
    BOOL flag = YES;
    NSInteger counter = 0;
    UIView *view;
    NSError *error;
}

+ (void)classMethod {
    static NSString *staticVar = @"static";
}

@end"""
    
    print("=== 调试方法体提取 ===")
    print(f"测试内容:")
    print(test_content)
    
    # 1. 测试方法体提取正则
    method_pattern = r'[-\+]\s*\([^)]*\)\s*[^{]*\{([^{}]*(?:\{[^{}]*\}[^{}]*)*)\}'
    method_bodies = re.findall(method_pattern, test_content, re.DOTALL)
    
    print(f"\\n1. 方法体提取结果:")
    print(f"   提取到的方法体数量: {len(method_bodies)}")
    for i, body in enumerate(method_bodies):
        print(f"   方法体 {i+1}:")
        print(f"   {repr(body)}")
        print(f"   内容:")
        print(f"   {body}")
    
    # 2. 测试预编译模式
    print(f"\\n2. 测试预编译模式:")
    compiled_method_bodies = custom_core._COMPILED_PATTERNS['method_body'].findall(test_content)
    print(f"   预编译模式提取到的方法体数量: {len(compiled_method_bodies)}")
    for i, body in enumerate(compiled_method_bodies):
        print(f"   方法体 {i+1}: {repr(body)}")
    
    # 3. 测试局部变量提取
    print(f"\\n3. 测试局部变量提取:")
    local_vars = custom_core.extract_variables_unified(test_content, extract_types=['local'])
    print(f"   提取到的局部变量: {local_vars}")
    
    # 4. 测试静态变量提取
    print(f"\\n4. 测试静态变量提取:")
    static_vars = custom_core.extract_variables_unified(test_content, extract_types=['static'])
    print(f"   提取到的静态变量: {static_vars}")
    
    # 5. 手动测试基本模式
    print(f"\\n5. 手动测试基本模式:")
    if method_bodies:
        body = method_bodies[0]
        print(f"   测试方法体: {repr(body)}")
        
        # 基本类型
        basic_matches = re.findall(r'(?:BOOL|NSInteger|NSUInteger|CGFloat|int|float|double|long|short|char|size_t|NSTimeInterval|CGPoint|CGSize|CGRect|NSRange|UIEdgeInsets)\s+(\w+)\s*[=;]', body)
        print(f"   基本类型匹配: {basic_matches}")
        
        # 指针类型
        pointer_matches = re.findall(r'([A-Z]\w+)\s*\*\s*(\w+)\s*[=;]', body)
        print(f"   指针类型匹配: {pointer_matches}")
        
        # 不带赋值的指针
        no_assign_matches = re.findall(r'([A-Z]\w+)\s*\*\s*(\w+)\s*;', body)
        print(f"   不带赋值的指针: {no_assign_matches}")

if __name__ == "__main__":
    debug_method_body()
