#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
逐步调试脚本：分析每个提取步骤
"""

import sys
import os
import re

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

def debug_step_by_step():
    """逐步调试每个提取步骤"""
    
    # 模拟必要的依赖
    class MockEPrintAnnotation:
        @staticmethod
        def search_annotation_and_delete(content):
            return content
        
        @staticmethod
        def search_print_and_delete(content):
            return content
    
    # 创建模拟模块
    sys.modules['ObjectiveC.oc_function.e_print_annotation'] = MockEPrintAnnotation()
    sys.modules['ObjectiveC.oc_custom.custom_util'] = type('MockUtil', (), {'list_system_pointer_types': []})()
    
    # 导入优化后的模块
    import custom_core
    
    # 简化的测试内容，专注于基本变量
    test_content = """@implementation TestClass

- (void)testMethod {
    // 基本类型
    NSString *localVar = @"test";
    BOOL flag = YES;
    NSInteger counter = 0;
    
    // 不带赋值的声明
    UIView *view;
    NSError *error;
    
    // 方法调用赋值
    NSString *result = [self someMethod];
    UILabel *label = [[UILabel alloc] init];
    
    // __block 变量
    __block NSInteger blockCounter = 0;
    
    // Block 声明和使用
    void(^testBlock)(NSString *) = ^(NSString *param) {
        int blockVar = 10;
    };
    
    // for 循环
    for (NSInteger i = 0; i < 10; i++) {
        NSString *loopVar = @"loop";
    }
}

+ (void)classMethod {
    static NSString *staticString = @"static";
    static BOOL staticFlag = YES;
}

@end"""
    
    print("=== 逐步调试局部变量提取 ===")
    print(f"测试内容:")
    print(test_content)
    
    # 1. 测试方法体提取
    print(f"\\n1. 方法体提取:")
    method_bodies = custom_core._COMPILED_PATTERNS['method_body'].findall(test_content)
    print(f"   提取到的方法体数量: {len(method_bodies)}")
    for i, body in enumerate(method_bodies):
        print(f"   方法体 {i+1}:")
        print(f"   {repr(body)}")
    
    # 2. 测试静态变量过滤
    print(f"\\n2. 静态变量过滤测试:")
    if method_bodies:
        for i, body in enumerate(method_bodies):
            print(f"   原始方法体 {i+1}:")
            print(f"   {repr(body)}")
            
            # 应用静态变量过滤
            body_lines = body.split('\\n')
            filtered_lines = []
            for line in body_lines:
                stripped_line = line.strip()
                if not stripped_line.startswith('static '):
                    filtered_lines.append(line)
                else:
                    print(f"     过滤掉的静态变量行: {repr(stripped_line)}")
            filtered_body = '\\n'.join(filtered_lines)
            
            print(f"   过滤后方法体 {i+1}:")
            print(f"   {repr(filtered_body)}")
    
    # 3. 测试基本类型匹配
    print(f"\\n3. 基本类型匹配测试:")
    if method_bodies:
        filtered_body = method_bodies[0]  # 使用第一个方法体
        # 应用过滤
        body_lines = filtered_body.split('\\n')
        filtered_lines = [line for line in body_lines if not line.strip().startswith('static ')]
        filtered_body = '\\n'.join(filtered_lines)
        
        basic_matches = custom_core._COMPILED_PATTERNS['local_basic_types'].findall(filtered_body)
        print(f"   基本类型匹配: {basic_matches}")
        
        # 手动测试基本类型模式
        manual_basic = re.findall(r'(?:BOOL|NSInteger|NSUInteger|CGFloat|int|float|double|long|short|char|size_t|NSTimeInterval|CGPoint|CGSize|CGRect|NSRange|UIEdgeInsets)\\s+(\\w+)\\s*[=;]', filtered_body)
        print(f"   手动基本类型匹配: {manual_basic}")
    
    # 4. 测试指针类型匹配
    print(f"\\n4. 指针类型匹配测试:")
    if method_bodies:
        pointer_matches = custom_core._COMPILED_PATTERNS['local_pointer_types'].findall(filtered_body)
        print(f"   指针类型匹配: {pointer_matches}")
        
        # 手动测试指针类型模式
        manual_pointer = re.findall(r'([A-Z]\\w+)\\s*\\*\\s*(\\w+)\\s*[=;]', filtered_body)
        print(f"   手动指针类型匹配: {manual_pointer}")
    
    # 5. 测试完整的局部变量提取
    print(f"\\n5. 完整的局部变量提取:")
    local_vars = custom_core.extract_variables_unified(test_content, extract_types=['local'])
    print(f"   提取到的局部变量: {local_vars}")
    
    # 6. 测试静态变量提取
    print(f"\\n6. 静态变量提取:")
    static_vars = custom_core.extract_variables_unified(test_content, extract_types=['static'])
    print(f"   提取到的静态变量: {static_vars}")
    
    # 7. 预期结果对比
    expected_local = ['localVar', 'flag', 'counter', 'view', 'error', 'result', 'label', 'blockCounter', 'param', 'blockVar', 'i', 'loopVar']
    expected_static = ['staticString', 'staticFlag']
    
    print(f"\\n7. 结果对比:")
    print(f"   预期局部变量: {expected_local}")
    print(f"   实际局部变量: {local_vars}")
    print(f"   预期静态变量: {expected_static}")
    print(f"   实际静态变量: {static_vars}")
    
    local_found = [var for var in expected_local if var in local_vars]
    local_missing = [var for var in expected_local if var not in local_vars]
    local_extra = [var for var in local_vars if var not in expected_local]
    
    print(f"\\n   局部变量分析:")
    print(f"   找到: {local_found} ({len(local_found)}/{len(expected_local)})")
    print(f"   缺失: {local_missing}")
    print(f"   额外: {local_extra}")

if __name__ == "__main__":
    debug_step_by_step()
