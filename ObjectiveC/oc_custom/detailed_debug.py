#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细调试脚本：逐步检查变量提取过程
"""

import sys
import os
import re

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

def detailed_debug():
    """详细调试变量提取过程"""
    
    # 模拟必要的依赖
    class MockEPrintAnnotation:
        @staticmethod
        def search_annotation_and_delete(content):
            return content
        
        @staticmethod
        def search_print_and_delete(content):
            return content
    
    # 创建模拟模块
    sys.modules['ObjectiveC.oc_function.e_print_annotation'] = MockEPrintAnnotation()
    sys.modules['ObjectiveC.oc_custom.custom_util'] = type('MockUtil', (), {'list_system_pointer_types': []})()
    
    # 导入优化后的模块
    import custom_core
    
    # 简单的测试内容，包含各种变量类型
    test_content = '''
    @implementation TestClass
    
    - (void)complexMethod {
        NSString *localVar = @"test";
        BOOL flag = YES;
        NSInteger counter = 0;
        UIView *view;
        NSError *error;
        NSString *result = [self someMethod];
        UILabel *label = [[UILabel alloc] init];
        NSString *title = self.titleLabel.text;
        __block NSInteger blockCounter = 0;
        
        void(^testBlock)(NSString *) = ^(NSString *param) {
            int blockVar = 10;
        };
        
        for (NSInteger i = 0; i < 10; i++) {
            NSString *loopVar = @"loop";
        }
    }
    
    @end
    '''
    
    print("=== 详细调试变量提取过程 ===")
    print(f"原始内容:")
    print(test_content)
    
    # 1. 清理内容
    cleaned_content = custom_core.clean_content(test_content)
    print(f"\\n1. 清理后内容:")
    print(cleaned_content)
    
    # 2. 预处理：移除属性声明
    preprocessed = re.sub(r'@property\s*\([^)]*\)[^;]*;', '', cleaned_content, flags=re.MULTILINE)
    preprocessed = re.sub(r'@interface[^{]*\{[^}]*\}', '', preprocessed, flags=re.DOTALL)
    print(f"\\n2. 预处理后内容:")
    print(preprocessed)
    
    # 3. 提取方法体
    method_pattern = r'[-\+]\s*\([^)]*\)\s*[^{]*\{([^{}]*(?:\{[^{}]*\}[^{}]*)*)\}'
    method_bodies = re.findall(method_pattern, preprocessed, re.DOTALL)
    print(f"\\n3. 提取到的方法体数量: {len(method_bodies)}")
    for i, body in enumerate(method_bodies):
        print(f"\\n方法体 {i+1}:")
        print(repr(body))
    
    # 4. 测试预编译模式
    _COMPILED_PATTERNS = {
        'local_basic': re.compile(r'(?:BOOL|NSInteger|NSUInteger|CGFloat|int|float|double|long|short|char|size_t|NSTimeInterval|CGPoint|CGSize|CGRect|NSRange|UIEdgeInsets)\s+(\w+)\s*[=;]'),
        'local_pointer': re.compile(r'([A-Z]\w+)\s*\*\s*(\w+)\s*[=;]'),
        'local_block_param': re.compile(r'\^[^{]*\(\s*\w+\s+(\w+)\s*\)'),
        'local_block_var': re.compile(r'__block\s+\w+\s+(\w+)\s*='),
        'local_for_loop': re.compile(r'for\s*\(\s*(?:NSInteger|NSUInteger|int)\s+(\w+)\s*='),
    }
    
    print(f"\\n4. 测试预编译模式:")
    all_vars = []
    for i, body in enumerate(method_bodies):
        print(f"\\n  方法体 {i+1} 的匹配结果:")
        
        basic_matches = _COMPILED_PATTERNS['local_basic'].findall(body)
        print(f"    基本类型: {basic_matches}")
        all_vars.extend(basic_matches)
        
        pointer_matches = _COMPILED_PATTERNS['local_pointer'].findall(body)
        print(f"    指针类型: {pointer_matches}")
        all_vars.extend([match[1] for match in pointer_matches if len(match) >= 2])
        
        block_param_matches = _COMPILED_PATTERNS['local_block_param'].findall(body)
        print(f"    Block 参数: {block_param_matches}")
        all_vars.extend(block_param_matches)
        
        block_var_matches = _COMPILED_PATTERNS['local_block_var'].findall(body)
        print(f"    __block 变量: {block_var_matches}")
        all_vars.extend(block_var_matches)
    
    # 5. for 循环变量
    for_matches = _COMPILED_PATTERNS['local_for_loop'].findall(preprocessed)
    print(f"\\n5. for 循环变量: {for_matches}")
    all_vars.extend(for_matches)
    
    # 6. 测试补充模式
    print(f"\\n6. 测试补充模式:")
    additional_patterns = [
        (r'([A-Z]\w+)\s*\*\s*(\w+)\s*;', '不带赋值的指针声明'),
        (r'([A-Z]\w+)\s*\*\s*(\w+)\s*=\s*\[', '方法调用赋值'),
        (r'([A-Z]\w+)\s*\*\s*(\w+)\s*=\s*\w+\.', '属性访问赋值'),
    ]
    
    for body in method_bodies:
        for pattern, desc in additional_patterns:
            matches = re.findall(pattern, body)
            if matches:
                print(f"    {desc}: {matches}")
                all_vars.extend([match[1] for match in matches if len(match) >= 2])
    
    print(f"\\n7. 所有提取到的变量（去重前）: {all_vars}")
    
    # 8. 去重和过滤
    unique_vars = list(dict.fromkeys(all_vars))
    print(f"\\n8. 去重后: {unique_vars}")
    
    # 9. 过滤
    skip_words = {
        'self', 'super', 'nil', 'YES', 'NO', 'NULL', 'true', 'false',
        'void', 'int', 'float', 'double', 'BOOL', 'NSInteger', 'NSUInteger', 'CGFloat',
        'for', 'while', 'if', 'else', 'return', 'const', 'static', 'extern'
    }
    
    filtered_vars = []
    var_pattern = re.compile(r'^[a-zA-Z_][a-zA-Z0-9_]*$')
    
    for var in unique_vars:
        if (var and
            var not in skip_words and
            len(var) >= 1 and
            var_pattern.match(var)):
            filtered_vars.append(var)
        else:
            print(f"    过滤掉: {var} (原因: {'在跳过列表中' if var in skip_words else '不符合模式' if not var_pattern.match(var) else '长度不足'})")
    
    print(f"\\n9. 过滤后: {filtered_vars}")
    
    # 10. 对比完整函数的结果
    print(f"\\n10. 完整函数的结果:")
    complete_result = custom_core.extract_variables_unified(test_content, extract_types=['local'])
    print(f"    完整函数结果: {complete_result}")

if __name__ == "__main__":
    detailed_debug()
