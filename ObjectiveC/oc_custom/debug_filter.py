#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试过滤过程
"""

import sys
import os

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

def debug_filter():
    """调试过滤过程"""
    
    # 模拟必要的依赖
    class MockEPrintAnnotation:
        @staticmethod
        def search_annotation_and_delete(content):
            return content
        
        @staticmethod
        def search_print_and_delete(content):
            return content
    
    # 创建模拟模块
    sys.modules['ObjectiveC.oc_function.e_print_annotation'] = MockEPrintAnnotation()
    sys.modules['ObjectiveC.oc_custom.custom_util'] = type('MockUtil', (), {'list_system_pointer_types': []})()
    
    # 导入优化后的模块
    import custom_core
    
    # 复杂测试内容（从 debug_100_percent.py 复制）
    test_content = """@interface TestClass : NSObject
@end

@implementation TestClass

- (void)complexLocalVariablesMethod {
    // 基本类型
    NSString *localVar = @"test";
    NSArray *array = [[NSArray alloc] init];
    BOOL flag = YES;
    NSInteger counter = 0;
    CGFloat value = 3.14f;
    unsigned int unsignedVar = 10;
    signed long signedVar = -5;
    
    // 不带赋值的声明
    UIView *view;
    NSError *error;
    NSMutableDictionary *dict;
    
    // 方法调用赋值
    NSString *result = [self someMethod];
    UILabel *label = [[UILabel alloc] init];
    NSData *data = [NSData dataWithContentsOfFile:@"path"];
    
    // 属性访问赋值
    NSString *title = self.titleLabel.text;
    CGRect frame = self.view.frame;
    
    // 复杂初始化
    NSMutableArray *mutableArray = [[NSMutableArray alloc] initWithCapacity:10];
    UIViewController *controller = [[UIViewController alloc] initWithNibName:nil bundle:nil];
    
    // 错误处理
    NSString *content = [NSString stringWithContentsOfFile:@"path" encoding:NSUTF8StringEncoding error:&error];
    BOOL success = [self performOperationWithOptions:options error:&error];
    
    // 泛型
    NSArray<NSString *> *stringArray = @[];
    NSMutableDictionary<NSString *, NSNumber *> *numberDict = [[NSMutableDictionary alloc] init];
    
    // __block 变量
    __block NSInteger blockCounter = 0;
    __block NSString *blockString = @"block";
    __block UIView *blockView = nil;
    
    // Block 声明和使用
    void(^testBlock)(NSString *) = ^(NSString *param) {
        NSLog(@"Block param: %@", param);
        int blockVar = 10;
        NSMutableArray *blockArray = [[NSMutableArray alloc] init];
        CGFloat blockValue = 2.5f;
    };
    
    // for 循环
    for (NSInteger i = 0; i < 10; i++) {
        NSString *loopVar = [NSString stringWithFormat:@"item%ld", i];
        UIView *loopView = [[UIView alloc] init];
    }
    
    // for-in 循环
    for (NSString *item in array) {
        NSLog(@"Item: %@", item);
    }
    
    for (UIView *subview in self.view.subviews) {
        // subview processing
    }
    
    // 长类型名
    UIApplicationStateRestorationManager *restorationManager = nil;
    NSURLSessionDataTaskCompletionHandler completionHandler = nil;
    
    // 复杂方法调用
    NSString *processedString = [self processString:@"input" withOptions:options error:&error];
}

- (void)methodWithCompletion:(void(^)(NSError *completionError))completion {
    // completion block parameter
    completion(nil);
}

- (void)methodWithAccessor:(void(^)(NSString *accessorParam))accessor 
                byAccessor:(void(^)(UIView *view, NSString *viewParam))byAccessor {
    // multiple block parameters
}

@end
"""
    
    print("=== 调试过滤过程 ===")
    
    # 1. 直接调用 _extract_local_variables_fast
    print("1. 直接调用 _extract_local_variables_fast:")
    raw_variables = custom_core._extract_local_variables_fast(test_content)
    print(f"   原始提取变量数: {len(raw_variables)}")
    print(f"   原始变量列表: {raw_variables}")
    
    # 2. 测试过滤函数
    print(f"\\n2. 测试过滤函数:")
    filtered_variables = custom_core._filter_and_sort_variables(raw_variables, is_local=True)
    print(f"   过滤后变量数: {len(filtered_variables)}")
    print(f"   过滤后变量列表: {filtered_variables}")
    
    # 3. 分析被过滤掉的变量
    print(f"\\n3. 分析被过滤掉的变量:")
    filtered_out = [var for var in raw_variables if var not in filtered_variables]
    print(f"   被过滤掉的变量: {filtered_out}")
    
    # 4. 检查过滤条件
    print(f"\\n4. 检查过滤条件:")
    skip_words = {
        'self', 'super', 'nil', 'YES', 'NO', 'NULL', 'true', 'false',
        'void', 'int', 'float', 'double', 'BOOL', 'NSInteger', 'NSUInteger', 'CGFloat',
        'for', 'while', 'if', 'else', 'return', 'const', 'static', 'extern'
    }
    
    for var in filtered_out:
        reasons = []
        if not var:
            reasons.append("空变量")
        if var in skip_words:
            reasons.append("在跳过列表中")
        if len(var) < 1:
            reasons.append("长度不足")
        if not custom_core._COMPILED_PATTERNS['variable_name'].match(var):
            reasons.append("不符合变量名模式")
        
        print(f"   {var}: {', '.join(reasons) if reasons else '未知原因'}")
    
    # 5. 对比完整函数结果
    print(f"\\n5. 完整函数结果:")
    complete_result = custom_core.extract_variables_unified(test_content, extract_types=['local'])
    print(f"   完整函数结果: {complete_result}")
    
    # 6. 检查是否有重复变量导致的问题
    print(f"\\n6. 检查重复变量:")
    from collections import Counter
    var_counts = Counter(raw_variables)
    duplicates = {var: count for var, count in var_counts.items() if count > 1}
    if duplicates:
        print(f"   重复变量: {duplicates}")
    else:
        print(f"   无重复变量")

if __name__ == "__main__":
    debug_filter()
