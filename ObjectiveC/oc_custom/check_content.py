#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查测试内容格式
"""

import sys
import os

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

def check_content_format():
    """检查测试内容格式"""
    
    # 模拟必要的依赖
    class MockEPrintAnnotation:
        @staticmethod
        def search_annotation_and_delete(content):
            return content
        
        @staticmethod
        def search_print_and_delete(content):
            return content
    
    # 创建模拟模块
    sys.modules['ObjectiveC.oc_function.e_print_annotation'] = MockEPrintAnnotation()
    sys.modules['ObjectiveC.oc_custom.custom_util'] = type('MockUtil', (), {'list_system_pointer_types': []})()
    
    # 导入优化后的模块
    import custom_core
    
    # 从 comprehensive_test.py 复制的测试内容
    test_content = '''
    @interface TestClass : NSObject
    @end
    
    @implementation TestClass
    
    - (void)complexLocalVariablesMethod {
        // 基本类型
        NSString *localVar = @"test";
        NSArray *array = [[NSArray alloc] init];
        BOOL flag = YES;
        NSInteger counter = 0;
        CGFloat value = 3.14f;
        unsigned int unsignedVar = 10;
        signed long signedVar = -5;
        
        // 不带赋值的声明
        UIView *view;
        NSError *error;
        NSMutableDictionary *dict;
        
        // 方法调用赋值
        NSString *result = [self someMethod];
        UILabel *label = [[UILabel alloc] init];
        NSData *data = [NSData dataWithContentsOfFile:@"path"];
        
        // 属性访问赋值
        NSString *title = self.titleLabel.text;
        CGRect frame = self.view.frame;
        
        // 复杂初始化
        NSMutableArray *mutableArray = [[NSMutableArray alloc] initWithCapacity:10];
        UIViewController *controller = [[UIViewController alloc] initWithNibName:nil bundle:nil];
        
        // 错误处理
        NSString *content = [NSString stringWithContentsOfFile:@"path" encoding:NSUTF8StringEncoding error:&error];
        BOOL success = [self performOperationWithOptions:options error:&error];
        
        // 泛型
        NSArray<NSString *> *stringArray = @[];
        NSMutableDictionary<NSString *, NSNumber *> *numberDict = [[NSMutableDictionary alloc] init];
        
        // __block 变量
        __block NSInteger blockCounter = 0;
        __block NSString *blockString = @"block";
        __block UIView *blockView = nil;
        
        // Block 声明和使用
        void(^testBlock)(NSString *) = ^(NSString *param) {
            NSLog(@"Block param: %@", param);
            int blockVar = 10;
            NSMutableArray *blockArray = [[NSMutableArray alloc] init];
            CGFloat blockValue = 2.5f;
        };
        
        // for 循环
        for (NSInteger i = 0; i < 10; i++) {
            NSString *loopVar = [NSString stringWithFormat:@"item%ld", i];
            UIView *loopView = [[UIView alloc] init];
        }
        
        // for-in 循环
        for (NSString *item in array) {
            NSLog(@"Item: %@", item);
        }
        
        for (UIView *subview in self.view.subviews) {
            // subview processing
        }
        
        // 长类型名
        UIApplicationStateRestorationManager *restorationManager = nil;
        NSURLSessionDataTaskCompletionHandler completionHandler = nil;
        
        // 复杂方法调用
        NSString *processedString = [self processString:@"input" withOptions:options error:&error];
    }
    
    + (void)classMethodWithStaticVars {
        static NSString *staticString = @"static";
        static const BOOL staticFlag = YES;
        static NSMutableArray *staticArray = nil;
        static UIView *staticView;
    }
    
    - (void)methodWithCompletion:(void(^)(NSError *completionError))completion {
        // completion block parameter
        completion(nil);
    }
    
    - (void)methodWithAccessor:(void(^)(NSString *accessorParam))accessor 
                    byAccessor:(void(^)(UIView *view, NSString *viewParam))byAccessor {
        // multiple block parameters
    }
    
    @end
    
    // 静态变量
    static NSString *globalStaticString = @"global";
    static const NSInteger globalStaticNumber = 42;
    static NSMutableDictionary *globalStaticDict = nil;
    static UIViewController *globalStaticController;
    
    // 外部常量
    extern NSString *externString;
    extern const NSInteger externNumber;
    extern UIView *externView;
    extern NSArray<NSString *> *externStringArray;
    '''
    
    print("=== 检查测试内容格式 ===")
    print(f"内容长度: {len(test_content)} 字符")
    print(f"行数: {len(test_content.split('\\n'))} 行")
    print(f"包含换行符: {'\\n' in test_content}")
    
    # 显示前几行
    lines = test_content.split('\\n')
    print(f"\\n前10行:")
    for i, line in enumerate(lines[:10]):
        print(f"  {i+1}: {repr(line)}")
    
    # 测试局部变量提取
    print(f"\\n局部变量提取结果:")
    local_vars = custom_core.extract_variables_unified(test_content, extract_types=['local'])
    print(f"  提取到的变量数: {len(local_vars)}")
    print(f"  变量列表: {local_vars}")

if __name__ == "__main__":
    check_content_format()
