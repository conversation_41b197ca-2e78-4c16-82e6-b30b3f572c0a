#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试复杂测试内容的变量提取
"""

import sys
import os
import re

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

def debug_complex_content():
    """调试复杂测试内容"""
    
    # 模拟必要的依赖
    class MockEPrintAnnotation:
        @staticmethod
        def search_annotation_and_delete(content):
            return content
        
        @staticmethod
        def search_print_and_delete(content):
            return content
    
    # 创建模拟模块
    sys.modules['ObjectiveC.oc_function.e_print_annotation'] = MockEPrintAnnotation()
    sys.modules['ObjectiveC.oc_custom.custom_util'] = type('MockUtil', (), {'list_system_pointer_types': []})()
    
    # 导入优化后的模块
    import custom_core
    
    # 复杂测试内容的一部分
    test_content = '''
    @implementation TestClass
    
    - (void)complexLocalVariablesMethod {
        // 基本类型
        NSString *localVar = @"test";
        NSArray *array = [[NSArray alloc] init];
        BOOL flag = YES;
        NSInteger counter = 0;
        CGFloat value = 3.14f;
        unsigned int unsignedVar = 10;
        signed long signedVar = -5;
        
        // 不带赋值的声明
        UIView *view;
        NSError *error;
        NSMutableDictionary *dict;
        
        // 方法调用赋值
        NSString *result = [self someMethod];
        UILabel *label = [[UILabel alloc] init];
        NSData *data = [NSData dataWithContentsOfFile:@"path"];
        
        // 属性访问赋值
        NSString *title = self.titleLabel.text;
        CGRect frame = self.view.frame;
        
        // 复杂初始化
        NSMutableArray *mutableArray = [[NSMutableArray alloc] initWithCapacity:10];
        UIViewController *controller = [[UIViewController alloc] initWithNibName:nil bundle:nil];
        
        // 错误处理
        NSString *content = [NSString stringWithContentsOfFile:@"path" encoding:NSUTF8StringEncoding error:&error];
        BOOL success = [self performOperationWithOptions:options error:&error];
        
        // 泛型
        NSArray<NSString *> *stringArray = @[];
        NSMutableDictionary<NSString *, NSNumber *> *numberDict = [[NSMutableDictionary alloc] init];
        
        // __block 变量
        __block NSInteger blockCounter = 0;
        __block NSString *blockString = @"block";
        __block UIView *blockView = nil;
        
        // Block 声明和使用
        void(^testBlock)(NSString *) = ^(NSString *param) {
            NSLog(@"Block param: %@", param);
            int blockVar = 10;
            NSMutableArray *blockArray = [[NSMutableArray alloc] init];
            CGFloat blockValue = 2.5f;
        };
        
        // for 循环
        for (NSInteger i = 0; i < 10; i++) {
            NSString *loopVar = [NSString stringWithFormat:@"item%ld", i];
            UIView *loopView = [[UIView alloc] init];
        }
        
        // for-in 循环
        for (NSString *item in array) {
            NSLog(@"Item: %@", item);
        }
        
        for (UIView *subview in self.view.subviews) {
            // subview processing
        }
        
        // 长类型名
        UIApplicationStateRestorationManager *restorationManager = nil;
        NSURLSessionDataTaskCompletionHandler completionHandler = nil;
        
        // 复杂方法调用
        NSString *processedString = [self processString:@"input" withOptions:options error:&error];
    }
    
    @end
    '''
    
    print("=== 调试复杂测试内容 ===")
    
    # 提取方法体
    method_pattern = r'[-\+]\s*\([^)]*\)\s*[^{]*\{([^{}]*(?:\{[^{}]*\}[^{}]*)*)\}'
    method_bodies = re.findall(method_pattern, test_content, re.DOTALL)
    
    print(f"提取到的方法体数量: {len(method_bodies)}")
    
    if method_bodies:
        body = method_bodies[0]
        print(f"\\n方法体内容:")
        print(body)
        
        # 测试各种模式
        patterns_to_test = [
            (r'(?:BOOL|NSInteger|NSUInteger|CGFloat|int|float|double|long|short|char|size_t|NSTimeInterval|CGPoint|CGSize|CGRect|NSRange|UIEdgeInsets)\s+(\w+)\s*[=;]', '基本类型'),
            (r'([A-Z]\w+)\s*\*\s*(\w+)\s*[=;]', '指针类型'),
            (r'([A-Z]\w+)\s*\*\s*(\w+)\s*;', '不带赋值的指针'),
            (r'([A-Z]\w+)\s*\*\s*(\w+)\s*=\s*\[', '方法调用赋值'),
            (r'([A-Z]\w+)\s*\*\s*(\w+)\s*=\s*\w+\.', '属性访问赋值'),
            (r'([A-Z]\w+)\s*<[^>]*>\s*\*\s*(\w+)\s*=', '泛型指针'),
            (r'__block\s+\w+\s+(\w+)\s*=', '__block 变量'),
            (r'__block\s+\w+\s*\*\s*(\w+)\s*=', '__block 指针'),
            (r'(?:unsigned|signed)\s+(?:int|long|short|char)\s+(\w+)\s*[=;]', 'unsigned/signed'),
            (r'([A-Z][a-zA-Z0-9_]{10,})\s*\*\s*(\w+)\s*=', '长类型名'),
        ]
        
        print(f"\\n各种模式的匹配结果:")
        all_matches = []
        
        for pattern, desc in patterns_to_test:
            matches = re.findall(pattern, body)
            print(f"  {desc}: {matches}")
            
            # 处理匹配结果
            for match in matches:
                if isinstance(match, tuple):
                    if len(match) >= 2:
                        all_matches.append(match[1])
                    elif len(match) == 1:
                        all_matches.append(match[0])
                else:
                    all_matches.append(match)
        
        print(f"\\n所有匹配到的变量: {all_matches}")
        
        # 测试 for 循环
        for_matches = re.findall(r'for\s*\(\s*(?:NSInteger|NSUInteger|int)\s+(\w+)\s*=', test_content)
        print(f"\\nfor 循环变量: {for_matches}")
        all_matches.extend(for_matches)
        
        # 测试 for-in 循环
        for_in_matches = re.findall(r'for\s*\(\s*(\w+)\s+\*\s*(\w+)\s+in\s+', test_content)
        print(f"for-in 循环变量: {for_in_matches}")
        all_matches.extend([match[1] for match in for_in_matches])
        
        # 去重
        unique_matches = list(dict.fromkeys(all_matches))
        print(f"\\n去重后的所有变量: {unique_matches}")
        
        # 对比完整函数结果
        complete_result = custom_core.extract_variables_unified(test_content, extract_types=['local'])
        print(f"\\n完整函数结果: {complete_result}")
        
        # 找出缺失的变量
        missing = [var for var in unique_matches if var not in complete_result]
        extra = [var for var in complete_result if var not in unique_matches]
        
        if missing:
            print(f"\\n缺失的变量: {missing}")
        if extra:
            print(f"额外的变量: {extra}")

if __name__ == "__main__":
    debug_complex_content()
