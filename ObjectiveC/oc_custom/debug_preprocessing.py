#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试预处理问题
"""

import sys
import os
import re

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

def debug_preprocessing():
    """调试预处理问题"""
    
    # 模拟必要的依赖
    class MockEPrintAnnotation:
        @staticmethod
        def search_annotation_and_delete(content):
            return content
        
        @staticmethod
        def search_print_and_delete(content):
            return content
    
    # 创建模拟模块
    sys.modules['ObjectiveC.oc_function.e_print_annotation'] = MockEPrintAnnotation()
    sys.modules['ObjectiveC.oc_custom.custom_util'] = type('MockUtil', (), {'list_system_pointer_types': []})()
    
    # 导入优化后的模块
    import custom_core
    
    # 复杂测试内容（从 debug_100_percent.py 复制）
    test_content = """@interface TestClass : NSObject
@end

@implementation TestClass

- (void)complexLocalVariablesMethod {
    // 基本类型
    NSString *localVar = @"test";
    NSArray *array = [[NSArray alloc] init];
    BOOL flag = YES;
    NSInteger counter = 0;
    CGFloat value = 3.14f;
    unsigned int unsignedVar = 10;
    signed long signedVar = -5;
    
    // 不带赋值的声明
    UIView *view;
    NSError *error;
    NSMutableDictionary *dict;
    
    // 方法调用赋值
    NSString *result = [self someMethod];
    UILabel *label = [[UILabel alloc] init];
    NSData *data = [NSData dataWithContentsOfFile:@"path"];
    
    // 属性访问赋值
    NSString *title = self.titleLabel.text;
    CGRect frame = self.view.frame;
    
    // 复杂初始化
    NSMutableArray *mutableArray = [[NSMutableArray alloc] initWithCapacity:10];
    UIViewController *controller = [[UIViewController alloc] initWithNibName:nil bundle:nil];
    
    // 错误处理
    NSString *content = [NSString stringWithContentsOfFile:@"path" encoding:NSUTF8StringEncoding error:&error];
    BOOL success = [self performOperationWithOptions:options error:&error];
    
    // 泛型
    NSArray<NSString *> *stringArray = @[];
    NSMutableDictionary<NSString *, NSNumber *> *numberDict = [[NSMutableDictionary alloc] init];
    
    // __block 变量
    __block NSInteger blockCounter = 0;
    __block NSString *blockString = @"block";
    __block UIView *blockView = nil;
    
    // Block 声明和使用
    void(^testBlock)(NSString *) = ^(NSString *param) {
        NSLog(@"Block param: %@", param);
        int blockVar = 10;
        NSMutableArray *blockArray = [[NSMutableArray alloc] init];
        CGFloat blockValue = 2.5f;
    };
    
    // for 循环
    for (NSInteger i = 0; i < 10; i++) {
        NSString *loopVar = [NSString stringWithFormat:@"item%ld", i];
        UIView *loopView = [[UIView alloc] init];
    }
    
    // for-in 循环
    for (NSString *item in array) {
        NSLog(@"Item: %@", item);
    }
    
    for (UIView *subview in self.view.subviews) {
        // subview processing
    }
    
    // 长类型名
    UIApplicationStateRestorationManager *restorationManager = nil;
    NSURLSessionDataTaskCompletionHandler completionHandler = nil;
    
    // 复杂方法调用
    NSString *processedString = [self processString:@"input" withOptions:options error:&error];
}

- (void)methodWithCompletion:(void(^)(NSError *completionError))completion {
    // completion block parameter
    completion(nil);
}

- (void)methodWithAccessor:(void(^)(NSString *accessorParam))accessor 
                byAccessor:(void(^)(UIView *view, NSString *viewParam))byAccessor {
    // multiple block parameters
}

@end
"""
    
    print("=== 调试预处理问题 ===")
    print(f"原始内容大小: {len(test_content)} 字符")
    print(f"原始内容行数: {len(test_content.split(chr(10)))} 行")
    
    # 1. 应用 clean_content
    print(f"\\n1. 应用 clean_content:")
    cleaned_content = custom_core.clean_content(test_content)
    print(f"   清理后大小: {len(cleaned_content)} 字符")
    print(f"   清理后行数: {len(cleaned_content.split(chr(10)))} 行")
    
    # 2. 应用 extract_variables_unified 中的预处理
    print(f"\\n2. 应用 extract_variables_unified 预处理:")
    
    # 第一步预处理：移除 @property
    step1 = re.sub(r'@property\s*\([^)]*\)[^;]*;', '', cleaned_content, flags=re.MULTILINE)
    print(f"   移除 @property 后大小: {len(step1)} 字符")
    print(f"   移除 @property 后行数: {len(step1.split(chr(10)))} 行")
    
    # 第二步预处理：移除 @interface
    step2 = re.sub(r'@interface[^{]*\{[^}]*\}', '', step1, flags=re.DOTALL)
    print(f"   移除 @interface 后大小: {len(step2)} 字符")
    print(f"   移除 @interface 后行数: {len(step2.split(chr(10)))} 行")
    
    # 3. 检查方法体提取
    print(f"\\n3. 检查方法体提取:")
    
    # 原始内容的方法体提取
    original_bodies = custom_core._COMPILED_PATTERNS['method_body'].findall(test_content)
    print(f"   原始内容方法体数: {len(original_bodies)}")
    
    # 清理后内容的方法体提取
    cleaned_bodies = custom_core._COMPILED_PATTERNS['method_body'].findall(cleaned_content)
    print(f"   清理后内容方法体数: {len(cleaned_bodies)}")
    
    # 预处理后内容的方法体提取
    processed_bodies = custom_core._COMPILED_PATTERNS['method_body'].findall(step2)
    print(f"   预处理后内容方法体数: {len(processed_bodies)}")
    
    # 4. 检查第一个方法体的内容
    if processed_bodies:
        print(f"\\n4. 第一个方法体内容检查:")
        first_body = processed_bodies[0]
        print(f"   方法体大小: {len(first_body)} 字符")
        print(f"   包含 localVar: {'localVar' in first_body}")
        print(f"   包含 flag: {'flag' in first_body}")
        print(f"   包含 counter: {'counter' in first_body}")
        
        # 显示前200个字符
        preview = first_body[:200] + "..." if len(first_body) > 200 else first_body
        print(f"   前200字符: {repr(preview)}")
    
    # 5. 直接在预处理后的内容上调用 _extract_local_variables_fast
    print(f"\\n5. 在预处理后内容上调用 _extract_local_variables_fast:")
    direct_vars = custom_core._extract_local_variables_fast(step2)
    print(f"   直接提取变量数: {len(direct_vars)}")
    print(f"   变量列表: {direct_vars[:20]}...")  # 只显示前20个
    
    # 6. 对比完整函数结果
    print(f"\\n6. 完整函数结果:")
    complete_result = custom_core.extract_variables_unified(test_content, extract_types=['local'])
    print(f"   完整函数结果: {complete_result}")

if __name__ == "__main__":
    debug_preprocessing()
