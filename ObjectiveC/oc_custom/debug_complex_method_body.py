#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试复杂测试内容的方法体提取
"""

import sys
import os
import re

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

def debug_complex_method_body():
    """调试复杂测试内容的方法体提取"""
    
    # 模拟必要的依赖
    class MockEPrintAnnotation:
        @staticmethod
        def search_annotation_and_delete(content):
            return content
        
        @staticmethod
        def search_print_and_delete(content):
            return content
    
    # 创建模拟模块
    sys.modules['ObjectiveC.oc_function.e_print_annotation'] = MockEPrintAnnotation()
    sys.modules['ObjectiveC.oc_custom.custom_util'] = type('MockUtil', (), {'list_system_pointer_types': []})()
    
    # 导入优化后的模块
    import custom_core
    
    # 复杂测试内容（从 debug_100_percent.py 复制）
    test_content = """@interface TestClass : NSObject
@end

@implementation TestClass

- (void)complexLocalVariablesMethod {
    // 基本类型
    NSString *localVar = @"test";
    NSArray *array = [[NSArray alloc] init];
    BOOL flag = YES;
    NSInteger counter = 0;
    CGFloat value = 3.14f;
    unsigned int unsignedVar = 10;
    signed long signedVar = -5;
    
    // 不带赋值的声明
    UIView *view;
    NSError *error;
    NSMutableDictionary *dict;
    
    // 方法调用赋值
    NSString *result = [self someMethod];
    UILabel *label = [[UILabel alloc] init];
    NSData *data = [NSData dataWithContentsOfFile:@"path"];
    
    // 属性访问赋值
    NSString *title = self.titleLabel.text;
    CGRect frame = self.view.frame;
    
    // 复杂初始化
    NSMutableArray *mutableArray = [[NSMutableArray alloc] initWithCapacity:10];
    UIViewController *controller = [[UIViewController alloc] initWithNibName:nil bundle:nil];
    
    // 错误处理
    NSString *content = [NSString stringWithContentsOfFile:@"path" encoding:NSUTF8StringEncoding error:&error];
    BOOL success = [self performOperationWithOptions:options error:&error];
    
    // 泛型
    NSArray<NSString *> *stringArray = @[];
    NSMutableDictionary<NSString *, NSNumber *> *numberDict = [[NSMutableDictionary alloc] init];
    
    // __block 变量
    __block NSInteger blockCounter = 0;
    __block NSString *blockString = @"block";
    __block UIView *blockView = nil;
    
    // Block 声明和使用
    void(^testBlock)(NSString *) = ^(NSString *param) {
        NSLog(@"Block param: %@", param);
        int blockVar = 10;
        NSMutableArray *blockArray = [[NSMutableArray alloc] init];
        CGFloat blockValue = 2.5f;
    };
    
    // for 循环
    for (NSInteger i = 0; i < 10; i++) {
        NSString *loopVar = [NSString stringWithFormat:@"item%ld", i];
        UIView *loopView = [[UIView alloc] init];
    }
    
    // for-in 循环
    for (NSString *item in array) {
        NSLog(@"Item: %@", item);
    }
    
    for (UIView *subview in self.view.subviews) {
        // subview processing
    }
    
    // 长类型名
    UIApplicationStateRestorationManager *restorationManager = nil;
    NSURLSessionDataTaskCompletionHandler completionHandler = nil;
    
    // 复杂方法调用
    NSString *processedString = [self processString:@"input" withOptions:options error:&error];
}

+ (void)classMethodWithStaticVars {
    static NSString *staticString = @"static";
    static const BOOL staticFlag = YES;
    static NSMutableArray *staticArray = nil;
    static UIView *staticView;
}

- (void)methodWithCompletion:(void(^)(NSError *completionError))completion {
    // completion block parameter
    completion(nil);
}

- (void)methodWithAccessor:(void(^)(NSString *accessorParam))accessor 
                byAccessor:(void(^)(UIView *view, NSString *viewParam))byAccessor {
    // multiple block parameters
}

@end

// 静态变量
static NSString *globalStaticString = @"global";
static const NSInteger globalStaticNumber = 42;
static NSMutableDictionary *globalStaticDict = nil;
static UIViewController *globalStaticController;

// 外部常量
extern NSString *externString;
extern const NSInteger externNumber;
extern UIView *externView;
extern NSArray<NSString *> *externStringArray;
"""
    
    print("=== 调试复杂测试内容的方法体提取 ===")
    print(f"测试内容大小: {len(test_content)} 字符")
    print(f"测试内容行数: {len(test_content.split(chr(10)))} 行")
    
    # 1. 测试方法体提取
    print(f"\\n1. 方法体提取:")
    method_bodies = custom_core._COMPILED_PATTERNS['method_body'].findall(test_content)
    print(f"   提取到的方法体数量: {len(method_bodies)}")
    
    for i, body in enumerate(method_bodies):
        print(f"\\n   方法体 {i+1} (长度: {len(body)} 字符):")
        # 只显示前200个字符
        preview = body[:200] + "..." if len(body) > 200 else body
        print(f"   {repr(preview)}")
        
        # 检查是否包含预期的变量声明
        if 'localVar' in body:
            print(f"   ✅ 包含 localVar")
        if 'flag' in body:
            print(f"   ✅ 包含 flag")
        if 'static' in body:
            print(f"   ⚠️  包含 static 声明")
    
    # 2. 测试第一个方法体的变量提取
    if method_bodies:
        main_body = method_bodies[0]
        print(f"\\n2. 第一个方法体的变量提取:")
        
        # 应用静态变量过滤
        filtered_body = re.sub(r'^\s*static\s+[^;]+;', '', main_body, flags=re.MULTILINE)
        filtered_body = re.sub(r'static\s+[^=;]+[=;][^;]*;', '', filtered_body)
        
        print(f"   过滤前长度: {len(main_body)}")
        print(f"   过滤后长度: {len(filtered_body)}")
        
        # 基本类型匹配
        basic_matches = custom_core._COMPILED_PATTERNS['local_basic_types'].findall(filtered_body)
        print(f"   基本类型匹配: {basic_matches}")
        
        # 指针类型匹配
        pointer_matches = custom_core._COMPILED_PATTERNS['local_pointer_types'].findall(filtered_body)
        print(f"   指针类型匹配: {[match[1] for match in pointer_matches if len(match) >= 2]}")
        
        # __block 变量匹配
        block_matches = custom_core._COMPILED_PATTERNS['local_block_var'].findall(filtered_body)
        print(f"   __block 变量匹配: {block_matches}")
    
    # 3. 测试完整的局部变量提取
    print(f"\\n3. 完整的局部变量提取:")
    local_vars = custom_core.extract_variables_unified(test_content, extract_types=['local'])
    print(f"   提取到的局部变量数: {len(local_vars)}")
    print(f"   变量列表: {local_vars}")
    
    # 4. 分析缺失的变量
    expected_vars = ['localVar', 'array', 'flag', 'counter', 'value', 'view', 'error', 'dict']
    found_vars = [var for var in expected_vars if var in local_vars]
    missing_vars = [var for var in expected_vars if var not in local_vars]
    
    print(f"\\n4. 基本变量分析:")
    print(f"   预期基本变量: {expected_vars}")
    print(f"   找到: {found_vars}")
    print(f"   缺失: {missing_vars}")

if __name__ == "__main__":
    debug_complex_method_body()
