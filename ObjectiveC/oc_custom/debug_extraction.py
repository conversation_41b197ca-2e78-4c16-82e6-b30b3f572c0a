#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试脚本：检查方法体提取是否正常工作
"""

import sys
import os
import re

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

def debug_method_extraction():
    """调试方法体提取"""
    
    # 模拟必要的依赖
    class MockEPrintAnnotation:
        @staticmethod
        def search_annotation_and_delete(content):
            return content
        
        @staticmethod
        def search_print_and_delete(content):
            return content
    
    # 创建模拟模块
    sys.modules['ObjectiveC.oc_function.e_print_annotation'] = MockEPrintAnnotation()
    sys.modules['ObjectiveC.oc_custom.custom_util'] = type('MockUtil', (), {'list_system_pointer_types': []})()
    
    # 导入优化后的模块
    import custom_core
    
    # 简单的测试内容
    test_content = '''
    @implementation TestClass
    
    - (void)simpleMethod {
        NSString *localVar = @"test";
        BOOL flag = YES;
        NSInteger counter = 0;
    }
    
    + (void)classMethod {
        static NSString *staticVar = @"static";
    }
    
    @end
    '''
    
    print("=== 调试方法体提取 ===")
    print(f"原始内容:")
    print(test_content)
    
    # 清理内容
    cleaned_content = custom_core.clean_content(test_content)
    print(f"\\n清理后内容:")
    print(cleaned_content)
    
    # 测试方法体提取正则
    method_pattern = r'[-\+]\s*\([^)]*\)\s*[^{]*\{([^{}]*(?:\{[^{}]*\}[^{}]*)*)\}'
    method_bodies = re.findall(method_pattern, cleaned_content, re.DOTALL)
    
    print(f"\\n提取到的方法体数量: {len(method_bodies)}")
    for i, body in enumerate(method_bodies):
        print(f"\\n方法体 {i+1}:")
        print(repr(body))
        print("内容:")
        print(body)
    
    # 测试预编译模式
    _COMPILED_PATTERNS = {
        'local_basic': re.compile(r'(?:BOOL|NSInteger|NSUInteger|CGFloat|int|float|double|long|short|char|size_t|NSTimeInterval|CGPoint|CGSize|CGRect|NSRange|UIEdgeInsets)\s+(\w+)\s*[=;]'),
        'local_pointer': re.compile(r'([A-Z]\w+)\s*\*\s*(\w+)\s*[=;]'),
    }
    
    print(f"\\n测试预编译模式:")
    for body in method_bodies:
        basic_matches = _COMPILED_PATTERNS['local_basic'].findall(body)
        pointer_matches = _COMPILED_PATTERNS['local_pointer'].findall(body)
        
        print(f"  基本类型匹配: {basic_matches}")
        print(f"  指针类型匹配: {pointer_matches}")
    
    # 测试完整的局部变量提取
    print(f"\\n完整的局部变量提取:")
    local_vars = custom_core.extract_variables_unified(test_content, extract_types=['local'])
    print(f"  提取到的局部变量: {local_vars}")
    
    # 测试静态变量提取
    print(f"\\n静态变量提取:")
    static_vars = custom_core.extract_variables_unified(test_content, extract_types=['static'])
    print(f"  提取到的静态变量: {static_vars}")

if __name__ == "__main__":
    debug_method_extraction()
