#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Objective-C 系统框架类型配置文件
基于iOS/macOS系统框架的常用类型
"""

# Foundation框架类型
FOUNDATION_TYPES = [
    # 基础类型
    'NSObject', 'NSProxy',
    
    # 字符串和数据
    'NSString', 'NSMutableString', 'NSAttributedString', 'NSMutableAttributedString',
    'NSData', 'NSMutableData', 'NSPurgeableData',
    
    # 集合类型
    'NSArray', 'NSMutableArray', 'NSPointerArray',
    'NSDictionary', 'NSMutableDictionary', 'NSMapTable',
    'NSSet', 'NSMutableSet', 'NSCountedSet', 'NSOrderedSet', 'NSMutableOrderedSet',
    'NSHashTable', 'NSPointerFunctions',
    
    # 数值类型
    'NSNumber', 'NSDecimalNumber', 'NSNumberFormatter',
    'NSValue', 'NSNull',
    
    # 日期和时间
    'NSDate', 'NSDateComponents', 'NSDateFormatter', 'NSDateInterval',
    'NSCalendar', 'NSTimeZone', 'NSLocale',
    'NSTimer', 'NSDateComponentsFormatter',
    
    # URL和网络
    'NSURL', 'NSURLComponents', 'NSURLQueryItem', 'NSURLRequest', 'NSMutableURLRequest',
    'NSURLResponse', 'NSHTTPURLResponse', 'NSURLSession', 'NSURLSessionTask',
    'NSURLSessionDataTask', 'NSURLSessionUploadTask', 'NSURLSessionDownloadTask',
    'NSURLConnection', 'NSURLCache', 'NSHTTPCookieStorage', 'NSHTTPCookie',
    
    # 文件系统
    'NSFileManager', 'NSFileHandle', 'NSFileWrapper', 'NSDirectoryEnumerator',
    'NSBundle', 'NSProcessInfo', 'NSPipe', 'NSTask', 'NSFileCoordinator',
    
    # 错误处理
    'NSError', 'NSException', 'NSAssertionHandler',
    
    # 线程和并发
    'NSThread', 'NSRunLoop', 'NSOperation', 'NSOperationQueue', 'NSBlockOperation',
    'NSInvocationOperation', 'NSCondition', 'NSConditionLock', 'NSLock', 'NSRecursiveLock',
    'NSDistributedLock', 'NSSemaphore',
    
    # 通知和观察
    'NSNotification', 'NSNotificationCenter', 'NSDistributedNotificationCenter',
    'NSNotificationQueue', 'NSKeyValueObservation',
    
    # 序列化
    'NSCoder', 'NSKeyedArchiver', 'NSKeyedUnarchiver', 'NSArchiver', 'NSUnarchiver',
    'NSPropertyListSerialization', 'NSJSONSerialization',
    
    # 正则表达式
    'NSRegularExpression', 'NSTextCheckingResult', 'NSDataDetector',
    
    # 其他工具类
    'NSScanner', 'NSFormatter', 'NSByteCountFormatter', 'NSLengthFormatter',
    'NSMassFormatter', 'NSEnergyFormatter', 'NSPersonNameComponents',
    'NSPersonNameComponentsFormatter', 'NSMeasurement', 'NSUnit', 'NSDimension',
    'NSUnitConverter', 'NSProgress', 'NSUserDefaults', 'NSUserActivity',
    'NSExtensionContext', 'NSExtensionItem', 'NSItemProvider',

    # 更多Foundation类型
    'NSCache', 'NSPurgeableData', 'NSHashTable', 'NSMapTable', 'NSPointerArray',
    'NSPointerFunctions', 'NSCountedSet', 'NSOrderedSet', 'NSMutableOrderedSet',
    'NSInvocation', 'NSMethodSignature', 'NSProxy', 'NSDistributedLock',
    'NSCondition', 'NSConditionLock', 'NSRecursiveLock', 'NSLock',
    'NSRunLoop', 'NSTimer', 'NSPort', 'NSMachPort', 'NSMessagePort', 'NSSocketPort',
    'NSConnection', 'NSDistributedObject', 'NSProtocolChecker',
    'NSUndoManager', 'NSValueTransformer', 'NSClassDescription',
    'NSScriptCommand', 'NSScriptCommandDescription', 'NSScriptExecutionContext',
    'NSAppleScript', 'NSAppleEventDescriptor', 'NSAppleEventManager',
    'NSMetadataQuery', 'NSMetadataItem', 'NSMetadataQueryAttributeValueTuple',
    'NSMetadataQueryResultGroup', 'NSSpellServer', 'NSOrthography',
    'NSLinguisticTagger', 'NSTextCheckingResult', 'NSDataDetector',
    'NSURLCredential', 'NSURLCredentialStorage', 'NSURLProtectionSpace',
    'NSURLAuthenticationChallenge', 'NSURLProtocol', 'NSCachedURLResponse',
    'NSHTTPCookieStorage', 'NSHTTPCookie', 'NSNetService', 'NSNetServiceBrowser',
    'NSStream', 'NSInputStream', 'NSOutputStream', 'NSHost'
    
    # 流和管道
    'NSStream', 'NSInputStream', 'NSOutputStream', 'NSHost', 'NSNetService',
    'NSNetServiceBrowser', 'NSPort', 'NSMachPort', 'NSMessagePort', 'NSSocketPort',
    
    # 表达式和谓词
    'NSExpression', 'NSPredicate', 'NSComparisonPredicate', 'NSCompoundPredicate',
    
    # 索引和排序
    'NSIndexPath', 'NSIndexSet', 'NSMutableIndexSet', 'NSSortDescriptor',
    
    # UUID和标识符
    'NSUUID', 'NSUserNotification', 'NSUserNotificationCenter'
]

# UIKit框架类型
UIKIT_TYPES = [
    # 视图基础
    'UIView', 'UIWindow', 'UIScreen', 'UIViewController', 'UINavigationController',
    'UITabBarController', 'UISplitViewController', 'UIPageViewController',
    'UIPopoverController', 'UIPopoverPresentationController', 'UIPresentationController',
    
    # 控件
    'UILabel', 'UIButton', 'UIImageView', 'UITextField', 'UITextView', 'UISearchBar',
    'UISlider', 'UISwitch', 'UIStepper', 'UISegmentedControl', 'UIProgressView',
    'UIActivityIndicatorView', 'UIPickerView', 'UIDatePicker', 'UIPageControl',
    'UIRefreshControl', 'UIStackView',
    
    # 滚动视图
    'UIScrollView', 'UITableView', 'UICollectionView', 'UIWebView', 'WKWebView',
    
    # 表格视图
    'UITableViewCell', 'UITableViewHeaderFooterView', 'UITableViewController',
    
    # 集合视图
    'UICollectionViewCell', 'UICollectionReusableView', 'UICollectionViewController',
    'UICollectionViewLayout', 'UICollectionViewFlowLayout',
    'UICollectionViewLayoutAttributes', 'UICollectionViewTransitionLayout',
    
    # 导航
    'UINavigationBar', 'UINavigationItem', 'UIBarButtonItem', 'UIToolbar',
    'UITabBar', 'UITabBarItem', 'UISearchController', 'UISearchDisplayController',
    
    # 弹窗和提示
    'UIAlertController', 'UIAlertView', 'UIActionSheet', 'UIAlertAction',
    'UIPopoverController', 'UIMenuController', 'UIMenuItem',
    
    # 图像和颜色
    'UIImage', 'UIImageAsset', 'UIColor', 'UIFont', 'UIFontDescriptor',
    'UIBezierPath', 'UIGraphicsRenderer', 'UIGraphicsImageRenderer',
    
    # 手势识别
    'UIGestureRecognizer', 'UITapGestureRecognizer', 'UIPinchGestureRecognizer',
    'UIRotationGestureRecognizer', 'UISwipeGestureRecognizer', 'UIPanGestureRecognizer',
    'UILongPressGestureRecognizer', 'UIScreenEdgePanGestureRecognizer',
    
    # 动画
    'UIViewPropertyAnimator', 'UISpringTimingParameters',
    'UICubicTimingParameters', 'UIViewAnimatingState',
    
    # 应用程序
    'UIApplication', 'UIApplicationDelegate', 'UIScene', 'UISceneDelegate',
    'UIWindowScene', 'UISceneSession', 'UISceneConfiguration',
    
    # 状态栏和导航栏
    'UIStatusBarManager', 'UINavigationBarAppearance', 'UIBarAppearance',
    'UITabBarAppearance',
    
    # 输入和键盘
    'UITextInput', 'UITextInputTraits', 'UITextPosition', 'UITextRange',
    'UITextSelectionRect', 'UIMenuController', 'UITextChecker',
    
    # 拖拽
    'UIDragItem', 'UIDragPreview', 'UIDragSession', 'UIDropProposal',
    'UIDragInteraction', 'UIDropInteraction',
    
    # 其他UI组件
    'UIVisualEffectView', 'UIBlurEffect', 'UIVibrancyEffect',
    'UIMotionEffect', 'UIInterpolatingMotionEffect', 'UIMotionEffectGroup',
    'UIFeedbackGenerator', 'UIImpactFeedbackGenerator', 'UISelectionFeedbackGenerator',
    'UINotificationFeedbackGenerator',

    # 更多UIKit类型
    'UIResponder', 'UIEvent', 'UITouch', 'UIPress', 'UIPressesEvent',
    'UIControl', 'UIButton', 'UIDatePicker', 'UIPageControl', 'UISegmentedControl',
    'UISlider', 'UIStepper', 'UISwitch', 'UITextField', 'UITextView',
    'UIActivityIndicatorView', 'UIProgressView', 'UIPickerView',
    'UISearchBar', 'UIToolbar', 'UINavigationBar', 'UITabBar',
    'UIBarItem', 'UIBarButtonItem', 'UITabBarItem', 'UINavigationItem',
    'UISearchController', 'UISearchContainerViewController', 'UISearchResultsUpdating',
    'UITableViewHeaderFooterView', 'UITableViewRowAction', 'UISwipeActionsConfiguration',
    'UIContextualAction', 'UITableViewDropCoordinator', 'UITableViewDragPreview',
    'UICollectionViewDropCoordinator', 'UICollectionViewDragPreview',
    'UICollectionViewCompositionalLayout', 'UICollectionViewDiffableDataSource',
    'NSCollectionLayoutSection', 'NSCollectionLayoutGroup', 'NSCollectionLayoutItem',
    'UIListContentConfiguration', 'UICellConfigurationState',
    'UIBackgroundConfiguration', 'UIContentConfiguration',
    'UIConfigurationState', 'UIViewConfigurationState',
    'UIImageConfiguration', 'UIImageSymbolConfiguration',
    'UIFontMetrics', 'UIFontTextStyle', 'UITraitCollection', 'UITraitEnvironment',
    'UIUserInterfaceStyle', 'UIUserInterfaceSizeClass', 'UIUserInterfaceIdiom',
    'UIDisplayGamut', 'UIForceTouchCapability', 'UILayoutGuide',
    'NSLayoutConstraint', 'NSLayoutAnchor', 'NSLayoutXAxisAnchor', 'NSLayoutYAxisAnchor',
    'NSLayoutDimension', 'UIStackView', 'UIStackViewDistribution',
    'UIStackViewAlignment', 'UILayoutPriority', 'UIEdgeInsets',
    'UIOffset', 'UIRectEdge', 'UIRectCorner', 'UIViewAnimationOptions',
    'UIViewKeyframeAnimationOptions', 'UIViewAnimationCurve',
    'UIModalTransitionStyle', 'UIModalPresentationStyle',
    'UIInterfaceOrientation', 'UIInterfaceOrientationMask',
    'UIStatusBarStyle', 'UIStatusBarAnimation', 'UIViewContentMode',
    'UIViewAutoresizing', 'UIViewTintAdjustmentMode',
    'UIScrollViewIndicatorStyle', 'UIScrollViewKeyboardDismissMode',
    'UIScrollViewIndexDisplayMode', 'UIScrollViewContentInsetAdjustmentBehavior',
    'UITableViewStyle', 'UITableViewCellStyle', 'UITableViewCellSelectionStyle',
    'UITableViewCellAccessoryType', 'UITableViewCellEditingStyle',
    'UITableViewRowAnimation', 'UITableViewScrollPosition',
    'UICollectionViewScrollDirection', 'UICollectionViewScrollPosition',
    'UICollectionElementKindSection', 'UICollectionElementCategory',
    'UITextBorderStyle', 'UITextFieldViewMode', 'UIControlContentVerticalAlignment',
    'UIControlContentHorizontalAlignment', 'UIControlState', 'UIControlEvents',
    'UIButtonType', 'UIDatePickerMode', 'UIDatePickerStyle',
    'UIProgressViewStyle', 'UIActivityIndicatorViewStyle',
    'UISegmentedControlStyle', 'UISliderStyle', 'UISearchBarStyle',
    'UIBarStyle', 'UIBarMetrics', 'UIBarPosition', 'UIBarButtonItemStyle',
    'UIBarButtonSystemItem', 'UITabBarSystemItem', 'UINavigationItemLargeTitleDisplayMode',
    'UISemanticContentAttribute', 'UIUserInterfaceLayoutDirection'
]

# Core Graphics类型
CORE_GRAPHICS_TYPES = [
    'CGContext', 'CGPath', 'CGMutablePath', 'CGImage', 'CGImageSource',
    'CGImageDestination', 'CGColorSpace', 'CGColor', 'CGGradient',
    'CGShading', 'CGPattern', 'CGFont', 'CGDataProvider', 'CGDataConsumer',
    'CGPDFDocument', 'CGPDFPage', 'CGLayer', 'CGFunction'
]

# Core Animation类型
CORE_ANIMATION_TYPES = [
    'CALayer', 'CAScrollLayer', 'CATextLayer', 'CAShapeLayer', 'CAGradientLayer',
    'CAReplicatorLayer', 'CATransformLayer', 'CAEmitterLayer', 'CAEmitterCell',
    'CAAnimation', 'CABasicAnimation', 'CAKeyframeAnimation', 'CAAnimationGroup',
    'CATransition', 'CASpringAnimation', 'CADisplayLink', 'CAMediaTiming',
    'CAMediaTimingFunction', 'CAValueFunction'
]

# Core Data类型
CORE_DATA_TYPES = [
    'NSManagedObject', 'NSManagedObjectContext', 'NSManagedObjectModel',
    'NSPersistentStore', 'NSPersistentStoreCoordinator', 'NSPersistentContainer',
    'NSEntityDescription', 'NSAttributeDescription', 'NSRelationshipDescription',
    'NSFetchRequest', 'NSFetchedResultsController', 'NSMigrationManager', 'NSMappingModel'
]

# AVFoundation类型
AVFOUNDATION_TYPES = [
    'AVPlayer', 'AVPlayerItem', 'AVPlayerLayer', 'AVPlayerViewController',
    'AVAudioPlayer', 'AVAudioRecorder', 'AVAudioSession', 'AVAudioEngine',
    'AVCaptureSession', 'AVCaptureDevice', 'AVCaptureInput', 'AVCaptureOutput',
    'AVCaptureVideoPreviewLayer', 'AVAsset', 'AVURLAsset', 'AVComposition',
    'AVVideoComposition', 'AVAudioMix'
]

# MapKit类型
MAPKIT_TYPES = [
    'MKMapView', 'MKAnnotation', 'MKAnnotationView', 'MKPinAnnotationView',
    'MKUserLocation', 'MKPlacemark', 'MKMapItem', 'MKDirections',
    'MKRoute', 'MKPolyline', 'MKPolygon', 'MKCircle', 'MKOverlay',
    'MKOverlayRenderer', 'MKTileOverlay', 'MKLocalSearch'
]

# 其他常用框架类型
OTHER_FRAMEWORK_TYPES = [
    # GameKit
    'GKLocalPlayer', 'GKAchievement', 'GKLeaderboard', 'GKScore',
    'GKMatch', 'GKMatchmaker', 'GKPlayer', 'GKTurnBasedMatch',
    'GKGameCenterViewController', 'GKAchievementDescription',

    # StoreKit
    'SKProduct', 'SKPayment', 'SKPaymentTransaction', 'SKPaymentQueue',
    'SKProductsRequest', 'SKStoreProductViewController', 'SKReceiptRefreshRequest',
    'SKDownload', 'SKPaymentDiscount', 'SKProductSubscriptionPeriod',
    'SKProductDiscount', 'SKStorefront', 'SKCloudServiceController',

    # Social
    'SLComposeViewController', 'SLRequest',

    # MessageUI
    'MFMailComposeViewController', 'MFMessageComposeViewController',

    # EventKit
    'EKEventStore', 'EKEvent', 'EKCalendar', 'EKReminder', 'EKAlarm',
    'EKRecurrenceRule', 'EKSource', 'EKParticipant', 'EKCalendarItem',

    # AddressBook (已弃用，但仍可能遇到)
    'ABAddressBook', 'ABPerson', 'ABRecord', 'ABGroup', 'ABSource',

    # Contacts
    'CNContact', 'CNContactStore', 'CNContactFormatter', 'CNMutableContact',
    'CNContactProperty', 'CNContactRelation', 'CNPhoneNumber', 'CNPostalAddress',
    'CNContactViewController', 'CNContactPickerViewController',

    # Photos
    'PHAsset', 'PHAssetCollection', 'PHPhotoLibrary', 'PHImageManager',
    'PHCachingImageManager', 'PHImageRequestOptions', 'PHVideoRequestOptions',
    'PHAssetChangeRequest', 'PHAssetCollectionChangeRequest', 'PHChange',
    'PHFetchResult', 'PHFetchOptions', 'PHCollection', 'PHCollectionList',

    # HealthKit
    'HKHealthStore', 'HKSample', 'HKQuantitySample', 'HKWorkout',
    'HKQuantityType', 'HKCategoryType', 'HKCharacteristicType',
    'HKCorrelationType', 'HKWorkoutType', 'HKQuery', 'HKSampleQuery',
    'HKAnchoredObjectQuery', 'HKStatisticsQuery', 'HKStatisticsCollectionQuery',

    # CloudKit
    'CKContainer', 'CKDatabase', 'CKRecord', 'CKRecordZone',
    'CKQuery', 'CKQueryOperation', 'CKModifyRecordsOperation',
    'CKFetchRecordsOperation', 'CKAsset', 'CKReference', 'CKLocation',

    # UserNotifications
    'UNUserNotificationCenter', 'UNNotificationRequest', 'UNNotificationContent',
    'UNMutableNotificationContent', 'UNNotificationTrigger', 'UNTimeIntervalNotificationTrigger',
    'UNCalendarNotificationTrigger', 'UNLocationNotificationTrigger',
    'UNNotificationAction', 'UNNotificationCategory', 'UNNotificationAttachment',
    'UNNotificationSound', 'UNNotificationServiceExtension',

    # App Tracking Transparency (用户报告的问题类型)
    'ATTrackingManager', 'ATTrackingManagerAuthorizationStatus',

    # Security Framework
    'SecKeyRef', 'SecCertificateRef', 'SecIdentityRef', 'SecTrustRef',
    'SecPolicyRef', 'SecAccessControlRef', 'SecRandomRef',

    # Network Framework
    'NWConnection', 'NWEndpoint', 'NWParameters', 'NWPath', 'NWPathMonitor',
    'NWListener', 'NWBrowser', 'NWAdvertiseDescriptor',

    # Combine Framework
    'AnyPublisher', 'AnySubscriber', 'AnySubject', 'PassthroughSubject',
    'CurrentValueSubject', 'AnyCancellable', 'Cancellable',

    # SwiftUI Bridge Types
    'UIHostingController', 'UIViewRepresentable', 'UIViewControllerRepresentable',

    # Core Location
    'CLLocationManager', 'CLLocation', 'CLLocationCoordinate2D', 'CLRegion',
    'CLCircularRegion', 'CLBeaconRegion', 'CLPlacemark', 'CLGeocoder',
    'CLHeading', 'CLVisit', 'CLBeacon', 'CLBeaconIdentityConstraint',

    # Core Motion
    'CMMotionManager', 'CMAccelerometerData', 'CMGyroData', 'CMMagnetometerData',
    'CMDeviceMotion', 'CMAttitude', 'CMRotationRate', 'CMAcceleration',
    'CMStepCounter', 'CMPedometer', 'CMAltimeter', 'CMActivityManager',

    # Core Bluetooth
    'CBCentralManager', 'CBPeripheralManager', 'CBPeripheral', 'CBService',
    'CBCharacteristic', 'CBDescriptor', 'CBUUID', 'CBAdvertisementData',

    # PassKit
    'PKPaymentAuthorizationViewController', 'PKPaymentRequest', 'PKPaymentSummaryItem',
    'PKContact', 'PKPayment', 'PKPaymentToken', 'PKPaymentMethod',
    'PKPass', 'PKPassLibrary', 'PKAddPassesViewController',

    # Vision Framework
    'VNRequest', 'VNImageRequestHandler', 'VNSequenceRequestHandler',
    'VNDetectFaceRectanglesRequest', 'VNDetectTextRectanglesRequest',
    'VNDetectBarcodesRequest', 'VNObservation', 'VNFaceObservation',

    # Natural Language
    'NLTokenizer', 'NLLanguageRecognizer', 'NLTagger', 'NLModel',
    'NLEmbedding', 'NLGazetteer', 'NLContextualEmbedding',

    # Speech Framework
    'SFSpeechRecognizer', 'SFSpeechAudioBufferRecognitionRequest',
    'SFSpeechURLRecognitionRequest', 'SFSpeechRecognitionTask',
    'SFSpeechRecognitionResult', 'SFTranscription', 'SFTranscriptionSegment',

    # Metal Framework
    'MTLDevice', 'MTLCommandQueue', 'MTLCommandBuffer', 'MTLRenderCommandEncoder',
    'MTLComputeCommandEncoder', 'MTLTexture', 'MTLBuffer', 'MTLLibrary',
    'MTLFunction', 'MTLRenderPipelineState', 'MTLComputePipelineState',

    # WebKit
    'WKWebView', 'WKWebViewConfiguration', 'WKUserContentController',
    'WKUserScript', 'WKScriptMessageHandler', 'WKNavigationDelegate',
    'WKUIDelegate', 'WKHTTPCookieStore', 'WKWebsiteDataStore',

    # QuickLook
    'QLPreviewController', 'QLPreviewItem', 'QLPreviewControllerDataSource',
    'QLPreviewControllerDelegate', 'QLThumbnailGenerator', 'QLThumbnailRequest',

    # FileProvider
    'NSFileProviderExtension', 'NSFileProviderItem', 'NSFileProviderManager',
    'NSFileProviderDomain', 'NSFileProviderEnumerator',

    # CallKit
    'CXCallController', 'CXProvider', 'CXProviderConfiguration', 'CXCall',
    'CXCallAction', 'CXStartCallAction', 'CXAnswerCallAction', 'CXEndCallAction',

    # Intents
    'INIntent', 'INIntentResponse', 'INInteraction', 'INVoiceShortcutCenter',
    'INUIHostedViewControlling', 'INUIAddVoiceShortcutViewController',

    # WidgetKit
    'WidgetConfiguration', 'StaticConfiguration', 'IntentConfiguration',
    'WidgetFamily', 'TimelineEntry', 'TimelineProvider',

    # BackgroundTasks
    'BGTaskScheduler', 'BGTask', 'BGAppRefreshTask', 'BGProcessingTask',
    'BGTaskRequest', 'BGAppRefreshTaskRequest', 'BGProcessingTaskRequest',

    # DeviceCheck
    'DCDevice', 'DCAppAttestService', 'DCError',

    # AdSupport
    'ASIdentifierManager', 'ASAdvertisingAttributionReportEndpoint',

    # AppClip
    'APActivationPayload', 'APActivationURL',

    # AuthenticationServices
    'ASAuthorizationController', 'ASAuthorizationRequest', 'ASAuthorizationCredential',
    'ASAuthorizationAppleIDCredential', 'ASAuthorizationPasswordCredential',
    'ASAuthorizationAppleIDProvider', 'ASAuthorizationPasswordProvider',
    'ASWebAuthenticationSession', 'ASAccountAuthenticationModificationController',

    # CryptoKit Bridge
    'CKRecord', 'CKAsset', 'CKReference', 'CKShare', 'CKUserIdentity',

    # LinkPresentation
    'LPLinkMetadata', 'LPMetadataProvider', 'LPLinkView',

    # PencilKit
    'PKCanvasView', 'PKDrawing', 'PKStroke', 'PKInk', 'PKTool',
    'PKInkingTool', 'PKLassoTool', 'PKEraserTool', 'PKToolPicker',

    # RealityKit
    'ARView', 'RealityKit', 'Entity', 'ModelEntity', 'AnchorEntity',

    # SoundAnalysis
    'SNAudioStreamAnalyzer', 'SNClassifySoundRequest', 'SNClassificationResult',

    # OSLog
    'OSLog', 'OSLogStore', 'OSLogEntry', 'OSLogEntryLog', 'OSLogEntrySignpost',

    # UniformTypeIdentifiers
    'UTType', 'UTTagClass', 'UTTypeReference'
]

# 系统枚举和常量类型
SYSTEM_ENUM_TYPES = [
    # Foundation枚举
    'NSStringEncoding', 'NSStringCompareOptions', 'NSStringEnumerationOptions',
    'NSDataReadingOptions', 'NSDataWritingOptions', 'NSDataSearchOptions',
    'NSArrayBinarySearchingOptions', 'NSDictionaryReadingOptions', 'NSDictionaryWritingOptions',
    'NSSetOptions', 'NSOrderedSetOptions', 'NSMapTableOptions', 'NSHashTableOptions',
    'NSPointerFunctionsOptions', 'NSNumberFormatterStyle', 'NSNumberFormatterBehavior',
    'NSDateFormatterStyle', 'NSDateFormatterBehavior', 'NSCalendarUnit', 'NSCalendarOptions',
    'NSTimeZoneNameStyle', 'NSLocaleLanguageDirection', 'NSComparisonResult',
    'NSOperationQueuePriority', 'NSQualityOfService', 'NSURLRequestCachePolicy',
    'NSURLRequestNetworkServiceType', 'NSHTTPCookieAcceptPolicy', 'NSStreamStatus',
    'NSStreamEvent', 'NSNetServicesError', 'NSNetServiceOptions',
    'NSFileManagerItemReplacementOptions', 'NSDirectoryEnumerationOptions',
    'NSFileManagerUnmountOptions', 'NSBundleResourceRequest', 'NSProcessInfoThermalState',
    'NSProcessInfoPowerState', 'NSErrorUserInfoKey', 'NSExceptionName',
    'NSRunLoopMode', 'NSNotificationSuspensionBehavior', 'NSPostingStyle',
    'NSNotificationCoalescing', 'NSKeyValueObservingOptions', 'NSKeyValueChange',
    'NSKeyValueSetMutationKind', 'NSPredicateOperatorType', 'NSComparisonPredicateModifier',
    'NSComparisonPredicateOptions', 'NSExpressionType', 'NSUndoManagerGroupingLevel',
    'NSValueTransformerName', 'NSStringTransform', 'NSLinguisticTag',
    'NSLinguisticTagScheme', 'NSOrthographyOptions', 'NSTextCheckingType',
    'NSTextCheckingOptions', 'NSRegularExpressionOptions', 'NSMatchingOptions',
    'NSMatchingFlags', 'NSDataDetectorTypes', 'NSURLSessionAuthChallengeDisposition',
    'NSURLCredentialPersistence', 'NSURLSessionTaskState', 'NSURLSessionResponseDisposition',
    'NSURLSessionDelayedRequestDisposition', 'NSURLErrorCode', 'NSCocoaError',
    'NSPOSIXError', 'NSMachError', 'NSXMLParserError', 'NSPropertyListFormat',
    'NSPropertyListMutabilityOptions', 'NSPropertyListWriteOptions', 'NSPropertyListReadOptions',
    'NSJSONReadingOptions', 'NSJSONWritingOptions', 'NSKeyedArchiveRootObjectKey',
    'NSUbiquitousKeyValueStoreChangeReason', 'NSUserDefaultsType',

    # UIKit枚举
    'UIDeviceOrientation', 'UIInterfaceOrientation', 'UIInterfaceOrientationMask',
    'UIUserInterfaceIdiom', 'UIUserInterfaceSizeClass', 'UIUserInterfaceStyle',
    'UIUserInterfaceLayoutDirection', 'UISemanticContentAttribute', 'UITraitEnvironment',
    'UIDisplayGamut', 'UIForceTouchCapability', 'UITouchType', 'UITouchPhase',
    'UITouchProperties', 'UIPressPhase', 'UIPressType', 'UIEventType', 'UIEventSubtype',
    'UIControlState', 'UIControlEvents', 'UIControlContentVerticalAlignment',
    'UIControlContentHorizontalAlignment', 'UIButtonType', 'UIButtonConfiguration',
    'UIDatePickerMode', 'UIDatePickerStyle', 'UIProgressViewStyle', 'UISliderStyle',
    'UIActivityIndicatorViewStyle', 'UISegmentedControlStyle', 'UISegmentedControlSegment',
    'UITextBorderStyle', 'UITextFieldViewMode', 'UITextAutocapitalizationType',
    'UITextAutocorrectionType', 'UITextSpellCheckingType', 'UITextSmartQuotesType',
    'UITextSmartDashesType', 'UITextSmartInsertDeleteType', 'UIKeyboardType',
    'UIKeyboardAppearance', 'UIReturnKeyType', 'UITextContentType',
    'UITextInputPasswordRules', 'UISearchBarStyle', 'UISearchBarIcon',
    'UIBarStyle', 'UIBarMetrics', 'UIBarPosition', 'UIBarButtonItemStyle',
    'UIBarButtonSystemItem', 'UITabBarSystemItem', 'UINavigationItemLargeTitleDisplayMode',
    'UIViewContentMode', 'UIViewAutoresizing', 'UIViewTintAdjustmentMode',
    'UIViewAnimationCurve', 'UIViewAnimationTransition', 'UIViewAnimationOptions',
    'UIViewKeyframeAnimationOptions', 'UISystemAnimation', 'UILayoutConstraintAxis',
    'UILayoutPriority', 'UIStackViewDistribution', 'UIStackViewAlignment',
    'UIScrollViewIndicatorStyle', 'UIScrollViewKeyboardDismissMode',
    'UIScrollViewIndexDisplayMode', 'UIScrollViewContentInsetAdjustmentBehavior',
    'UIScrollViewDecelerationRate', 'UITableViewStyle', 'UITableViewCellStyle',
    'UITableViewCellSelectionStyle', 'UITableViewCellAccessoryType',
    'UITableViewCellEditingStyle', 'UITableViewCellStateMask', 'UITableViewRowAnimation',
    'UITableViewScrollPosition', 'UITableViewAutomaticDimension', 'UITableViewSeparatorStyle',
    'UICollectionViewScrollDirection', 'UICollectionViewScrollPosition',
    'UICollectionElementKindSection', 'UICollectionElementCategory',
    'UICollectionViewReloadItem', 'UICollectionViewItemHighlightState',
    'UICollectionViewCellDragState', 'UICollectionViewDropIntent',
    'UIModalTransitionStyle', 'UIModalPresentationStyle', 'UIPopoverArrowDirection',
    'UIAlertControllerStyle', 'UIAlertActionStyle', 'UIImageOrientation',
    'UIImageResizingMode', 'UIImageRenderingMode', 'UIImagePickerControllerSourceType',
    'UIImagePickerControllerQualityType', 'UIImagePickerControllerCameraDevice',
    'UIImagePickerControllerCameraCaptureMode', 'UIImagePickerControllerCameraFlashMode',
    'UIStatusBarStyle', 'UIStatusBarAnimation', 'UIApplicationState',
    'UIBackgroundTaskIdentifier', 'UIBackgroundRefreshStatus', 'UIRemoteNotificationType',
    'UIUserNotificationType', 'UIUserNotificationActionBehavior', 'UIUserNotificationActivationMode',
    'UIUserNotificationActionContext', 'UILocalNotificationDefaultSoundName',
    'UIGestureRecognizerState', 'UISwipeGestureRecognizerDirection',
    'UITextLayoutDirection', 'UITextStorageDirection', 'UITextWritingDirection',
    'UITextGranularity', 'UITextDirection', 'UITextSelectionAffinity',
    'UIMenuControllerArrowDirection', 'UIDocumentState', 'UIDocumentChangeKind',
    'UIDocumentSaveOperation', 'UIPrintInfoOutputType', 'UIPrintInfoOrientation',
    'UIPrintInfoDuplex', 'UIPrintJobDisposition', 'UIPrinterJobTypes',
    'UIVideoEditorControllerQualityType', 'UIScreenOverscanCompensation',
    'UIFontTextStyle', 'UIFontWeight', 'UIFontWidth', 'UIAccessibilityTraits',
    'UIAccessibilityNavigationStyle', 'UIAccessibilityScrollDirection',
    'UIAccessibilityZoomType', 'UIAccessibilityCustomRotorDirection',
    'UIAccessibilityCustomSystemRotorType', 'UIAccessibilityContainerType',
    'UILargeContentViewerStyle', 'UIPointerEffectTintMode', 'UIPointerShape',
    'UIBandSelectionInteractionState', 'UIEditMenuArrowDirection',
    'UISheetPresentationControllerDetentIdentifier', 'UINavigationBarNSToolbarSection',

    # Core Graphics枚举
    'CGBlendMode', 'CGColorSpace', 'CGColorRenderingIntent', 'CGInterpolationQuality',
    'CGLineCap', 'CGLineJoin', 'CGPathDrawingMode', 'CGTextDrawingMode',
    'CGTextEncoding', 'CGFontPostScriptFormat', 'CGImageAlphaInfo',
    'CGImageByteOrderInfo', 'CGImagePixelFormatInfo', 'CGBitmapInfo',
    'CGColorSpaceModel', 'CGPatternTiling', 'CGGradientDrawingOptions',
    'CGShadingType', 'CGFunctionType', 'CGDataProviderSequentialCallbacks',
    'CGDataProviderDirectCallbacks', 'CGDataConsumerCallbacks', 'CGPDFObjectType',
    'CGPDFDataFormat', 'CGPDFBox', 'CGPDFAccessPermissions',

    # Core Animation枚举
    'CALayerContentsGravity', 'CALayerContentsFormat', 'CALayerCornerCurve',
    'CALayerMaskedCorners', 'CAEdgeAntialiasingMask', 'CAAutoresizingMask',
    'CAConstraintAttribute', 'CATransform3D', 'CAMediaTimingFillMode',
    'CAAnimationCalculationMode', 'CAAnimationRotationMode', 'CATransitionType',
    'CATransitionSubtype', 'CAEmitterLayerEmitterShape', 'CAEmitterLayerEmitterMode',
    'CAEmitterLayerRenderMode', 'CAEmitterCellEmitterType', 'CAScrollLayerScrollMode',
    'CATextLayerTruncationMode', 'CATextLayerAlignmentMode', 'CAGradientLayerType',
    'CAShapeLayerLineJoin', 'CAShapeLayerLineCap', 'CAShapeLayerFillRule',
    'CAValueFunctionName', 'CAMediaTimingFunctionName', 'CAFrameRateRange',

    # AVFoundation枚举
    'AVMediaType', 'AVFileType', 'AVVideoCodecType', 'AVAudioFormat',
    'AVPlayerStatus', 'AVPlayerItemStatus', 'AVPlayerTimeControlStatus',
    'AVPlayerActionAtItemEnd', 'AVPlayerHDRMode', 'AVAudioSessionCategory',
    'AVAudioSessionCategoryOptions', 'AVAudioSessionMode', 'AVAudioSessionRouteSharingPolicy',
    'AVAudioSessionInterruptionType', 'AVAudioSessionInterruptionOptions',
    'AVAudioSessionRouteChangeReason', 'AVAudioSessionSilenceSecondaryAudioHintType',
    'AVCaptureSessionPreset', 'AVCaptureDeviceType', 'AVCaptureDevicePosition',
    'AVCaptureFlashMode', 'AVCaptureTorchMode', 'AVCaptureFocusMode',
    'AVCaptureExposureMode', 'AVCaptureWhiteBalanceMode', 'AVCaptureAutoFocusSystem',
    'AVCaptureVideoStabilizationMode', 'AVCaptureColorSpace', 'AVVideoFieldMode',
    'AVVideoCodecSettings', 'AVVideoCompressionProperties', 'AVVideoProfileLevel',
    'AVAssetImageGeneratorApertureMode', 'AVAssetImageGeneratorResult',
    'AVAssetExportSessionStatus', 'AVAssetReaderStatus', 'AVAssetWriterStatus',
    'AVKeyValueStatus', 'AVPlayerLooper', 'AVQueuedSampleBufferRenderingStatus',

    # MapKit枚举
    'MKMapType', 'MKUserTrackingMode', 'MKAnnotationViewDragState',
    'MKPinAnnotationColor', 'MKOverlayLevel', 'MKDirectionsTransportType',
    'MKDistanceFormatter', 'MKLaunchOptionsKey', 'MKLocalSearchCompleterResultType',
    'MKSearchCompletionFilterType', 'MKPointOfInterestCategory',
    'MKFeatureVisibility', 'MKMapFeatureType', 'MKStandardMapEmphasisStyle'
]

def get_all_pointer_types():
    """获取所有指针类型"""
    all_types = (FOUNDATION_TYPES + UIKIT_TYPES + CORE_GRAPHICS_TYPES +
                CORE_ANIMATION_TYPES + CORE_DATA_TYPES + AVFOUNDATION_TYPES +
                MAPKIT_TYPES + OTHER_FRAMEWORK_TYPES + SYSTEM_ENUM_TYPES)

    # 去重并按长度降序排序
    unique_types = list(set(all_types))
    return sorted(unique_types, key=lambda x: len(x), reverse=True)

def get_foundation_types():
    """获取Foundation框架类型"""
    return FOUNDATION_TYPES

def get_uikit_types():
    """获取UIKit框架类型"""
    return UIKIT_TYPES

def get_types_by_framework(framework_name):
    """根据框架名获取类型"""
    framework_map = {
        'Foundation': FOUNDATION_TYPES,
        'UIKit': UIKIT_TYPES,
        'CoreGraphics': CORE_GRAPHICS_TYPES,
        'CoreAnimation': CORE_ANIMATION_TYPES,
        'CoreData': CORE_DATA_TYPES,
        'AVFoundation': AVFOUNDATION_TYPES,
        'MapKit': MAPKIT_TYPES,
        'Other': OTHER_FRAMEWORK_TYPES
    }
    return framework_map.get(framework_name, [])

if __name__ == "__main__":
    # 测试代码
    all_types = get_all_pointer_types()
    print(f"总共有 {len(all_types)} 个类型")
    print(f"Foundation: {len(FOUNDATION_TYPES)} 个")
    print(f"UIKit: {len(UIKIT_TYPES)} 个")
    print(f"其他框架: {len(OTHER_FRAMEWORK_TYPES)} 个")
